# ChallengesKeystoneFrameMixin:CreateAndPositionAffixes 函数技术文档

## 1. 函数概述

`CreateAndPositionAffixes` 是魔兽世界大秘境（Mythic+ Dungeons）UI系统中的核心函数，负责在挑战模式钥石界面中动态创建、定位和管理词缀（Affix）图标框架。该函数确保词缀图标能够根据数量自动调整布局，实现完美的居中对齐显示效果。

## 2. 函数签名

```lua
function ChallengesKeystoneFrameMixin:CreateAndPositionAffixes(num)
```

- **所属类**: `ChallengesKeystoneFrameMixin`
- **函数名**: `CreateAndPositionAffixes`
- **访问修饰符**: 公共方法
- **调用方式**: 实例方法调用

## 3. 参数详解

### num
- **类型**: `number` (整数)
- **含义**: 需要显示的词缀总数量
- **取值范围**: 通常为 2-5 个（2个基础词缀 + 0-3个额外词缀）
- **用途**: 确定需要创建和显示的词缀框架数量
- **组成**: 
  - 2个基础词缀：伤害增幅和生命值增幅
  - 0-3个特殊词缀：根据钥石等级动态添加

## 4. 返回值

该函数无返回值（`void`类型）。

## 5. 功能描述

该函数实现了大秘境界面中词缀图标的完整生命周期管理：

- **动态创建**: 根据需要创建新的词缀框架
- **智能布局**: 实现基于数学算法的居中对齐
- **资源管理**: 复用现有框架，隐藏多余框架
- **响应式设计**: 适应不同数量的词缀显示需求

## 6. 算法流程

### 6.1 框架创建阶段
```lua
local index = #self.Affixes + 1;
while (#self.Affixes < num) do
    local frame = CreateFrame("Frame", nil, self, "ChallengesKeystoneFrameAffixTemplate");
    local prev = self.Affixes[index - 1];
    frame:SetPoint("LEFT", prev, "RIGHT", spacing, 0);
    index = index + 1;
end
```

### 6.2 居中对齐算法

#### 奇数词缀布局
```lua
if (num % 2 == 1) then
    local x = (num - 1) / 2;
    frame:SetPoint("TOPLEFT", self.Divider, "TOP", 
        -((frameWidth / 2) + (frameWidth * x) + (spacing * x)), distance);
end
```

#### 偶数词缀布局
```lua
else
    local x = num / 2;
    frame:SetPoint("TOPLEFT", self.Divider, "TOP", 
        -((frameWidth * x) + (spacing * (x - 1)) + (spacing / 2)), distance);
end
```

### 6.3 资源清理阶段
```lua
for i = num + 1, #self.Affixes do
    self.Affixes[i]:Hide();
end
```

## 7. 关键变量

| 变量名 | 类型 | 值 | 说明 |
|--------|------|----|----- |
| `index` | number | #self.Affixes + 1 | 新框架的索引位置 |
| `frameWidth` | number | 52 | 每个词缀框架的宽度（像素） |
| `spacing` | number | 4 | 词缀框架之间的间距（像素） |
| `distance` | number | -34 | 相对于分隔线的垂直偏移（像素） |
| `x` | number | 计算值 | 居中对齐的辅助计算变量 |
| `frame` | Frame | UI对象 | 词缀框架的引用 |
| `prev` | Frame | UI对象 | 前一个词缀框架的引用 |

## 8. API调用

### 8.1 CreateFrame
```lua
CreateFrame("Frame", nil, self, "ChallengesKeystoneFrameAffixTemplate")
```
- **功能**: 创建新的UI框架
- **参数1**: 框架类型（"Frame"）
- **参数2**: 框架名称（nil表示匿名）
- **参数3**: 父框架（self）
- **参数4**: 使用的模板名称

### 8.2 SetPoint
```lua
frame:SetPoint("LEFT", prev, "RIGHT", spacing, 0)
```
- **功能**: 设置框架的锚点位置
- **参数1**: 当前框架的锚点
- **参数2**: 参考框架
- **参数3**: 参考框架的锚点
- **参数4**: 水平偏移量
- **参数5**: 垂直偏移量

### 8.3 ClearAllPoints
```lua
frame:ClearAllPoints()
```
- **功能**: 清除框架的所有锚点设置

### 8.4 Hide
```lua
self.Affixes[i]:Hide()
```
- **功能**: 隐藏UI框架

## 9. UI布局原理

### 9.1 布局基准
- **参考点**: `self.Divider`（分隔线）的顶部中心
- **布局方向**: 水平排列
- **对齐方式**: 居中对齐

### 9.2 数学原理

#### 奇数布局计算
```
总宽度 = frameWidth * num + spacing * (num - 1)
左偏移 = -(总宽度 / 2 - frameWidth / 2)
```

#### 偶数布局计算
```
总宽度 = frameWidth * num + spacing * (num - 1)
左偏移 = -(总宽度 / 2)
```

## 10. 使用场景

该函数在以下情况下被调用：

1. **钥石插入时**: 当玩家将大秘境钥石插入钥石槽时
2. **界面刷新时**: 当挑战模式界面需要更新词缀显示时
3. **钥石信息变更时**: 当钥石的词缀信息发生变化时

### 调用示例
```lua
-- 在OnKeystoneSlotted函数中的调用
self:CreateAndPositionAffixes(2 + #affixes);
```

## 11. 相关函数

### 11.1 调用此函数的方法
- `ChallengesKeystoneFrameMixin:OnKeystoneSlotted()`: 钥石插入事件处理

### 11.2 相关的词缀处理方法
- `AffixFrame:SetUp(affixData)`: 设置词缀框架的数据和显示
- `ChallengesKeystoneFrameMixin:Reset()`: 重置钥石框架状态

### 11.3 相关的UI模板
- `ChallengesKeystoneFrameAffixTemplate`: 词缀框架的XML模板

### 11.4 相关的API函数
- `C_ChallengeMode.GetSlottedKeystoneInfo()`: 获取已插入钥石的信息
- `C_ChallengeMode.GetPowerLevelDamageHealthMod()`: 获取伤害和生命值修正

## 12. 技术特点

- **响应式布局**: 自动适应不同数量的词缀
- **性能优化**: 复用现有框架，避免频繁创建销毁
- **数学精确**: 使用精确的数学计算确保完美居中
- **模板化设计**: 使用XML模板确保UI一致性
