<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">

	<Script file="Blizzard_AutoQuestPopUpTracker.lua"/>

	<Texture name="AutoQuest-QuestLogIcon" file="Interface\QuestFrame\AutoQuest-Parts" virtual="true">
		<Size x="10" y="13"/>
		<TexCoords left="0.00195313" right="0.02148438" top="0.01562500" bottom="0.21875000"/>
	</Texture>
	<Texture name="AutoQuestToastBorder-TopLeft" file="Interface\QuestFrame\AutoQuest-Parts" virtual="true">
		<Size x="16" y="16"/>
		<TexCoords left="0.02539063" right="0.05664063" top="0.01562500" bottom="0.26562500"/>
	</Texture>
	<Texture name="AutoQuestToastBorder-TopRight" file="Interface\QuestFrame\AutoQuest-Parts" virtual="true">
		<Size x="16" y="16"/>
		<TexCoords left="0.02539063" right="0.05664063" top="0.29687500" bottom="0.54687500"/>
	</Texture>
	<Texture name="AutoQuestToastBorder-BotLeft" file="Interface\QuestFrame\AutoQuest-Parts" virtual="true">
		<Size x="16" y="16"/>
		<TexCoords left="0.02539063" right="0.05664063" top="0.57812500" bottom="0.82812500"/>
	</Texture>
	<Texture name="AutoQuestToastBorder-BotRight" file="Interface\QuestFrame\AutoQuest-Parts" virtual="true">
		<Size x="16" y="16"/>
		<TexCoords left="0.06054688" right="0.09179688" top="0.01562500" bottom="0.26562500"/>
	</Texture>
	<Texture name="TrackerButton-AutoComplete-Up" file="Interface\QuestFrame\AutoQuest-Parts" virtual="true">
		<Size x="18" y="19"/>
		<TexCoords left="0.09570313" right="0.13085938" top="0.01562500" bottom="0.31250000"/>
	</Texture>
	<Texture name="TrackerButton-AutoComplete-Down" file="Interface\QuestFrame\AutoQuest-Parts" virtual="true">
		<Size x="18" y="19"/>
		<TexCoords left="0.09570313" right="0.13085938" top="0.34375000" bottom="0.64062500"/>
	</Texture>
	<Texture name="QuestIcon-Exclamation" file="Interface\QuestFrame\AutoQuest-Parts" virtual="true">
		<Size x="19" y="33"/>
		<TexCoords left="0.13476563" right="0.17187500" top="0.01562500" bottom="0.53125000"/>
	</Texture>
	<Texture name="QuestIcon-QuestionMark" file="Interface\QuestFrame\AutoQuest-Parts" virtual="true">
		<Size x="19" y="33"/>
		<TexCoords left="0.17578125" right="0.21289063" top="0.01562500" bottom="0.53125000"/>
	</Texture>
	<Texture name="QuestIcon-WhiteFlash" file="Interface\QuestFrame\AutoQuest-Parts" virtual="true">
		<Size x="42" y="42"/>
		<TexCoords left="0.21679688" right="0.29882813" top="0.01562500" bottom="0.67187500"/>
	</Texture>
	<Texture name="QuestIcon-Large" file="Interface\QuestFrame\AutoQuest-Parts" virtual="true">
		<Size x="60" y="60"/>
		<TexCoords left="0.30273438" right="0.41992188" top="0.01562500" bottom="0.95312500"/>
	</Texture>

	<ScrollFrame name="AutoQuestPopUpBlockTemplate" enableMouse="true" virtual="true" hidden="true">
		<Size x="232" y="68"/>
		<Scripts>
			<OnMouseUp function="AutoQuestPopUpTracker_OnMouseDown"/>
		</Scripts>
		<ScrollChild>
			<Frame parentKey="ScrollChild">
				<Size x="227" y="68"/>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture parentKey="Bg">
							<Anchors>
								<Anchor point="TOPLEFT" x="36" y="-4"/>
								<Anchor point="BOTTOMRIGHT" x="0" y="4"/>
							</Anchors>
							<Color r="0" g="0" b="0" a="0.5"/>
						</Texture>
					</Layer>
					<Layer level="BORDER">
						<Texture parentKey="BorderTopLeft" inherits="AutoQuestToastBorder-TopLeft">
							<Anchors>
								<Anchor point="TOPLEFT" x="32" y="0" />
							</Anchors>
						</Texture>
						<Texture parentKey="BorderTopRight" inherits="AutoQuestToastBorder-TopRight">
							<Anchors>
								<Anchor point="TOPRIGHT" x="4" y="0" />
							</Anchors>
						</Texture>
						<Texture parentKey="BorderBotLeft" inherits="AutoQuestToastBorder-BotLeft">
							<Anchors>
								<Anchor point="BOTTOMLEFT" x="32" y="0" />
							</Anchors>
						</Texture>
						<Texture parentKey="BorderBotRight" inherits="AutoQuestToastBorder-BotRight">
							<Anchors>
								<Anchor point="BOTTOMRIGHT" x="4" y="0"/>
							</Anchors>
						</Texture>
						<Texture parentKey="BorderLeft" file="Interface\QuestFrame\AutoQuestToastBorder-LeftRight" vertTile="true">
							<Size x="8" y="16"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeKey="$parent.BorderTopLeft" relativePoint="BOTTOMLEFT"/>
								<Anchor point="BOTTOMLEFT" relativeKey="$parent.BorderBotLeft" relativePoint="TOPLEFT"/>
							</Anchors>
							<TexCoords left="0" right="0.5" top="0" bottom="1"/>
						</Texture>
						<Texture parentKey="BorderRight" file="Interface\QuestFrame\AutoQuestToastBorder-LeftRight" vertTile="true">
							<Size x="8" y="16"/>
							<Anchors>
								<Anchor point="TOPRIGHT" relativeKey="$parent.BorderTopRight" relativePoint="BOTTOMRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BorderBotRight" relativePoint="TOPRIGHT"/>
							</Anchors>
							<TexCoords left="0.5" right="1" top="0" bottom="1"/>
						</Texture>
						<Texture parentKey="BorderTop" file="Interface\QuestFrame\AutoQuestToastBorder-TopBot" horizTile="true">
							<Size x="16" y="8"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeKey="$parent.BorderTopLeft" relativePoint="TOPRIGHT"/>
								<Anchor point="TOPRIGHT" relativeKey="$parent.BorderTopRight" relativePoint="TOPLEFT"/>
							</Anchors>
							<TexCoords left="0" right="1" top="0" bottom="0.5"/>
						</Texture>
						<Texture parentKey="BorderBottom" file="Interface\QuestFrame\AutoQuestToastBorder-TopBot" horizTile="true">
							<Size x="16" y="8"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativeKey="$parent.BorderBotLeft" relativePoint="BOTTOMRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BorderBotRight" relativePoint="BOTTOMLEFT"/>
							</Anchors>
							<TexCoords left="0" right="1" top="0.5" bottom="1"/>
						</Texture>
					</Layer>
					<Layer level="ARTWORK">
						<Texture parentKey="QuestIconBg" inherits="QuestIcon-Large">
							<Anchors>
								<Anchor point="CENTER" relativePoint="LEFT" x="36" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="ARTWORK" textureSubLevel="1">
						<Texture parentKey="Exclamation" inherits="QuestIcon-Exclamation" hidden="true">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.QuestIconBg" x="0.5" />
							</Anchors>
						</Texture>
						<Texture parentKey="QuestionMark" inherits="QuestIcon-QuestionMark" hidden="true">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.QuestIconBg" x="0.5" />
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="ARTWORK" textureSubLevel="2">
						<Texture parentKey="QuestIconBadgeBorder" atlas="AutoQuest-badgeborder" useAtlasSize="true">
							<Anchors>
								<Anchor point="TOPLEFT" relativeKey="$parent.QuestIconBg" x="8" y="-8" />
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BORDER">
						<FontString parentKey="QuestName" inherits="QuestFont_Large">
							<Size x="158" y="30"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.QuestIconBg" relativePoint="RIGHT" x="-6"/>
								<Anchor point="RIGHT" x="-8"/>
								<!-- top anchor set programatically -->
							</Anchors>
							<Color r="1" g="1" b="1"/>
						</FontString>
						<FontString parentKey="TopText" inherits="GameFontNormalSmall" >
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.QuestIconBg" relativePoint="RIGHT" x="-6"/>
								<Anchor point="RIGHT" x="-8"/>
								<!-- top anchor set programatically -->
							</Anchors>
						</FontString>
						<FontString parentKey="BottomText" inherits="GameFontDisableSmall">
							<Anchors>
								<Anchor point="BOTTOM" x="0" y="8"/>
								<Anchor point="LEFT" relativeKey="$parent.QuestIconBg" relativePoint="RIGHT" x="-6"/>
								<Anchor point="RIGHT" x="-8"/>
							</Anchors>
						</FontString>
					</Layer>
					<Layer level="BORDER" textureSubLevel="1">
						<Texture parentKey="Shine" alphaMode="ADD" alpha="0" hidden="true">
							<Animations>
								<AnimationGroup parentKey="Flash">
									<Alpha startDelay="0" duration="0.25" order="1" fromAlpha="0" toAlpha="0.4"/>
									<Alpha startDelay="0.05" duration="0.25" order="2" fromAlpha="0.4" toAlpha="0"/>
									<Scripts>
										<OnStop>
											self:GetParent():Hide();
										</OnStop>
										<OnFinished>
											self:GetParent():Hide();
										</OnFinished>
									</Scripts>
								</AnimationGroup>
							</Animations>
							<Anchors>
								<Anchor point="TOPLEFT" x="35" y="-3"/>
								<Anchor point="BOTTOMRIGHT" x="1" y="3"/>
							</Anchors>
							<Color r="1" g="1" b="1"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture parentKey="IconShine" alphaMode="ADD" inherits="QuestIcon-WhiteFlash" alpha="0" hidden="true">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.QuestIconBg"/>
							</Anchors>
							<Animations>
								<AnimationGroup parentKey="Flash">
									<Alpha duration="0.25" order="1" fromAlpha="0" toAlpha="0.9"/>
									<Alpha startDelay="0.05" duration="0.25" order="2" fromAlpha="0.9" toAlpha="0"/>
									<Scripts>
										<OnStop>
											self:GetParent():Hide();
										</OnStop>
										<OnFinished>
											self:GetParent():Hide();
											if (self:GetParent():GetParent():GetParent().popUpType=="COMPLETED") then
												self:GetParent():GetParent().FlashFrame:Show();
											end
										</OnFinished>
									</Scripts>
								</AnimationGroup>
							</Animations>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Frame parentKey="FlashFrame" useParentLevel="true" setAllPoints="true" hidden="true">
						<Layers>
							<Layer level="BORDER">
								<Texture parentKey="Flash" inherits="UIPanelButtonHighlightTexture">
									<Size x="180" y="28"/>
									<Anchors>
										<Anchor point="TOPLEFT" x="52" y="-6"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="OVERLAY">
								<Texture parentKey="IconFlash" alphaMode="ADD" inherits="QuestIcon-WhiteFlash" alpha="0.5">
									<Anchors>
										<Anchor point="CENTER" relativePoint="LEFT" x="36"/>
									</Anchors>
								</Texture>
							</Layer>
						</Layers>
						<Scripts>
							<OnLoad>
								self.IconFlash:SetVertexColor(1, 0, 0);
							</OnLoad>
							<OnShow>
								UIFrameFlash(self, 0.75, 0.75, -1, nil);
							</OnShow>
							<OnHide>
								UIFrameFlashStop(self);
							</OnHide>
						</Scripts>
					</Frame>
				</Frames>
			</Frame>
		</ScrollChild>		
	</ScrollFrame>
	
</Ui>