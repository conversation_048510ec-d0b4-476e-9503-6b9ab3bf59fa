<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">

	<Script file="Blizzard_BonusObjectiveTracker.lua"/>

	<Frame name="BonusObjectiveTrackerLineTemplate" inherits="ObjectiveTrackerCheckLineTemplate" virtual="true">
		<Layers>
			<Layer level="ARTWORK">
				<FontString parentKey="Dash" inherits="ObjectiveFont" text="QUEST_DASH">
					<Anchors>
						<Anchor point="TOPLEFT" x="20" y="1"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad inherit="append">
				self.Text:ClearAllPoints();
				self.Text:SetPoint("TOP");
				self.Text:SetPoint("LEFT", self.Dash, "RIGHT");

				self.Glow:SetPoint("LEFT", self.Dash, -2, 0);
			</OnLoad>
		</Scripts>
	</Frame>

	<ScrollFrame name="BonusObjectiveTrackerBlockTemplate" hidden="true" virtual="true" alpha="0">
		<Size x="240" y="10"/>
		<Frames>
			<Button parentKey="TrackedQuest">
				<Layers>
					<Layer level="BACKGROUND" textureSubLevel="-2">
						<Texture parentKey="Glow" alphaMode="ADD" file="Interface/WorldMap/UI-QuestPoi-IconGlow">
							<Size x="50" y="50" />
							<Anchors>
								<Anchor point="CENTER" />
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BACKGROUND" textureSubLevel="-1">
						<Texture parentKey="Underlay">
							<Size x="34" y="34" />
							<Anchors>
								<Anchor point="CENTER" y="-2" />
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture parentKey="Texture">
							<Anchors>
								<Anchor point="CENTER" />
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="OVERLAY" textureSubLevel="2">
						<Texture parentKey="SelectedGlow" alphaMode="ADD" />
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
					</OnLoad>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						if GetSuperTrackedQuestID() ~= self.questID then
							SetSuperTrackedQuestID(self.questID);
						end

						WorldMapPing_StartPingQuest(self.questID);
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<ScrollChild>
			<Frame parentKey="ScrollContents">
				<Size x="10" y="10"/>
			</Frame>
		</ScrollChild>
		<Animations>
			<AnimationGroup parentKey="AnimIn">
				<Translation parentKey="TransOut" childKey="ScrollContents" offsetX="0" offsetY="0" duration="0.01" endDelay="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.1" order="2"/>
				<Translation parentKey="TransIn" childKey="ScrollContents" offsetX="0" offsetY="0" duration="0" order="2"/>
				<Scripts>
					<OnFinished function="BonusObjectiveTracker_OnBlockAnimInFinished"/>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="AnimOut">
				<Alpha fromAlpha="1" toAlpha="0" duration="0.5" startDelay="3.5" order="1"/>
				<Scripts>
					<OnFinished function="BonusObjectiveTracker_OnBlockAnimOutFinished"/>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Scripts>
			<OnLoad>
				self:SetWidth(OBJECTIVE_TRACKER_LINE_WIDTH);
			</OnLoad>
			<OnEnter function="BonusObjectiveTracker_OnBlockEnter"/>
			<OnLeave function="BonusObjectiveTracker_OnBlockLeave"/>
			<OnMouseUp function="BonusObjectiveTracker_OnBlockClick" />
		</Scripts>
	</ScrollFrame>

	<Frame name="BonusObjectiveTrackerRewardTemplate" parentArray="Rewards" virtual="true">
		<Size x="32" y="32"/>
		<Anchors>
			<Anchor point="TOPLEFT" relativeKey="$parent.RewardsTop" relativePoint="BOTTOMLEFT" x="25" y="0"/>
		</Anchors>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="ItemIcon" hidden="false" alpha="0" file="Interface\Icons\XP_Icon">
					<Size x="32" y="32"/>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="ItemBorder" hidden="false" alpha="0" atlas="Objective-ItemBorder" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.ItemIcon" relativePoint="TOPRIGHT" x="2" y="-1"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<FontString parentKey="Label" alpha="0" inherits="GameFontHighlightSmall" justifyH="LEFT">
					<Size x="76" y="36"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.ItemIcon" relativePoint="RIGHT" x="6" y="0"/>
					</Anchors>
				</FontString>
				<Texture parentKey="ItemOverlay" alpha="0">
					<Size x="51" y="51"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.ItemIcon"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Count" alpha="0" inherits="NumberFontNormalSmall" justifyH="RIGHT">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.ItemIcon" x="5" y="2"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="Anim">
				<Alpha childKey="ItemIcon" startDelay="0.95" duration="0.3" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="ItemIcon" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="ItemOverlay" startDelay="0.95" duration="0.3" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="ItemOverlay" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Label" startDelay="0.95" duration="0.3" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Label" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Count" startDelay="0.95" duration="0.3" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Count" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="ItemBorder" startDelay="0.95" duration="0.3" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="ItemBorder" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
			</AnimationGroup>
		</Animations>
	</Frame>

	<Frame name="BonusTrackerProgressBarFlareAnimTemplate" enableMouse="false" virtual="true">
		<Size x="100" y="38"/>
		<Anchors>
			<Anchor point="LEFT" x="0" y="0"/>
		</Anchors>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="AnimTopLine" hidden="false" alpha="0" alphaMode="ADD" atlas="OBJFX_LineBurst">
					<Size x="60" y="10"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="10"/>
					</Anchors>
				</Texture>
				<Texture parentKey="AnimBottomLine" hidden="false" alpha="0" alphaMode="ADD" atlas="OBJFX_LineBurst">
					<Size x="60" y="10"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="-10"/>
					</Anchors>
				</Texture>
				<Texture parentKey="AnimBarGlow" hidden="false" alpha="0" alphaMode="ADD" atlas="OBJFX_LineGlow">
					<Size x="100" y="38"/>
					<Anchors>
						<Anchor point="CENTER" x="5" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="FlareAnim" setToFinalAlpha="true">
				<Alpha childKey="AnimTopLine" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="AnimTopLine" startDelay="0.3" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation parentKey="TransAnim" childKey="AnimTopLine" duration="0.25" order="1" offsetX="5" offsetY="0"/>
				<Alpha childKey="AnimBottomLine" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="AnimBottomLine" startDelay="0.3" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation parentKey="TransAnim2" childKey="AnimBottomLine" duration="0.25" order="1" offsetX="5" offsetY="0"/>
				<Alpha childKey="AnimBarGlow" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="AnimBarGlow" startDelay="0.3" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation parentKey="TransAnim3" childKey="AnimBarGlow" duration="0.25" order="1" offsetX="5" offsetY="0"/>
				<Scale childKey="AnimBarGlow" duration="0.25" order="1" fromScaleX="0.25" fromScaleY="1" toScaleX="0.75" toScaleY="1">
					<Origin point="LEFT">
						<Offset x="20" y="0"/>
					</Origin>
				</Scale>
			</AnimationGroup>
		</Animations>
	</Frame>

	<Frame name="BonusTrackerProgressBarFullBarFlareTemplate" hidden="false" alpha="1" virtual="true">
		<Size x="128" y="128"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="BarGlow" hidden="false" alpha="0" alphaMode="BLEND" atlas="OBJFX-BarGlow">
					<Size x="300" y="32"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="FlareAnim" setToFinalAlpha="true">
				<Alpha childKey="BarGlow" duration="0.15" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="BarGlow" startDelay="0.3" duration="0.4" order="1" fromAlpha="1" toAlpha="0"/>
			</AnimationGroup>
		</Animations>
	</Frame>

	<Frame name="BonusTrackerProgressBarSmallFlareAnimTemplate" hidden="false" alpha="1" virtual="true">
		<Size x="100" y="30"/>
		<Anchors>
			<Anchor point="LEFT" x="0" y="0"/>
		</Anchors>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="BarGlow" hidden="false" alpha="0" alphaMode="ADD" atlas="OBJFX_LineGlow">
					<Size x="100" y="30"/>
					<Anchors>
						<Anchor point="CENTER" x="5" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="FlareAnim" setToFinalAlpha="true">
				<Alpha childKey="BarGlow" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="BarGlow" startDelay="0.3" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation parentKey="TransAnim" childKey="BarGlow" duration="0.25" order="1" offsetX="2" offsetY="0"/>
				<Scale childKey="BarGlow" duration="0.25" order="1" fromScaleX="0.15" fromScaleY="1" toScaleX="0.2" toScaleY="1">
					<Origin point="LEFT">
						<Offset x="20" y="0"/>
					</Origin>
				</Scale>
			</AnimationGroup>
		</Animations>
	</Frame>

	<Frame name="BonusTrackerProgressBarTemplate" virtual="true" hidden="true">
		<Size x="192" y="38"/>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		<Frames>
			<StatusBar parentKey="Bar" drawLayer="BACKGROUND" minValue="0" maxValue="100" defaultValue="0">
				<Size x="191" y="17"/>
				<Anchors>
					<Anchor point="LEFT" x="10" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture parentKey="BarFrame" atlas="bonusobjectives-bar-frame" useAtlasSize="true">
							<Anchors>
								<Anchor point="LEFT" x="-8" y="-1"/>
							</Anchors>
						</Texture>
						<Texture parentKey="IconBG" atlas="bonusobjectives-bar-ring" useAtlasSize="true">
							<Anchors>
								<Anchor point="RIGHT" relativeKey="$parent.BarFrame" x="0" y="0"/>
							</Anchors>
						</Texture>
						<FontString parentKey="Label" inherits="GameFontHighlightMedium" justifyH="CENTER">
						  <Anchors>
							<Anchor point="CENTER" x="-1" y="-1"/>
						  </Anchors>
						</FontString>
					</Layer>
					<Layer level="ARTWORK" textureSubLevel="1">
						<Texture parentKey="BarFrame2" alpha="0" alphaMode="ADD" atlas="bonusobjectives-bar-frame" useAtlasSize="true">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.BarFrame"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="ARTWORK" textureSubLevel="2">
						<Texture parentKey="BarFrame3" alpha="0" alphaMode="ADD" atlas="bonusobjectives-bar-frame" useAtlasSize="true">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.BarFrame"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BACKGROUND" textureSubLevel="-1">
						<Texture parentKey="BarBG">
							<Color r="0.04" g="0.07" b="0.18"/>
						</Texture>
						<Texture parentKey="Icon">
							<Size x="32" y="32"/>
							<Anchors>
								<Anchor point="RIGHT" x="33" y="2"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture parentKey="BarGlow" alpha="0" alphaMode="ADD" atlas="bonusobjectives-bar-glow" useAtlasSize="true">
							<Anchors>
								<Anchor point="LEFT" x="-8" y="-1"/>
							</Anchors>
						</Texture>
						<Texture parentKey="Sheen" alpha="0" alphaMode="ADD" atlas="bonusobjectives-bar-sheen">
							<Size x="97" y="22"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.BarFrame" x="-60" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="OVERLAY" textureSubLevel="1">
						<Texture parentKey="Starburst" alpha="0" alphaMode="ADD" atlas="bonusobjectives-bar-starburst" useAtlasSize="true">
							<Anchors>
								<Anchor point="TOPRIGHT" relativeKey="$parent.BarFrame" x="1" y="6"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Animations>
					<AnimationGroup parentKey="AnimIn" setToFinalAlpha="true">
						<Alpha duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="BarGlow" startDelay="1.34" smoothing="NONE" duration="0.53" order="1" fromAlpha="0" toAlpha="0.5"/>
						<Alpha childKey="BarGlow" startDelay="1.87" smoothing="IN_OUT" duration="0.53" order="1" fromAlpha="0.5" toAlpha="0"/>
						<Scale childKey="Starburst" startDelay="1" duration="0.1" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.5" toScaleY="0.5"/>
						<Scale childKey="Starburst" startDelay="1.34" duration="0.5" order="1" fromScaleX="1" fromScaleY="1" toScaleX="2" toScaleY="2"/>
						<Scale childKey="Starburst" startDelay="1.84" duration="0.5" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.5" toScaleY="0.5"/>
						<Alpha childKey="Starburst" startDelay="1.34" duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="Starburst" startDelay="1.44" duration="0.9" order="1" fromAlpha="1" toAlpha="0"/>
						<Rotation childKey="Starburst" startDelay="1" duration="0.1" order="1" degrees="-41"/>
						<Rotation childKey="Starburst" startDelay="1.2" duration="1.41" order="1" degrees="-35"/>
						<Alpha childKey="BarFrame2" startDelay="1.34" smoothing="NONE" duration="0.53" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="BarFrame2" startDelay="1.87" smoothing="IN_OUT" duration="0.53" order="1" fromAlpha="1" toAlpha="0"/>
						<Alpha childKey="BarFrame3" startDelay="1.34" smoothing="NONE" duration="0.53" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="BarFrame3" startDelay="1.87" smoothing="IN_OUT" duration="0.53" order="1" fromAlpha="1" toAlpha="0"/>
						<Translation childKey="Sheen" startDelay="1.06" duration="0.48" order="1" offsetX="68" offsetY="0"/>
						<Alpha childKey="Sheen" startDelay="1.09" duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="Sheen" startDelay="1.34" duration="0.05" order="1" fromAlpha="1" toAlpha="0"/>
					</AnimationGroup>
				</Animations>
				<BarTexture file="Interface\TargetingFrame\UI-StatusBar"/>
				<BarColor r="0.26" g="0.42" b="1"/>
			</StatusBar>
			<Frame parentKey="Flare1" inherits="BonusTrackerProgressBarFlareAnimTemplate"/>
			<Frame parentKey="Flare2" inherits="BonusTrackerProgressBarFlareAnimTemplate"/>

			<Frame parentKey="SmallFlare1" inherits="BonusTrackerProgressBarSmallFlareAnimTemplate"/>
			<Frame parentKey="SmallFlare2" inherits="BonusTrackerProgressBarSmallFlareAnimTemplate"/>

			<Frame parentKey="FullBarFlare1" inherits="BonusTrackerProgressBarFullBarFlareTemplate">
				<Anchors>
					<Anchor point="CENTER" relativeKey="$parent.Bar" relativePoint="CENTER" x="12" y="0"/>
				</Anchors>
			</Frame>

			<Frame parentKey="FullBarFlare2" inherits="BonusTrackerProgressBarFullBarFlareTemplate">
				<Anchors>
					<Anchor point="CENTER" relativeKey="$parent.Bar" relativePoint="CENTER" x="12" y="0"/>
				</Anchors>
			</Frame>

		</Frames>
		<Scripts>
			<OnLoad>
				self.Bar.Icon:SetMask("Interface\\CharacterFrame\\TempPortraitAlphaMask");
			</OnLoad>
			<OnEvent function="BonusObjectiveTrackerProgressBar_OnEvent"/>
		</Scripts>
	</Frame>

	<Frame name="BonusObjectiveRewardsFrameTemplate" parent="UIParent" hidden="true" virtual="true">
		<Size x="168" y="128"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="RewardsTop" hidden="false" alpha="0" atlas="Rewards-Top" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPRIGHT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="HeaderTop" hidden="false" alpha="0" atlas="OBJFX_LineGlow" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.RewardsTop" x="50" y="6"/>
					</Anchors>
				</Texture>
				<Texture parentKey="RewardsBottom" hidden="false" alpha="0" atlas="Rewards-Top" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.RewardsTop" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="RewardsShadow" hidden="false" alpha="0" atlas="Rewards-Shadow" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.RewardsBottom" relativePoint="BOTTOM" x="0" y="10"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<FontString parentKey="Header" alpha="0" inherits="QuestFont_Outline_Huge" text="REWARDS">
					<Anchors>
						<Anchor point="TOP" x="0" y="2"/>
					</Anchors>
				</FontString>
				<Texture parentKey="HeaderGlow" hidden="false" alpha="0" alphaMode="ADD" atlas="OBJFX_LineGlow" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.HeaderTop" x="-25" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame inherits="BonusObjectiveTrackerRewardTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.RewardsTop" relativePoint="BOTTOMLEFT" x="25" y="0"/>
				</Anchors>
			</Frame>
		</Frames>
		<Animations>
			<AnimationGroup parentKey="Anim" setToFinalAlpha="true">
				<Scale childKey="RewardsTop" duration="0.25" order="1" fromScaleX="0.25" fromScaleY="1" toScaleX="1" toScaleY="1">
					<Origin point="RIGHT"/>
				</Scale>
				<Alpha childKey="RewardsTop" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="RewardsTop" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Header" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Header" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
				<Scale childKey="HeaderTop" duration="0.25" order="1" fromScaleX="0.25" fromScaleY="1.2" toScaleX="1.2" toScaleY="1.2"/>
				<Alpha childKey="HeaderTop" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Translation childKey="HeaderTop" duration="0.25" order="1" offsetX="-30" offsetY="0"/>
				<Alpha childKey="HeaderTop" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
				<Scale childKey="HeaderGlow" startDelay="0.05" duration="0.5" order="1" fromScaleX="1" fromScaleY="1.2" toScaleX="1.2" toScaleY="1.2"/>
				<Alpha childKey="HeaderGlow" startDelay="0.05" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="HeaderGlow" startDelay="0.3" duration="0.5" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="HeaderGlow" startDelay="0.05" duration="0.5" order="1" offsetX="-70" offsetY="0"/>
				<Alpha childKey="RewardsBottom" startDelay="0.5" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Translation parentKey="RewardsBottomAnim" childKey="RewardsBottom" startDelay="0.5" duration="0.35" order="1" offsetX="0" offsetY="-83"/>
				<Alpha childKey="RewardsBottom" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
				<Scale parentKey="RewardsShadowAnim" childKey="RewardsShadow" startDelay="0.5" duration="0.35" order="1" fromScaleX="0.8" fromScaleY="0.2" toScaleX="0.8" toScaleY="3">
					<Origin point="TOP"/>
				</Scale>
				<Alpha childKey="RewardsShadow" startDelay="0.5" duration="0.35" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="RewardsShadow" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
				<Scripts>
					<OnFinished function="BonusObjectiveTracker_OnAnimateRewardDone"/>
				</Scripts>
			</AnimationGroup>
		</Animations>
	</Frame>

    <Frame name="ObjectiveTrackerBonusRewardsFrame" inherits="BonusObjectiveRewardsFrameTemplate" />
	<Frame name="ObjectiveTrackerWorldQuestRewardsFrame" inherits="BonusObjectiveRewardsFrameTemplate" />

	<Frame name="ObjectiveTrackerBonusBannerFrame" parent="UIParent" hidden="true">
		<Size x="128" y="128"/>
		<Anchors>
			<Anchor point="TOP" x="0" y="-170"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Icon" hidden="false" alpha="0" alphaMode="BLEND" atlas="bonusobjectives-title-icon" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="0" y="4"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture parentKey="Icon2" hidden="false" alpha="0" alphaMode="ADD" atlas="bonusobjectives-title-icon" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Icon"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="2">
				<Texture parentKey="Icon3" hidden="false" alpha="0" alphaMode="ADD" atlas="bonusobjectives-title-icon" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Icon"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="BG1" hidden="false" alpha="0" alphaMode="BLEND" atlas="bonusobjectives-title-bg" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="1">
				<Texture parentKey="BG2" hidden="false" alpha="0" alphaMode="ADD" atlas="bonusobjectives-title-bg" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Title" inherits="QuestFont_Super_Huge" alpha="0">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.BG1" x="0" y="16"/>
					</Anchors>
				</FontString>
				<FontString parentKey="TitleFlash" inherits="QuestFont_Super_Huge" alpha="0">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.BG1" x="0" y="16"/>
					</Anchors>
					<Color r="1" g="1" b="1"/>
				</FontString>
				<FontString parentKey="BonusLabel" inherits="GameFontHighlightLarge" alpha="0">
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.Title" relativePoint="BOTTOM" x="0" y="-7"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="Anim" setToFinalAlpha="true">
				<Scale childKey="BG1" duration="0.33" order="1" fromScaleX="0.001" fromScaleY="1" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="BG1" duration="0" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="BG2" duration="0.33" order="1" fromScaleX="0.001" fromScaleY="1" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="BG2" duration="0.33" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="BG2" startDelay="0.33" smoothing="IN" duration="0.33" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Title" duration="0.33" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="BonusLabel" duration="0.33" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="TitleFlash" duration="0.33" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="TitleFlash" startDelay="0.33" smoothing="IN" duration="0.33" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Icon" duration="0.33" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="Icon" smoothing="IN" duration="0.33" order="1" fromScaleX="1.4" fromScaleY="1.4" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="Icon2" duration="0.33" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Icon2" startDelay="0.33" smoothing="IN" duration="0.33" order="1" fromAlpha="1" toAlpha="0"/>
				<Scale childKey="Icon2" smoothing="IN" duration="0.33" order="1" fromScaleX="1.4" fromScaleY="1.4" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="Icon3" duration="0.33" order="1" fromAlpha="0" toAlpha="0.35"/>
				<Alpha childKey="Icon3" startDelay="0.33" smoothing="IN" duration="0.33" order="1" fromAlpha="0.35" toAlpha="0"/>
				<Scale childKey="Icon3" smoothing="IN" duration="0.33" order="1" fromScaleX="1.8" fromScaleY="1.8" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="BG1" startDelay="2.5" smoothing="IN" duration="0.46" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Title" startDelay="2.5" smoothing="IN" duration="0.46" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="BonusLabel" startDelay="2.5" smoothing="IN" duration="0.46" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Icon" startDelay="2.5" smoothing="IN" duration="0.46" order="1" fromAlpha="1" toAlpha="0"/>
				<Scale childKey="BG1" startDelay="2.5" smoothing="IN" duration="0.46" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.55" toScaleY="0.55"/>
				<Scale childKey="Title" startDelay="2.5" smoothing="IN" duration="0.46" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.55" toScaleY="0.55"/>
				<Scale childKey="BonusLabel" startDelay="2.5" smoothing="IN" duration="0.46" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.55" toScaleY="0.55"/>
				<Scale childKey="Icon" startDelay="2.5" smoothing="IN" duration="0.46" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.55" toScaleY="0.55"/>
				<Translation parentKey="BG1Translation" childKey="BG1" startDelay="2.5" smoothing="IN" duration="0.46" order="1" offsetX="269.1" offsetY="-84.7"/>
				<Translation parentKey="TitleTranslation" childKey="Title" startDelay="2.5" smoothing="IN" duration="0.46" order="1" offsetX="269.1" offsetY="-84.7"/>
				<Translation parentKey="BonusLabelTranslation" childKey="BonusLabel" startDelay="2.5" smoothing="IN" duration="0.46" order="1" offsetX="269.1" offsetY="-84.7"/>
				<Translation parentKey="IconTranslation" childKey="Icon" startDelay="2.5" smoothing="IN" duration="0.46" order="1" offsetX="269.1" offsetY="-84.7"/>
				<Scripts>
					<OnFinished function="ObjectiveTrackerBonusBannerFrame_OnAnimFinished"/>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Scripts>
			<OnLoad function="ObjectiveTrackerBonusBannerFrame_OnLoad"/>
		</Scripts>
	</Frame>

	<Frame name="BonusObjectiveTrackerHeaderTemplate" inherits="ObjectiveTrackerHeaderTemplate" hidden="true" virtual="true">
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="TopShadow" atlas="OBJBonusBar-Top" useAtlasSize="true" alpha="0">
					<Anchors>
						<Anchor point="TOPLEFT" x="-27" y="-24"/>
					</Anchors>
					<Color r="1" g="0" b="0"/>
				</Texture>
				<Texture parentKey="BottomShadow" atlas="OBJBonusBar-Top" useAtlasSize="true" alpha="0">
					<Anchors>
						<Anchor point="TOPLEFT" x="-27" y="0"/>
					</Anchors>
					<TexCoords left="0" right="1" top="1" bottom="0"/>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="ShadowAnim">
				<Alpha childKey="TopShadow" fromAlpha="0" toAlpha="1" duration="0.05" startDelay="0.05" order="1"/>
				<Alpha childKey="BottomShadow" fromAlpha="0" toAlpha="1" duration="0.05" order="2"/>
				<Translation parentKey="TransAnim" childKey="BottomShadow" offsetX="0" offsetY="-100" duration="1" endDelay="1" order="3"/>
				<Alpha childKey="TopShadow" fromAlpha="1" toAlpha="0" duration="0.2" order="4"/>
				<Alpha childKey="BottomShadow" fromAlpha="1" toAlpha="0" duration="0.2" order="4"/>
			</AnimationGroup>
		</Animations>
		<Scripts>
			<OnLoad function="BonusObjectiveTracker_OnHeaderLoad"/>
			<OnEvent function="BonusObjectiveTracker_OnEvent"/>
		</Scripts>
	</Frame>

	<Frame parent="ObjectiveTrackerBlocksFrame" inherits="BonusObjectiveTrackerHeaderTemplate" hidden="true">
		<KeyValues>
			<KeyValue key="ModuleName" value="BONUS_OBJECTIVE_TRACKER_MODULE" />
			<KeyValue key="RewardsFrame" value="ObjectiveTrackerBonusRewardsFrame" type="global" />
			<KeyValue key="DefaultHeaderText" value="TRACKER_HEADER_BONUS_OBJECTIVES" type="global"/>
			<KeyValue key="ShowWorldQuests" value="false" type="boolean" />
		</KeyValues>
	</Frame>

	<Frame parent="ObjectiveTrackerBlocksFrame" inherits="BonusObjectiveTrackerHeaderTemplate" hidden="true">
		<KeyValues>
			<KeyValue key="ModuleName" value="WORLD_QUEST_TRACKER_MODULE" />
			<KeyValue key="RewardsFrame" value="ObjectiveTrackerWorldQuestRewardsFrame" type="global" />
			<KeyValue key="DefaultHeaderText" value="TRACKER_HEADER_WORLD_QUESTS" type="global"/>
			<KeyValue key="ShowWorldQuests" value="true" type="boolean" />
		</KeyValues>
		<Scripts>
			<!-- Remove event handler, not needed for world quests currently -->
			<OnEvent />
		</Scripts>
	</Frame>
</Ui>
