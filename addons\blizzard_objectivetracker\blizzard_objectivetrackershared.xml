<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">

	<Script file="Blizzard_ObjectiveTrackerShared.lua"/>

	<Button name="QuestObjectiveItemButtonTemplate" virtual="true">
		<Size x="26" y="26"/>
		<Layers>
			<Layer level="ARTWORK">
				<FontString parentKey="HotKey" inherits="NumberFontNormalSmallGray" justifyH="LEFT" text="RANGE_INDICATOR">
					<Size x="29" y="10"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="16" y="-2"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="icon"/>
				<FontString parentKey="Count" inherits="NumberFontNormal" justifyH="RIGHT" hidden="true">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="-3" y="2"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Cooldown parentKey="Cooldown" inherits="CooldownFrameTemplate"/>
		</Frames>
		<Scripts>
			<OnLoad function="QuestObjectiveItem_OnLoad"/>
			<OnEvent function="QuestObjectiveItem_OnEvent"/>
			<OnUpdate function="QuestObjectiveItem_OnUpdate"/>
			<OnShow function="QuestObjectiveItem_OnShow"/>
			<OnHide function="QuestObjectiveItem_OnHide"/>
			<OnEnter function="QuestObjectiveItem_OnEnter"/>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
			<OnClick function="QuestObjectiveItem_OnClick"/>
		</Scripts>
		<NormalTexture parentKey="NormalTexture" file="Interface\Buttons\UI-Quickslot2">
			<Size x="42" y="42"/>
			<Anchors>
				<Anchor point="CENTER"/>
			</Anchors>
		</NormalTexture>
		<PushedTexture file="Interface\Buttons\UI-Quickslot-Depress"/>
		<HighlightTexture file="Interface\Buttons\ButtonHilight-Square" alphaMode="ADD"/>
	</Button>

	<Button name="QuestObjectiveFindGroupButtonTemplate" motionScriptsWhileDisabled="true" virtual="true">
		<Size x="30" y="30"/>
		<Layers>
			<Layer level="ARTWORK" textureSubLevel="5">
				<Texture parentKey="Icon" atlas="socialqueuing-icon-eye">
					<Size x="13" y="13"/>
					<Anchors>
						<Anchor point="CENTER" x="-1" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<NormalTexture file="Interface\Buttons\UI-SquareButton-Up"/>
		<PushedTexture file="Interface\Buttons\UI-SquareButton-Down"/>
		<DisabledTexture file="Interface\Buttons\UI-SquareButton-Disabled"/>
		<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD" alpha=".5"/>
		<Scripts>
			<OnMouseDown function="QuestObjectiveFindGroup_OnMouseDown"/>
			<OnMouseUp function="QuestObjectiveFindGroup_OnMouseUp"/>
			<OnEnter function="QuestObjectiveFindGroup_OnEnter"/>
			<OnLeave function="QuestObjectiveFindGroup_OnLeave"/>
			<OnClick function="QuestObjectiveFindGroup_OnClick"/>
		</Scripts>
	</Button>
</Ui>
