<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">

	<Script file="Blizzard_QuestObjectiveTracker.lua"/>

	<Frame name="QuestObjectiveAnimLineTemplate" inherits="ObjectiveTrackerLineTemplate" virtual="true">
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="Check" atlas="Tracker-Check" useAtlasSize="true" hidden="true">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-10" y="2"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="CheckFlash" file="Interface\Scenarios\ScenarioIcon-Check" alphaMode="ADD" alpha="0">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Check"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="Anim">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.067" order="1"/>
							<Scale scaleX="1.25" scaleY="1.25" duration="0.2" order="2"/>
							<Alpha fromAlpha="1" toAlpha="0" startDelay="0.167" duration="0.23" order="2"/>
						</AnimationGroup>
					</Animations>
				</Texture>
				<Texture parentKey="Glow" file="Interface\Scenarios\Objective-Lineglow" alpha="0" alphaMode="ADD">
					<Size x="80" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Text" x="-2" y="0"/>
						<Anchor point="TOP" x="0" y="0"/>
						<Anchor point="BOTTOM" x="0" y="-4"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="Anim">
							<Scale parentKey="ScaleGlow" startDelay="0.067" scaleX="2" scaleY="1" duration="0.433" order="1">
								<Origin point="LEFT"/>
							</Scale>
							<Alpha startDelay="0.067" fromAlpha="0" toAlpha="1" duration="0.1" order="1"/>
							<Alpha startDelay="0.467" endDelay="0" fromAlpha="1" toAlpha="0" duration="0.267" order="1"/>
							<Scripts>
								<OnFinished>
									local line = self:GetParent():GetParent();
									QuestObjectiveTracker_FinishGlowAnim(line);
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
				</Texture>
				<Texture parentKey="Sheen" file="Interface\Scenarios\Objective-Sheen" alpha="0">
					<Size x="32" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Glow"/>
						<Anchor point="TOP" x="0" y="2"/>
						<Anchor point="BOTTOM" x="0" y="-14"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="Anim">
							<Translation startDelay="0.067" offsetX="110" offsetY="0" duration="0.467" order="1"/>
							<Alpha startDelay="0.067" fromAlpha="0" toAlpha="1" duration="0.133" order="1"/>
							<Alpha startDelay="0.2" fromAlpha="1" toAlpha="0" duration="0.133" order="1" smoothing="IN"/>
						</AnimationGroup>
					</Animations>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="FadeOutAnim">
				<Alpha startDelay="1" fromAlpha="1" toAlpha="0" duration="0.1" order="1"/>
				<Scripts>
					<OnFinished>
						local line = self:GetParent();
						QuestObjectiveTracker_FinishFadeOutAnim(line);
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
	</Frame>
</Ui>
