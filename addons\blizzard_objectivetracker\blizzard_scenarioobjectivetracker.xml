<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">

	<Script file="Blizzard_ScenarioObjectiveTracker.lua"/>

	<Frame name="ScenarioSpellFrameTemplate" hidden="true" virtual="true">
		<Size x="216" y="34"/>
		<Layers>
			<Layer level="ARTWORK">
				<FontString parentKey="SpellName" inherits="GameFontHighlight" justifyH="LEFT">
					<Size x="184" y="26"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="52" y="-4"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="SpellButton">
				<Size x="26" y="26"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="20" y="-4"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture parentKey="Icon">
							<Size x="26" y="26"/>
							<Anchors>
								<Anchor point="TOPLEFT"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Cooldown parentKey="Cooldown" inherits="CooldownFrameTemplate"/>
				</Frames>
				<NormalTexture parentKey="NormalTexture" file="Interface\Buttons\UI-Quickslot2">
					<Size x="42" y="42"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</NormalTexture>
				<PushedTexture file="Interface\Buttons\UI-Quickslot-Depress"/>
				<HighlightTexture file="Interface\Buttons\ButtonHilight-Square" alphaMode="ADD"/>
				<Scripts>
					<OnEnter function="ScenarioSpellButton_OnEnter"/>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick function="ScenarioSpellButton_OnClick"/>
				</Scripts>
			</Button>
		</Frames>
		<Animations>
			<AnimationGroup parentKey="Fadein" setToFinalAlpha="true">
				<Alpha duration="0.25" startDelay="0.1" order="1" fromAlpha="0" toAlpha="1"/>
			</AnimationGroup>
		</Animations>
	</Frame>

	<Frame name="ScenarioTrackerProgressBarTemplate" virtual="true" hidden="true">
		<Size x="192" y="38"/>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		<Frames>
			<StatusBar parentKey="Bar" drawLayer="BACKGROUND" minValue="0" maxValue="100" defaultValue="0">
				<Size x="191" y="17"/>
				<Anchors>
					<Anchor point="LEFT" x="10" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture parentKey="BarFrame" atlas="bonusobjectives-bar-frame" useAtlasSize="true">
							<Anchors>
								<Anchor point="LEFT" x="-8" y="-1"/>
							</Anchors>
						</Texture>
						<Texture parentKey="IconBG" atlas="bonusobjectives-bar-ring" useAtlasSize="true">
							<Anchors>
								<Anchor point="RIGHT" relativeKey="$parent.BarFrame" x="0" y="0"/>
							</Anchors>
						</Texture>
						<FontString parentKey="Label" inherits="GameFontHighlightMedium" justifyH="CENTER">
						  <Anchors>
							<Anchor point="CENTER" x="-1" y="-1"/>
						  </Anchors>
						</FontString>
					</Layer>
					<Layer level="ARTWORK" textureSubLevel="1">
						<Texture parentKey="BarFrame2" alpha="0" alphaMode="ADD" atlas="bonusobjectives-bar-frame" useAtlasSize="true">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.BarFrame"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="ARTWORK" textureSubLevel="2">
						<Texture parentKey="BarFrame3" alpha="0" alphaMode="ADD" atlas="bonusobjectives-bar-frame" useAtlasSize="true">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.BarFrame"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BACKGROUND" textureSubLevel="-1">
						<Texture parentKey="BarBG">
							<Color r="0.04" g="0.07" b="0.18"/>
						</Texture>
						<Texture parentKey="Icon">
							<Size x="32" y="32"/>
							<Anchors>
								<Anchor point="RIGHT" x="33" y="2"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture parentKey="BarGlow" alpha="0" alphaMode="ADD" atlas="bonusobjectives-bar-glow" useAtlasSize="true">
							<Anchors>
								<Anchor point="LEFT" x="-8" y="-1"/>
							</Anchors>
						</Texture>
						<Texture parentKey="Sheen" alpha="0" alphaMode="ADD" atlas="bonusobjectives-bar-sheen">
							<Size x="97" y="22"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.BarFrame" x="-60" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="OVERLAY" textureSubLevel="1">
						<Texture parentKey="Starburst" alpha="0" alphaMode="ADD" atlas="bonusobjectives-bar-starburst" useAtlasSize="true">
							<Anchors>
								<Anchor point="TOPRIGHT" relativeKey="$parent.BarFrame" x="1" y="6"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Animations>
					<AnimationGroup parentKey="AnimIn" setToFinalAlpha="true">
						<Alpha duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="BarGlow" startDelay="1.34" smoothing="NONE" duration="0.53" order="1" fromAlpha="0" toAlpha="0.5"/>
						<Alpha childKey="BarGlow" startDelay="1.87" smoothing="IN_OUT" duration="0.53" order="1" fromAlpha="0.5" toAlpha="0"/>
						<Scale childKey="Starburst" startDelay="1" duration="0.1" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.5" toScaleY="0.5"/>
						<Scale childKey="Starburst" startDelay="1.34" duration="0.5" order="1" fromScaleX="1" fromScaleY="1" toScaleX="2" toScaleY="2"/>
						<Scale childKey="Starburst" startDelay="1.84" duration="0.5" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.5" toScaleY="0.5"/>
						<Alpha childKey="Starburst" startDelay="1.34" duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="Starburst" startDelay="1.44" duration="0.9" order="1" fromAlpha="1" toAlpha="0"/>
						<Rotation childKey="Starburst" startDelay="1" duration="0.1" order="1" degrees="-41"/>
						<Rotation childKey="Starburst" startDelay="1.2" duration="1.41" order="1" degrees="-35"/>
						<Alpha childKey="BarFrame2" startDelay="1.34" smoothing="NONE" duration="0.53" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="BarFrame2" startDelay="1.87" smoothing="IN_OUT" duration="0.53" order="1" fromAlpha="1" toAlpha="0"/>
						<Alpha childKey="BarFrame3" startDelay="1.34" smoothing="NONE" duration="0.53" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="BarFrame3" startDelay="1.87" smoothing="IN_OUT" duration="0.53" order="1" fromAlpha="1" toAlpha="0"/>
						<Translation childKey="Sheen" startDelay="1.06" duration="0.48" order="1" offsetX="68" offsetY="0"/>
						<Alpha childKey="Sheen" startDelay="1.09" duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="Sheen" startDelay="1.34" duration="0.05" order="1" fromAlpha="1" toAlpha="0"/>
					</AnimationGroup>
				</Animations>
				<BarTexture file="Interface\TargetingFrame\UI-StatusBar"/>
				<BarColor r="0.26" g="0.42" b="1"/>
			</StatusBar>
			<Frame parentKey="Flare1" inherits="BonusTrackerProgressBarFlareAnimTemplate"/>
			<Frame parentKey="Flare2" inherits="BonusTrackerProgressBarFlareAnimTemplate"/>

			<Frame parentKey="SmallFlare1" inherits="BonusTrackerProgressBarSmallFlareAnimTemplate"/>
			<Frame parentKey="SmallFlare2" inherits="BonusTrackerProgressBarSmallFlareAnimTemplate"/>

			<Frame parentKey="FullBarFlare1" inherits="BonusTrackerProgressBarFullBarFlareTemplate">
				<Anchors>
					<Anchor point="CENTER" relativeKey="$parent.Bar" relativePoint="CENTER" x="12" y="0"/>
				</Anchors>
			</Frame>

			<Frame parentKey="FullBarFlare2" inherits="BonusTrackerProgressBarFullBarFlareTemplate">
				<Anchors>
					<Anchor point="CENTER" relativeKey="$parent.Bar" relativePoint="CENTER" x="12" y="0"/>
				</Anchors>
			</Frame>

		</Frames>
		<Scripts>
			<OnLoad>
				self.Bar.Icon:SetMask("Interface\\CharacterFrame\\TempPortraitAlphaMask");
			</OnLoad>
			<OnEvent function="ScenarioTrackerProgressBar_OnEvent"/>
		</Scripts>
	</Frame>

	<Frame name="ScenarioChallengeModeAffixTemplate" parentArray="Affixes" virtual="true" mixin="ScenarioChallengeModeAffixMixin">
		<Size x="22" y="22"/>
		<Layers>
			<Layer level="OVERLAY">
				<Texture parentKey="Border" atlas="ChallengeMode-AffixRing-Sm" setAllPoints="true"/>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Portrait">
					<Size x="20" y="20"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Border"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnEnter method="OnEnter"/>
			<OnLeave function="GameTooltip_Hide"/>
		</Scripts>
	</Frame>

	<ScrollFrame name="ScenarioBlocksFrame" parent="ObjectiveTrackerBlocksFrame" hidden="true">
		<Size x="212" y="10"/>
		<ScrollChild>
			<Frame parentKey="ScrollContents">
				<Size x="10" y="10"/>
				<Frames>
					<Frame name="ScenarioObjectiveBlock" hidden="true">
						<Size x="192" y="10"/>
					</Frame>
					<Frame name="ScenarioStageBlock" hidden="true">
						<Size x="201" y="83"/>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture parentKey="NormalBG" atlas="ScenarioTrackerToast" useAtlasSize="true">
									<Anchors>
										<Anchor point="TOPLEFT" x="0" y="0"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="BORDER">
								<Texture parentKey="FinalBG" atlas="ScenarioTrackerToast-FinalFiligree" useAtlasSize="true">
									<Anchors>
										<Anchor point="TOPLEFT" x="4" y="-4"/>
									</Anchors>
								</Texture>
								<Texture parentKey="GlowTexture" atlas="ScenarioTrackerToast" useAtlasSize="true" alpha="0" alphaMode="ADD">
									<Anchors>
										<Anchor point="TOPLEFT" x="0" y="0"/>
									</Anchors>
									<Animations>
										<AnimationGroup parentKey="AlphaAnim">
											<Alpha fromAlpha="0" toAlpha="1" duration="0.266" order="1"/>
											<Alpha endDelay="0.2" fromAlpha="1" toAlpha="0" duration="0.333" order="2"/>
										</AnimationGroup>
									</Animations>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<FontString parentKey="Stage" inherits="QuestTitleFont" wordwrap="true" justifyH="LEFT" mixin="ShrinkUntilTruncateFontStringMixin">
									<Size x="172" y="18"/>
									<Anchors>
										<Anchor point="TOPLEFT" x="15" y="-10"/>
									</Anchors>
									<Color r="1" g="0.914" b="0.682"/>
									<Shadow>
										<Offset>
											<AbsDimension x="1" y="-1"/>
										</Offset>
										<Color r="0" g="0" b="0"/>
									</Shadow>
								</FontString>
								<FontString parentKey="CompleteLabel" inherits="QuestTitleFont" text="STAGE_COMPLETE" hidden="true">
									<Anchors>
										<Anchor point="LEFT" x="15" y="3"/>
									</Anchors>
									<Color r="1" g="0.914" b="0.682"/>
									<Shadow>
										<Offset>
											<AbsDimension x="1" y="-1"/>
										</Offset>
										<Color r="0" g="0" b="0"/>
									</Shadow>
								</FontString>
								<FontString parentKey="Name" inherits="GameFontNormal" justifyH="LEFT" justifyV="TOP" spacing="2">
									<Size x="172" y="28"/>
									<Anchors>
										<Anchor point="TOPLEFT" relativeKey="$parent.Stage" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
									</Anchors>
									<Color r="1" g="0.831" b="0.380"/>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Button parentKey="RewardButton" hidden="true">
								<Size x="48" y="48"/>
								<Anchors>
									<Anchor point="BOTTOMRIGHT" x="50" y="-3"/>
								</Anchors>
								<Layers>
									<Layer level="OVERLAY" textureSubLevel="1">
										<Texture parentKey="RewardRing" atlas="legioninvasion-scenario-rewardring" useAtlasSize="true">
											<Anchors>
												<Anchor point="CENTER" relativeKey="$parent.RewardRing"/>
											</Anchors>
										</Texture>
									</Layer>
									<Layer level="OVERLAY" textureSubLevel="0">
										<Texture parentKey="RewardIcon">
											<Size x="29" y="29"/>
											<Anchors>
												<Anchor point="CENTER" relativeKey="$parent.RewardRing"/>
											</Anchors>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnEnter function="ScenarioRewardButton_OnEnter"/>
									<OnLeave function="ScenarioRewardButton_OnLeave"/>
								</Scripts>
							</Button>
						</Frames>
						<Scripts>
							<OnLoad>
								self.Stage:SetFontObjectsToTry(QuestTitleFont, Fancy16Font, SystemFont_Med1);
							</OnLoad>
							<OnEnter function="ScenarioObjectiveStageBlock_OnEnter"/>
							<OnLeave>
								GameTooltip:Hide();
							</OnLeave>
						</Scripts>
					</Frame>
					<Frame name="ScenarioChallengeModeBlock" hidden="true">
						<Size x="251" y="87"/>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture parentKey="TimerBGBack" atlas="ChallengeMode-TimerBG-Back" useAtlasSize="true">
									<Anchors>
										<Anchor point="BOTTOM" x="0" y="13"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="BACKGROUND" textureSubLevel="1">
								<Texture parentKey="TimerBG" atlas="ChallengeMode-TimerBG" useAtlasSize="true">
									<Anchors>
										<Anchor point="BOTTOM" x="0" y="13"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="OVERLAY">
								<Texture atlas="challengemode-timer" setAllPoints="true"/>
								<FontString parentKey="Level" inherits="GameFontNormalMed2" justifyH="LEFT">
									<Anchors>
										<Anchor point="TOPLEFT" x="28" y="-18"/>
									</Anchors>
								</FontString>
								<FontString parentKey="TimeLeft" inherits="GameFontHighlightHuge" justifyH="LEFT">
									<Anchors>
										<Anchor point="TOPLEFT" relativeKey="$parent.Level" relativePoint="BOTTOMLEFT" x="0" y="-8"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Frame parentKey="StartedDepleted" hidden="true" enableMouse="true">
								<Size x="19" y="20"/>
								<Anchors>
									<Anchor point="LEFT" relativeKey="$parent.Level" relativePoint="RIGHT" x="4" y="0"/>
								</Anchors>
								<Layers>
									<Layer level="ARTWORK">
										<Texture atlas="ChallengeMode-icon-chest" useAtlasSize="true">
											<Anchors>
												<Anchor point="CENTER"/>
											</Anchors>
										</Texture>
									</Layer>
									<Layer level="ARTWORK" textureSubLevel="1">
										<Texture atlas="ChallengeMode-icon-redline" useAtlasSize="true">
											<Anchors>
												<Anchor point="CENTER"/>
											</Anchors>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnEnter>
										GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
										GameTooltip:SetText(CHALLENGE_MODE_DEPLETED_KEYSTONE, 1, 1, 1);
										GameTooltip:AddLine(CHALLENGE_MODE_KEYSTONE_DEPLETED_AT_START, nil, nil, nil, true);
										GameTooltip:Show();
									</OnEnter>
									<OnLeave function="GameTooltip_Hide"/>
								</Scripts>
							</Frame>
							<Frame parentKey="TimesUpLootStatus" hidden="true" enableMouse="true">
								<Size x="19" y="20"/>
								<Anchors>
									<Anchor point="LEFT" relativeKey="$parent.TimeLeft" relativePoint="RIGHT" x="4" y="0"/>
								</Anchors>
								<Layers>
									<Layer level="ARTWORK">
										<Texture atlas="ChallengeMode-icon-chest" useAtlasSize="true">
											<Anchors>
												<Anchor point="CENTER"/>
											</Anchors>
										</Texture>
									</Layer>
									<Layer level="ARTWORK" textureSubLevel="1">
										<Texture parentKey="NoLoot" atlas="ChallengeMode-icon-redline" useAtlasSize="true">
											<Anchors>
												<Anchor point="CENTER"/>
											</Anchors>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnEnter function="Scenario_ChallengeMode_TimesUpLootStatus_OnEnter"/>
									<OnLeave function="GameTooltip_Hide"/>
								</Scripts>
							</Frame>
							<Frame parentKey="DeathCount" hidden="true" enableMouse="true" mixin="ScenarioChallengeDeathCountMixin">
								<Size x="20" y="16"/>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parent" relativePoint="BOTTOMRIGHT" x="-47" y="43"/>
								</Anchors>
								<Layers>
									<Layer level="ARTWORK">
										<Texture parentKey="Icon" atlas="poi-graveyard-neutral" useAtlasSize="true">
											<Anchors>
												<Anchor point="LEFT"/>
											</Anchors>
										</Texture>
										<FontString parentKey="Count" inherits="GameFontHighlightSmall2">
											<Anchors>
												<Anchor point="LEFT" relativeKey="$parent.Icon" relativePoint="RIGHT" x="1" y="0"/>
											</Anchors>
										</FontString>
									</Layer>
								</Layers>
								<Scripts>
									<OnLoad method="OnLoad"/>
									<OnEvent method="OnEvent"/>
									<OnEnter method="OnEnter"/>
									<OnLeave function="GameTooltip_Hide"/>
								</Scripts>
							</Frame>
							<StatusBar parentKey="StatusBar" useParentLevel="true">
								<Size x="207" y="13"/>
								<Anchors>
									<Anchor point="BOTTOM" x="0" y="10"/>
								</Anchors>
								<BarTexture atlas="ChallengeMode-TimerFill"/>
							</StatusBar>
							<Frame hidden="true" inherits="ScenarioChallengeModeAffixTemplate"/>
						</Frames>
					</Frame>
					<Frame name="ScenarioProvingGroundsBlock" hidden="true">
						<Size x="201" y="77"/>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture parentKey="BG" atlas="ScenarioTrackerToast" useAtlasSize="true">
									<Anchors>
										<Anchor point="TOPLEFT" x="0" y="0"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="BORDER">
								<Texture parentKey="GoldCurlies" atlas="ScenarioTrackerToast-FinalFiligree" useAtlasSize="true">
									<Anchors>
										<Anchor point="TOPLEFT" x="4" y="-4"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<Texture parentKey="MedalIcon" alphaMode="ADD" file="Interface\Challenges\challenges-plat">
									<Size x="52" y="52"/>
									<Anchors>
										<Anchor point="LEFT" x="5" y="-1"/>
									</Anchors>
								</Texture>
								<FontString inherits="QuestFont_Large" parentKey="WaveLabel" text="PROVING_GROUNDS_WAVE">
									<Anchors>
										<Anchor point="TOPLEFT" relativeKey="$parent.MedalIcon" relativePoint="TOPRIGHT" x="1" y="-4"/>
									</Anchors>
									<Color r="1.0" g="0.82" b="0"/>
									<Shadow>
										<Offset x="1" y="-1"/>
										<Color r="0" g="0" b="0"/>
									</Shadow>
								</FontString>
								<FontString inherits="GameFontHighlightLarge" parentKey="Wave" text="0">
									<Anchors>
										<Anchor point="BOTTOMLEFT" relativeKey="$parent.WaveLabel" relativePoint="BOTTOMRIGHT" x="4" y="-1"/>
									</Anchors>
								</FontString>
								<FontString inherits="QuestFont_Large" parentKey="ScoreLabel" text="PROVING_GROUNDS_SCORE" hidden="true">
									<Anchors>
										<Anchor point="TOPLEFT" relativeKey="$parent.WaveLabel" relativePoint="BOTTOMLEFT" x="0" y="-3"/>
									</Anchors>
									<Color r="1.0" g="0.82" b="0"/>
									<Shadow>
										<Offset x="1" y="-1"/>
										<Color r="0" g="0" b="0"/>
									</Shadow>
								</FontString>
								<FontString inherits="GameFontHighlightLarge" parentKey="Score" text="0" hidden="true">
									<Anchors>
										<Anchor point="BOTTOMLEFT" relativeKey="$parent.ScoreLabel" relativePoint="BOTTOMRIGHT" x="4" y="-1"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<StatusBar parentKey="StatusBar" useParentLevel="true">
								<Size x="177" y="15"/>
								<Layers>
									<Layer level="OVERLAY">
										<Texture atlas="challenges-timerborder">
											<Size x="184" y="25"/>
											<Anchors>
												<Anchor point="CENTER" x="0" y="0"/>
											</Anchors>
										</Texture>
										<FontString inherits="GameFontHighlight" justifyH="CENTER" parentKey="TimeLeft" />
									</Layer>
								</Layers>
								<BarTexture file="Interface\TargetingFrame\UI-StatusBar"/>
								<BarColor r="0" g="0.33" b="0.61"/>
							</StatusBar>
						</Frames>
					</Frame>
				</Frames>
			</Frame>
		</ScrollChild>
		<Scripts>
			<OnLoad function="ScenarioBlocksFrame_OnLoad"/>
			<OnEvent function="ScenarioBlocksFrame_OnEvent"/>
		</Scripts>
	</ScrollFrame>

	<Frame name="ScenarioProvingGroundsBlockAnim" parent="UIParent">
		<Anchors>
			<Anchor point="TOPLEFT" relativeTo="ScenarioProvingGroundsBlock"/>
			<Anchor point="BOTTOMRIGHT" relativeTo="ScenarioProvingGroundsBlock"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="CountdownAnim">
				<Alpha childKey="BGAnim" fromAlpha="0" toAlpha="1" duration="0.25" order="1"/>
				<Alpha childKey="BGAnim" endDelay="0.45" fromAlpha="1" toAlpha="0" duration="0.3" order="2"/>
				<Alpha childKey="BorderAnim" fromAlpha="0" toAlpha="1" duration="0.25" order="1"/>
				<Alpha childKey="BorderAnim" endDelay="0.45" fromAlpha="1" toAlpha="0" duration="0.3" order="2"/>
				<Alpha childKey="Glow" duration="0.25" order="1" fromAlpha="0" toAlpha="0.5"/>
				<Alpha childKey="Glow" endDelay="0.45" duration="0.3" order="2" fromAlpha="0.5" toAlpha="0"/>
				<Scripts>
					<OnFinished function="Scenario_ProvingGrounds_CountdownAnim_OnFinished"/>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="OVERLAY">
				<Texture parentKey="BGAnim" atlas="ScenarioTrackerToast" useAtlasSize="true" alpha="0" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BorderAnim" atlas="ScenarioTrackerToast-FinalFiligree" useAtlasSize="true" alpha="0" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPLEFT" x="4" y="-4"/>
					</Anchors>
				</Texture>
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" parentKey="Glow" alpha="0" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BGAnim" x="-35" y="45"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BGAnim" x="35" y="-45"/>
					</Anchors>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="ObjectiveTrackerScenarioRewardsFrame" parent="UIParent" hidden="true">
        <Size x="168" y="128"/>
		<Anchors>
			<Anchor point="RIGHT" relativeTo="ScenarioBlocksFrame" relativePoint="LEFT" x="8" y="-24"/>
		</Anchors>
        <Layers>
            <Layer level="ARTWORK">
                <Texture parentKey="RewardsTop" hidden="false" alpha="0" atlas="Rewards-Top" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPRIGHT"/>
					</Anchors>
				</Texture>
                <Texture parentKey="HeaderTop" hidden="false" alpha="0" atlas="OBJFX_LineGlow" useAtlasSize="true">
                    <Anchors>
                        <Anchor point="CENTER" relativeKey="$parent.RewardsTop" x="50" y="6"/>
                    </Anchors>
                </Texture>
                <Texture parentKey="RewardsBottom" hidden="false" alpha="0" atlas="Rewards-Top" useAtlasSize="true">
                    <Anchors>
                        <Anchor point="CENTER" relativeKey="$parent.RewardsTop" x="0" y="0"/>
                    </Anchors>
                </Texture>
            </Layer>
            <Layer level="BORDER">
                <Texture parentKey="RewardsShadow" hidden="false" alpha="0" atlas="Rewards-Shadow" useAtlasSize="true">
                    <Anchors>
                        <Anchor point="TOP" relativeKey="$parent.RewardsBottom" relativePoint="BOTTOM" x="0" y="10"/>
                    </Anchors>
                </Texture>
            </Layer>
            <Layer level="OVERLAY">
				<FontString parentKey="Header" alpha="0" inherits="QuestFont_Outline_Huge" text="REWARDS">
					<Anchors>
						<Anchor point="TOP" x="0" y="2"/>
					</Anchors>
				</FontString>
                <Texture parentKey="HeaderGlow" hidden="false" alpha="0" alphaMode="ADD" atlas="OBJFX_LineGlow" useAtlasSize="true">
                    <Anchors>
                        <Anchor point="CENTER" relativeKey="$parent.HeaderTop" x="-25" y="0"/>
                    </Anchors>
                </Texture>
            </Layer>
        </Layers>
		<Frames>
			<Frame inherits="BonusObjectiveTrackerRewardTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.RewardsTop" relativePoint="BOTTOMLEFT" x="25" y="0"/>
                </Anchors>
			</Frame>
		</Frames>
        <Animations>
            <AnimationGroup parentKey="Anim" setToFinalAlpha="true">
                <Scale childKey="RewardsTop" duration="0.25" order="1" fromScaleX="0.25" fromScaleY="1" toScaleX="1" toScaleY="1">
					<Origin point="RIGHT"/>
				</Scale>
                <Alpha childKey="RewardsTop" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
                <Alpha childKey="RewardsTop" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
                <Alpha childKey="Header" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
                <Alpha childKey="Header" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
                <Scale childKey="HeaderTop" duration="0.25" order="1" fromScaleX="0.25" fromScaleY="1.2" toScaleX="1.2" toScaleY="1.2"/>
                <Alpha childKey="HeaderTop" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
                <Translation childKey="HeaderTop" duration="0.25" order="1" offsetX="-30" offsetY="0"/>
                <Alpha childKey="HeaderTop" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
                <Scale childKey="HeaderGlow" startDelay="0.05" duration="0.5" order="1" fromScaleX="1" fromScaleY="1.2" toScaleX="1.2" toScaleY="1.2"/>
                <Alpha childKey="HeaderGlow" startDelay="0.05" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
                <Alpha childKey="HeaderGlow" startDelay="0.3" duration="0.5" order="1" fromAlpha="1" toAlpha="0"/>
                <Translation childKey="HeaderGlow" startDelay="0.05" duration="0.5" order="1" offsetX="-70" offsetY="0"/>
                <Alpha childKey="RewardsBottom" startDelay="0.5" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
                <Translation parentKey="RewardsBottomAnim" childKey="RewardsBottom" startDelay="0.5" duration="0.35" order="1" offsetX="0" offsetY="-83"/>
                <Alpha childKey="RewardsBottom" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
                <Scale parentKey="RewardsShadowAnim" childKey="RewardsShadow" startDelay="0.5" duration="0.35" order="1" fromScaleX="0.8" fromScaleY="0.2" toScaleX="0.8" toScaleY="3">
					<Origin point="TOP"/>
				</Scale>
                <Alpha childKey="RewardsShadow" startDelay="0.5" duration="0.35" order="1" fromAlpha="0" toAlpha="1"/>
                <Alpha childKey="RewardsShadow" startDelay="2" duration="1" order="2" fromAlpha="1" toAlpha="0"/>
				<Scripts>
					<OnFinished function="ScenarioObjectiveTracker_OnAnimateRewardDone"/>
				</Scripts>
            </AnimationGroup>
        </Animations>
    </Frame>

	<Frame name="ScenarioTimerFrame" hidden="true">
		<Scripts>
			<OnUpdate function="ScenarioTimer_OnUpdate"/>
		</Scripts>
	</Frame>

	<Frame name="ScenarioStepRewardTooltip" inherits="VerticalLayoutFrame" parent="UIParent" frameStrata="TOOLTIP" clampedToScreen="true" hidden="true">
		<KeyValues>
			<KeyValue key="fixedWidth" value="100" type="number"/>
			<KeyValue key="expand" value="true" type="boolean"/>
			<KeyValue key="topPadding" value="10" type="number"/>
			<KeyValue key="bottomPadding" value="12" type="number"/>
			<KeyValue key="leftPadding" value="10" type="number"/>
			<KeyValue key="rightPadding" value="10" type="number"/>
			<KeyValue key="spacing" value="8" type="number"/>
		</KeyValues>
		<Backdrop bgFile="Interface\Tooltips\UI-Tooltip-Background" edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
			<EdgeSize val="16"/>
			<TileSize val="16"/>
			<BackgroundInsets left="5" right="5" top="5" bottom="5"/>
		</Backdrop>
		<Anchors>
			<Anchor point="BOTTOMRIGHT"/>
		</Anchors>
		<Layers>
			<Layer level="OVERLAY">
				<FontString parentKey="Title" inherits="GameFontNormal" justifyH="LEFT" wordwrap="true" text="SCENARIO_INVASION_REWARD_TOOLTIP_TEXT">
					<Size x="100" y="0"/>
					<KeyValues>
						<KeyValue key="layoutIndex" value="1" type="number"/>
						<KeyValue key="expand" value="true" type="boolean"/>
					</KeyValues>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="ItemTooltip" inherits="EmbeddedItemTooltip">
				<KeyValues>
					<KeyValue key="leftPadding" value="8" type="number"/>
					<KeyValue key="layoutIndex" value="2" type="number"/>
				</KeyValues>
				<Size x="100" y="100"/>
				<Scripts>
					<OnSizeChanged inherit="prepend">
						self:GetParent():Layout();
					</OnSizeChanged>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b);
				self:SetBackdropColor(TOOLTIP_DEFAULT_BACKGROUND_COLOR.r, TOOLTIP_DEFAULT_BACKGROUND_COLOR.g, TOOLTIP_DEFAULT_BACKGROUND_COLOR.b);
			</OnLoad>
		</Scripts>
	</Frame>

</Ui>
