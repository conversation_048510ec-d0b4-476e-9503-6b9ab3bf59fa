{"Lua.runtime.version": "Lua 5.1", "Lua.runtime.builtin": {"basic": "disable", "debug": "disable", "io": "disable", "math": "disable", "os": "disable", "package": "disable", "string": "disable", "table": "disable", "utf8": "disable"}, "Lua.workspace.library": ["~\\.vscode\\extensions\\ketho.wow-api-0.20.8\\Annotations\\Core"], "Lua.diagnostics.globals": ["PLAYER_FACTION_GROUP", "WorldFrame", "SOUNDKIT", "TimerTracker", "IsInLFDBattlefield", "UnitFrame_Initialize", "SecureUnitButton_OnLoad", "UnitFrame_OnEvent", "CloseDropDownMenus", "UIParent_UpdateTopFramePositions", "DEAD", "TargetFrame", "SET_FOCUS", "FocusFrame", "UIDropDownMenu_Initialize", "UnitFrame_Update", "UIParent_ManageFramePositions", "UnitFrame_UpdateThreatIndicator", "RAID_TARGET_ICON", "RefreshDebuffs", "FocusFrameToT", "ToggleDropDownMenu", "<PERSON><PERSON><PERSON><PERSON>", "PET_TYPE_SUFFIX", "CooldownFrame_Set", "GetCreatureDifficultyColor", "MainMenuBar", "DebuffTypeColor", "CompactUnitFrame_SetUnit", "CompactUnitFrame_SetUpFrame", "CompactUnitFrame_SetUpdateAllEvent", "MEMBERS_PER_RAID_GROUP", "CompactPartyFrame", "DefaultCompactUnitFrameSetup", "PARTY", "SECOND_NUMBER_CAP_NO_SPACE", "FIRST_NUMBER_CAP_NO_SPACE", "SECOND_NUMBER_CAP", "FIRST_NUMBER_CAP", "PlayerLevelText", "INTERRUPT", "MISS", "RESIST", "DODGE", "PARRY", "BLOCK", "EVADE", "IMMUNE", "DEFLECT", "ABSORB", "REFLECT", "StaticPopup_Show", "SOUND", "UIDropDownMenu_SetWidth", "UIDropDownMenu_SetSelectedValue", "VOICE_LABEL", "UIDropDownMenu_DisableDropDown", "UIDropDownMenu_EnableDropDown", "PUSH_TO_TALK", "OPTION_TOOLTIP_VOICE_TYPE1", "UIDropDownMenu_AddButton", "VOICE_ACTIVATED", "OPTION_TOOLTIP_VOICE_TYPE2", "UIDropDownMenu_GetSelectedValue", "UIDropDownMenu_CreateInfo", "OPTION_TOOLTIP_VOICE_SOUND", "OPTION_TOOLTIP_VOICE_MUSIC", "OPTION_TOOLTIP_VOICE_AMBIENCE", "UIDropDownMenu_SetText", "PTT_BOUND", "GetBindingFromClick", "ERROR_CANNOT_BIND", "UIErrorsFrame", "CAN_BIND_PTT", "ALREADY_BOUND", "WHOS_TO_DISPLAY", "MAX_PARTY_MEMBERS", "WhoFrameColumnHeader2", "PetFrameHealthBarText", "PetFrameManaBarText", "CombatConfigColorsExampleTitle", "CombatConfigColorsExampleString1", "CombatConfigFormattingExampleTitle", "CombatConfigFormattingExampleString1", "AddFriendNameEditBox", "TradeFramePlayerEnchantText", "TradeFrame", "GuildInviteFrameInviterName", "GuildInviteFrame", "GuildInviteFrameInviteText", "GuildInviteFrameGuildName", "GarrisonBuildingAlertSystem", "GarrisonFollowerAlertSystem", "GarrisonShipFollowerAlertSystem", "Character<PERSON><PERSON>e", "MailFrame", "RaidNotice_Clear", "CinematicFrame", "UpperBlackBar", "LowerBlackBar", "LowHealthFrame", "RaidBossEmoteFrame", "SUBTITLE_FORMAT", "CoinPickupFrame", "CoinPickupText", "CoinPickupLeftButton", "CoinPickupRightButton", "COPPER_AMOUNT_SYMBOL", "SILVER_AMOUNT_SYMBOL", "GOLD_AMOUNT_SYMBOL", "MoneyTypeInfo", "CoinPickupCopperIcon", "CoinPickupSilverIcon", "CoinPickupGoldIcon", "COPPER_PER_SILVER", "COPPER_PER_GOLD", "DEBUFF_SYMBOL_MAGIC", "DEBUFF_SYMBOL_CURSE", "DEBUFF_SYMBOL_DISEASE", "DEBUFF_SYMBOL_POISON", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TOOLTIP_UPDATE_TIME", "LE_AUTOCOMPLETE_PRIORITY_OTHER", "LE_AUTOCOMPLETE_PRIORITY_INTERACTED", "LE_AUTOCOMPLETE_PRIORITY_IN_GROUP", "LE_AUTOCOMPLETE_PRIORITY_GUILD", "LE_AUTOCOMPLETE_PRIORITY_FRIEND", "LE_AUTOCOMPLETE_PRIORITY_ACCOUNT_CHARACTER", "LE_AUTOCOMPLETE_PRIORITY_ACCOUNT_CHARACTER_SAME_REALM", "AutoCompleteBox", "AutoCompleteInstructions", "NORMAL_FONT_COLOR_CODE", "AUTOCOMPLETE_LABEL_INTERACTED", "AUTOCOMPLETE_LABEL_GROUP", "AUTOCOMPLETE_LABEL_GUILD", "AUTOCOMPLETE_LABEL_FRIEND", "AutoCompleteButton1", "PRESS_TAB", "CONTINUED", "FONT_COLOR_CODE_CLOSE", "RGBTableToColorCode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CreateFramePool", "AchievementAlertSystem", "CriteriaAlertSystem", "LootAlertSystem", "LootUpgradeAlertSystem", "GarrisonRandomMissionAlertSystem", "NewRecipeLearnedAlertSystem", "LegendaryItemAlertSystem", "NewPetAlertSystem", "NewMountAlertSystem", "WorldQuestCompleteAlertFrame_GetIconForQuestID", "AchievementFrame_LoadUI", "LE_SCENARIO_TYPE_LEGION_INVASION", "QuestUtils_IsQuestWorldQuest", "Achievement<PERSON><PERSON><PERSON>", "DungeonCompletionAlertSystem", "MoneyWonAlertSystem", "HonorAwardedAlertSystem", "GarrisonTalentAlertSystem", "DoesFollowerMatchCurrentGarrisonType", "WorldQuestCompleteAlertSystem", "InvasionAlertSystem", "ScenarioAlertSystem", "GarrisonFollowerOptions", "GarrisonShipMissionAlertSystem", "GarrisonMissionAlertSystem", "SPEC_MAGE_ARCANE", "ARCANE_CHARGES", "ARCANE_CHARGES_TOOLTIP", "MAC_DISABLE_OS_SHORTCUTS_TOOLTIP", "MAC_USE_COMMAND_AS_CONTROL_TOOLTIP", "MAC_USE_COMMAND_LEFT_CLICK_AS_RIGHT_CLICK_TOOLTIP", "KEYBOARD_HEADER", "FAILED", "INTERRUPTED", "BNToastFrame", "BNToastFrameTopLine", "BNToastFrameMiddleLine", "BNToastFrameBottomLine", "TimeAlertFrame", "TimeAlertFrameText", "TIME_PLAYED_ALERT", "BNToastFrameGlowFrame", "StaticPopupSpecial_Hide", "StaticPopupSpecial_Show", "BNToastFrameIconTexture", "BNToastFrameDoubleLine", "BN_TOAST_NEW_INVITE", "BN_TOAST_PENDING_INVITES", "BN_TOAST_ONLINE", "BN_TOAST_OFFLINE", "FriendsTabHeaderTab1", "ToggleFriendsFrame", "FriendsListFrame_ToggleInvites", "FriendsFrame", "SPEC_PRIEST_SHADOW", "UpdateMicroButtons", "ERR_RESTRICTED_ACCOUNT_TRIAL", "BLIZZARD_STORE_ERROR_PARENTAL_CONTROLS", "WOW_GAMES_CATEGORY_ID", "GarrisonShipyardFollowerTooltip", "GarrisonFollowerTooltip", "SPEC_PALADIN_RETRIBUTION", "GetColorForCurrencyReward", "BONUS_OBJECTIVE_REWARD_WITH_COUNT_FORMAT", "LE_EXPANSION_LEVEL_CURRENT", "CursorUpdate", "SetItemButtonQuality", "PanelTemplates_SetNumTabs", "BankFramePurchaseInfo", "NUM_BANKBAGSLOTS", "BankFrame", "MoneyFrame_Update", "NUM_BANKGENERIC_SLOTS", "OpenAllBags", "StaticPopup_Hide", "CloseAllBags", "UpdateContainerFrameAnchors", "NUM_CONTAINER_FRAMES", "PanelTemplates_SetTab", "BANK_CONTAINER", "NUM_BAG_SLOTS", "CloseBag", "REAGENTBANK_CONTAINER", "LE_FRAME_TUTORIAL_REAGENT_BANK_UNLOCK", "SetItemButtonCount", "SetItemButtonTextureVertexColor", "SetItemButtonDesaturated", "BankSlotsFrame", "SetMoneyFrameColor", "ToggleBag", "ReagentBankFrame", "HandleModifiedItemClick", "BankFrameTitleText", "REAGENT_BANK", "TEXTURE_ITEM_QUEST_BANG", "TEXTURE_ITEM_QUEST_BORDER", "BANK_BAG", "BANK_BAG_PURCHASE", "BAG_FILTER_ASSIGNED_TO", "BAG_FILTER_LABELS", "ZoneAbilityFrame", "LE_NUM_NORMAL_ACTION_PAGES", "LE_NUM_ACTIONS_PER_PAGE", "LE_FRAME_TUTORIAL_GARRISON_ZONE_ABILITY", "SetDesaturation", "GameTooltip_SetDefaultAnchor", "Round", "FloatingGarrisonFollowerTooltip", "FloatingGarrisonMissionTooltip", "GARRISON_FOLLOWER_ITEM_LEVEL", "FloatingGarrisonFollowerAbilityTooltip", "FloatingGarrisonShipyardFollowerTooltip", "RETRIEVING_DATA", "GARRISON_FOLLOWER_TOOLTIP_UPGRADE_XP", "GARRISON_SHIPYARD_MISSION_TOOLTIP_NUM_REQUIRED_FOLLOWERS", "GARRISON_MISSION_TOOLTIP_NUM_REQUIRED_FOLLOWERS", "GARRISON_FOLLOWER_MAX_UPGRADE_QUALITY", "GARRISON_FOLLOWER_TOOLTIP_XP", "PercentageBetween", "GARRISON_FOLLOWER_BELOW_LEVEL_MAX_XP_TOOLTIP", "GARRISON_FOLLOWER_BELOW_ITEM_LEVEL_TOOLTIP", "GARRISON_FOLLOWER_BELOW_LEVEL_TOOLTIP", "GARRISON_HIGH_THREAT_VALUE", "GARRISON_ABILITY_COUNTERS_FORMAT", "ITEM_QUALITY_COLORS", "<PERSON><PERSON>", "GameTooltip_AddQuestRewardsToTooltip", "BOUNTY_BOARD_NO_BOUNTIES", "BOUNTY_BOARD_LOCKED_TITLE", "LE_FRAME_TUTORIAL_BOUNTY_INTRO", "LE_FRAME_TUTORIAL_BOUNTY_FINISHED", "QUEST_DASH", "BOUNTY_TUTORIAL_INTRO", "BOUNTY_TUTORIAL_BOUNTY_FINISHED", "MovieFrame", "GetUIPanel", "UNKNOWNOBJECT", "FRIENDS_WOW_NAME_COLOR_CODE", "SOCIAL_QUEUE_QUEUED_FOR", "SOCIAL_QUEUE_FORMAT_ARENA_SKIRMISH", "FRIENDS_BNET_NAME_COLOR_CODE", "GetPlayerLink", "LFG_LIST_ENTRY_DELISTED", "QUICK_JOIN_TOOLTIP_NO_AVAILABLE_ROLES", "QUICK_JOIN_IS_AUTO_ACCEPT_TOOLTIP", "GetBNPlayerLink", "LFG_SUBTYPEID_DUNGEON", "LFG_SUBTYPEID_HEROIC", "LFG_SUBTYPEID_RAID", "LFG_SUBTYPEID_FLEXRAID", "LFG_SUBTYPEID_WORLDPVP", "SOCIAL_QUEUE_FORMAT_BATTLEGROUND", "SOCIAL_QUEUE_FORMAT_ARENA", "QUICK_JOIN_TOOLTIP_AVAILABLE_ROLES_FORMAT", "QUICK_JOIN_TOOLTIP_AVAILABLE_ROLES", "TYPEID_RANDOM_DUNGEON", "SOCIAL_QUEUE_FORMAT_DUNGEON", "SOCIAL_QUEUE_FORMAT_HEROIC_DUNGEON", "SOCIAL_QUEUE_FORMAT_RAID", "SOCIAL_QUEUE_FORMAT_WORLDPVP", "GARRISON_PARTY_NOT_FULL_TOOLTIP", "GARRISON_SHIPYARD_PARTY_NOT_FULL_TOOLTIP", "GARRISON_PARTY_NOT_ENOUGH_CHAMPIONS", "GarrisonLandingPage", "GARRISON_LANDING_BUILDING_COMPLEATE", "GARRISON_MISSION_TOOLTIP_RETURN_TO_START", "GARRISON_FOLLOWER_CONFIRM_EQUIPMENT", "GARRISON_TRAITS", "GARRISON_FOLLOWER_ADDED_TOAST", "GARRISON_FOLLOWER_ADDED_UPGRADED_TOAST", "GARRISON_FOLLOWERS", "GARRISON_FOLLOWER_COUNT", "GARRISON_SHIPYARD_MISSION_TOOLTIP_RETURN_TO_START", "GARRISON_SHIPYARD_CONFIRM_EQUIPMENT", "GARRISON_FOLLOWER_CONFIRM_EQUIPMENT_REPLACEMENT", "GARRISON_SHIPYARD_FOLLOWER_ADDED_TOAST", "GARRISON_SHIPYARD_FOLLOWER_ADDED_UPGRADED_TOAST", "GARRISON_FLEET", "GARRISON_SHIPYARD_FOLLOWER_COUNT", "ORDER_HALL_LANDING_COMPLETE", "ORDER_HALL_MISSION_TOOLTIP_RETURN_TO_START", "ORDER_HALL_EQUIPMENT_SLOTS", "ORDER_HALL_FOLLOWER_ADDED_TOAST", "ORDER_HALL_FOLLOWER_ADDED_UPGRADED_TOAST", "ORDER_HALL_TROOP_ADDED_TOAST", "ORDER_HALL_TROOP_ADDED_UPGRADED_TOAST", "FOLLOWERLIST_LABEL_CHAMPIONS", "GARRISON_CHAMPION_COUNT", "GARRISON_LANDING_PAGE_TITLE", "ORDER_HALL_LANDING_PAGE_TITLE", "Garrison_LoadUI", "OrderHall_LoadUI", "GarrisonMissionFrame", "OrderHallMissionFrame", "C<PERSON>", "MAX_RAID_MEMBERS", "LE_PARTY_CATEGORY_HOME", "PLAYER_IS_PVP_AFK", "UIDropDownMenu_JustifyText", "UIDROPDOWNMENU_SHOW_TIME", "TRANSMOG_SLOTS", "WardrobeOutfitEditFrame", "GRAY_FONT_COLOR_CODE", "TRANSMOG_OUTFIT_NONE", "WardrobeOutfitCheckAppearancesFrame", "WardrobeTransmogFrame", "LE_FRAME_TUTORIAL_TRANSMOG_OUTFIT_DROPDOWN", "TRANSMOG_OUTFIT_ALREADY_EXISTS", "StaticPopup_Visible", "GREEN_FONT_COLOR_CODE", "TRANSMOG_OUTFIT_NEW", "SetLargeGuildTabardTextures", "AchievementShield_SetPoints", "CloseAllWindows", "AchievementFrame_SelectAchievement", "SetCollectionsJournalShown", "PetJournal_SelectPet", "MountJournal_SelectByMountID", "GUILD_CHALLENGE_PROGRESS_FORMAT", "LOOT_BORDER_BY_QUALITY", "GARRISON_BUILDING_COMPLETE_TOAST", "YOU_EARNED_LABEL", "COLLECTIONS_JOURNAL_TAB_INDEX_PETS", "PetJournal", "COLLECTIONS_JOURNAL_TAB_INDEX_MOUNTS", "ToggleGuildFrame", "SetTooltipMoney", "SetSmallGuildTabardTextures", "AchievementFrame_SetFilter", "YOU_WON_LABEL", "YOU_RECEIVED_LABEL", "ITEM_UPGRADED_LABEL", "GARRISON_CACHE", "GetItemInfoFromHyperlink", "SearchBagsForItem", "OpenBag", "SearchBagsForItemLink", "ToggleCollectionsJournal", "YOU_RECEIVED", "ACHIEVEMENT_FILTER_ALL", "LOOT_ROLL_TYPE_NEED", "LOOT_ROLL_TYPE_GREED", "LOOTUPGRADEFRAME_TITLE", "MERCHANT_HONOR_POINTS", "BONUS_VALOR_TOOLTIP", "BONUS_OBJECTIVE_EXPERIENCE_FORMAT", "GUILD_ACHIEVEMENT_UNLOCKED", "ACHIEVEMENT_UNLOCKED", "ACHIEVEMENTUI_SELECTEDFILTER", "NEW_RECIPE_LEARNED_TITLE", "GAIN_EXPERIENCE", "AchievementFrameFilters", "ACHIEVEMENT_FILTER_INCOMPLETE", "ACHIEVEMENT_FILTER_COMPLETE", "GARRISON_RESOURCES_LOOT", "CURRENCY_QUANTITY_TEMPLATE", "UPGRADED_RECIPE_LEARNED_TITLE", "StaticPopupDialogs", "CONFIRM_GORGROND_GARRISON_CHOICE", "OKAY", "CANCEL", "ShowInspectCursor", "SetItemButtonTexture", "REWARD_REPUTATION", "SplashFrame", "HideParentPanel", "LE_EXPANSION_LEGION", "SPLASH_BASE_HEADER", "SPLASH_LEGION_BOX_LABEL", "SPLASH_LEGION_BOX_FEATURE1_TITLE", "SPLASH_LEGION_BOX_FEATURE1_DESC", "SPLASH_LEGION_BOX_FEATURE2_TITLE", "SPLASH_LEGION_BOX_FEATURE2_DESC", "SPLASH_LEGION_BOX_RIGHT_TITLE", "SPLASH_LEGION_BOX_RIGHT_DESC", "SPLASH_LEGION_NEW_7_3_5_LABEL", "SPLASH_LEGION_NEW_7_3_5_FEATURE1_TITLE", "SPLASH_LEGION_NEW_7_3_5_FEATURE1_DESC", "SPLASH_LEGION_NEW_7_3_5_FEATURE2_TITLE", "SPLASH_LEGION_NEW_7_3_5_FEATURE2_DESC", "SPLASH_LEGION_NEW_7_3_5_RIGHT_TITLE", "SPLASH_LEGION_NEW_7_3_5_RIGHT_DESC", "SPLASH_OPENS_SOON", "QuestFrame", "PVPTalentPrestigeLevelDialog", "HONOR_REWARD_ARTIFACT_POWER", "HONOR_REWARD_MONEY", "HONOR_REWARD_TITLE_TOOLTIP", "PVP_HONOR_XP_BAR_CANNOT_PRESTIGE_HERE", "EXHAUST_TOOLTIP2", "ARTIFACT_POWER_GAIN", "PVP_HONOR_PRESTIGE_AVAILABLE", "MAX_HONOR_LEVEL", "PVP_PRESTIGE_RANK_UP_NEXT_MAX_LEVEL_REWARD", "XP_TEXT", "EXHAUST_HONOR_TOOLTIP1", "HONOR_BAR", "SILVER_PER_GOLD", "EXHAUST_TOOLTIP4", "HONOR_LEVEL_LABEL", "AnimateTexCoords", "ActionBarActionEventsFrame", "OverrideActionBar", "PetBattleFrame", "ActionBarButtonEventsFrame", "SecureActionButton_OnClick", "PetBattleFrame_ButtonDown", "PetBattleFrame_ButtonUp", "SecureButton_GetEffectiveButton", "SecureButton_GetModifiedAttribute", "MultiBarLeft", "MultiBarBottomRight", "MultiBarRight", "SpellFlyout", "HybridScrollFrame_CreateButtons", "UIDropDownMenu_SetInitializeFunction", "FriendsFrame_CloseQuickJoinHelpTip", "HybridScrollFrame_Update", "WHISPER", "QuickJoinFrame", "JOIN_QUEUE", "HybridScrollFrame_GetOffset", "QUICK_JOIN_ALREADY_IN_PARTY", "SetItemRef", "GMError", "QUICK_JOIN_ROLE_NOT_NEEDED", "LFGListApplicationDialog", "QuickJoinRoleSelectionFrame", "WillAcceptInviteRemoveQueues", "QUICK_JOIN_FAILED", "SIGN_UP", "QUICK_JOIN_TOAST_EXTRA_PLAYERS", "DISABLED_FONT_COLOR_CODE", "PLAYER_LIST_DELIMITER", "LFG_LIST_IN_QUOTES", "CLASS_SORT_ORDER", "LFG_LIST_CONVERT_TO_RAID_WARNING", "INVITE", "PVEFrame_ShowFrame", "AUTO_GROUP_CREATION_NORMAL_QUEST", "SearchBoxTemplate_OnTextChanged", "UIDropDownMenu_SetAnchor", "NUM_LE_LFG_CATEGORYS", "LFGListPVEStub", "LFG_LIST_ENTER_NAME", "nop", "FILTER", "LFG_LIST_APP_UNEMPOWERED", "LFG_LIST_MUST_SIGN_UP_TO_WHISPER", "LFG_LIST_SELECT_A_CATEGORY", "LFG_LIST_NOT_LEADER", "LFG_LIST_MORE", "AUTO_GROUP_CREATION_WORLD_QUEST", "LFG_LIST_MUST_HAVE_NAME", "ERR_RESTRICTED_ACCOUNT_LFG_LIST_TRIAL", "LFG_LIST_PROVING_GROUND_TITLE", "LFG_LIST_APP_CURRENTLY_APPLYING", "LFG_LIST_MAX_MEMBERS", "LFG_LIST_MUST_CHOOSE_SPEC", "LFG_LIST_OFFLINE_MEMBER", "LFG_LIST_SELECT_A_SEARCH_RESULT", "LFG_LIST_MUST_SELECT_ROLE", "LFG_LIST_INVITED_TO_GROUP", "LFG_LIST_ILVL_ABOVE_YOURS", "LFG_LIST_HONOR_LEVEL_ABOVE_YOURS", "LFG_LIST_LEGACY", "LFG_LIST_CATEGORY_FORMAT", "WHISPER_LEADER", "LFG_LIST_REPORT_GROUP_FOR", "LFG_LIST_REPORT_FOR", "IGNORE_PLAYER", "PVEFrame_ToggleFrame", "CANNOT_DO_THIS_WITH_LFGLIST_APP", "CANNOT_DO_THIS_WHILE_LFGLIST_LISTED", "NO_LFG_LIST_AVAILABLE", "LFG_LIST_LOADING", "LFG_LIST_RECOMMENDED_ILVL", "LFG_LIST_ITEM_LEVEL_INSTR_SHORT", "DONE_EDITING", "LIST_GROUP", "LFG_LIST_ITEM_LEVEL_CURRENT", "LFG_LIST_PRIVATE", "LFG_LIST_INVITE_GROUP", "LFG_LIST_APP_INVITED", "LFG_LIST_APP_CANCELLED", "LFG_LIST_APP_DECLINED", "LFG_LIST_APP_TIMED_OUT", "LFG_LIST_APP_INVITE_ACCEPTED", "LFG_LIST_APP_INVITE_DECLINED", "RAID_CLASS_COLORS", "LFG_LIST_PROVING_TANK_GOLD", "LFG_LIST_PROVING_TANK_SILVER", "LFG_LIST_PROVING_TANK_BRONZE", "LFG_LIST_PROVING_HEALER_GOLD", "LFG_LIST_PROVING_HEALER_SILVER", "LFG_LIST_PROVING_HEALER_BRONZE", "LFG_LIST_PROVING_DAMAGER_GOLD", "LFG_LIST_PROVING_DAMAGER_SILVER", "LFG_LIST_PROVING_DAMAGER_BRONZE", "SearchBoxTemplateClearButton_OnClick", "LFG_LIST_ROLE_CHECK", "LFG_LIST_PENDING", "LFG_LIST_JOINED_GROUP_NOTICE", "LFG_LIST_OFFLINE_MEMBER_NOTICE", "LFG_LIST_OFFLINE_MEMBER_NOTICE_GONE", "LE_LFG_LIST_DISPLAY_TYPE_ROLE_COUNT", "LE_LFG_LIST_DISPLAY_TYPE_ROLE_ENUMERATE", "LE_LFG_LIST_DISPLAY_TYPE_CLASS_ENUMERATE", "LE_LFG_LIST_DISPLAY_TYPE_PLAYER_COUNT", "LE_LFG_LIST_DISPLAY_TYPE_HIDE_ALL", "GetLFGMode", "CANNOT_DO_THIS_WHILE_PVE_QUEUING", "CANNOT_DO_THIS_IN_BATTLEGROUND", "LFG_LIST_TOOLTIP_FRIENDS_IN_GROUP", "LFG_LIST_BOSSES_DEFEATED", "LFG_LIST_TOOLTIP_AUTO_ACCEPT", "LFGListFrame", "LFG_LIST_NO_RESULTS_FOUND", "LFG_LIST_AND_MORE", "CANNOT_DO_THIS_WHILE_PVP_QUEUING", "LFG_LIST_TOO_MANY_FOR_ACTIVITY", "LFG_LIST_GROUP_TOO_FULL", "LFG_LIST_INVITED_APP_FILLS_GROUP", "UNIT_TYPE_LEVEL_TEMPLATE", "LFG_LIST_HONOR_LEVEL_CURRENT_PVP", "LFG_LIST_COMMENT_FORMAT", "LFG_LIST_SEARCH_FAILED", "LFG_LIST_HIT_MAX_APPLICATIONS", "CANNOT_DO_THIS_IN_LFG_PARTY", "CANNOT_DO_THIS_IN_PVE_QUEUE", "LFG_LIST_TOOLTIP_ILVL", "LFG_LIST_TOOLTIP_HONOR_LEVEL", "LFG_LIST_TOOLTIP_VOICE_CHAT", "LFG_LIST_TOOLTIP_LEADER", "LFG_LIST_TOOLTIP_AGE", "LFG_LIST_TOOLTIP_MEMBERS_SIMPLE", "LFG_LIST_TOOLTIP_MEMBERS", "LFG_LIST_SPAM", "LFG_LIST_BAD_NAME", "LFG_LIST_BAD_DESCRIPTION", "LFG_LIST_BAD_LEADER_NAME", "LFG_LIST_TOOLTIP_CLASS_ROLE", "LFG_LIST_APP_DECLINED_MESSAGE", "LFG_LIST_APP_DECLINED_FULL_MESSAGE", "LFG_LIST_APP_DECLINED_DELISTED_MESSAGE", "LFG_LIST_APP_TIMED_OUT_MESSAGE", "LE_FRAME_TUTORIAL_LFG_LIST", "ToggleQuickJoinPanel", "GameTooltip_AddNewbieTip", "NEWBIE_TOOLTIP_SOCIAL", "MicroButtonTooltipText", "QUICK_JOIN_TOAST_LFGLIST_MESSAGE", "QUICK_JOIN_TOAST_MESSAGE", "SOCIAL_QUEUE_TOOLTIP_HEADER", "SOCIAL_QUEUE_CLICK_TO_JOIN", "SOCIAL_BUTTON", "QUICK_JOIN_TOAST_EXTRA_QUEUES", "FauxScrollFrame_SetOffset", "HideDropDownMenu", "FauxScrollFrame_Update", "OpacityFrame", "ValidateFramePosition", "PanelTemplates_TabResize", "CHANNELS", "DISPLAY_ACTIVE_CHANNEL", "DISPLAY_OPTIONS", "LOCK_CHANNELPULLOUT_LABEL", "CHANNELPULLOUT_OPACITY_LABEL", "OpacityFrameSlider", "ChannelFrame", "CHAT_JOIN", "CHAT_LEAVE", "FauxScrollFrame_GetOffset", "CHANNEL_ROSTER", "UIFrameFlash", "UIFrameFlashStop", "CHANNEL_CHANNEL_NAME", "PASSWORD", "CHANNEL_NEW_CHANNEL", "CHAT_INVALID_NAME_NOTICE", "CHAT_PASSWORD", "PARTY_INVITE", "NO_VOICE_SESSIONS", "CHANNEL_JOIN_CHANNEL", "HIGHLIGHT_FONT_COLOR_CODE", "LE_ACTIONBAR_STATE_MAIN", "MultiActionBar_Update", "UnitFrameManaBar_Update", "ExtraActionBar_Update", "LE_ACTIONBAR_STATE_OVERRIDE", "IconIntroTracker", "OverrideActionBarPowerBar", "PowerBarColor", "BattlePetTooltipTemplate_SetBattlePet", "BattlePetTooltip", "ButtonFrameTemplate_HideButtonBar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PetFrameHealthBar", "Pet<PERSON>rameMana<PERSON>ar", "MicroButtonPulseStop", "PaperDollFrame", "UpdateUIPanelPositions", "PaperDollFrame_SetLevel", "PaperDollFrame_UpdateSidebarTabs", "PANEL_INSET_BOTTOM_OFFSET", "HEALTH", "MANA", "XP", "Character<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PANEL_DEFAULT_WIDTH", "PAPERDOLL_SIDEBARS", "CharacterFrameInsetRight", "PANEL_INSET_RIGHT_OFFSET", "CharacterFramePortrait", "CharacterFrameTitleText", "CharacterStatsPane", "CharacterFrameTab3", "CLASS_ICON_TCOORDS", "ChatFrame1", "InspectUnit", "ShowMacroFrame", "RaidFrame", "ToggleLFDParentFrame", "FCF_ResetAllWindows", "ReloadUI", "ToggleAchievementFrame", "UIParentLoadAddOn", "FrameStackTooltip_Toggle", "ToggleLootHistoryFrame", "APIDocumentation_LoadUI", "UIFrameFadeRemoveFrame", "MOVE_TO_NEW_WINDOW", "FCF_CopyChatSettings", "WhoFrameEditBox", "RaidFinderFrame", "APIDocumentation", "SELECTED_DOCK_FRAME", "HELP_TEXT_SIMPLE", "SELECTED_CHAT_FRAME", "SAY_MESSAGE", "SLASH_SAY1", "PARTY_MESSAGE", "SLASH_PARTY1", "RAID_MESSAGE", "SLASH_RAID1", "INSTANCE_CHAT_MESSAGE", "SLASH_INSTANCE_CHAT1", "GUILD_MESSAGE", "SLASH_GUILD1", "YELL_MESSAGE", "SLASH_YELL1", "WHISPER_MESSAGE", "SLASH_SMART_WHISPER1", "EMOTE_MESSAGE", "SLASH_EMOTE1", "REPLY_MESSAGE", "SLASH_REPLY1", "LANGUAGE", "VOICEMACRO_LABEL", "MACRO", "SLASH_MACRO1", "ICON_TAG_RAID_TARGET_STAR1", "ICON_TAG_RAID_TARGET_STAR2", "ICON_TAG_RAID_TARGET_STAR3", "ICON_TAG_RAID_TARGET_CIRCLE1", "ICON_TAG_RAID_TARGET_CIRCLE2", "ICON_TAG_RAID_TARGET_CIRCLE3", "ICON_TAG_RAID_TARGET_DIAMOND1", "ICON_TAG_RAID_TARGET_DIAMOND2", "ICON_TAG_RAID_TARGET_DIAMOND3", "ICON_TAG_RAID_TARGET_TRIANGLE1", "ICON_TAG_RAID_TARGET_TRIANGLE2", "ICON_TAG_RAID_TARGET_TRIANGLE3", "ICON_TAG_RAID_TARGET_MOON1", "ICON_TAG_RAID_TARGET_MOON2", "ICON_TAG_RAID_TARGET_MOON3", "ICON_TAG_RAID_TARGET_SQUARE1", "ICON_TAG_RAID_TARGET_SQUARE2", "ICON_TAG_RAID_TARGET_SQUARE3", "ICON_TAG_RAID_TARGET_CROSS1", "ICON_TAG_RAID_TARGET_CROSS2", "ICON_TAG_RAID_TARGET_CROSS3", "ICON_TAG_RAID_TARGET_SKULL1", "ICON_TAG_RAID_TARGET_SKULL2", "ICON_TAG_RAID_TARGET_SKULL3", "RAID_TARGET_1", "RAID_TARGET_2", "RAID_TARGET_3", "RAID_TARGET_4", "RAID_TARGET_5", "RAID_TARGET_6", "RAID_TARGET_7", "RAID_TARGET_8", "GROUP1_CHAT_TAG1", "GROUP1_CHAT_TAG2", "GROUP2_CHAT_TAG1", "GROUP2_CHAT_TAG2", "GROUP3_CHAT_TAG1", "GROUP3_CHAT_TAG2", "GROUP4_CHAT_TAG1", "GROUP4_CHAT_TAG2", "GROUP5_CHAT_TAG1", "GROUP5_CHAT_TAG2", "GROUP6_CHAT_TAG1", "GROUP6_CHAT_TAG2", "GROUP7_CHAT_TAG1", "GROUP7_CHAT_TAG2", "GROUP8_CHAT_TAG1", "GROUP8_CHAT_TAG2", "CHAT_JOIN_HELP", "ShowWhoPanel", "ToggleFriendsPanel", "ToggleIgnorePanel", "ToggleRaidFrame", "StopwatchFrame", "Calendar_Toggle", "DisplayTableInspectorWindow", "DevTools_DumpCommand", "ToggleGuildFinder", "SocialFrame_LoadUI", "FCF_RemoveAllMessagesFromChanSender", "GameTime_GetGameTime", "FCFClickAnywhereButton_UpdateState", "FCF_OpenNewWindow", "ChatHistory_GetAccessID", "HUNTER_DISMISS_PET", "CHATLOGDISABLED", "CHATLOGENABLED", "COMBATLOGDISABLED", "COMBATLOGENABLED", "ERR_NAME_TOO_LONG2", "RaidInfoFrame", "GameLimitedMode_IsActive", "ERROR_SLASH_COMMENTATOROVERRIDE", "ERROR_SLASH_COMMENTATOROVERRIDE_EXAMPLE", "REALM_SEPARATORS", "ERROR_SLASH_COMMENTATOR_NAMETEAM", "ERROR_SLASH_COMMENTATOR_NAMETEAM_EXAMPLE", "CONTEXT_ERROR_SLASH_COMMENTATOR_NAMETEAM", "ERROR_SLASH_COMMENTATOR_ASSIGNPLAYER", "ERROR_SLASH_COMMENTATOR_ASSIGNPLAYER_EXAMPLE", "CHAT_SERVER_DISCONNECTED_MESSAGE", "CHAT_SERVER_RECONNECTED_MESSAGE", "BN_CHAT_CONNECTED", "BN_CHAT_DISCONNECTED", "ChatFrameMenuButton", "TIME_PLAYED_TOTAL", "TIME_PLAYED_LEVEL", "MacroFrameText", "CHAT_WHISPER_SEND", "CHAT_BN_WHISPER_SEND", "CHAT_EMOTE_SEND", "GetUnitName", "WhoFrame_GetDefaultWhoCommand", "Stopwatch_StartCountdown", "Stopwatch_Toggle", "SLASH_COMMENTATOROVERRIDE_SUCCESS", "SLASH_COMMENTATOR_ASSIGNPLAYER_SUCCESS", "FCF_GetChatWindowInfo", "WORLD_QUESTS_AVAILABLE_QUEST_ID", "LE_PARTY_CATEGORY_INSTANCE", "FCF_GetButtonSide", "SLASH_WHISPER1", "FCFDock_GetSelectedWindow", "FCF_GetNumActiveChatFrames", "ERROR_SLASH_LOOT_SETTHRESHOLD", "TITLE_DOESNT_EXIST", "FrameStackTooltip", "FloatingChatFrame_OnMouseScroll", "ZONE_UNDER_ATTACK", "NEW_TITLE_EARNED", "OLD_TITLE_LOST", "GUILD_MOTD_TEMPLATE", "FCFManager_ShouldSuppressMessage", "CHAT_RESTRICTED_TRIAL", "TIME_DAYHOURMINUTESECOND", "GENERAL_CHAT_DOCK", "CHAT_CHANNEL_SEND", "ALL", "Stopwatch_Play", "Stopwatch_Pause", "Stopwatch_Clear", "SetTitleByName", "SLASH_COMMENTATOR_NAMETEAM_SUCCESS", "CHAT_TRIAL_RESTRICTED_NOTICE_TRIAL", "PaperDoll_IsEquippedSlot", "ERROR_SLASH_EQUIP_TO_SLOT", "ERROR_SLASH_CHANGEACTIONBAR", "ERR_CANNOT_IGNORE_BN_FRIEND", "ITEM_QUALITY2_DESC", "ITEM_QUALITY3_DESC", "ITEM_QUALITY4_DESC", "ITEM_QUALITY5_DESC", "ITEM_QUALITY6_DESC", "NO_RAID_INSTANCES_SAVED", "CHAT_IGNORED", "CHAT_FILTERED", "CHAT_MSG_BLOCK_CHAT_CHANNEL_INVITE", "BN_INLINE_TOAST_BROADCAST_INFORM", "GetAchievementInfoFromHyperlink", "GMChatFrame_IsGM", "BetterDate", "SLASH_CAST1", "ERROR_SLASH_SWAPACTIONBAR", "BN_INLINE_TOAST_FRIEND_PENDING", "BN_INLINE_TOAST_BROADCAST", "BN_UNABLE_TO_RESOLVE_NAME", "FCF_StartAlertFlash", "SLASH_USE1", "SLASH_EQUIP1", "ERR_CHAT_PLAYER_NOT_FOUND_S", "ERR_FRIEND_ONLINE_SS", "ERR_FRIEND_OFFLINE_S", "FCFManager_ShouldSuppressMessageFlash", "GROUP_NUMBER", "AchievementFrame_DisplayComparison", "StoreFrame_SetShown", "GarrisonBuildingUI_ToggleFrame", "GarrisonMissionFrame_ToggleFrame", "OrderHallTalentFrame_ToggleFrame", "DeathRecapFrame_OpenRecap", "FCF_DockUpdate", "CollectionsJournal", "TicketStatusFrame", "GMChatStatusFrame", "MATERIAL_TEXT_COLOR_TABLE", "MATERIAL_TITLETEXT_COLOR_TABLE", "MAX_PARTY_BUFFS", "MAX_PARTY_DEBUFFS", "TIMEMANAGER_AM", "LE_SCENARIO_TYPE_BOOST_TUTORIAL", "MacroFrame_Show", "AchievementFrame_ToggleAchievementFrame", "TimeManager_Toggle", "EncounterJournal", "StoreFrame_IsShown", "InspectFrame_Show", "StoreFrame_CheckForFree", "ItemSocketingFrame_Update", "HandleLuaWarning", "GarrisonShipyardFrame", "GarrisonMonuntmentFrame_OnEvent", "HideMultiCastActionBar", "ShowMultiCastActionBar", "DurabilityFrame", "OrderHallTalentFrame", "MultiCastFlyoutFrame_Hide", "TIMEMANAGER_PM", "LE_LFG_CATEGORY_BATTLEFIELD", "NewPlayerExperience", "LFDParentFrame", "HelpFrame", "GarrisonBuildingFrame", "DeathRecapFrame", "GhostFrame", "DEATH_CORPSE_SKINNED", "LFGInvitePopup", "PetBattleQueueReadyFrame", "GossipFrame", "ObliterumForgeFrame", "ItemSocketingFrame", "ArtifactFrame", "BENCHMARK_TAXI_MODE_ON", "BENCHMARK_TAXI_MODE_OFF", "ArcheologyDigsiteProgressBar", "GarrisonMonumentFrame", "GarrisonRecruiterFrame", "ChallengesKeystoneFrame", "AlliedRacesFrame", "OrderHallCommandBar", "HasMultiCastActionBar", "IsNormalActionBarState", "VehicleSeatIndicator", "Boss1TargetFrame", "MinimapCluster", "ObjectiveTrackerFrame", "CONTAINER_OFFSET_Y", "RED_FONT_COLOR_CODE", "ModelPreviewFrame", "StoreFrame_EscapePressed", "WowTokenRedemptionFrame_EscapePressed", "GameMenuFrame", "TimeManagerFrame", "TimeManagerFrameCloseButton", "MultiCastFlyoutFrame", "CloseCalendarMenus", "GarrisonMissionFrame_ClearMouse", "GarrisonShipyardFrame_ClearMouse", "LootFrame", "StaticPopup_FindVisible", "LE_INVITE_CONFIRMATION_REQUEST", "LE_INVITE_CONFIRMATION_SUGGEST", "QuestDifficultyColors", "QuestDifficultyHighlightColors", "LE_LFG_CATEGORY_RF", "LE_LFG_CATEGORY_LFR", "CAPPED_LEVEL_TRIAL", "CAPPED_MONEY_TRIAL", "GMChatFrame_OnEvent", "LFGInvitePopup_Update", "StaticPopup_Resize", "BonusRollFrame_StartBonusRoll", "BonusRollFrame_CloseBonusRoll", "ClassTrainerFrame_Show", "ClassTrainerFrame_Hide", "BarberShopFrame", "GuildBankFrame", "ArchaeologyFrame_Show", "ArchaeologyFrame_Hide", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EJSuggestFrame_OpenFrame", "BlackMarketFrame_Show", "BlackMarketFrame_Hide", "ItemUpgradeFrame_Show", "ItemUpgradeFrame_Hide", "CollectionsJournal_SetTab", "ExtraActionBarFrame", "MultiBarBottomLeft", "CONTAINER_OFFSET_X", "IsFrameLockActive", "MINUTES_SECONDS", "HOURS_MINUTES_SECONDS", "CONFIRM_LEAVE_INSTANCE_PARTY", "GMChatFrame", "PanelTemplates_GetSelectedTab", "GMChatFrameEditBox", "INVITATION", "MAX_QUESTS", "INTERFACE_ACTION_BLOCKED", "LE_SPELL_CONFIRMATION_PROMPT_TYPE_STATIC_TEXT", "LE_SPELL_CONFIRMATION_PROMPT_TYPE_SIMPLE_WARNING", "LE_SPELL_CONFIRMATION_PROMPT_TYPE_BONUS_ROLL", "LOOT_NO_DROP_DISENCHANT", "LE_SUMMON_REASON_SCENARIO", "HeirloomsJournal", "GarrisonCapacitiveDisplayFrame", "WOW_TOKEN_ITEM_ID", "TalkingHeadFrame", "ChallengeModeCompleteBanner", "TaxiFrame", "FlightMapFrame", "PlayerPowerBarAlt", "LASTONLINE_YEARS", "INVITE_CONFIRMATION_SUGGEST", "INVITE_CONFIRMATION_QUEUE_WARNING", "CONFIRM_LEAVE_BATTLEFIELD", "ERR_LOOT_SPEC_CHANGED_S", "INVITATION_XREALM", "ChatFrame2", "TutorialFrameAlertButton", "MINIMAP_BOTTOM_EDGE_EXTENT", "DUNGEON_NAME_WITH_DIFFICULTY", "LE_LFG_CATEGORY_WORLDPVP", "ADDON_LOAD_FAILED", "GM_CHAT_LAST_SESSION", "RAID_INSTANCE_WELCOME", "ERR_AUCTION_SOLD_S", "MainMenuBarVehicleLeaveButton", "LASTONLINE_MONTHS", "PvPObjectiveBannerFrame", "GarrisonMissionListTab_SetTab", "ACCEPTING_INVITE_WILL_REMOVE_QUEUE", "ArchaeologyFrame", "LASTONLINE_MINS", "INSTANCE_UNAVAILABLE_OTHER_NO_VALID_ROLES", "GetGMLink", "RAID_INSTANCE_WELCOME_EXTENDED", "MultiCastActionBarFrame", "LASTONLINE_DAYS", "INVITE_CONFIRMATION_REQUEST_QUICKJOIN", "INVITE_CONFIRMATION_REQUEST", "GarrisonLandingPageTab_SetTab", "GarrisonLandingPageReport_SetTab", "GarrisonLandingPageTab1", "RAID_INSTANCE_WELCOME_LOCKED", "RAID_INSTANCE_WELCOME_LOCKED_EXTENDED", "LASTONLINE_HOURS", "INVITE_CONFIRMATION_REQUEST_FRIEND_QUICKJOIN", "INVITE_CONFIRMATION_REQUEST_FRIEND", "INVITE_CONFIRMATION_REQUEST_GUILD_QUICKJOIN", "INVITE_CONFIRMATION_REQUEST_GUILD", "GarrisonLandingPageReport", "WorldMapFrame", "NavBar_Initialize", "ButtonFrameTemplate_HidePortrait", "HelpPlate_Hide", "UIDropDownMenu_SetSelectedID", "PVP_REPORT_AFK", "ToggleFrame", "QuestMapFrame_UpdateAll", "QuestMapFrame_Hide", "SetUIPanelAttribute", "ButtonFrameTemplate_ShowPortrait", "RESET_POSITION", "WORLD_MAP_FILTER_TITLE", "SHOW_QUEST_OBJECTIVES_ON_MAP_TEXT", "UIDropDownMenu_AddSeparator", "WORLD_QUEST_REWARD_FILTERS_TITLE", "WORLD_QUEST_REWARD_FILTERS_ORDER_RESOURCES", "WORLD_QUEST_REWARD_FILTERS_ARTIFACT_POWER", "WORLD_QUEST_REWARD_FILTERS_PROFESSION_MATERIALS", "WORLD_QUEST_REWARD_FILTERS_GOLD", "WORLD_QUEST_REWARD_FILTERS_EQUIPMENT", "NavBar_Reset", "MAP_AND_QUEST_LOG", "UIDROPDOWNMENU_MENU_LEVEL", "WORLD", "QuestMapFrame_CloseQuestDetails", "GameTooltip_InsertFrame", "UIDropDownMenu_ClearAll", "UNLOCK_FRAME", "LOCK_FRAME", "QuestMapFrame_GetDetailQuestID", "CanShowEncounterJournal", "ARCHAEOLOGY_SHOW_DIG_SITES", "SHOW_PET_BATTLES_ON_MAP_TEXT", "SHOW_PRIMARY_PROFESSION_ON_MAP_TEXT", "SHOW_SECONDARY_PROFESSION_ON_MAP_TEXT", "AZEROTH", "NavBar_AddButton", "WORLD_MAP_TUTORIAL1", "WORLD_MAP_TUTORIAL4", "HelpPlate_Show", "NEXT_BATTLE", "MAP_UNDER_INVASION", "WORLD_QUEST_QUALITY_COLORS", "PERCENTAGE_STRING", "QUEST_WATCH_QUEST_READY", "LE_FRAME_TUTORIAL_WORLD_MAP_FRAME", "QuestMapFrame_Close", "HelpPlate_ShowTutorialPrompt", "QuestMapFrame_CheckTutorials", "QuestMapFrame", "EncounterJournal_ListInstances", "EncounterJournal_DisplayInstance", "FLOOR_NUMBER", "PVP_REPORT_AFK_ALL", "QuestMapFrame_Show", "WORLD_MAP_TUTORIAL2", "WORLD_MAP_TUTORIAL3", "MAP_BAR_TOOLTIP_TITLE", "WORLD_QUESTS_TIME_CRITICAL_MINUTES", "BONUS_OBJECTIVE_TIME_LEFT", "QuestMapFrame_Open", "ConvertRGBtoColorString", "HelpPlate_IsShowing", "INVASION_TIME_FORMAT", "GRAVEYARD_SELECTED", "GRAVEYARD_SELECTED_TOOLTIP", "GRAVEYARD_ELIGIBLE", "GRAVEYARD_ELIGIBLE_TOOLTIP", "D_HOURS", "D_DAYS", "WORLD_QUEST_CANT_COMPLETE_BY_SPELL", "GetQuestDifficultyColor", "ToggleEncounterJournal", "WORLD_MAP_WILDBATTLEPET_LEVEL", "GetRelativeDifficultyColor", "LE_EXPANSION_CLASSIC", "LE_EXPANSION_BURNING_CRUSADE", "LE_EXPANSION_WRATH_OF_THE_LICH_KING", "LE_EXPANSION_CATACLYSM", "LE_EXPANSION_MISTS_OF_PANDARIA", "LE_EXPANSION_WARLORDS_OF_DRAENOR", "STRING_SCHOOL_PHYSICAL", "STRING_SCHOOL_HOLY", "STRING_SCHOOL_FIRE", "STRING_SCHOOL_NATURE", "STRING_SCHOOL_FROST", "STRING_SCHOOL_SHADOW", "STRING_SCHOOL_ARCANE", "WEEKDAY_SUNDAY", "WEEKDAY_MONDAY", "WEEKDAY_TUESDAY", "WEEKDAY_WEDNESDAY", "WEEKDAY_THURSDAY", "WEEKDAY_FRIDAY", "WEEKDAY_SATURDAY", "LE_LFG_CATEGORY_LFD", "LOOKING_FOR_DUNGEON", "RAID_FINDER", "LE_LFG_CATEGORY_SCENARIO", "SCENARIOS", "LOOKING_FOR_RAID", "LE_LFG_CATEGORY_FLEXRAID", "FLEX_RAID", "WORLD_PVP", "LFG_CATEGORY_BATTLEFIELD", "CONTRIBUTION_UNDER_CONSTRUCTION", "CONTRIBUTION_POI_TOOLTIP_PERCENTAGE_BUILT", "CONTRIBUTION_ACTIVE", "CONTRIBUTION_UNDER_ATTACK", "CONTRIBUTION_POI_TOOLTIP_REMAINING_TIME", "CONTRIBUTION_DESTROYED", "ChatConfigCombatSettingsFilters", "Blizzard_CombatLog_Update_QuickButtons", "Blizzard_CombatLog_RefreshGlobalLinks", "CombatConfigColorsColorizeUnitNameCheck", "CombatConfigColorsColorizeSpellNamesCheck", "CombatConfigColorsColorizeSpellNamesSchoolColoring", "CombatConfigColorsColorizeSpellNamesColorSwatchNormalTexture", "CombatConfigColorsColorizeDamageNumberCheck", "CombatConfigColorsColorizeDamageNumberSchoolColoring", "CombatConfigColorsColorizeDamageNumberColorSwatchNormalTexture", "CombatConfigColorsColorizeDamageSchoolCheck", "CombatConfigColorsColorizeEntireLineCheck", "CombatConfigColorsHighlightingLine", "CombatConfigColorsHighlightingAbility", "CombatConfigColorsHighlightingDamage", "CombatConfigColorsHighlightingSchool", "CombatConfigColorsExampleString2", "CombatConfigFormattingShowTimeStamp", "CombatConfigFormattingShowBraces", "CombatConfigFormattingUnitNames", "CombatConfigFormattingSpellNames", "CombatConfigFormattingItemNames", "CombatConfigFormattingFullText", "CombatConfigFormattingExampleString2", "CombatConfigSettingsShowQuickButton", "CombatConfigSettingsSolo", "CombatConfigSettingsParty", "CombatConfigSettingsRaid", "ChatConfigBackgroundFrame", "ChatConfigCategoryFrame", "CombatConfigMessageSourcesDoneBy", "CombatConfigMessageSourcesDoneTo", "CombatConfigMessageTypesLeft", "CombatConfigMessageTypesRight", "CombatConfigMessageTypesMisc", "CombatConfigColorsUnitColors", "CombatConfigSettingsNameEditBox", "ChatConfigChatSettingsLeft", "ChatConfigOtherSettingsCombat", "ChatConfigOtherSettingsPVP", "ChatConfigOtherSettingsSystem", "ChatConfigOtherSettingsCreature", "ChatConfigFrame", "ChatConfigChannelSettingsLeft", "GUILD_CHAT", "OFFICER_CHAT", "SAY", "EMOTE", "YELL", "SKILLUPS", "ITEM_LOOT", "CURRENCY", "MONEY_LOOT", "SYSTEM_MESSAGES", "FILTER_BY_ME_COMBATLOG_TOOLTIP", "COMBATLOG_FILTER_STRING_MY_PET", "FILTER_BY_PET_COMBATLOG_TOOLTIP", "COMBATLOG_FILTER_STRING_FRIENDLY_UNITS", "FILTER_BY_FRIENDS_COMBATLOG_TOOLTIP", "COMBATLOG_FILTER_STRING_HOSTILE_PLAYERS", "FILTER_BY_HOSTILE_PLAYERS_COMBATLOG_TOOLTIP", "COMBATLOG_FILTER_STRING_HOSTILE_UNITS", "FILTER_BY_ENEMIES_COMBATLOG_TOOLTIP", "COMBATLOG_FILTER_STRING_NEUTRAL_UNITS", "FILTER_BY_NEUTRAL_COMBATLOG_TOOLTIP", "COMBATLOG_FILTER_STRING_UNKNOWN_UNITS", "FILTER_BY_UNKNOWN_COMBATLOG_TOOLTIP", "FILTER_TO_ME_COMBATLOG_TOOLTIP", "FILTER_TO_PET_COMBATLOG_TOOLTIP", "FILTER_TO_FRIENDS_COMBATLOG_TOOLTIP", "FILTER_TO_HOSTILE_PLAYERS_COMBATLOG_TOOLTIP", "FILTER_TO_HOSTILE_COMBATLOG_TOOLTIP", "FILTER_TO_NEUTRAL_COMBATLOG_TOOLTIP", "FILTER_TO_UNKNOWN_COMBATLOG_TOOLTIP", "MELEE", "MELEE_COMBATLOG_TOOLTIP", "RANGED", "RANGED_COMBATLOG_TOOLTIP", "AURAS", "AURAS_COMBATLOG_TOOLTIP", "PERIODIC", "SPELL_PERIODIC_COMBATLOG_TOOLTIP", "SPELLS", "SPELLS_COMBATLOG_TOOLTIP", "SPELL_CASTING", "SPELL_CASTING_COMBATLOG_TOOLTIP", "DAMAGE_SHIELD", "DAMAGE_SHIELD_COMBATLOG_TOOLTIP", "ENVIRONMENTAL_DAMAGE", "ENVIRONMENTAL_DAMAGE_COMBATLOG_TOOLTIP", "KILLS", "KILLS_COMBATLOG_TOOLTIP", "DEATHS", "DEATHS_COMBATLOG_TOOLTIP", "COMBATLOG_FILTER_STRING_ME", "CombatLog_OnEvent", "MESSAGE_SOURCES", "MESSAGE_TYPES", "COLORS", "FORMATTING", "SETTINGS", "Blizzard_CombatLog_QuickButton_OnClick", "PLAYER_MESSAGES", "COMBAT", "PVP", "OTHER", "CREATURE_MESSAGES", "DONE_BY", "DONE_TO", "UNIT_COLORS", "ChatConfigCategoryFrameButton2", "CombatConfigColorsColorizeEntireLineBySource", "CombatConfigColorsColorizeEntireLineByTarget", "EXAMPLE_TARGET_MONSTER", "EXAMPLE_SPELL_FROSTBOLT", "EXAMPLE_SPELL_FIREBALL", "ColorPickerFrame", "CombatLogDefaultButton", "ChatConfigCategoryFrameButton3", "ChatConfigCombatSettingsFiltersCopyFilterButton", "ChatConfigCombatSettingsFiltersDeleteButton", "ChatConfigCombatSettingsFiltersAddFilterButton", "Blizzard_CombatLog_Filter_Defaults", "COMBATLOG_FILTER_VERSION", "DEFAULT_COMBATLOG_FILTER_TEMPLATE", "DEFAULT_COMBATLOG_FILTER_NAME", "DAMAGE", "SWING_DAMAGE_COMBATLOG_TOOLTIP", "MISSES", "SWING_MISSED_COMBATLOG_TOOLTIP", "RANGE_DAMAGE_COMBATLOG_TOOLTIP", "RANGE_MISSED_COMBATLOG_TOOLTIP", "BENEFICIAL", "BENEFICIAL_AURA_COMBATLOG_TOOLTIP", "HOSTILE", "HARMFUL_AURA_COMBATLOG_TOOLTIP", "DISPELS", "DISPEL_AURA_COMBATLOG_TOOLTIP", "ENCHANTS", "ENCHANT_AURA_COMBATLOG_TOOLTIP", "SPELL_PERIODIC_DAMAGE_COMBATLOG_TOOLTIP", "SPELL_PERIODIC_MISSED_COMBATLOG_TOOLTIP", "HEALS", "SPELL_PERIODIC_HEAL_COMBATLOG_TOOLTIP", "SPELL_PERIODIC_OTHER_COMBATLOG_TOOLTIP", "SPELL_DAMAGE_COMBATLOG_TOOLTIP", "SPELL_MISSED_COMBATLOG_TOOLTIP", "SPELL_HEAL_COMBATLOG_TOOLTIP", "POWER_GAINS", "POWER_GAINS_COMBATLOG_TOOLTIP", "DRAINS", "SPELL_DRAIN_COMBATLOG_TOOLTIP", "INTERRUPTS", "SPELL_INTERRUPT_COMBATLOG_TOOLTIP", "SPECIAL", "SPELL_INSTAKILL_COMBATLOG_TOOLTIP", "EXTRA_ATTACKS", "SPELL_EXTRA_ATTACKS_COMBATLOG_TOOLTIP", "SUMMONS", "SPELL_SUMMON_COMBATLOG_TOOLTIP", "RESURRECT", "SPELL_RESURRECT_COMBATLOG_TOOLTIP", "BUILDING_DAMAGE", "BUILDING_DAMAGE_COMBATLOG_TOOLTIP", "BUILDING_HEAL", "BUILDING_HEAL_COMBATLOG_TOOLTIP", "START", "SPELL_CAST_START_COMBATLOG_TOOLTIP", "SUCCESS", "SPELL_CAST_SUCCESS_COMBATLOG_TOOLTIP", "FAILURES", "SPELL_CAST_FAILED_COMBATLOG_TOOLTIP", "COMBATLOG_FILTER_STRING_CUSTOM_UNIT", "PLAYER_OFFLINE", "PARTY_IN_PUBLIC_GROUP_MESSAGE", "PARTY_PHASED_MESSAGE", "BUFF_STACKS_OVERFLOW", "LOST_HEALTH", "EquipmentFlyoutFrame", "EQUIPMENT_MANAGER_IGNORE_SLOT", "EQUIPMENT_MANAGER_UNIGNORE_SLOT", "EQUIPMENT_MANAGER_PLACE_IN_BAGS", "NEWBIE_TOOLTIP_EQUIPMENT_MANAGER_IGNORE_SLOT", "NEWBIE_TOOLTIP_EQUIPMENT_MANAGER_UNIGNORE_SLOT", "NEWBIE_TOOLTIP_EQUIPMENT_MANAGER_PLACE_IN_BAGS", "ERR_CLIENT_LOCKED_OUT", "DISPLAY", "FONT_SIZE", "BACKGROUND", "GENERAL", "COMBAT_LOG", "CHAT_WINDOWS_COUNT", "UNDOCK_WINDOW", "UNLOCK_WINDOW", "LOCK_WINDOW", "MAKE_INTERACTABLE", "MAKE_UNINTERACTABLE", "RENAME_CHAT_WINDOW", "NEW_CHAT_WINDOW", "FILTERS", "CHAT_CONFIGURATION", "PET_BATTLE_COMBAT_LOG", "QuickJoinToastButton", "COMBATLOG", "CLOSE_CHAT_WINDOW", "CLOSE_CHAT_WHISPER_WINDOW", "CombatLogQuickButtonFrame_Custom", "CHAT_NAME_TEMPLATE", "FONT_SIZE_TEMPLATE", "PetJournal_SelectSpecies", "BATTLE_PET_CAGE_TOOLTIP_LEVEL", "FloatingPetBattleAbilityTooltip", "FloatingBattlePetTooltip", "QUEST_REWARDS", "CONTRIBUTION_REWARD_TOOLTIP_TEXT", "SELL_PRICE", "MINIMUM", "MAXIMUM", "BONUS_OBJECTIVE_ARTIFACT_XP_FORMAT", "HONOR", "SelectGossipOption", "GossipFramePortrait", "IGNORED_QUEST_DISPLAY", "TRIVIAL_QUEST_DISPLAY", "NORMAL_QUEST_DISPLAY", "GuildInviteFrameTabardEmblem", "GuildInviteFrameTabardBackground", "GuildInviteFrameTabardBorder", "GuildInviteFrameWarningText", "GUILD_REPUTATION_WARNING", "GUILD_REPUTATION_WARNING_GENERIC", "GuildRegistrarGreetingFrame", "GuildRegistrarPurchaseFrame", "GuildRegistrarFramePortrait", "GuildRegistrarFrameNpcNameText", "GuildRegistrarFrame", "GuildRegistrarFrameEditBox", "BROWSER_EXTERNAL_LINK_DIALOG", "BROWSER_COPY_LINK", "KNOWLEDGE_BASE", "HELPFRAME_ACCOUNTSECURITY_TITLE", "HELPFRAME_STUCK_TITLE", "HELPFRAME_REPORT_BUG_TITLE", "HELPFRAME_REPORT_PLAYER_TITLE", "HELP_TICKET_OPEN", "BUTTON_LAG_LOOT_TOOLTIP", "BUTTON_LAG_LOOT_NEWBIE", "BUTTON_LAG_AUCTIONHOUSE_TOOLTIP", "BUTTON_LAG_AUCTIONHOUSE_NEWBIE", "BUTTON_LAG_MAIL_TOOLTIP", "BUTTON_LAG_MAIL_NEWBIE", "BUTTON_LAG_CHAT_TOOLTIP", "BUTTON_LAG_CHAT_NEWBIE", "BUTTON_LAG_MOVEMENT_TOOLTIP", "BUTTON_LAG_MOVEMENT_NEWBIE", "BUTTON_LAG_SPELL_TOOLTIP", "BUTTON_LAG_SPELL_NEWBIE", "KBASE_TOP_ISSUES", "HELPFRAME_SUBMIT_SUGGESTION_TITLE", "HELPFRAME_ITEM_RESTORATION", "ReportCheatingDialog", "EditBox_ClearFocus", "Help<PERSON><PERSON>er", "SEARCH", "HOME", "KBASE_SEARCH_RESULTS", "BrowserSettingsTooltip", "TicketStatusTitleText", "CHOSEN_FOR_GMSURVEY", "TicketStatusTime", "TicketStatusFrameIcon", "GM_RESPONSE_ALERT", "HELPFRAME_TICKET_CLICK_HELP", "KBASE_ERROR_LOAD_FAILURE", "KBASE_ARTICLE_ID", "TICKET_STATUS", "GM_TICKET_WAIT_TIME", "KBASE_ERROR_NO_RESULTS", "TICKET_STATUS_NMI", "LE_TICKET_STATUS_OPEN", "LE_TICKET_STATUS_NMI", "LE_TICKET_STATUS_RESPONSE", "LE_TICKET_STATUS_SURVEY", "GM_TICKET_ESCALATED", "GM_TICKET_SERVICE_SOON", "GM_TICKET_UNAVAILABLE", "GM_TICKET_HIGH_VOLUME", "NONE", "RANK", "PanelTemplates_UpdateTabs", "CONTROLS_LABEL", "PanelTemplates_Tab_OnClick", "PanelTemplates_ResizeTabsToFit", "FriendsTooltip", "FRIENDS_FRIENDS_CHOICE_EVERYONE", "FRIENDS_FRIENDS_CHOICE_POTENTIAL", "FRIENDS_FRIENDS_CHOICE_MUTUAL", "FriendsFriendsFrame", "TRAVEL_PASS_INVITE", "FriendsTabHeaderTab2", "FriendsTabHeader", "WhoFrameTotals", "WHO_TAG_ZONE", "AddFriendFrame", "AddFriendInfoFrame", "AddFriendEntryFrame", "AddFriendEntryFrameAcceptButton", "ADD_FRIEND", "AddFriendEntryFrameRightTitle", "AddFriendEntryFrameRightDescription", "AddFriendEntryFrameRightIcon", "AddFriendEntryFrameRightFriend", "AddFriendEntryFrameLeftIcon", "FriendsFriendsFrameTitle", "FRIENDS_FRIENDS_HEADER", "FriendsFriendsWaitFrame", "ZONE", "GUILD", "RACE", "ButtonFrameTemplate_ShowButtonBar", "QUICK_JOIN", "DECLINE", "BNET_REPORT", "BLOCK_INVITES", "<PERSON><PERSON><PERSON><PERSON>", "LASTONLINE_SECS", "FriendsFrameBattlenetFrame", "FRIENDS_LIST_AVAILABLE", "FRIENDS_LIST_AWAY", "FRIENDS_LIST_BUSY", "FRIENDS_LIST_ZONE", "ERR_TRAVEL_PASS_NOT_LEADER", "ERR_TRAVEL_PASS_NOT_ALLIED", "ERR_TRAVEL_PASS_NO_INFO", "ERR_TRAVEL_PASS_NOT_WOW", "BattleTagInviteFrame", "FriendsFrameInset", "FriendsFrameIcon", "FriendsFrameSendMessageButton", "FriendsFrameUnsquelchButton", "WhoFrameGroupInviteButton", "WhoFrameAddFriendButton", "LE_FRAME_TUTORIAL_FRIENDS_LIST_QUICK_JOIN", "FRIEND_REQUESTS", "FRIENDS_LIST_STATUS_TOOLTIP", "FriendsTooltipBroadcastIcon", "FriendsTooltipBroadcastText", "FriendsTooltipLastOnline", "FriendsTooltipOtherGameAccounts", "FriendsTooltipGameAccountMany", "AddFriendInfoFrameFactionIcon", "AddFriendEntryFrameLeftTitle", "AddFriendEntryFrameLeftDescription", "AddFriendEntryFrameLeftFriend", "BATTLENET_UNAVAILABLE", "AddFriendEntryFrameOrLabel", "IgnoreListFrame", "BNET_REPORT_ABUSE", "BNET_REPORT_NAME", "FriendsListFrame", "WHO_FRAME_TOTAL_TEMPLATE", "LASTONLINE_MINUTES", "UNKNOWN", "FriendsFrameTitleText", "FRIENDS_LIST", "FriendsFrameIgnorePlayerButton", "IGNORE_LIST", "WHO_LIST", "CHAT_CHANNELS", "RAID", "NUMBER_IN_PARENTHESES", "WHO_FRAME_SHOWN_TEMPLATE", "FRIENDS_LIST_REALM", "FriendsTooltipHeader", "FriendsTooltipGameAccount1Info", "FriendsTooltipGameAccount1Name", "FriendsTooltipNoteIcon", "FriendsTooltipNoteText", "FRIENDS_TOOLTIP_TOO_MANY_CHARACTERS", "REAL_ID", "REALID_BATTLETAG_FRIEND_LABEL", "AddFriendNameEditBoxFill", "ENTER_NAME_OR_BATTLETAG_OR_EMAIL", "REALID_FRIEND_LABEL", "ENTER_NAME_OR_EMAIL", "BATTLETAG", "BATTLETAG_FRIEND_LABEL", "ENTER_NAME_OR_BATTLETAG", "SUGGEST_INVITE", "REQUEST_INVITE", "FRIENDS_LIST_OFFLINE", "CHAT_FLAG_AFK", "CHAT_FLAG_DND", "FRIENDS_LEVEL_TEMPLATE", "FRIENDS_TOOLTIP_WOW_TOON_TEMPLATE", "CANNOT_COOPERATE_LABEL", "BNET_LAST_ONLINE_TIME", "FRIENDS_FRIENDS_REQUESTED_TEXT", "SOCIAL_QUEUE_ALSO_IN_GROUP", "SOCIAL_QUEUE_AND_MORE", "BNET_BROADCAST_SENT_TIME", "FRIENDS_FRIENDS_MUTUAL_TEXT", "ALT_KEY", "OPTION_TOOLTIP_AUTO_LOOT_ALT_KEY", "CTRL_KEY", "OPTION_TOOLTIP_AUTO_LOOT_CTRL_KEY", "SHIFT_KEY", "OPTION_TOOLTIP_AUTO_LOOT_SHIFT_KEY", "NONE_KEY", "OPTION_TOOLTIP_AUTO_LOOT_NONE_KEY", "OPTION_TOOLTIP_AUTO_SELF_CAST_ALT_KEY", "OPTION_TOOLTIP_AUTO_SELF_CAST_CTRL_KEY", "OPTION_TOOLTIP_AUTO_SELF_CAST_SHIFT_KEY", "OPTION_TOOLTIP_AUTO_SELF_CAST_NONE_KEY", "OPTION_TOOLTIP_FOCUS_CAST_ALT_KEY", "OPTION_TOOLTIP_FOCUS_CAST_CTRL_KEY", "OPTION_TOOLTIP_FOCUS_CAST_SHIFT_KEY", "OPTION_TOOLTIP_FOCUS_CAST_NONE_KEY", "COMBAT_LABEL", "DISPLAY_LABEL", "OBJECT_NPC_OUTLINE_DISABLED", "OBJECT_NPC_OUTLINE_MODE_ONE", "OBJECT_NPC_OUTLINE_MODE_TWO", "OBJECT_NPC_OUTLINE_MODE_THREE", "OPTION_TOOLTIP_SELF_HIGHLIGHT", "SELF_HIGHLIGHT_MODE_CIRCLE", "SELF_HIGHLIGHT_MODE_OUTLINE", "SELF_HIGHLIGHT_MODE_CIRCLE_AND_OUTLINE", "OFF", "OPTION_TOOLTIP_CHAT_BUBBLES", "CHAT_BUBBLES_EXCLUDE_PARTY_CHAT", "SOCIAL_LABEL", "IM_STYLE", "OPTION_CHAT_STYLE_IM", "CLASSIC_STYLE", "OPTION_CHAT_STYLE_CLASSIC", "CONVERSATION_MODE_POPOUT", "CONVERSATION_MODE_INLINE", "CONVERSATION_MODE_POPOUT_AND_INLINE", "TIMESTAMP_FORMAT_NONE", "ACTIONBARS_LABEL", "OPTION_TOOLTIP_PICKUP_ACTION_ALT_KEY", "OPTION_TOOLTIP_PICKUP_ACTION_CTRL_KEY", "OPTION_TOOLTIP_PICKUP_ACTION_SHIFT_KEY", "OPTION_TOOLTIP_PICKUP_ACTION_NONE_KEY", "NPC_NAMES_DROPDOWN_TRACKED", "NPC_NAMES_DROPDOWN_TRACKED_TOOLTIP", "NPC_NAMES_DROPDOWN_HOSTILE", "NPC_NAMES_DROPDOWN_HOSTILE_TOOLTIP", "NPC_NAMES_DROPDOWN_INTERACTIVE", "NPC_NAMES_DROPDOWN_INTERACTIVE_TOOLTIP", "NPC_NAMES_DROPDOWN_ALL", "NPC_NAMES_DROPDOWN_ALL_TOOLTIP", "NPC_NAMES_DROPDOWN_NONE", "NPC_NAMES_DROPDOWN_NONE_TOOLTIP", "STATUS_TEXT_VALUE", "OPTION_TOOLTIP_STATUS_TEXT_DISPLAY", "STATUS_TEXT_PERCENT", "STATUS_TEXT_BOTH", "NUM_RAID_PULLOUT_FRAMES", "CAMERA_LABEL", "CAMERA_SMART", "OPTION_TOOLTIP_CAMERA_SMART", "CAMERA_SMARTER", "OPTION_TOOLTIP_CAMERA_SMARTER", "CAMERA_ALWAYS", "OPTION_TOOLTIP_CAMERA_ALWAYS", "CAMERA_NEVER", "OPTION_TOOLTIP_CAMERA_NEVER", "ACCESSIBILITY_LABEL", "COLORBLIND_OPTION_NONE", "COLORBLIND_OPTION_PROTANOPIA", "COLORBLIND_OPTION_DEUTERANOPIA", "COLORBLIND_OPTION_TRITANOPIA", "TIMESTAMP_FORMAT_HHMM", "TIMESTAMP_FORMAT_HHMMSS", "TIMESTAMP_FORMAT_HHMM_AMPM", "TIMESTAMP_FORMAT_HHMMSS_AMPM", "TIMESTAMP_FORMAT_HHMM_24HR", "TIMESTAMP_FORMAT_HHMMSS_24HR", "PetFrame", "OPTION_TOOLTIP_OBJECT_NPC_OUTLINE", "OPTION_TOOLTIP_OBJECT_NPC_OUTLINE_NOT_SUPPORTED", "OPTION_TOOLTIP_OBJECT_NPC_OUTLINE_NOT_ALLOWED", "CombatText_UpdateDisplayedMessages", "OPTION_TOOLTIP_TIMESTAMPS", "LOOT_KEY_TEXT", "AUTO_LOOT_KEY_TEXT", "SOCIAL_TWITTER_STATUS_NOT_CONNECTED", "SOCIAL_TWITTER_DISCONNECT", "SOCIAL_TWITTER_SIGN_IN", "NamePlateDriverFrame", "KEYBIND_NOT_SET_TOOLTIP", "RaidPullout_Update", "OPTION_TOOLTIP_UNIT_NAMEPLATES_SHOW_FRIENDS", "OPTION_TOOLTIP_UNIT_NAMEPLATES_SHOW_ENEMIES", "OPTION_TOOLTIP_CAMERA3", "SOCIAL_TWITTER_CONNECT_SUCCESS_MESSAGE", "SOCIAL_TWITTER_CONNECT_FAIL_MESSAGE", "SOCIAL_TWITTER_STATUS_CONNECTED", "MERCHANT_STOCK", "GMChatStatusFrame_OnClick", "ItemRefTooltip", "StoreFrame_SetTokenCategory", "StoreFrame_OpenGamesCategory", "StoreFrame_SetServicesCategory", "WHO_TAG_EXACT", "ItemTextScrollFrame", "ItemTextCurrentPage", "ItemTextStatusBar", "ItemTextPrevPageButton", "ItemTextNextPageButton", "ItemTextPageText", "ItemTextMaterialTopLeft", "ItemTextMaterialTopRight", "ItemTextMaterialBotLeft", "ItemTextMaterialBotRight", "ItemTextFramePageBg", "ITEM_TEXT_FROM", "ButtonFrameTemplate_HideAttic", "SPECIFIC_DUNGEONS", "LFDQueueFrame", "LFDQueueFrameCooldownFrame", "LFDRoleCheckPopupAcceptButton", "LFDQueueFrameRoleButtonTank", "LFDQueueFrameRoleButtonHealer", "LFDQueueFrameRoleButtonDPS", "YOU_MAY_NOT_QUEUE_FOR_DUNGEON", "LFDQueueFrameBackground", "LFDQueueFrameSpecific", "LFDQueueFrameRandom", "LFDQueueFrameRandomScrollFrameChildFrame", "LFDRoleCheckPopupDescription", "LFDRoleCheckPopupRoleButtonTank", "LFDRoleCheckPopupRoleButtonHealer", "LFDRoleCheckPopupRoleButtonDPS", "LFDRoleCheckPopup", "LFDQueueFrameRoleButtonLeader", "LFDQueueFrameFindGroupButton", "INSTANCE_ROLE_WARNING_TITLE", "MULTIPLE_DUNGEONS", "CONFIRM_YOU_ARE_READY", "LFDReadyCheckPopup", "ERR_ROLE_UNAVAILABLE", "INSTANCE_ROLE_WARNING_TEXT", "LEAVE_QUEUE", "LFDQueueFramePartyBackfillBackfillButton", "LFDRoleCheckPopupDescriptionText", "LFG_LIST_APPLYING_TO", "QUEUED_FOR", "REQUEUE_CONFIRM_YOUR_ROLE", "CONFIRM_YOUR_ROLE", "YOU_MAY_NOT_QUEUE_FOR_THIS", "LFG_DESERTER_YOU", "LFG_RANDOM_COOLDOWN_YOU", "LFG_DESERTER_OTHER", "LFG_RANDOM_COOLDOWN_OTHER", "JOIN_AS_PARTY", "FIND_A_GROUP", "VOTE_BOOT_PLAYER", "VOTE_BOOT_PLAYER_NO_REASON", "LFDQueueFramePartyBackfill", "HEROIC_PREFIX", "LFGDungeonReadyPopup", "LFGDungeonReadyDialogInstanceInfoFrame", "LFGInvitePopupRoleButtonTank", "LFGInvitePopupRoleButtonHealer", "LFGInvitePopupRoleButtonDPS", "RaidFinderQueueFrameRoleButtonTank", "RaidFinderQueueFrameRoleButtonHealer", "RaidFinderQueueFrameRoleButtonDPS", "RaidFinderQueueFrameRoleButtonLeader", "LFG_CALL_TO_ARMS_EXPLANATION", "BOSSES", "PLAYERS_FOUND_OUT_OF_MAX", "LFGInvitePopupText", "RaidFinderQueueFrame", "ScenarioQueueFrame", "INSTANCE_UNAVAILABLE_OTHER_TOO_SOON", "LFGDungeonReadyDialog", "LFG_ROLE_UNAVAILABLE", "INCLUDED_DUNGEONS", "INCLUDED_DUNGEONS_TIMEWALKER_EMPTY", "INCLUDED_SCENARIOS", "INCLUDED_SCENARIOS_EMPTY", "INCLUDED_SCENARIOS_SUBTEXT", "INCLUDED_DUNGEONS_EMPTY", "INCLUDED_DUNGEONS_SUBTEXT", "RaidWarningFrame", "LFG_CALL_TO_ARMS", "ENTER_RAID", "ENTER_DUNGEON", "LFGDungeonReadyStatus", "LFGDungeonReadyDialogRewardsFrame", "LFGDungeonReadyDialogRewardsFrameReward1", "LFGDungeonReadyDialogRewardsFrameLabel", "REWARD_ITEMS_ONLY", "BOSSES_KILLED", "LFD_RANDOM_REWARD_EXPLANATION2", "LFG_TYPE_RANDOM_TIMEWALKER_DUNGEON", "LFD_TIMEWALKER_RANDOM_EXPLANATION", "LFGInvitePopupAcceptButton", "BOSS_DEAD", "BOSS_ALIVE", "YOUR_CLASS_MAY_NOT_PERFORM_ROLE", "YOU_ARE_NOT_SPECIALIZED_IN_ROLE", "LFG_ROLE_CHECK_ROLE_CHOSEN", "LFG_READY_CHECK_PLAYER_IS_READY", "LFG_LEADER_CHANGED_WARNING", "ENTER_SCENARIO", "ACCEPT", "LFGDungeonReadyStatusIndividual", "LFGDungeonReadyStatusRoleless", "LFGDungeonReadyStatusGrouped", "LFGDungeonReadyStatusGroupedTank", "LFGDungeonReadyStatusGroupedHealer", "LFGDungeonReadyStatusGroupedDamager", "RANDOM_DUNGEON_IS_READY", "LFGDungeonReadyDialogRoleIcon", "LFGDungeonReadyDialogYourRoleDescription", "LFGDungeonReadyDialogRoleLabel", "LFGDungeonReadyDialogRoleIconTexture", "LFGDungeonReadyDialogRoleIconLeaderIcon", "LFD_HOLIDAY_REWARD_EXPLANATION2", "LFD_HOLIDAY_REWARD_EXPLANATION1", "RF_REWARD_EXPLANATION2", "RF_REWARD_EXPLANATION1", "LFG_TYPE_RANDOM_DUNGEON", "LFD_RANDOM_EXPLANATION", "ERR_LOOT_GONE", "LFG_BONUS_REPUTATION_FACTION", "ERR_LFG_MEMBERS_REQUIRED", "LFG_OFFER_CONTINUE", "TANK", "SPECIFIC_DUNGEON_IS_READY", "SPECIFIC_INSTANCE_IS_READY", "LFD_REWARD_DESCRIPTION_WEEKLY", "LFD_REWARD_DESCRIPTION_DAILY", "LFG_TYPE_RANDOM_HEROIC_SCENARIO", "SCENARIO_RANDOM_HEROIC_EXPLANATION", "LFG_TYPE_RANDOM_SCENARIO", "SCENARIO_RANDOM_EXPLANATION", "LFD_LEVEL_FORMAT_SINGLE", "LFD_LEVEL_FORMAT_RANGE", "HEALER", "DAMAGER", "RANDOM_SCENARIO", "PET_BATTLE_RESULT_LOSE", "BossBanner", "ZoneTextFrame", "SubZoneTextFrame", "LEVEL_UP_TALENT_MAIN", "LEVEL_UP_TALENT_SUB", "LEVEL_UP_TALENTPOINT_LINK", "PET_LEVEL_UP_TALENT_MAIN", "PET_LEVEL_UP_TALENT_SUB", "PET_LEVEL_UP_TALENTPOINT_LINK", "SPECIALIZATION", "LEVEL_UP_FEATURE", "TALENT_POINTS", "BATTLEFIELDS", "PVP_TALENTS", "GARRISON_ABILITY_BARRACKS_UNLOCKED", "PET_BATTLE_RESULT_WIN", "PetBattleFrame_GetAbilityAtLevel", "BATTLE_PET_CAPTURED", "LEVEL_UP_FEATURE2", "LEVEL_UP_SPECIALIZATION_LINK", "LEVEL_UP_TALENTS_LINK", "LEVEL_UP_BG_LINK", "LEVEL_UP_LFD_LINK", "LEVEL_UP_HONOR_LINK", "LEVEL_UP_ABILITY", "LEVEL_UP_WORLD_QUESTS", "LEVEL_UP_WORLD_QUESTS_INSTRUCTIONS", "LEVEL_UP_YOU_REACHED", "LEVEL_GAINED", "PET_LEVEL_UP_REACHED", "LEVEL_UP_ABILITY2", "BATTLE_PET_LOOT_RECEIVED", "LEVEL_UP_DUNGEON", "LEVEL_UP_RAID", "LEVEL_UP_WORLD_QUEST_LINK", "PET_BATTLE_TRAP_UPGRADE", "CHALLENGE_MODE_POWER_LEVEL", "CHALLENGE_MODE_NEW_BEST", "LEVEL_UP", "BOSS_BANNER_LOOT_SET", "LEVEL_UP_DUNGEON2", "LEVEL_UP_RAID2", "PLAYER_LEVEL_UP", "PET_LEVEL_UP", "SPELL_BUCKET_ALL_ABILITIES_UNLOCKED_MESSAGE", "SPELL_BUCKET_LEVEL_UP", "LEVEL_UP_HEROIC2", "BOSS_YOU_DEFEATED", "SCENARIO_STAGE_FINAL", "SCENARIO_STAGE", "RaidParentFrame", "RaidParentFrameInset", "ALL_BOSSES_ALIVE", "LFG_TOOLTIP_ROLES", "UNLIST_MY_GROUP", "LFDQueueFrameNoLFDWhileLFRLeaveQueueButton", "UNLIST_ME", "LIST_MY_GROUP", "LIST_ME", "LFM_NUM_RAID_MEMBER_TEMPLATE", "ITEM_LEVEL", "BOSS_ALIVE_INELIGIBLE", "IGNORED", "FRIEND", "IMPORTANT_PEOPLE_IN_GROUP", "MASTER_LOOTER", "ASSIGN_LOOT", "REQUEST_ROLL", "BonusRollFrame", "EncounterJournal_SetClassAndSpecFilter", "EncounterJournal_OpenJournal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BONUS_ROLL_COST", "BONUS_ROLL_CURRENT_COUNT", "GroupLootContainer", "BONUS_ROLL_TOOLTIP_TITLE", "BONUS_ROLL_TOOLTIP_TEXT", "LE_FRAME_TUTORIAL_BONUS_ROLL_ENCOUNTER_JOURNAL_LINK", "DropDownList1", "BONUS_ROLL_TOOLTIP_ENCOUNTER_JOURNAL_LINK", "BonusRollLootWonFrame", "BonusRollMoneyWonFrame", "ERR_LOOT_HISTORY_EXPIRED", "PASS", "LOOT_HISTORY_ALL_PASSED", "MASTER_LOOTER_GIVE_TO", "NEED", "GREED", "ROLL_DISENCHANT", "LOSS_OF_CONTROL_DISPLAY_CYCLONE", "LOSS_OF_CONTROL_DISPLAY_INTERRUPT_SCHOOL", "InboxFrame", "OpenMailFrame", "SendMailFrame", "SendMailMoney", "SendMailBodyEditBox", "SendMailNameEditBox", "SendMailMoneyFrame", "MailFrameTab2", "OpenMailSubject", "OpenMailBodyText", "OpenMailScrollFrame", "OpenMailScrollChildFrame", "OpenMailHorizontalBarLeft", "OpenStationeryBackgroundLeft", "OpenStationeryBackgroundRight", "MAIL_REPLY_PREFIX", "SendMailSubjectEditBox", "OpenMailReportSpamButton", "SendMailMailButton", "SendMailScrollFrame", "SendMailScrollChildFrame", "SendMailHorizontalBarLeft2", "SendStationeryBackgroundLeft", "SendStationeryBackgroundRight", "OPEN_ALL_MAIL_BUTTON_OPENING", "OPEN_ALL_MAIL_BUTTON", "OpenMailSender", "SendMailFrameLockSendMail", "MailFrameInset", "InboxPrevPageButton", "InboxNextPageButton", "InboxTooMuchMail", "OpenMailFrameInset", "ENCLOSED_MONEY", "COD_AMOUNT", "OpenMailLetterButton", "OpenMailMoneyButton", "OpenMailReplyButton", "OpenMailInvoiceFrame", "OpenMailAttachmentButton1", "OpenMailAttachmentText", "TAKE_ATTACHMENTS", "NO_ATTACHMENTS", "OpenMailDeleteButton", "DELETE", "MAIL_RETURN", "SendMailCODButton", "SendMailCODButtonText", "SendMailAttachment1", "SendMailSendMoneyButton", "SendMailMoneyText", "AMOUNT_TO_SEND", "ATTACHMENT_TEXT", "TIME_UNTIL_DELETED", "TIME_UNTIL_RETURNED", "OpenMailInvoicePurchaser", "SendMailErrorText", "SendMailError<PERSON>oin", "MAIL_MULTIPLE_ITEMS", "OpenMailInvoiceItemLabel", "OpenMailInvoiceAmountReceived", "AMOUNT_PAID_COLON", "OpenMailArithmeticLine", "OpenMailInvoiceSalePrice", "OpenMailInvoiceDeposit", "OpenMailInvoiceHouseCut", "OpenMailDepositMoneyFrame", "OpenMailHouseCutMoneyFrame", "OpenMailSalePriceMoneyFrame", "OpenMailInvoiceNotYetSent", "OpenMailInvoiceMoneyDelay", "AMOUNT_RECEIVED_COLON", "AUCTION_INVOICE_PENDING_FUNDS_COLON", "AUCTION_INVOICE_FUNDS_DELAY", "ITEM_PURCHASED_COLON", "SOLD_BY_COLON", "ITEM_SOLD_COLON", "PURCHASED_BY_COLON", "DAYS_ABBR", "AUCTION_MAIL_ITEM_STACK", "BUYOUT", "HIGH_BIDDER", "XPBAR_LABEL", "NEWBIE_TOOLTIP_XPBAR", "ARTIFACT_POWER_TOOLTIP_TITLE", "ARTIFACT_POWER_TOOLTIP_BODY", "TAXI_CANCEL", "TAXI_CANCEL_DESCRIPTION", "LEAVE_VEHICLE", "EXHAUST_TOOLTIP1", "MAINMENUBAR_LATENCY_LABEL", "NEWBIE_TOOLTIP_LATENCY", "MAINMENUBAR_FPS_LABEL", "NEWBIE_TOOLTIP_FRAMERATE", "MAINMENUBAR_BANDWIDTH_LABEL", "NEWBIE_TOOLTIP_BANDWIDTH", "MAINMENUBAR_DOWNLOAD_PERCENT_LABEL", "NEWBIE_TOOLTIP_DOWNLOAD_PERCENT", "ARTIFACT_POWER_BAR", "PossessButton2", "MAINMENUBAR_PROTOCOLS_LABEL", "NEWBIE_TOOLTIP_PROTOCOLS", "NEWBIE_TOOLTIP_MEMORY", "TRIAL_CAP_BANKED_XP_TOOLTIP", "CINEMATICS", "CINEMATIC_DOWNLOAD_FORMAT", "TOTAL_MEM_MB_ABBR", "TOTAL_MEM_KB_ABBR", "ADDON_MEM_MB_ABBR", "ADDON_MEM_KB_ABBR", "StoreMicroButton", "CAP_REACHED_TRIAL", "TRIAL_CAP_BANKED_LEVELS_TOOLTIP", "TokenFrame", "MainMenuBarBackpackButton", "MainMenuBarBackpackButtonIconTexture", "CharacterBag0Slot", "CharacterBag0SlotIconTexture", "CharacterBag1Slot", "CharacterBag1SlotIconTexture", "CharacterBag2Slot", "CharacterBag2SlotIconTexture", "CharacterBag3Slot", "CharacterBag3SlotIconTexture", "MainMenuBarBackpackButtonCount", "EQUIP_CONTAINER", "ContainerFrame1", "MerchantFrame", "BAG_FILTER_EQUIPMENT", "BAG_FILTER_CONSUMABLES", "BAG_FILTER_JUNK", "BAG_FILTER_CLEANUP", "BAG_FILTER_IGNORE", "CLICK_BAG_SETTINGS", "BagItemSearchBox", "BAG_FILTER_ASSIGN_TO", "BagItemAutoSortButton", "CONFIRM_REFUND_ITEM_ENHANCEMENTS_LOST", "StackSplitFrame", "REPAIR_COST", "BACKPACK_TOOLTIP", "LE_FRAME_TUTORIAL_CLEAN_UP_BAGS", "ContainerFrame4", "LE_FRAME_TUTORIAL_BAG_SETTINGS", "LE_FRAME_TUTORIAL_ARTIFACT_RELIC_MATCH", "BackpackTokenFrame", "CLEAN_UP_BAGS_TUTORIAL", "BAG_SETTINGS_TUTORIAL", "KEYRING", "RETRIEVING_ITEM_INFO", "LE_FRAME_TUTORIAL_BAG_SLOTS_AUTHENTICATOR", "ITEM_QUANTITY_TEMPLATE", "ERR_ARTIFACT_RELIC_DOES_NOT_MATCH_ARTIFACT", "ItemUpgradeFrame", "VoidStorageFrame", "NEWBIE_TOOLTIP_CHARACTER", "NEWBIE_TOOLTIP_ENCOUNTER_JOURNAL", "EJMicroButton", "BLIZZARD_STORE", "LFDMicroButton", "MainMenuMicroButton", "ERR_SYSTEM_DISABLED", "GuildMicroButton", "BLIZZARD_STORE_ERROR_UNAVAILABLE", "AchievementMicro<PERSON><PERSON><PERSON>", "QuestLogMicroButton", "PVEFrame", "CollectionsMicroButton", "StoreFrame", "CHARACTER_BUTTON", "ENCOUNTER_JOURNAL", "NEWBIE_TOOLTIP_GUILDTAB", "NEWBIE_TOOLTIP_LOOKINGFORGUILDTAB", "MacroFrame", "ACHIEVEMENT_BUTTON", "TALENT_MICRO_BUTTON_UNSPENT_TALENTS", "TALENTS_BUTTON", "ADVENTURE_JOURNAL", "STORE_MICRO_BUTTON_ALERT_TRIAL_CAP_REACHED", "LE_FRAME_TUTORIAL_TRIAL_BANKED_XP", "TALENT_MICRO_BUTTON_UNSPENT_HONOR_TALENTS", "COMPANIONS_MICRO_BUTTON_NEW_BATTLE_SLOT", "FEATURE_NOT_YET_AVAILABLE", "FEATURE_NOT_AVAILBLE_PANDAREN", "LOOKINGFORGUILD", "TALENT_MICRO_BUTTON_SPEC_TUTORIAL", "TALENT_MICRO_BUTTON_TALENT_TUTORIAL", "COLLECTION_UNOPENED_SINGULAR", "COLLECTION_UNOPENED_PLURAL", "HEIRLOOMS_MICRO_BUTTON_SPEC_TUTORIAL", "LE_FRAME_TUTORIAL_HEIRLOOM_JOURNAL", "TOYBOX_MICRO_BUTTON_SPEC_TUTORIAL", "LE_FRAME_TUTORIAL_TOYBOX", "FEATURE_BECOMES_AVAILABLE_AT_LEVEL", "AJ_MICRO_BUTTON_ALERT_TEXT", "MAP_BAR_PARTICIPATION", "LE_LOOT_FILTER_CLASS", "ALL_SPECS", "ITEM_BIND_ON_EQUIP", "LE_LOOT_FILTER_BOE", "LE_LOOT_FILTER_ALL", "MerchantMoneyFrame", "MerchantFramePortrait", "MerchantPageText", "MERCHANT_PAGE_NUMBER", "MerchantBuyBackItem", "MerchantFrameBottomLeftBorder", "MerchantItem11", "MerchantItem12", "BuybackBG", "MerchantItem3", "MerchantItem5", "MerchantItem7", "MerchantItem9", "MERCHANT_BUYBACK", "MerchantRepairAllButton", "MerchantRepairItemButton", "MerchantPrevPageButton", "MerchantNextPageButton", "MerchantGuildBankRepairButton", "MerchantBuyBackItemName", "MerchantBuyBackItemItemButton", "MerchantBuyBackItemMoneyFrame", "MerchantExtraCurrencyInset", "MerchantExtraCurrencyBg", "LE_LOOT_FILTER_SPEC1", "MinimapZoneText", "GUILD_GROUP", "GARRISON_LANDING_INVASION_ALERT", "HAVE_MAIL_FROM", "HAVE_MAIL", "MINIMAP_TRACKING_NONE", "TOWNSFOLK_TRACKING_TEXT", "MINIMAP_GARRISON_LANDING_PAGE_TOOLTIP", "MINIMAP_ORDER_HALL_LANDING_PAGE_TOOLTIP", "GARRISON_LANDING_RECRUITMENT_STARTED_ALERT", "GARRISON_LANDING_SHIPMENT_STARTED_ALERT", "MinimapCompassTexture", "SANCTUARY_TERRITORY", "FREE_FOR_ALL_TERRITORY", "CONTESTED_TERRITORY", "COMBAT_ZONE", "GUILD_ACHIEVEMENTS_ELIGIBLE_MINXP", "GUILD_ACHIEVEMENTS_ELIGIBLE_MAXXP", "GUILD_ACHIEVEMENTS_ELIGIBLE", "HUNTER_TRACKING_TEXT", "FACTION_CONTROLLED_TERRITORY", "HONOR_POINTS", "ERR_NOT_ENOUGH_MONEY", "CHI_POWER", "CHI_TOOLTIP", "MonkHarmonyBarFrame", "MultiCastSummonSpellButton", "MultiCastRecallSpellButton", "MultiCastFlyoutFrameOpenButton", "MultiCastSlotButton1", "MultiCastFlyoutFrameCloseButton", "MULTI_CAST_TOOLTIP_NO_TOTEM", "OverrideActionBarHealthBar", "HOLY_POWER", "HOLY_POWER_TOOLTIP", "PaperDollItemsFrame", "Model_OnMouseUp", "STAT_SPELLPOWER", "STAT_SPELLPOWER_TOOLTIP", "STAT_ENERGY_REGEN_TOOLTIP", "STAT_FOCUS_REGEN_TOOLTIP", "STAT_RUNE_REGEN_TOOLTIP", "STAT_AVERAGE_ITEM_LEVEL_TOOLTIP", "EQUIPMENT_SET_EDIT", "EQUIPMENT_SET_ASSIGN_TO_SPEC", "HybridScrollFrame_OnLoad", "PLAYER_TITLE_NONE", "CharacterTrialLevelErrorText", "STAT_ARMOR", "STAT_DODGE", "STAT_BLOCK", "STAT_PARRY", "STAT_RESILIENCE", "RESILIENCE_TOOLTIP", "WEAPON_SPEED", "STAT_ATTACK_POWER", "STAT_CRITICAL_STRIKE", "STAT_ENERGY_REGEN", "STAT_FOCUS_REGEN", "STAT_RUNE_REGEN", "STAT_HASTE", "MANA_REGEN", "STAT_MASTERY", "STAT_SPEED", "STAT_LIFESTEAL", "STAT_AVOIDANCE", "STAT_VERSATILITY", "STAT_AVERAGE_ITEM_LEVEL", "STAT_MOVEMENT_SPEED", "PaperDollSidebarTabs", "INVTYPE_WEAPONMAINHAND", "CharacterModelFrameBackgroundTopLeft", "CharacterModelFrameBackgroundTopRight", "CharacterModelFrameBackgroundBotLeft", "CharacterModelFrameBackgroundBotRight", "PAPERDOLL_SIDEBAR_STATS", "PAPERDOLL_SIDEBAR_TITLES", "NO_TITLES_TOOLTIP", "PAPERDOLL_EQUIPMENTMANAGER", "STAT_HEALTH_TOOLTIP", "STAT_HEALTH_PET_TOOLTIP", "RANGED_ATTACK_POWER", "RANGED_ATTACK_POWER_TOOLTIP", "MELEE_ATTACK_POWER", "MELEE_ATTACK_POWER_TOOLTIP", "STAT_HASTE_TOOLTIP", "FauxScrollFrame_OnVerticalScroll", "LE_FRAME_TUTORIAL_INVENTORY_FIXUP_EXPANSION_LEGION", "LE_FRAME_TUTORIAL_INVENTORY_FIXUP_CHECK_EXPANSION_LEGION", "CharacterLevelText", "PLAYER_LEVEL", "PLAYER_LEVEL_NO_SPEC", "STAT_ARMOR_TOOLTIP", "CR_DODGE_TOOLTIP", "CR_BLOCK_TOOLTIP", "CR_PARRY_TOOLTIP", "STAT_RESILIENCE_BASE_TOOLTIP", "STAT_ATTACK_SPEED_BASE_TOOLTIP", "STAT_RUNE_REGEN_FORMAT", "STAT_HASTE_BASE_TOOLTIP", "NOT_APPLICABLE", "MANA_REGEN_TOOLTIP", "STAT_MASTERY_TOOLTIP_NO_TALENT_SPEC", "CR_SPEED_TOOLTIP", "CR_LIFESTEAL_TOOLTIP", "CR_AVOIDANCE_TOOLTIP", "CR_VERSATILITY_TOOLTIP", "STAT_MOVEMENT_GROUND_TOOLTIP", "STAT_MOVEMENT_SWIM_TOOLTIP", "INVTYPE_WEAPONOFFHAND", "INVTYPE_WEAPONMAINHAND_PET", "STAT_FORMAT", "ATTACK_SPEED_SECONDS", "EQUIPMENT_SETS_TOO_MANY", "PAPERDOLLFRAME_TOOLTIP_FORMAT", "STAT_TOOLTIP_BONUS_AP", "ARMOR", "DODGE_CHANCE", "BLOCK_CHANCE", "PARRY_CHANCE", "ATTACK_SPEED", "VERSATILITY_TOOLTIP_FORMAT", "PET_BONUS_TOOLTIP_WARLOCK_SPELLDMG", "PET_BONUS_TOOLTIP_WARLOCK_SPELLDMG_SHADOW", "PET_BONUS_TOOLTIP_WARLOCK_SPELLDMG_FIRE", "LE_FRAME_TUTORIAL_REPUTATION_EXALTED_PLUS", "RELICSLOT", "LE_UNIT_STAT_STRENGTH", "LE_UNIT_STAT_AGILITY", "LE_UNIT_STAT_INTELLECT", "LE_UNIT_STAT_STAMINA", "EFFECTIVE_LEVEL_FORMAT", "MELEE_ATTACK_POWER_SPELL_POWER_TOOLTIP", "CR_CRIT_PARRY_RATING_TOOLTIP", "CR_CRIT_TOOLTIP", "STAT_MASTERY_TOOLTIP", "STAT_MOVEMENT_FLIGHT_TOOLTIP", "EQUIPMENT_SETS_CANT_RENAME", "STAT_TOOLTIP_BONUS_AP_SP", "STAT_NO_BENEFIT_TOOLTIP", "STAT_AVERAGE_ITEM_LEVEL_EQUIPPED", "PAPERDOLL_NEWEQUIPMENTSET", "PAPERDOLL_INVENTORY_FIXUP_COMPLETE", "CharacterMainHandSlot", "STAT_TOOLTIP_SP_AP_DRUID", "CR_PARRY_BASE_STAT_TOOLTIP", "CR_DODGE_BASE_STAT_TOOLTIP", "PetFrameHealthBarTextLeft", "PetFrameHealthBarTextRight", "PetFrameManaBarTextLeft", "PetFrameManaBarTextRight", "PetName", "PetPortrait", "PetFrameFlash", "PetFrameMyHealPredictionBar", "PetFrameOtherHealPredictionBar", "PetFrameTotalAbsorbBar", "PetFrameOverAbsorbGlow", "PetFrameOverHealAbsorbGlow", "PetFrameHealAbsorbBar", "PetHitIndicator", "PetAttackModeTexture", "PetFrameTexture", "PetitionFrameSignButton", "PetitionFrameRequestButton", "PetitionFrameRenameButton", "PetitionFrameNpcNameText", "GUILD_CHARTER_TEMPLATE", "PetitionFrameCharterTitle", "GUILD_NAME", "PetitionFrameCharterName", "PetitionFrameMasterTitle", "GUILD_RANK0_DESC", "PetitionFrameMasterName", "RENAME_GUILD", "NOT_YET_SIGNED", "PetitionFrameInstructions", "GUILD_PETITION_LEADER_INSTRUCTIONS", "ARENA_PETITION_LEADER_INSTRUCTIONS", "GUILD_PETITION_MEMBER_INSTRUCTIONS", "ARENA_PETITION_MEMBER_INSTRUCTIONS", "PET_STABLE_TITLE", "PANEL_INSET_LEFT_OFFSET", "EMPTY_STABLE_SLOT", "PET_STABLE_SLOT_LOCKED", "PANEL_INSET_TOP_OFFSET", "PANEL_INSET_ATTIC_OFFSET", "STABLE_PET_INFO_TEXT", "UNIT_LEVEL_TEMPLATE", "STABLE_PET_INFO_TOOLTIP_TEXT", "PET_STABLE_SLOT_LOCKED_TOOLTIP", "PET_DIET_TEMPLATE", "RECRUIT_A_FRIEND_REWARDS", "ModelPreviewFrame_ShowModel", "PRODUCT_CHOICE_SUBTEXT", "RAF_PRODUCT_CHOICE_EARNED", "RAF_PRODUCT_CHOICE_CLAIM", "PRODUCT_CHOICE_PAGE_NUMBER", "GroupFinderFrame", "PVEFrameLeftInset", "PVEFrameBlueBg", "PVEFrameTLCorner", "PVEFrame<PERSON>Corner", "PVEFrameBRCorner", "PVEFrameBLCorner", "PVEFrameLLVert", "PVEFrameRLVert", "PVEFrameBottomLine", "PVEFrameTopLine", "PVEFrameTopFiligree", "PVEFrameBottomFiligree", "LOOKING_FOR_DUNGEON_PVEFRAME", "SCENARIOS_PVEFRAME", "RAID_FINDER_PVEFRAME", "LFGLIST_NAME", "GROUP_FINDER", "HonorFrameSpecificList_FindAndSelectBattleground", "HonorFrame_SetType", "HonorFrameBonusFrame_SelectButton", "PanelTemplates_HideTab", "PanelTemplates_ShowTab", "<PERSON><PERSON><PERSON><PERSON>", "PVPFramePopup", "PVPHelperFrame", "PVPReadyDialog", "WARGAME_CHALLENGED", "PVPRoleCheckPopup", "PVPTimerFrame", "ARENA_IS_READY", "WARGAME_IS_READY", "BATTLEGROUND_IS_READY", "PVPQueueFrame_OnEvent", "PVPRoleCheckPopupRoleButtonTank", "PVPRoleCheckPopupRoleButtonHealer", "PVPRoleCheckPopupRoleButtonDPS", "PVPUIFrame", "PVPQueueFrame", "RATED_BATTLEGROUND_IS_READY", "INSTANCE_SHUTDOWN_MESSAGE", "ARENA_COMPLETE_MESSAGE", "BATTLEGROUND_COMPLETE_MESSAGE", "QuestFrameDetailPanel", "QuestFrameGreetingPanel", "QuestFrameProgressPanel", "QuestFrameRewardPanel", "QuestRewardScrollChildFrame", "QuestFrameCompleteQuestButton", "QuestProgressTitleText", "QuestProgressText", "GreetingText", "CurrentQuestsText", "AvailableQuestsText", "QuestDetailScrollChildFrame", "QuestFrameAcceptButton", "QuestInfoFrame", "QuestLogPopupDetailFrame", "QuestFramePortrait", "QuestFrameCompleteButton", "QuestProgressRequiredMoneyText", "QuestProgressRequiredMoneyFrame", "QuestProgressRequiredItemsText", "QuestGreetingFrameHorizontalBreak", "QuestFrameDeclineButton", "QuestFrameCloseButton", "QuestNPCModelText", "QuestNPCModelNameText", "TutorialFrame", "QuestNPCModelTextScrollChildFrame", "QuestNPCModelTextScrollFrame", "QuestInfoTitleHeader", "QuestInfoDescriptionText", "QuestInfoTimerFrame", "QuestInfoDescriptionHeader", "QuestInfoObjectivesHeader", "QuestInfoObjectivesText", "QuestInfoSpacerFrame", "QuestInfoAnchor", "QuestInfoRewardText", "QuestInfoSealFrame", "REWARD_FOLLOWER", "REWARD_TRADESKILL_SPELL", "REWARD_ABILITY", "REWARD_AURA", "REWARD_SPELL", "QuestInfoQuestType", "QuestInfoObjectivesFrame", "QuestInfoSpellObjectiveFrame", "QuestInfoSpecialObjectivesFrame", "QuestInfoRequiredMoneyFrame", "QuestInfoGroupSize", "QuestInfoTimerText", "QuestInfoItemHighlight", "QuestInfoRewardsFrame", "QuestInfoSpellObjectiveLearnLabel", "QUEST_KING_VARIAN_WRYNN", "QUEST_WARCHIEF_VOLJIN", "QUEST_KING_ANDUIN_WRYNN", "QUEST_WARCHIEF_SYLVANAS_WINDRUNNER", "TIME_REMAINING", "MapQuestInfoRewardsFrame", "LEARN_SPELL_OBJECTIVE", "QuestInfoRequiredMoneyText", "QUEST_SUGGESTED_GROUP_NUM", "REWARD_CHOOSE", "REWARD_CHOICES", "REWARD_ITEMS", "BONUS_SKILLPOINTS", "COMPLETE", "BONUS_SKILLPOINTS_TOOLTIP", "TRACK_QUEST", "SHARE_QUEST", "CLICK_QUEST_DETAILS", "QuestScrollFrame", "STORY_CHAPTERS", "UNTRACK_QUEST", "ABANDON_QUEST", "UNTRACK_QUEST_ABBREV", "TRACK_QUEST_ABBREV", "QUEST_STORY_STATUS", "DAILY", "WEEKLY", "FACTION_ALLIANCE", "GameTooltipTextLeft1", "FACTION_HORDE", "PARTY_QUEST_STATUS_ON", "ERR_QUEST_ADD_FOUND_SII", "LE_QUEST_FACTION_HORDE", "LFG_LIST_VIEW_GROUP", "CANCEL_SIGN_UP", "PET_BATTLE_PVP_QUEUE", "QueueStatusFrame", "QUEUED_STATUS_LISTED", "QUEUED_STATUS_SIGNED_UP", "QUEUED_STATUS_ROLE_CHECK_IN_PROGRESS", "QUEUED_STATUS_READY_CHECK_IN_PROGRESS", "QUEUED_STATUS_IN_PROGRESS", "ENTER_BATTLE", "LEAVE_BATTLEGROUND", "ENTER_PET_BATTLE", "QUEUED_STATUS_PROPOSAL", "QUEUED_STATUS_SUSPENDED", "QUEUED_STATUS_UNKNOWN", "LFG_LIST_PENDING_APPLICANTS", "QUEUED_STATUS_LOCKED", "QUEUED_STATUS_LOCKED_EXPLANATION", "QUEUED_STATUS_WAITING", "LFG_STATISTIC_AVERAGE_WAIT", "TIME_IN_QUEUE", "LESS_THAN_ONE_MINUTE", "ERR_NOT_WHILE_FALLING", "ERR_NOT_IN_COMBAT", "TOGGLE_SCOREBOARD", "TOGGLE_BATTLEFIELD_MAP", "SURRENDER_ARENA", "LEAVE_ARENA", "LEAVE_ALL_QUEUES", "ALSO_QUEUED_FOR", "LEAVE_ZONE", "INSTANCE_PARTY_LEAVE", "TELEPORT_OUT_OF_DUNGEON", "TELEPORT_TO_DUNGEON", "RAID_MEMBER_NOT_READY", "QUEUED_STATUS_BRAWL_RULES_SUBTITLE", "RaidFinderQueueFrameIneligibleFrame", "RaidFinderQueueFrameCooldownFrame", "RaidFinderQueueFrameScrollFrameChildFrame", "RaidFinderQueueFrameBackground", "RaidFinderQueueFrameCooldownFrameName1", "DESERTER", "RaidFinderFrameFindRaidButton", "LFR_QUEUE_GROUP_INELIGIBLE", "LFR_QUEUE_PLAYER_INELIGIBLE", "RAID_BOSSES", "RaidFinderQueueFramePartyBackfillBackfillButton", "RF_DESERTER_YOU", "RF_DESERTER_OTHER", "NO_RF_AVAILABLE_WITH_NEXT_LEVEL", "NO_RF_AVAILABLE", "NO_RF_WHILE_LFD", "NO_RF_WHILE_LFR", "RF_COOLDOWN_ADDITIONAL_PEOPLE", "RaidFinderQueueFramePartyBackfill", "RolePollPopup", "RolePollPopupRoleButtonTank", "RolePollPopupRoleButtonHealer", "RolePollPopupRoleButtonDPS", "ROLE_REMOVED_INFORM", "ROLE_REMOVED_INFORM_WITH_SOURCE", "ROLE_CHANGED_INFORM", "ROLE_CHANGED_INFORM_WITH_SOURCE", "RUNES_TOOLTIP", "SOR_DEFAULT_MESSAGE", "SOR_SUCCESSFULLY_SENT", "SOR_DISABLE_NO_INVITES_LEFT", "SOR_NUM_REMAINING", "SOR_DISABLE_CHOOSE_A_TARGET", "SOUND_DISABLED", "MUSIC_ENABLED", "MUSIC_DISABLED", "SOUND_EFFECTS_ENABLED", "SOUND_EFFECTS_DISABLED", "ReadyCheckFrame", "ReadyCheckFrameYesButton", "ReadyCheckFrameNoButton", "ReadyCheckListenerFrame", "ReadyCheckPortrait", "READY_CHECK_YOU_WERE_AFK", "ReadyCheckFrameText", "READY_CHECK_MESSAGE", "RAID_DIFFICULTY", "ReputationFrame", "PARAGON_REPUTATION_TOOLTIP_TEXT_LOW_LEVEL", "PARAGON_REPUTATION_TOOLTIP_TEXT", "REPUTATION_PROGRESS_FORMAT", "SPELLBOOK", "TRADE_SKILLS", "PET", "APPRENTICE", "JOURNEYMAN", "EXPERT", "ARTISAN", "MASTER", "GRAND_MASTER", "ILLUSTRIOUS", "ZEN_MASTER", "DRAENOR_MASTER", "LEGION_MASTER", "PAGE_NUMBER", "PrimaryProfession1", "PrimaryProfession2", "SecondaryProfession1", "SecondaryProfession2", "SecondaryProfession3", "LE_FRAME_TUTORIAL_SPELLBOOK", "LE_FRAME_TUTORIAL_PROFESSIONS", "SPELLBOOK_HELP_1", "SPELLBOOK_HELP_2", "SPELLBOOK_HELP_3", "PROFESSIONS_HELP_1", "PROFESSIONS_HELP_2", "PanelTemplates_SelectTab", "PanelTemplates_DeselectTab", "SPELLBOOK_SPELL_NOT_ON_ACTION_BAR", "SPELL_PASSIVE", "LE_FRAME_TUTORIAL_BOOSTED_SPELL_BOOK", "BOOSTED_CHAR_SPELL_TEMPLOCK", "SPELLBOOK_AVAILABLE_AT", "TRADESKILL_RANK_WITH_MODIFIER", "TRADESKILL_RANK", "TALENT_PASSIVE", "TALENT", "TotemFrame", "TradeFrameTradeButton", "TradeFrameInset", "TradeRecipientMoneyBg", "TradeFramePlayerNameText", "TradeFrameRecipientNameText", "TradeHighlightRecipient", "TradeHighlightPlayer", "TradeHighlightPlayerEnchant", "TradeHighlightRecipientEnchant", "TradePlayerInputMoneyFrame", "TRADEFRAME_NOT_MODIFIED_TEXT", "PetCastingBarFrame", "<PERSON><PERSON><PERSON>", "ComboFrame", "PVPFFA", "NEWBIE_TOOLTIP_PVPFFA", "PlayerPVPTimerText", "PLAYER_IN_MULTI_GROUP_RAID_MESSAGE", "PLAYER_IN_MULTI_GROUP_PARTY_MESSAGE", "RuneFrame", "PLAYTIME_TIRED", "PLAYTIME_UNHEALTHY", "MEMBER_COUNT_IN_RAID_LIST", "MEMBERS_IN_PARTY_LIST", "PlayerFrameGroupIndicatorText", "GROUP", "CONFIRM_OVERWRITE_EQUIPMENT_SET", "YES", "NO", "CONFIRM_SAVE_EQUIPMENT_SET", "ERROR_CINEMATIC", "ERR_SOR_STARTING_EXPERIENCE_INCOMPLETE", "ERR_AUTH_CHALLENGE_UI_INVALID", "CONFIRM_DELETE_EQUIPMENT_SET", "CONFIRM_RESET_SETTINGS", "ALL_SETTINGS", "CURRENT_SETTINGS", "CONFIRM_RESET_INTERFACE_SETTINGS", "CONFIRM_REDOCK_CHAT", "MAC_OPEN_UNIVERSAL_ACCESS", "CONFIRM_PURCHASE_TOKEN_ITEM", "CONFIRM_PURCHASE_NONREFUNDABLE_ITEM", "CONFIRM_UPGRADE_ITEM", "CONFIRM_REFUND_TOKEN_ITEM", "CONFIRM_REFUND_MAX_HONOR", "CONFIRM_REFUND_MAX_ARENA_POINTS", "CONFIRM_REFUND_MAX_HONOR_AND_ARENA", "CONFIRM_HIGH_COST_ITEM", "CONFIRM_COMPLETE_EXPENSIVE_QUEST", "COMPLETE_QUEST", "CONFIRM_ACCEPT_PVP_QUEST", "USE_GUILDBANK_REPAIR", "USE_PERSONAL_FUNDS", "GUILDBANK_WITHDRAW", "GUILDBANK_DEPOSIT", "CONFIRM_BUY_GUILDBANK_TAB", "CONFIRM_BUY_REAGNETBANK_TAB", "TOO_MANY_LUA_ERRORS", "DISABLE_ADDONS", "IGNORE_ERRORS", "CONFIRM_ACCEPT_SOCKETS", "TAKE_GM_SURVEY", "CONFIRM_RESET_INSTANCES", "CONFIRM_RESET_CHALLENGE_MODE", "CONFIRM_GUILD_DISBAND", "CONFIRM_BUY_BANK_SLOT", "MACRO_ACTION_FORBIDDEN", "ADDON_ACTION_FORBIDDEN", "DISABLE", "IGNORE_DIALOG", "CONFIRM_LOOT_DISTRIBUTION", "CONFIRM_BATTLEFIELD_ENTRY", "WORLD_PVP_QUEUED", "WORLD_PVP_QUEUED_WARMUP", "WORLD_PVP_FAIL", "WORLD_PVP_INVITED", "WORLD_PVP_INVITED_WARMUP", "WORLD_PVP_ENTER", "WORLD_PVP_PENDING", "WORLD_PVP_PENDING_REMOTE", "WORLD_PVP_EXITED_BATTLE", "WORLD_PVP_LOW_LEVEL", "WORLD_PVP_NOT_WHILE_IN_RAID", "WORLD_PVP_DESERTER", "CONFIRM_GUILD_LEAVE", "CONFIRM_GUILD_PROMOTE", "RENAME_GUILD_LABEL", "HELP_TICKET_QUEUE_DISABLED", "CLIENT_RESTART_ALERT", "CLIENT_LOGOUT_ALERT", "COD_INSUFFICIENT_MONEY", "CLOSE", "COD_CONFIRMATION", "DELETE_MAIL_CONFIRMATION", "DELETE_MONEY_CONFIRMATION", "REPORT_SPAM_CONFIRMATION", "REPORT_BATTLEPET_NAME_CONFIRMATION", "REPORT_PET_NAME_CONFIRMATION", "REPORT_BAD_LANGUAGE_CONFIRMATION", "ADD_CHANNEL", "CHANNEL_INVITE", "ACCEPT_ALT", "CHANNEL_PASSWORD", "NAME_CHAT_WINDOW", "RESET_CHAT_WINDOW", "HELP_TICKET_ABANDON_CONFIRM", "HELP_TICKET_EDIT_ABANDON", "HELP_TICKET_EDIT", "HELP_TICKET_ABANDON", "GM_RESPONSE_POPUP_NEED_MORE_HELP_WARNING", "GM_RESPONSE_POPUP_RESOLVE_CONFIRM", "GM_RESPONSE_POPUP_MUST_RESOLVE_RESPONSE", "GM_RESPONSE_POPUP_VIEW_RESPONSE", "PET_RENAME_CONFIRMATION", "DEATH_RELEASE_TIMER", "DEATH_RELEASE", "USE_SOULSTONE", "DEATH_RECAP", "RESURRECT_REQUEST_TIMER", "RESURRECT_REQUEST", "RESURRECT_REQUEST_NO_SICKNESS_TIMER", "RESURRECT_REQUEST_NO_SICKNESS", "TRADE_WITH_QUESTION", "CHAT_INVITE_NOTICE_POPUP", "LEVEL_GRANT", "BN_BLOCK_FAILED_TOO_MANY_RID", "BN_BLOCK_FAILED_TOO_MANY_CID", "CHAT_PASSWORD_NOTICE_POPUP", "CAMP_TIMER", "QUIT_TIMER", "QUIT_NOW", "LOOT_NO_DROP", "EQUIP_NO_DROP", "END_BOUND_TRADEABLE", "USE_NO_DROP", "CONFIRM_ITEM_USE", "END_REFUND", "DELETE_ITEM", "DELETE_QUEST_ITEM", "DELETE_GOOD_ITEM", "DELETE_GOOD_QUEST_ITEM", "QUEST_ACCEPT", "QUEST_ACCEPT_LOG_FULL", "ABANDON_PET", "ABANDON_QUEST_CONFIRM", "ABANDON_QUEST_CONFIRM_WITH_ITEMS", "ADD_FRIEND_LABEL", "SET_FRIENDNOTE_LABEL", "ADD_IGNORE_LABEL", "ADD_MUTE_LABEL", "ADD_GUILDMEMBER_LABEL", "ADD_RAIDMEMBER_LABEL", "CONVERT_TO_RAID_LABEL", "CONVERT", "SET_GUILDPLAYERNOTE_LABEL", "SET_GUILDOFFICERNOTE_LABEL", "PET_RENAME_LABEL", "DUEL_REQUESTED", "DUEL_OUTOFBOUNDS_TIMER", "PET_BATTLE_PVP_DUEL_REQUESTED", "UNLEARN_SKILL", "UNLEARN", "CONFIRM_XP_LOSS", "CONFIRM_XP_LOSS_NO_DURABILITY", "CONFIRM_XP_LOSS_NO_SICKNESS", "CONFIRM_XP_LOSS_NO_SICKNESS_NO_DURABILITY", "RECOVER_CORPSE_TIMER", "RECOVER_CORPSE", "RECOVER_CORPSE_INSTANCE", "AREA_SPIRIT_HEAL", "CHOOSE_LOCATION", "BIND_ENCHANT", "ACTION_WILL_BIND_ITEM", "REPLACE_ENCHANT", "TRADE_POTENTIAL_BIND_ENCHANT", "TRADE_POTENTIAL_REMOVE_TRANSMOG", "CONFIRM_MERCHANT_TRADE_TIMER_REMOVAL", "INSTANCE_BOOT_TIMER", "GARRISON_BOOT_TIMER", "INSTANCE_LOCK_TIMER", "INSTANCE_LEAVE", "CONFIRM_BINDER", "CONFIRM_SUMMON", "CONFIRM_SUMMON_SCENARIO", "CONFIRM_SUMMON_STARTING_AREA", "BILLING_NAG_DIALOG", "IGR_BILLING_NAG_DIALOG", "ENTER_CODE", "ENTER_FILTER_NAME", "CONFIRM_COMBAT_FILTER_DELETE", "CONFIRM_COMBAT_FILTER_DEFAULTS", "WOW_MOUSE_NOT_FOUND", "CONFIRM_BUY_STABLE_SLOT", "TALENTS_INVOLUNTARILY_RESET", "TALENTS_INVOLUNTARILY_RESET_PET", "SPEC_INVOLUNTARILY_CHANGED", "VOTE_BOOT_REASON_REQUIRED", "HELPFRAME_REPORTLAG_TEXT1", "ERR_AUCTION_HOUSE_DISABLED", "BLOCK_INVITES_CONFIRMATION", "BATTLENET_UNAVAILABLE_ALERT", "WEB_PROXY_FAILED", "WEB_ERROR", "AMOUNT_TO_PICKUP", "GUILD_IMPEACH_POPUP_TEXT", "GUILD_IMPEACH_POPUP_CONFIRM", "CONFIRM_LAUNCH_URL", "CONFIRM_SURRENDER_ARENA", "SAVED_VARIABLES_TOO_LARGE", "PRODUCT_CLAIMING_FAILED", "LFG_LIST_ENTRY_EXPIRED_TOO_MANY_PLAYERS", "LFG_LIST_ENTRY_EXPIRED_TIMEOUT", "TRANSMOG_OUTFIT_NAME", "SAVE", "TRANSMOG_OUTFIT_CONFIRM_OVERWRITE", "TRANSMOG_OUTFIT_CONFIRM_DELETE", "TRANSMOG_OUTFIT_CHECKING_APPEARANCES", "TRANSMOG_OUTFIT_ALL_INVALID_APPEARANCES", "TRANSMOG_OUTFIT_SOME_INVALID_APPEARANCES", "TRANSMOG_FAVORITE_LOSE_REFUND_AND_TRADE", "CHARACTER_UPGRADE_FINISH_BUTTON_POPUP_TEXT", "DANGEROUS_SCRIPTS_WARNING", "EXPERIMENTAL_FEATURE_TURNED_ON_WARNING", "PREMADE_GROUP_SEARCH_DELIST_WARNING_TEXT", "PREMADE_GROUP_INSECURE_SEARCH", "BACKPACK_AUTHENTICATOR_DIALOG_DESCRIPTION", "ACTIVATE", "BACKPACK_AUTHENTICATOR_FULL_INVENTORY", "LEVEL_GRANT_ALLIED_RACE", "VOID_STORAGE_DEPOSIT_CONFIRMATION", "LEVEL_GRANT_ALLIED_RACE_WARNING", "REMOVE_GUILDMEMBER_LABEL", "CONFIRM_CONTINUE", "GuildControlUI_CheckClicked", "VoidStorage_UpdateTransferButton", "VoidStorage_CloseConfirmationDialog", "CONFIRM_GLYPH_PLACEMENT_NO_COST", "CONFIRM_GLYPH_REMOVAL", "BATTLEFIELD_BORDER_WARNING", "ClassTrialThanksForPlayingDialog", "MINUTES", "MAC_OPEN_UNIVERSAL_ACCESS1090", "DEATH_RELEASE_SPECTATOR", "DEATH_RELEASE_NOTIMER", "ARENA_SPECTATOR", "DELETE_ITEM_CONFIRM_STRING", "CONFIRM_XP_LOSS_AGAIN", "CONFIRM_XP_LOSS_AGAIN_NO_DURABILITY", "CONFIRM_XP_LOSS_AGAIN_NO_SICKNESS", "INSTANCE_LOCK_SEPARATOR", "CONFIRM_LEAVE_ARENA", "CONTINUE", "CHARACTER_LINK_CLASS_LEVEL_SPEC_TOOLTIP", "CHARACTER_LINK_ITEM_LEVEL_TOOLTIP", "CAN_NOT_RELEASE_IN_COMBAT", "CAN_NOT_RELEASE_RIGHT_NOW", "INSTANCE_LOCK_WARNING", "SECONDS", "ERR_SPELL_FAILED_S", "INSTANCE_LOCK_WARNING_PREVIOUSLY_SAVED", "INSTANCE_LOCK_TIMER_PREVIOUSLY_SAVED", "DEATH_RECAP_UNAVAILABLE", "FLIGHT_MAP", "TAXINODEYOUAREHERE", "ERR_TAXINOPATHS", "TaxiRouteMap", "VIDEO_QUALITY_LABEL6", "InputBoxInstructions_OnTextChanged", "ScrollingEdit_OnTextChanged", "NEWBIE_TOOLTIP_HEALTHBAR", "PARTY_OPTIONS_LABEL", "NEWBIE_TOOLTIP_PARTYOPTIONS", "PLAYER_OPTIONS_LABEL", "NEWBIE_TOOLTIP_PLAYEROPTIONS", "LE_REALM_RELATION_VIRTUAL", "FOREIGN_SERVER_LABEL", "GARRISON_VISIT_LEADER", "TRADE", "INSPECT", "COMPARE_ACHIEVEMENTS", "TARGET", "MOVE_TO_WHISPER_WINDOW", "DUEL", "PET_BATTLE_PVP_DUEL", "PARTY_UNINVITE", "REMOVE_FRIEND", "SET_NOTE", "VIEW_FRIENDS_OF_FRIENDS", "BLOCK_COMMUNICATION", "VOTE_TO_KICK", "PARTY_PROMOTE", "PARTY_PROMOTE_GUIDE", "GUILD_PROMOTE", "GUILD_LEAVE", "PARTY_LEAVE", "FOLLOW", "PET_DISMISS", "PET_ABANDON", "PET_RENAME", "PET_SHOW_IN_JOURNAL", "LOOT_METHOD", "LOOT_FREE_FOR_ALL", "LOOT_MASTER_LOOTER", "LOOT_GROUP_LOOT", "LOOT_PERSONAL_LOOT", "RESET_INSTANCES", "RESET_CHALLENGE_MODE", "CONVERT_TO_RAID", "CONVERT_TO_PARTY", "UNIT_FRAME_DROPDOWN_SUBSECTION_TITLE_LOOT", "UNIT_FRAME_DROPDOWN_SUBSECTION_TITLE_INSTANCE", "UNIT_FRAME_DROPDOWN_SUBSECTION_TITLE_OTHER", "UNIT_FRAME_DROPDOWN_SUBSECTION_TITLE_INTERACT", "UNIT_FRAME_DROPDOWN_SUBSECTION_TITLE_LEGACY_RAID", "REPORT_PLAYER_FOR", "REPORT_SPAMMING", "REPORT_BAD_LANGUAGE", "REPORT_BAD_NAME", "REPORT_BAD_GUILD_NAME", "REPORT_CHEATING", "REPORT_PET_NAME", "DUNGEON_DIFFICULTY", "PLAYER_DIFFICULTY1", "PLAYER_DIFFICULTY2", "PLAYER_DIFFICULTY6", "RAID_DIFFICULTY1", "RAID_DIFFICULTY2", "PVP_FLAG", "ENABLE", "LOOT_THRESHOLD", "LOOT_PROMOTE", "SELECT_LOOT_SPECIALIZATION", "SELECT_LOOT_SPECIALIZATION_TOOLTIP", "LOOT_SPECIALIZATION_DEFAULT", "OPT_OUT_LOOT_TITLE", "NEWBIE_TOOLTIP_UNIT_OPT_OUT_LOOT", "SET_RAID_LEADER", "SET_RAID_ASSISTANT", "SET_MAIN_TANK", "SET_MAIN_ASSIST", "DEMOTE", "REMOVE", "RAF_SUMMON", "RAF_GRANT_LEVEL", "VEHICLE_LEAVE", "CLEAR_FOCUS", "FULL_SIZE_FOCUS_FRAME_TEXT", "LOCK_FOCUS_FRAME", "UNLOCK_FOCUS_FRAME", "MOVE_FRAME", "BUFFS_ON_TOP", "PLAYER_FRAME_SHOW_CASTBARS", "ADD_CHARACTER_FRIEND", "SEND_BATTLETAG_REQUEST", "RAID_TARGET_NONE", "SET_ROLE", "NO_ROLE", "MAKE_MODERATOR", "REMOVE_MODERATOR", "CHAT_OWNER", "CHAT_KICK", "CHAT_BAN", "NEWBIE_TOOLTIP_UNIT_PERSONAL", "NEWBIE_TOOLTIP_UNIT_GROUP_LOOT", "NEWBIE_TOOLTIP_UNIT_FREE_FOR_ALL", "NEWBIE_TOOLTIP_UNIT_MASTER_LOOTER", "GARRISON_RETURN", "IGNORE", "ERR_REPORT_SUBMISSION_FAILED", "IGNORE_REMOVE", "RAF_SUMMON_WITH_COOLDOWN", "LE_REALM_RELATION_SAME", "BATTLETAG_REMOVE_FRIEND_CONFIRMATION", "REMOVE_FRIEND_CONFIRMATION", "TimeManager_CheckAlarm", "StopwatchTicker_OnUpdate", "ActionStatus", "StopwatchTicker", "TimeManagerClockButton", "TimeManager_ShouldCheckAlarm", "Stopwatch_IsPlaying", "ItemTextFrame", "SCREENSHOT_SUCCESS", "SCREENSHOT_FAILURE", "TIMEMANAGER_TOOLTIP_TITLE", "TIMEMANAGER_TOOLTIP_REALMTIME", "TIMEMANAGER_TOOLTIP_LOCALTIME", "GameTimeFrame", "GameTimeTexture", "GameTimeCalendarInvitesTexture", "GameTimeCalendarInvitesGlow", "TIMEMANAGER_TICKER_24HOUR", "TIME_TWELVEHOURAM", "Calendar_Show", "CALENDAR_EVENT_ALARM_MESSAGE", "GAMETIME_TOOLTIP_CALENDAR_INVITES", "GAMETIME_TOOLTIP_TOGGLE_CALENDAR", "TIME_TWELVEHOURPM", "TIMEMANAGER_TICKER_12HOUR", "CalendarFrame", "TimeManager_IsAlarmFiring", "STAT_TEMPLATE", "BATTLEGROUND_YOUR_AVERAGE_RATING", "BATTLEGROUND_ENEMY_AVERAGE_RATING", "PLAYER_COUNT_ALLIANCE", "PLAYER_COUNT_HORDE", "TIME_ELAPSED", "TIME_TO_PORT_ARENA", "LEAVE_LFD_BATTLEFIELD", "TIME_TO_PORT", "ERR_PLAYERLIST_LEFT_BATTLE", "ERR_PLAYERS_LEFT_BATTLE_D", "ERR_PLAYER_LEFT_BATTLE_D", "ERR_PLAYERLIST_JOINED_BATTLE", "ERR_PLAYERS_JOINED_BATTLE_D", "ERR_PLAYER_JOINED_BATTLE_D", "VICTORY_TEXT_LFD_BATTLEFIELD_WINS", "VICTORY_TEXT_LFD_BATTLEFIELD_DRAW", "ARENA_TEAM_NAME_PURPLE", "ARENA_TEAM_NAME_GOLD", "TALENT_SPEC_AND_CLASS", "VICTORY_TEXT_ARENA_WINS", "VICTORY_TEXT_ARENA_DRAW", "ARENA_TEAM_NAME_GREEN", "FLAG_COUNT_TEMPLATE", "PVPInfoTextString", "PVPArenaTextString", "SubZoneTextString", "ZoneTextString", "AutoFollowStatusText", "AUTOFOLLOWSTART", "AUTOFOLLOWSTOP"]}