<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">

	<Texture name="ActionBarFlyoutButton-ArrowUp" file="Interface\Buttons\ActionBarFlyoutButton" virtual="true" >
		<Size x="23" y="11"/>	
		<TexCoords left="0.62500000" right="0.98437500" top="0.74218750" bottom="0.82812500"/>	
	</Texture>
	<Texture name="ActionBarFlyoutButton-IconFrame" file="Interface\Buttons\ActionBarFlyoutButton" virtual="true" >
		<Size x="42" y="42"/>	
		<TexCoords left="0.01562500" right="0.67187500" top="0.39843750" bottom="0.72656250"/>	
	</Texture>
	<Texture name="ActionBarFlyoutButton-IconShadow" file="Interface\Buttons\ActionBarFlyoutButton" virtual="true" >
		<Size x="48" y="48"/>	
		<TexCoords left="0.01562500" right="0.76562500" top="0.00781250" bottom="0.38281250"/>	
	</Texture>
	
	<CheckButton name="ActionButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="36" y="36"/>
		</Size>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentIcon" parentKey="icon"/>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="1">
				<Texture name="$parentFlash" parentKey="Flash" file="Interface\Buttons\UI-QuickslotRed" hidden="true"/>
				<Texture name="$parentFlyoutBorder" inherits="ActionBarFlyoutButton-IconFrame" parentKey="FlyoutBorder" hidden="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
				<Texture name="$parentFlyoutBorderShadow" inherits="ActionBarFlyoutButton-IconShadow" parentKey="FlyoutBorderShadow" hidden="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="2">
				<Texture name="$parentFlyoutArrow" inherits="ActionBarFlyoutButton-ArrowUp" parentKey="FlyoutArrow" hidden="true"/>
				<FontString name="$parentHotKey" inherits="NumberFontNormalSmallGray" parentKey="HotKey" justifyH="RIGHT">
					<Size x="36" y="10"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="1" y="-3"/>
					</Anchors>
				</FontString>
				<FontString name="$parentCount" inherits="NumberFontNormal" parentKey="Count" justifyH="RIGHT">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="-2" y="2"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<FontString name="$parentName" parentKey="Name" inherits="GameFontHighlightSmallOutline">
					<Size x="36" y="10"/>
					<Anchors>
						<Anchor point="BOTTOM" x="0" y="2"/>
					</Anchors>
				</FontString>
				<Texture name="$parentBorder" file="Interface\Buttons\UI-ActionButton-Border" parentKey="Border" hidden="true" alphaMode="ADD">
					<Size x="62" y="62"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="1">
				<Texture parentKey="NewActionTexture" atlas="bags-newitem" useAtlasSize="false" alphaMode="ADD" hidden="true">
					<Size x="44" y="44"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
				<Texture parentKey="SpellHighlightTexture" atlas="bags-newitem" useAtlasSize="false" alphaMode="ADD" hidden="true">
					<Size x="44" y="44"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
				<Texture parentKey="AutoCastable" file="Interface\Buttons\UI-AutoCastableOverlay" hidden="true">
					<Size x="58" y="58"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="SpellHighlightAnim" looping="REPEAT">
				<Alpha childKey="SpellHighlightTexture" smoothing="OUT" duration=".35" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="SpellHighlightTexture" smoothing="IN" duration=".35" order="2" fromAlpha="1" toAlpha="0"/>
			</AnimationGroup>
		</Animations>
		<Frames>
			<Frame name="$parentShine" parentKey="AutoCastShine" inherits="AutoCastShineTemplate">
				<Anchors>
					<Anchor point="CENTER" x="0" y="0"/>
				</Anchors>
				<Size x="28" y="28"/>
			</Frame>
			<Cooldown name="$parentCooldown" inherits="CooldownFrameTemplate" parentKey="cooldown">
				<Size x="36" y="36"/>
				<Anchors>
					<Anchor point="CENTER" x="0" y="-1"/>
				</Anchors>
				<SwipeTexture>
					<Color r="1" g="1" b="1" a="0.8"/>
				</SwipeTexture>
			</Cooldown>
		</Frames>
		<NormalTexture name="$parentNormalTexture" parentKey="NormalTexture" file="Interface\Buttons\UI-Quickslot2">
			<Anchors>
				<Anchor point="TOPLEFT" x="-15" y="15"/>
				<Anchor point="BOTTOMRIGHT" x="15" y="-15"/>
			</Anchors>
		</NormalTexture>
		<PushedTexture file="Interface\Buttons\UI-Quickslot-Depress"/>
		<HighlightTexture alphaMode="ADD" file="Interface\Buttons\ButtonHilight-Square"/>
		<CheckedTexture alphaMode="ADD" file="Interface\Buttons\CheckButtonHilight"/>
	</CheckButton>
</Ui>

