<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="AlertFrameSystems.lua"/>
	<Button name="DungeonCompletionAlertFrameRewardTemplate" parentArray="RewardFrames" virtual="true">
		<Size x="36" y="36"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="texture">
					<Size>
						<AbsDimension x="27" y="27"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER" x="-3" y="3"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture setAllPoints="true" file="Interface\LFGFrame\UI-LFG-ICON-REWARDRING">
					<TexCoords left="0" right="0.675" top="0" bottom="0.675"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnEnter function="DungeonCompletionAlertFrameReward_OnEnter"/>
			<OnLeave function="DungeonCompletionAlertFrameReward_OnLeave"/>
		</Scripts>
	</Button>

	<Button name="InvasionAlertFrameRewardTemplate" virtual="true" inherits="DungeonCompletionAlertFrameRewardTemplate">
		<Scripts>
			<OnEnter function="StandardRewardAlertFrame_OnEnter"/>
		</Scripts>
	</Button>

	<Button name="WorldQuestFrameRewardTemplate" virtual="true" inherits="DungeonCompletionAlertFrameRewardTemplate">
		<Scripts>
			<OnEnter function="StandardRewardAlertFrame_OnEnter"/>
		</Scripts>
	</Button>

	<Button name="DungeonCompletionAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" virtual="true" inherits="AlertFrameTemplate">
		<Size x="336" y="80"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="128"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="dungeonTexture">
					<Size x="45" y="45"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="13" y="13"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture file="Interface\LFGFrame\LFR-Texture" parentKey="raidArt">
					<Size x="310" y="72"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
					<TexCoords left="0.00195313" right="0.60742188" top="0.00390625" bottom="0.28515625"/>
				</Texture>
				<Texture file="Interface\LFGFrame\UI-LFG-DUNGEONTOAST" parentKey="dungeonArt1">
					<Size x="70" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.546875" top="0.28515" bottom="0.5664"/>
				</Texture>
				<Texture file="Interface\LFGFrame\UI-LFG-DUNGEONTOAST" parentKey="dungeonArt2">
					<Size x="15" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
					<TexCoords left="0.5546875" right="0.671875" top="0.28515" bottom="0.5664"/>
				</Texture>
				<Texture file="Interface\LFGFrame\UI-LFG-DUNGEONTOAST" parentKey="dungeonArt3">
					<Size x="127" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="69" y="0"/>
					</Anchors>
					<TexCoords left="0" right="0.9921875" top="0" bottom="0.28125"/>
				</Texture>
				<Texture file="Interface\LFGFrame\UI-LFG-DUNGEONTOAST" parentKey="dungeonArt4">
					<Size x="127" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="-14" y="0"/>
					</Anchors>
					<TexCoords left="0" right="0.9921875" top="0.58203" bottom="0.86328"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString inherits="GameFontWhiteTiny" text="DUNGEON_COMPLETED">
					<Size x="200" y="12"/>
					<Anchors>
						<Anchor point="TOP" x="25" y="-29"/>
					</Anchors>
				</FontString>
				<FontString inherits="GameFontNormal" parentKey="instanceName">
					<!--Size x="200" y="16"/-->
					<Anchors>
						<Anchor point="TOP" x="25" y="-44"/>
					</Anchors>
				</FontString>
				<Texture file="Interface/LFGFrame/UI-LFG-ICON-HEROIC" parentKey="heroicIcon">
					<Size x="16" y="20"/>
					<Anchors>
						<Anchor point="RIGHT" relativeKey="$parent.instanceName" relativePoint="LEFT" x="-3" y="-3"/>
					</Anchors>
					<TexCoords left="0.0" right="0.5" top="0" bottom="0.625"/>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="260" offsetY="0" duration="0.85" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame setAllPoints="true" enableMouse="false" parentKey="glowFrame">
				<Layers>
					<Layer level="OVERLAY">
						<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="glow">
							<Size x="415" y="171"/>
							<Anchors>
								<Anchor point="CENTER" x="0" y="-5"/>
							</Anchors>
							<Animations>
								<AnimationGroup parentKey="animIn">
									<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
									<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
									<Scripts>
										<OnFinished>
											self:GetParent():Hide();
										</OnFinished>
									</Scripts>
								</AnimationGroup>
							</Animations>
							<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad function="RaiseFrameLevel"/>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad inherit="prepend" function="DungeonCompletionAlertFrame_OnLoad"/>
		</Scripts>
	</Button>

	<Button name="AchievementAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" virtual="true" inherits="AlertFrameTemplate">
		<Size x="300" y="88"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="128"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Background" file="Interface\AchievementFrame\UI-Achievement-Alert-Background">
					<Anchors>
						<Anchor point="TOPLEFT"/>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
					<TexCoords left="0" right=".605" top="0" bottom=".703"/>
				</Texture>
				<FontString parentKey="Unlocked" inherits="GameFontBlackTiny" text="ACHIEVEMENT_UNLOCKED">
					<Size x="200" y="12"/>
					<Anchors>
						<Anchor point="TOP" x="7" y="-23"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Name" inherits="GameFontHighlight">
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="72" y="36"/>
						<Anchor point="BOTTOMRIGHT" x="-60" y="36"/>
					</Anchors>
					<Size x="240" y="16"/>
				</FontString>
				<FontString parentKey="GuildName" inherits="GameFontNormal" hidden="true">
					<Anchors>
						<Anchor point="TOPLEFT" x="50" y="-10"/>
						<Anchor point="TOPRIGHT" x="-50" y="-10"/>
					</Anchors>
					<Size x="0" y="16"/>
				</FontString>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture parentKey="GuildBanner" file="Interface\AchievementFrame\UI-Achievement-Guild" hidden="true">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="-13" y="-29"/>
					</Anchors>
					<TexCoords left="0.76171875" right="0.88671875" top="0.00195313" bottom="0.12695313"/>
				</Texture>
				<Texture parentKey="OldAchievement" file="Interface\AchievementFrame\UI-Achievement-Borders" hidden="true">
					<Size x="193" y="19"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="93" y="-34"/>
					</Anchors>
					<TexCoords left="0.185546875" right="0.623046875" top="0.65625" bottom="0.734375"/>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="GuildBorder" file="Interface\AchievementFrame\UI-Achievement-Guild" hidden="true">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="-13" y="-29"/>
					</Anchors>
					<TexCoords left="0.63281250" right="0.75781250" top="0.00195313" bottom="0.12695313"/>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size x="400" y="171"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
				</Texture>
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="8"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="240" offsetY="0" duration="0.85" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="Icon">
				<Size x="124" y="124"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="-26" y="16"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture parentKey="Bling" file="Interface\AchievementFrame\UI-Achievement-Bling" hidden="true">
							<Anchors>
								<Anchor point="CENTER" x="-1" y="1"/>
							</Anchors>
							<Size x="116" y="116"/>
						</Texture>
					</Layer>
					<Layer level="ARTWORK">
						<Texture parentKey="Texture">
							<Anchors>
								<Anchor point="CENTER" x="0" y="3"/>
							</Anchors>
							<Size x="50" y="50"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture parentKey="Overlay" file="Interface\AchievementFrame\UI-Achievement-IconFrame">
							<Anchors>
								<Anchor point="CENTER" x="-1" y="2"/>
							</Anchors>
							<Size x="72" y="72"/>
							<TexCoords left="0" right="0.5625" top="0" bottom="0.5625"/>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame parentKey="Shield">
				<Size x="64" y="64"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="-10" y="-13"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture parentKey="Icon" file="Interface\AchievementFrame\UI-Achievement-Shields">
							<Size x="52" y="48"/>
							<Anchors>
								<Anchor point="TOPRIGHT" x="1" y="-6"/>
							</Anchors>
							<TexCoords left="0" right=".5" top="0" bottom=".45"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<FontString parentKey="Points" inherits="GameFontNormal">
							<Anchors>
								<Anchor point="CENTER" x="7" y="2"/>
							</Anchors>
							<Size x="64" y="64"/>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad function="AchievementShield_OnLoad" />
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnClick function="AchievementAlertFrame_OnClick" />
		</Scripts>
	</Button>

	<Button name="CriteriaAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" virtual="true" inherits="AlertFrameTemplate">
		<Size x="256" y="52"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="0"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Background" file="Interface\AchievementFrame\UI-Achievement-Alert-Background-Mini">
					<Anchors>
						<Anchor point="LEFT" x="23" y="0"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Unlocked" inherits="GameFontBlackTiny" text="ACHIEVEMENT_PROGRESSED">
					<Size x="200" y="0"/>
					<Anchors>
						<Anchor point="TOP">
							<Offset x="23" y="-14"/>
						</Anchor>
					</Anchors>
				</FontString>
				<FontString parentKey="Name" inherits="GameFontHighlight">
					<Size x="200" y="16"/>
					<Anchors>
						<Anchor point="TOP">
							<Offset x="23" y="-26"/>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size x="257" y="52"/>
					<Anchors>
						<Anchor point="CENTER" x="23" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
				</Texture>
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="52"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="23" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="210" offsetY="0" duration="0.85" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="Icon">
				<Size x="116" y="116"/>
				<Anchors>
					<Anchor point="LEFT" x="-44" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture parentKey="Bling" file="Interface\AchievementFrame\UI-Achievement-Bling" hidden="true">
							<Anchors>
								<Anchor point="CENTER" x="-1" y="1"/>
							</Anchors>
							<Size x="116" y="116"/>
						</Texture>
					</Layer>
					<Layer level="ARTWORK">
						<Texture parentKey="Texture">
							<Anchors>
								<Anchor point="CENTER" x="0" y="3"/>
							</Anchors>
							<Size x="50" y="50"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture parentKey="Overlay" file="Interface\AchievementFrame\UI-Achievement-IconFrame">
							<Anchors>
								<Anchor point="CENTER" x="-1" y="2"/>
							</Anchors>
							<Size x="72" y="72"/>
							<TexCoords left="0" right="0.5625" top="0" bottom="0.5625"/>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
		<Scripts>
			<OnClick function="AchievementAlertFrame_OnClick"/>
		</Scripts>
	</Button>

	<Button name="GuildChallengeAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" inherits="AlertFrameTemplate" virtual="true">
		<Size x="270" y="75"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="128"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture file="Interface\GuildFrame\GuildChallenges" parentKey="EmblemBackground">
					<Size x="37" y="36"/>
					<Anchors>
						<Anchor point="LEFT" x="14" y="0"/>
					</Anchors>
					<TexCoords left="0.06054688" right="0.13281250" top="0.00390625" bottom="0.14453125"/>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture file="Interface\GuildFrame\GuildChallenges">
					<Size x="270" y="67"/>
					<Anchors>
						<Anchor point="LEFT"/>
					</Anchors>
					<TexCoords left="0.13671875" right="0.66406250" top="0.00390625" bottom="0.26562500"/>
				</Texture>
				<Texture file="Interface\GuildFrame\GuildChallenges" parentKey="EmblemBorder">
					<Size x="37" y="36"/>
					<Anchors>
						<Anchor point="LEFT" x="14" y="0"/>
					</Anchors>
					<TexCoords left="0.06054688" right="0.13281250" top="0.15234375" bottom="0.29296875"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="EmblemIcon">
					<Size x="35" y="35"/>
					<Anchors>
						<Anchor point="LEFT" x="15" y="0"/>
					</Anchors>
				</Texture>
				<FontString inherits="GameFontNormal" text="GUILD_CHALLENGE_LABEL">
					<Anchors>
						<Anchor point="LEFT" x="66" y="11"/>
					</Anchors>
				</FontString>
				<FontString inherits="GameFontWhiteTiny" parentKey="Type">
					<Anchors>
						<Anchor point="LEFT" x="72" y="-12"/>
					</Anchors>
				</FontString>
				<FontString inherits="GameFontNormal" parentKey="Count">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="-18" y="20"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size x="350" y="151"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
				</Texture>
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="60"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="8"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="200" offsetY="0" duration="0.71" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick function="GuildChallengeAlertFrame_OnClick"/>
		</Scripts>
	</Button>

	<Button name="ScenarioLegionInvasionAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" virtual="true" inherits="AlertFrameTemplate">
		<Size x="311" y="78"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="128"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture atlas="legioninvasion-Toast-Frame" useAtlasSize="true">
					<Anchors>
						<Anchor point="LEFT"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="-1">
				<Texture file="Interface\Icons\Ability_Warlock_DemonicPower">
					<Size x="48" y="48"/>
					<Anchors>
						<Anchor point="LEFT" x="15" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString inherits="GameFontWhiteTiny" text="SCENARIO_INVASION_COMPLETE">
					<Size x="200" y="12"/>
					<Anchors>
						<Anchor point="TOP" x="28" y="-22"/>
					</Anchors>
					<Color r="0.973" g="0.937" b="0.580"/>
				</FontString>
				<FontString inherits="GameFontHighlight" parentKey="ZoneName">
					<Size x="200" y="0"/>
					<Anchors>
						<Anchor point="CENTER" relativePoint="TOP" x="28" y="-46"/>
					</Anchors>
				</FontString>
				<Texture parentKey="BonusStar" atlas="Bonus-ToastBanner" useAtlasSize="true" hidden="true">
					<Anchors>
						<Anchor point="TOPRIGHT" x="-18" y="-5"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Button>

	<Button name="ScenarioAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" virtual="true" inherits="AlertFrameTemplate">
		<Size x="311" y="78"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="128"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="-1">
				<Texture atlas="Toast-IconBG" useAtlasSize="true">
					<Anchors>
						<Anchor point="LEFT" x="15" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND">
				<Texture parentKey="dungeonTexture">
					<Size x="45" y="45"/>
					<Anchors>
						<Anchor point="LEFT" x="17" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture atlas="Toast-Frame" setAllPoints="true"/>
			</Layer>
			<Layer level="ARTWORK">
				<FontString inherits="GameFontWhiteTiny" text="SCENARIO_COMPLETED">
					<Size x="200" y="12"/>
					<Anchors>
						<Anchor point="TOP" x="20" y="-22"/>
					</Anchors>
					<Color r="0.973" g="0.937" b="0.580"/>
				</FontString>
				<FontString inherits="GameFontHighlight" parentKey="dungeonName">
					<Size x="200" y="0"/>
					<Anchors>
						<Anchor point="CENTER" relativePoint="TOP" x="20" y="-46"/>
					</Anchors>
				</FontString>
				<Texture parentKey="BonusStar" atlas="Bonus-ToastBanner" useAtlasSize="true" hidden="true">
					<Anchors>
						<Anchor point="TOPRIGHT" x="-18" y="-5"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="260" offsetY="0" duration="0.85" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame setAllPoints="true" enableMouse="false" parentKey="glowFrame">
				<Layers>
					<Layer level="OVERLAY">
						<Texture atlas="Toast-Flash" useAtlasSize="true" alphaMode="ADD" hidden="true" parentKey="glow">
							<Animations>
								<AnimationGroup parentKey="animIn">
									<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
									<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
									<Scripts>
										<OnFinished>
											self:GetParent():Hide();
										</OnFinished>
									</Scripts>
								</AnimationGroup>
							</Animations>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad function="RaiseFrameLevel"/>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad inherit="prepend">
				self.glow = self.glowFrame.glow;
			</OnLoad>
		</Scripts>
	</Button>

	<Button name="MoneyWonAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" virtual="true" inherits="AlertFrameTemplate">
		<Size x="249" y="71"/>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<!--PerPlayerLoot-Gold-->
				<Texture parentKey="Background" file="Interface\LootFrame\LootToast" setAllPoints="true">
					<TexCoords left="0.56347656" right="0.80664063" top="0.28906250" bottom="0.56640625"/>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="Icon" file="Interface\Icons\INV_Misc_Coin_02">
					<Size x="38" y="38"/>
					<Anchors>
						<Anchor point="LEFT" x="16" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<!--ItemBorder-Gold-->
				<Texture parentKey="IconBorder" file="Interface\LootFrame\LootToast">
					<Size x="45" y="45"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Icon" relativePoint="CENTER" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.73242188" right="0.78906250" top="0.57421875" bottom="0.80078125"/>
				</Texture>
				<FontString parentKey="Label" inherits="GameFontNormal" justifyH="LEFT" text="YOU_RECEIVED_LABEL">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.IconBorder" relativePoint="TOPRIGHT" x="7" y="-1"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Amount" inherits="GameFontHighlightLarge" justifyH="LEFT">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.IconBorder" relativePoint="BOTTOMRIGHT" x="10" y="7"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Button>
	
	<Button name="HonorAwardedAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" virtual="true" inherits="AlertFrameTemplate">
		<Size x="249" y="70"/>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<!--PerPlayerLoot-Gold-->
				<Texture parentKey="Background" file="Interface\LootFrame\LootToast" setAllPoints="true">
					<TexCoords left="0.56347656" right="0.80664063" top="0.28906250" bottom="0.56640625"/>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="Icon" file="Interface\Icons\Achievement_LegionPVPTier4">
					<Size x="38" y="38"/>
					<Anchors>
						<Anchor point="LEFT" x="16" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<!--ItemBorder-Gold-->
				<Texture parentKey="IconBorder" file="Interface\LootFrame\LootToast">
					<Size x="45" y="45"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Icon" relativePoint="CENTER" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.73242188" right="0.78906250" top="0.57421875" bottom="0.80078125"/>
				</Texture>
				<FontString parentKey="Label" inherits="GameFontNormal" justifyH="LEFT" text="YOU_RECEIVED">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.IconBorder" relativePoint="TOPRIGHT" x="7" y="-1"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Amount" inherits="GameFontHighlightMedium" justifyH="LEFT">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.IconBorder" relativePoint="BOTTOMRIGHT" x="10" y="8"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Button>

	<Texture name="LootUpgradeFrame_ArrowTemplate" hidden="false" alpha="0" alphaMode="BLEND" atlas="loottoast-arrow-blue" useAtlasSize="true" virtual ="true"/>
	<Button name="LootWonAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" virtual="true" inherits="AlertFrameTemplate">
		<Size x="276" y="96"/>
		<KeyValues>
			<KeyValue key="numArrows" value="5" type="number"/>
		</KeyValues>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
			<AnimationGroup parentKey="animArrows" setToFinalAlpha="true">
				<Alpha childKey="Arrow1" startDelay="0" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Arrow1" startDelay="0.3" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Arrow1" startDelay="0" duration="0.5" order="1" offsetX="0" offsetY="60"/>
				<Alpha childKey="Arrow2" startDelay="0.1" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Arrow2" startDelay="0.4" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Arrow2" startDelay="0.1" duration="0.5" order="1" offsetX="0" offsetY="60"/>
				<Alpha childKey="Arrow3" startDelay="0.2" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Arrow3" startDelay="0.5" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Arrow3" startDelay="0.2" duration="0.5" order="1" offsetX="0" offsetY="60"/>
				<Alpha childKey="Arrow4" startDelay="0.4" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Arrow4" startDelay="0.7" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Arrow4" startDelay="0.4" duration="0.5" order="1" offsetX="0" offsetY="60"/>
				<Alpha childKey="Arrow5" startDelay="0.6" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Arrow5" startDelay="0.9" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Arrow5" startDelay="0.6" duration="0.5" order="1" offsetX="0" offsetY="60" endDelay="360000"/>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture parentKey="Background" file="Interface\LootFrame\LootToast">
					<Size x="276" y="96"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
					<TexCoords left="0.28222656" right="0.55175781" top="0.57812500" bottom="0.95312500"/>
				</Texture>
				<Texture parentKey="PvPBackground" atlas="loottoast-bg-alliance" useAtlasSize="true" alpha="1">
					<Anchors>
						<Anchor point="CENTER" x="-1" y="-1"/>
					</Anchors>
				</Texture>
				<Texture parentKey="RatedPvPBackground" atlas="pvprated-loottoast-bg-alliance" useAtlasSize="true" alpha="1">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BGAtlas" hidden="true">
					<Size x="276" y="96"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="Icon">
					<Size x="52" y="52"/>
					<Anchors>
						<Anchor point="LEFT" x="23" y="-2"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="IconBorder" atlas="loottoast-itemborder-green">
					<Size x="60" y="60"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Icon" relativePoint="CENTER" x="0" y="0"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Label" inherits="GameFontNormal" justifyH="LEFT" text="YOU_WON_LABEL">
					<Size x="167" y="16"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Icon" relativePoint="TOPRIGHT" x="7" y="5"/>
					</Anchors>
				</FontString>
				<FontString parentKey="ItemName" inherits="GameFontNormalMed3" justifyH="LEFT" justifyV="MIDDLE">
					<Size x="167" y="33"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Icon" relativePoint="TOPRIGHT" x="10" y="-16"/>
					</Anchors>
				</FontString>
				<Texture parentKey="RollTypeIcon" file="Interface\Buttons\UI-GroupLoot-Dice-Up">
					<Size x="22" y="22"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="-20" y="-18"/>
					</Anchors>
				</Texture>
				<FontString parentKey="RollValue" inherits="GameFontGreen" justifyH="RIGHT">
					<Anchors>
						<Anchor point="RIGHT" relativeKey="$parent.RollTypeIcon" relativePoint="LEFT" x="-3" y="2"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="1">
				<Texture parentKey="SpecIcon">
					<Size x="24" y="24"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="8" y="-7"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="2">
				<Texture parentKey="SpecRing" file="Interface/Minimap/MiniMap-TrackingBorder">
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Arrow1" inherits="LootUpgradeFrame_ArrowTemplate">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Background" x="39" y="-45"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Arrow2" inherits="LootUpgradeFrame_ArrowTemplate">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Arrow1" x="16" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Arrow3" inherits="LootUpgradeFrame_ArrowTemplate">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Arrow1" x="-16" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Arrow4" inherits="LootUpgradeFrame_ArrowTemplate">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Arrow1" x="5" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Arrow5" inherits="LootUpgradeFrame_ArrowTemplate">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Arrow1" x="-12" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<!--LootWin-Glow-->
				<Texture atlas="loottoast-glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size>
						<AbsDimension x="286" y="109"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
				</Texture>
				<!--LootWin-Sheen-->
				<Texture file="Interface\LootFrame\LootToast" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="171" y="75"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-10" y="12"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.1" order="1"/>
							<Translation offsetX="165" offsetY="0" duration="0.425" order="2"/>
							<Alpha startDelay="0.175" fromAlpha="1" toAlpha="0" duration="0.25" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.56347656" right="0.73046875" top="0.57421875" bottom="0.86718750"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad inherit="prepend">
				self.UpdateTooltip = LootAlertFrame_OnEnter;
			</OnLoad>
			<OnEnter>
				AlertFrame_StopOutAnimation(self);
				LootAlertFrame_OnEnter(self);
			</OnEnter>
			<OnLeave>
				AlertFrame_ResumeOutAnimation(self);
				GameTooltip:Hide();
			</OnLeave>
			<OnClick function="LootWonAlertFrame_OnClick"/>
		</Scripts>
	</Button>

	<FontString name="LootUpgradeFrame_ItemNameTemplate" inherits="GameFontNormalMed3"  justifyH="LEFT" justifyV="MIDDLE" hidden="false" wordwrap="true" virtual ="true">
		<Size x="160" y="34"/>
		<Color r="1" g="1" b="1"/>
	</FontString>

	<Button name="LootUpgradeFrameTemplate" parent="UIParent" hidden="true" alpha="0" frameStrata="DIALOG" frameLevel="1" inherits="AlertFrameTemplate" virtual="true">
		<Size x="276" y="96"/>
		<KeyValues>
			<KeyValue key="numArrows" value="5" type="number"/>
		</KeyValues>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Background" atlas="loottoast-bg-questrewardupgrade" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="2">
				<Texture parentKey="BaseQualityBorder" hidden="false" alpha="1" alphaMode="BLEND" atlas="loottoast-itemborder-green" useAtlasSize="true">
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Background" x="19" y="-1"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="1">
				<Texture parentKey="Icon" hidden="false" alpha="1" alphaMode="BLEND">
					<Size x="50" y="50"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BaseQualityBorder" x="4" y = "-4"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="3">
				<Texture parentKey="UpgradeQualityBorder" hidden="false" alpha="0" alphaMode="BLEND" atlas="loottoast-itemborder-blue" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.BaseQualityBorder"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="4">
				<Texture parentKey="BorderGlow" hidden="false" alpha="0" alphaMode="ADD" atlas="loottoast-itemborder-glow" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.BaseQualityBorder"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Arrow1" inherits="LootUpgradeFrame_ArrowTemplate">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Background" x="39" y="-45"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Arrow2" inherits="LootUpgradeFrame_ArrowTemplate">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Arrow1" x="16" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Arrow3" inherits="LootUpgradeFrame_ArrowTemplate">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Arrow1" x="-16" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Arrow4" inherits="LootUpgradeFrame_ArrowTemplate">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Arrow1" x="5" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Arrow5" inherits="LootUpgradeFrame_ArrowTemplate">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Arrow1" x="-12" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="1">
				<FontString parentKey="TitleText" inherits="GameFontNormal" justifyH="LEFT">
					<Size x="148" y="16"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BaseQualityBorder" relativePoint="TOPRIGHT" x="28" y="-1"/>
					</Anchors>
				</FontString>
				<FontString parentKey="BaseQualityItemName" inherits="LootUpgradeFrame_ItemNameTemplate">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Background" x="28" y="-10"/>
					</Anchors>
					<Color r="0" g="1" b="0"/>
				</FontString>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="2">
				<FontString parentKey="UpgradeQualityItemName" inherits="LootUpgradeFrame_ItemNameTemplate">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.BaseQualityItemName"/>
					</Anchors>
					<Color r="0" g="0" b="1"/>
				</FontString>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="3">
				<FontString parentKey="WhiteText" inherits="LootUpgradeFrame_ItemNameTemplate">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.BaseQualityItemName"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="4">
				<FontString parentKey="WhiteText2" inherits="LootUpgradeFrame_ItemNameTemplate">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.BaseQualityItemName"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="Sheen" hidden="false" alpha="0" alphaMode="ADD" atlas="loottoast-sheen">
					<Size x="171" y="71"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Background" x="-80" y="-1"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="animIn" setToFinalAlpha="true">
				<Alpha startDelay="0.5" duration="0.01" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Sheen" startDelay="1.1" duration="0.18" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Sheen" startDelay="1.575" duration="0.25" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Sheen" startDelay="1.1" duration="0.725" order="1" offsetX="165" offsetY="0"/>

				<Alpha childKey="BorderGlow" startDelay="0.5" smoothing="IN" duration="0.8" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="BorderGlow" startDelay="1.3" smoothing="NONE" duration="0.8" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="UpgradeQualityBorder" startDelay="0.5" smoothing="IN" duration="0.8" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="WhiteText" startDelay="0.5" smoothing="IN" duration="0.8" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="WhiteText" startDelay="1.3" smoothing="NONE" duration="0.8" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="WhiteText2" startDelay="0.5" smoothing="IN" duration="0.8" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="WhiteText2" startDelay="1.3" smoothing="NONE" duration="0.8" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="UpgradeQualityItemName" startDelay="0.5" smoothing="IN" duration="0.8" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="BaseQualityItemName" startDelay="1.3" smoothing="NONE" duration="0.8" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="BaseQualityBorder" startDelay="2.5" duration="0.1" order="1" fromAlpha="1" toAlpha="0"/>

				<Alpha childKey="Arrow1" startDelay="0.9" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Arrow1" startDelay="1.2" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Arrow1" startDelay="0.9" duration="0.5" order="1" offsetX="0" offsetY="60"/>
				<Alpha childKey="Arrow2" startDelay="1" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Arrow2" startDelay="1.3" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Arrow2" startDelay="1" duration="0.5" order="1" offsetX="0" offsetY="60"/>
				<Alpha childKey="Arrow3" startDelay="1.1" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Arrow3" startDelay="1.4" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Arrow3" startDelay="1.1" duration="0.5" order="1" offsetX="0" offsetY="60"/>
				<Alpha childKey="Arrow4" startDelay="1.3" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Arrow4" startDelay="1.6" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Arrow4" startDelay="1.3" duration="0.5" order="1" offsetX="0" offsetY="60"/>
				<Alpha childKey="Arrow5" startDelay="1.5" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Arrow5" startDelay="1.8" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Arrow5" startDelay="1.5" duration="0.5" order="1" offsetX="0" offsetY="60" endDelay="360000"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut"/>
				<Scripts>
					<OnFinished function="LootUpgradeFrame_AnimDone"/>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Scripts>
			<OnLoad inherit="prepend">
				self.UpdateTooltip = LootAlertFrame_OnEnter;
			</OnLoad>
			<OnEnter>
				AlertFrame_StopOutAnimation(self);
				LootAlertFrame_OnEnter(self);
			</OnEnter>
			<OnLeave>
				AlertFrame_ResumeOutAnimation(self);
				GameTooltip:Hide();
			</OnLeave>
			<OnClick function="LootUpgradeFrame_OnClick"/>
		</Scripts>
	</Button>

	<Button name="DigsiteCompleteToastFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" inherits="AlertFrameTemplate" virtual="true">
		<Size x="241" y="81"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="170"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture file="Interface\Archeology\ArcheologyToast">
					<Size x="241" y="81"/>
					<Anchors>
						<Anchor point="LEFT"/>
					</Anchors>
					<TexCoords left="0.00390625" right="0.94531250" top="0.37500000" bottom="0.69140625"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="DigsiteType" inherits="GameFontWhite">
					<Anchors>
						<Anchor point="CENTER" x="28" y="-8"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Title" inherits="GameFontWhiteSmall" text="ARCHAEOLOGY_DIGSITE_COMPLETE_TOAST_FRAME_TITLE">
					<Anchors>
						<Anchor point="CENTER" x="28" y="13"/>
					</Anchors>
				</FontString>
				<Texture parentKey="DigsiteTypeTexture">
					<Size x="90" y="90"/>
					<Anchors>
						<Anchor point="LEFT" x="13" y="-14"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size x="300" y="162"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
				</Texture>
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="80"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="200" offsetY="0" duration="0.71" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnEnter>
				--Overiding the alertFrame onEnter so the this alert animates out even if the player hovers their mouse over it.
			</OnEnter>
		</Scripts>
	</Button>

	<Button name="StorePurchaseAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="FULLSCREEN_DIALOG" frameLevel="63" inherits="AlertFrameTemplate" virtual="true">
		<Size x="294" y="84"/>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<!--inherits="store-toast"-->
				<Texture parentKey="Background" file="Interface\Store\Store-Main">
					<TexCoords left="0.50878906" right="0.79589844" top="0.54199219" bottom="0.62402344"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Icon">
					<Size x="44" y="44"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent" relativePoint="LEFT" x="21" y="0"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Title" inherits="GameFontNormalLarge" text="BLIZZARD_STORE_PURCHASE_COMPLETE" justifyV="MIDDLE">
					<Size x="200" y="24"/>
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent" relativePoint="TOP" x="30" y="-15"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Description" inherits="GameFontHighlight" text="BLIZZARD_STORE_PURCHASE_COMPLETE_DESC">
					<Anchors>
						<Anchor point="BOTTOM" relativeKey="$parent" relativePoint="BOTTOM" x="30" y="23"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size x="375" y="175"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
				</Texture>
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="85"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="200" offsetY="0" duration="0.71" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick function="StorePurchaseAlertFrame_OnClick"/>
		</Scripts>
	</Button>

	<Button name="GarrisonBuildingAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" inherits="AlertFrameTemplate" virtual="true">
		<Size x="317" y="82"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="170"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture atlas="Garr_Toast" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="-1">
				<Texture parentKey="Icon" file="Interface\Icons\Garrison_Build">
					<Size x="40" y="40"/>
					<Anchors>
						<Anchor point="LEFT" relativePoint="LEFT" x="20" y="2"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Title" inherits="GameFontNormalSmall" text="GARRISON_UPDATE">
					<Anchors>
						<Anchor point="TOP" x="23" y="-18"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Name" inherits="GameFontHighlight">
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.Title" relativePoint="BOTTOM" y="-12"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size x="400" y="171"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
				</Texture>
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="8"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="200" offsetY="0" duration="0.85" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick function="GarrisonAlertFrame_OnClick"/>
		</Scripts>
	</Button>

	<Button name="GarrisonRandomMissionAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" inherits="AlertFrameTemplate" virtual="true">
		<Size x="317" y="82"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="170"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture parentKey="Background" atlas="Garr_MissionToast" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="2">
				<Texture parentKey="Blank" atlas="Garr_MissionToast-Blank" useAtlasSize="true">
					<Anchors>
						<Anchor point="BOTTOM" x="8" y="13"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="4">
				<Texture parentKey="IconBG" atlas="Garr_MissionToast-IconBG" useAtlasSize="true">
					<Anchors>
						<Anchor point="LEFT" x="10" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="3">
				<Texture parentKey="MissionType" atlas="GarrMission_MissionIcon-Combat" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPLEFT" x="14" y="-8"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString inherits="SystemFont_Med3" text="GARRISON_MISSION_ADDED_TOAST1">
					<Anchors>
						<Anchor point="TOP" x="20" y="-18"/>
					</Anchors>
					<Color r="1.0" g="0.82" b="0.0"/>
				</FontString>
				<FontString inherits="Game24Font" text="GARRISON_MISSION_ADDED_TOAST2">
					<Anchors>
						<Anchor point="TOP" x="20" y="-34"/>
					</Anchors>
					<Color r="1.0" g="0.82" b="0.0"/>
				</FontString>
				<FontString parentKey="Level" inherits="Game24Font">
					<Anchors>
						<Anchor point="TOP" x="-115" y="-14"/>
					</Anchors>
					<Color r="0.82" g="0.69" b="0.53"/>
				</FontString>
				<FontString parentKey="ItemLevel" inherits="InvoiceFont_Small">
					<Anchors>
						<Anchor point="TOP" x="-115" y="-37"/>
					</Anchors>
					<Color r="0.82" g="0.69" b="0.53"/>
				</FontString>
				<FontString parentKey="Rare" inherits="MailFont_Large" text="GARRISON_MISSION_RARE">
					<Anchors>
						<Anchor point="TOP" x="-115" y="-48"/>
					</Anchors>
					<Color r="0.098" g="0.537" b="0.969" a="1"/>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size x="400" y="171"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
				</Texture>
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="8"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="200" offsetY="0" duration="0.85" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
	</Button>

	<Button name="GarrisonMissionAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" inherits="AlertFrameTemplate" virtual="true">
		<Size x="317" y="82"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="170"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="ARTWORK">
				<FontString parentKey="Title" inherits="GameFontNormalSmall" text="GARRISON_MISSION_COMPLETE">
					<Anchors>
						<Anchor point="TOP" x="20" y="-18"/>
					</Anchors>
				</FontString>
				<Texture parentKey="MissionType" atlas="GarrMission_MissionIcon-Combat" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPLEFT" x="14" y="-8"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size x="400" y="171"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
				</Texture>
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="8"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="200" offsetY="0" duration="0.85" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick function="GarrisonAlertFrame_OnClick"/>
			<OnEnter>
				if ( self.Name:IsTruncated() ) then
					GameTooltip:SetOwner(self, "ANCHOR_TOP", 0, -8);
					GameTooltip:SetText(self.Name:GetText(), 1, 1, 1, 1, 1);
				end
				AlertFrame_StopOutAnimation(self);
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
				AlertFrame_ResumeOutAnimation(self);
			</OnLeave>
		</Scripts>
	</Button>

	<Button name="GarrisonFollowerAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" inherits="AlertFrameTemplate" virtual="true">
		<Size x="317" y="82"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="170"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BORDER">
				<Texture parentKey="FollowerBG" atlas="Garr_FollowerToast-Epic" useAtlasSize="true" hidden="true">
					<Anchors>
						<Anchor point="BOTTOM" x="22" y="23"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="DieIcon" file="Interface\Buttons\UI-GroupLoot-Dice-Up" hidden="true">
					<Size x="22" y="22"/>
					<Anchors>
						<!-- Anchors set in Lua -->
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size x="400" y="171"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
				</Texture>
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="8"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="200" offsetY="0" duration="0.85" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="Arrows" setAllPoints="true">
				<KeyValues>
					<KeyValue key="numArrows" value="5" type="number"/>
				</KeyValues>
				<Layers>
					<Layer level="ARTWORK">
						<Texture parentKey="Arrow1" inherits="LootUpgradeFrame_ArrowTemplate">
							<Anchors>
								<Anchor point="TOPLEFT" x="32" y="-30"/>
							</Anchors>
						</Texture>
						<Texture parentKey="Arrow2" inherits="LootUpgradeFrame_ArrowTemplate">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.Arrow1" x="16" y="0"/>
							</Anchors>
						</Texture>
						<Texture parentKey="Arrow3" inherits="LootUpgradeFrame_ArrowTemplate">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.Arrow1" x="-16" y="0"/>
							</Anchors>
						</Texture>
						<Texture parentKey="Arrow4" inherits="LootUpgradeFrame_ArrowTemplate">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.Arrow1" x="5" y="0"/>
							</Anchors>
						</Texture>
						<Texture parentKey="Arrow5" inherits="LootUpgradeFrame_ArrowTemplate">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.Arrow1" x="-12" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Animations>
					<AnimationGroup parentKey="ArrowsAnim">
						<Alpha childKey="Arrow1" startDelay="0.9" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="Arrow1" startDelay="1.2" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
						<Translation childKey="Arrow1" startDelay="0.9" duration="0.5" order="1" offsetX="0" offsetY="60"/>
						<Alpha childKey="Arrow2" startDelay="1" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="Arrow2" startDelay="1.3" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
						<Translation childKey="Arrow2" startDelay="1" duration="0.5" order="1" offsetX="0" offsetY="60"/>
						<Alpha childKey="Arrow3" startDelay="1.1" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="Arrow3" startDelay="1.4" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
						<Translation childKey="Arrow3" startDelay="1.1" duration="0.5" order="1" offsetX="0" offsetY="60"/>
						<Alpha childKey="Arrow4" startDelay="1.3" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="Arrow4" startDelay="1.6" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
						<Translation childKey="Arrow4" startDelay="1.3" duration="0.5" order="1" offsetX="0" offsetY="60"/>
						<Alpha childKey="Arrow5" startDelay="1.5" smoothing="IN" duration="0.2" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="Arrow5" startDelay="1.8" smoothing="OUT" duration="0.2" order="1" fromAlpha="1" toAlpha="0"/>
						<Translation childKey="Arrow5" startDelay="1.5" duration="0.5" order="1" offsetX="0" offsetY="60"/>
					</AnimationGroup>
				</Animations>
			</Frame>
		</Frames>
		<Scripts>
			<OnEnter function="GarrisonFollowerAlertFrame_OnEnter"/>
			<OnLeave function="GarrisonFollowerAlertFrame_OnLeave"/>
			<OnClick function="GarrisonFollowerAlertFrame_OnClick"/>
		</Scripts>
	</Button>

	<Button name="GarrisonStandardFollowerAlertFrameTemplate" inherits="GarrisonFollowerAlertFrameTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND">
				<Texture atlas="Garr_MissionToast" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Title" inherits="GameFontNormalSmall" text="GARRISON_FOLLOWER_ADDED_TOAST">
					<Anchors>
						<Anchor point="TOP" x="20" y="-18"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Name" inherits="GameFontHighlight" mixin="ShrinkUntilTruncateFontStringMixin" maxLines="1">
					<Size x="205" y="0"/>
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.Title" relativePoint="BOTTOM" y="-10"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="PortraitFrame" inherits="GarrisonFollowerPortraitTemplate" mixin="GarrisonFollowerPortraitMixin" hidden="false">
				<Anchors>
					<Anchor point="LEFT" x="23" y="3"/>
				</Anchors>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad inherit="append">
				self.Name:SetFontObjectsToTry(GameFontHighlight, GameFontHighlightSmall, GameFontWhiteTiny);
			</OnLoad>
			<OnShow inherit="append">
				self.Name:ApplyFontObjects();
			</OnShow>
		</Scripts>
	</Button>

	<Button name="GarrisonShipFollowerAlertFrameTemplate" inherits="GarrisonFollowerAlertFrameTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Background" atlas="ShipMission_Toast" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="Portrait" atlas="Ships_Dreadnaught-List" useAtlasSize="false">
					<Size x="115" y="60"/>
					<Anchors>
						<Anchor point="LEFT" x="10"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Title" inherits="GameFontNormalSmall" text="GARRISON_FOLLOWER_ADDED_TOAST">
					<Anchors>
						<Anchor point="TOP" x="45" y="-18"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Name" inherits="GameFontHighlight">
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.Title" relativePoint="BOTTOM" y="-8"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Class" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.Name" relativePoint="BOTTOM" y="-3"/>
					</Anchors>
					<Color r="0.5" g="0.5" b="0.5"/>
				</FontString>
			</Layer>
		</Layers>
	</Button>

	<Button name="GarrisonStandardMissionAlertFrameTemplate" inherits="GarrisonMissionAlertFrameTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Background" atlas="Garr_MissionToast" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="2">
				<Texture parentKey="IconBG" atlas="Garr_MissionToast-IconBG" useAtlasSize="true">
					<Anchors>
						<Anchor point="LEFT" x="10" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Name" inherits="GameFontHighlight">
					<Size x="196" y="10"/>
					<Anchors>
						<Anchor point="TOP" x="20" y="-39"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Button>

	<Button name="GarrisonShipMissionAlertFrameTemplate" inherits="GarrisonMissionAlertFrameTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Background" atlas="ShipMission_Toast" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Name" inherits="GameFontHighlight">
					<Size x="196" y="10"/>
					<Anchors>
						<Anchor point="TOP" x="20" y="-44"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Button>

    <Button name="GarrisonTalentAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" inherits="AlertFrameTemplate" virtual="true">
		<Size x="317" y="82"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="170"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture atlas="Garr_Toast" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="-1">
				<Texture parentKey="Icon" file="Interface\Icons\Garrison_Build">
					<Size x="40" y="40"/>
					<Anchors>
						<Anchor point="LEFT" relativePoint="LEFT" x="20" y="2"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Title" inherits="GameFontNormalSmall" text="GARRISON_TALENT_ORDER_ADVANCEMENT">
					<Anchors>
						<Anchor point="TOP" x="23" y="-18"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Name" inherits="GameFontHighlight" text="GARRISON_TALENT_RESEARCH_COMPLETE">
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.Title" relativePoint="BOTTOM" y="-12"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size x="400" y="171"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
				</Texture>
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="8"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="200" offsetY="0" duration="0.85" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick function="GarrisonAlertFrame_OnClick"/>
		</Scripts>
	</Button>

	<Button name="NewRecipeLearnedAlertFrameTemplate" hidden="true" frameStrata="DIALOG" inherits="AlertFrameTemplate" virtual="true">
		<Size x="312" y="89"/>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture atlas="recipetoast-bg" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="-1">
				<Texture parentKey="Icon">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="LEFT" relativePoint="LEFT" x="18" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Title" inherits="GameFontHighlight">
					<Anchors>
						<Anchor point="TOP" x="31" y="-15"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Name" inherits="GameFontNormal" justifyH="CENTER" justifyV="MIDDLE">
					<Anchors>
						<Anchor point="TOPLEFT" x="98" y="-34"/>
						<Anchor point="BOTTOMRIGHT" x="-32" y="17"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size x="400" y="171"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.66796875"/>
				</Texture>
				<Texture file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="67" y="82"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="3"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="240" offsetY="0" duration="0.65" order="2"/>
							<Alpha startDelay="0.25" fromAlpha="1" toAlpha="0" duration="0.45" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick function="NewRecipeLearnedAlertFrame_OnClick"/>
		</Scripts>
	</Button>

	<Button name="WorldQuestCompleteAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" inherits="AlertFrameTemplate" virtual="true">
		<Size x="311" y="78"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="128"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="QuestTexture">
					<Size x="45" y="45"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="13" y="15"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture file="Interface\LFGFrame\UI-LFG-DUNGEONTOAST">
					<Size x="70" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.546875" top="0.28515" bottom="0.5664"/>
				</Texture>
				<Texture file="Interface\LFGFrame\UI-LFG-DUNGEONTOAST">
					<Size x="15" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
					<TexCoords left="0.5546875" right="0.671875" top="0.28515" bottom="0.5664"/>
				</Texture>
				<Texture file="Interface\LFGFrame\UI-LFG-DUNGEONTOAST">
					<Size x="127" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="69" y="0"/>
					</Anchors>
					<TexCoords left="0" right="0.9921875" top="0" bottom="0.28125"/>
				</Texture>
				<Texture file="Interface\LFGFrame\UI-LFG-DUNGEONTOAST">
					<Size x="127" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="-14" y="0"/>
					</Anchors>
					<TexCoords left="0" right="0.9921875" top="0.58203" bottom="0.86328"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString inherits="GameFontWhiteTiny" text="WORLD_QUEST_COMPLETE">
					<Size x="200" y="12"/>
					<Anchors>
						<Anchor point="TOP" x="25" y="-29"/>
					</Anchors>
				</FontString>
				<FontString parentKey="QuestName" inherits="GameFontNormal" wordwrap="false">
					<Size x="214"/>
					<Anchors>
						<Anchor point="TOP" x="25" y="-44"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="shine" file="Interface\AchievementFrame\UI-Achievement-Alert-Glow" alphaMode="ADD" hidden="true">
					<Size x="67" y="72"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Translation offsetX="260" offsetY="0" duration="0.85" order="2"/>
							<Alpha startDelay="0.35" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.78125" right="0.912109375" top="0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
	</Button>

	<Button name="LegendaryItemAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" inherits="AlertFrameTemplate" virtual="true">
		<Size x="302" y="119"/>
		<Layers>
			<Layer level="BORDER">
				<Texture parentKey="Icon">
					<Size x="52" y="52"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="48" y="-32"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="1">
				<Texture parentKey="Background" hidden="false" alpha="0" alphaMode="BLEND" atlas="LegendaryToast-background" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND">
				<Texture parentKey="Ring1" hidden="false" alpha="0" alphaMode="BLEND" atlas="LegendaryToast-ring1" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="-80" y="-4"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture parentKey="Particles3" hidden="false" alpha="0" alphaMode="BLEND" atlas="LegendaryToast-particles3" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="-80" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="2">
				<Texture parentKey="Particles2" hidden="false" alpha="0" alphaMode="BLEND" atlas="LegendaryToast-particles2" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="-80" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="3">
				<Texture parentKey="Particles1" hidden="false" alpha="0" alphaMode="BLEND" atlas="LegendaryToast-particles1" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="-80" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="4">
				<Texture parentKey="Starglow" hidden="false" alpha="0" alphaMode="ADD" atlas="LegendaryToast-OrangeStarglow">
					<Size x="230" y="230"/>
					<Anchors>
						<Anchor point="CENTER" x="-80" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Background2" alpha="0" alphaMode="ADD" atlas="LegendaryToast-background" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn" setToFinalAlpha="true">
							<Alpha startDelay="0.6" duration="0.5" fromAlpha="0" toAlpha="1"/>
							<Alpha startDelay="1.1" duration="0.5" fromAlpha="1" toAlpha="0"/>
						</AnimationGroup>
					</Animations>
				</Texture>
				<Texture parentKey="Background3" alpha="0" alphaMode="ADD" atlas="LegendaryToast-background" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn" setToFinalAlpha="true">
							<Alpha startDelay="0.6" duration="0.5" fromAlpha="0" toAlpha="1"/>
							<Alpha startDelay="1.1" duration="0.5" fromAlpha="1" toAlpha="0"/>
						</AnimationGroup>
					</Animations>
				</Texture>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="1">
				<FontString inherits="GameFontNormal" text="LEGENDARY_ITEM_LOOT_LABEL">
					<Anchors>
						<Anchor point="TOPLEFT" x="107" y="-31"/>
					</Anchors>
				</FontString>
				<FontString parentKey="ItemName" inherits="GameFontNormalMed3" justifyH="LEFT" justifyV="MIDDLE">
					<Size x="165" y="33"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Icon" relativePoint="TOPRIGHT" x="11" y="-16"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<!--LootWin-Glow-->
				<Texture atlas="loottoast-glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size>
						<AbsDimension x="298" y="109"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER" x="10" y="1"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
				</Texture>
				<!--LootWin-Sheen-->
				<Texture file="Interface\LootFrame\LootToast" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="171" y="75"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="10" y="24"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.1" order="1"/>
							<Translation offsetX="165" offsetY="0" duration="0.425" order="2"/>
							<Alpha startDelay="0.175" fromAlpha="1" toAlpha="0" duration="0.25" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.56347656" right="0.73046875" top="0.57421875" bottom="0.86718750"/>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="animIn" setToFinalAlpha="true">
				<Alpha childKey="Background" duration="0.4" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Icon" duration="0.4" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Particles1" startDelay="0.2" smoothing="NONE" duration="0.3" order="3" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Particles1" startDelay="0.5" smoothing="NONE" duration="0.3" order="3" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Particles2" startDelay="0.2" smoothing="NONE" duration="0.3" order="3" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Particles2" startDelay="0.5" smoothing="NONE" duration="1" order="3" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Particles3" startDelay="0.2" smoothing="NONE" duration="0.3" order="3" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Particles3" startDelay="0.5" smoothing="NONE" duration="0.3" order="3" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Ring1" startDelay="0.2" duration="0.5" order="3" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Ring1" startDelay="0.7" duration="0.5" order="3" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Starglow" startDelay="0.2" duration="0.5" order="3" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Starglow" startDelay="0.7" duration="0.5" order="3" fromAlpha="1" toAlpha="0"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Scripts>
			<OnClick function="LegendaryItemAlertFrame_OnClick"/>
			<OnEnter function="LegendaryItemAlertFrame_OnEnter"/>
			<OnLeave function="LegendaryItemAlertFrame_OnLeave"/>
		</Scripts>
	</Button>
	<Frame name="AlertFrameSystemsRegistrar" hidden="true">
		<Scripts>
			<OnLoad function="AlertFrameSystems_Register"/>
		</Scripts>
	</Frame>
	<Button name="ItemAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" virtual="true" mixin="ItemAlertFrameMixin" inherits="AlertFrameTemplate">
		<Size x="276" y="96"/>
		<Animations>
			<AnimationGroup parentKey="animIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0" order="1"/>
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="waitAndAnimOut">
				<Alpha startDelay="4.05" fromAlpha="1" toAlpha="0" duration="1.5" parentKey="animOut">
					<Scripts>
						<OnFinished>
							self:GetRegionParent():Hide();
						</OnFinished>
					</Scripts>
				</Alpha>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BORDER">
				<Texture parentKey="Icon">
					<Size x="52" y="52"/>
					<Anchors>
						<Anchor point="LEFT" x="23" y="-2"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="IconBorder" atlas="loottoast-itemborder-green">
					<Size x="60" y="60"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Icon" relativePoint="CENTER" x="0" y="0"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Label" inherits="GameFontNormal" justifyH="LEFT" text="YOU_EARNED_LABEL">
					<Size x="167" y="16"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Icon" relativePoint="TOPRIGHT" x="7" y="5"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Name" inherits="GameFontNormalMed3" justifyH="LEFT" justifyV="MIDDLE">
					<Size x="167" y="33"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Icon" relativePoint="TOPRIGHT" x="10" y="-16"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<!--LootWin-Glow-->
				<Texture atlas="loottoast-glow" alphaMode="ADD" hidden="true" parentKey="glow">
					<Size>
						<AbsDimension x="286" y="109"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
							<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
				</Texture>
				<!--LootWin-Sheen-->
				<Texture file="Interface\LootFrame\LootToast" alphaMode="ADD" hidden="true" parentKey="shine">
					<Size x="171" y="75"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-10" y="12"/>
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="animIn">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.1" order="1"/>
							<Translation offsetX="165" offsetY="0" duration="0.425" order="2"/>
							<Alpha startDelay="0.175" fromAlpha="1" toAlpha="0" duration="0.25" order="2"/>
							<Scripts>
								<OnFinished>
									self:GetParent():Hide();
								</OnFinished>
							</Scripts>
						</AnimationGroup>
					</Animations>
					<TexCoords left="0.56347656" right="0.73046875" top="0.57421875" bottom="0.86718750"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick method="OnClick"/>
		</Scripts>
	</Button>
	
	<Button name="NewPetAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" virtual="true" mixin="NewPetAlertFrameMixin" inherits="ItemAlertFrameTemplate">
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture parentKey="Background" atlas="PetToast-background" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Button>
	
	<Button name="NewMountAlertFrameTemplate" parent="UIParent" hidden="true" frameStrata="DIALOG" virtual="true" mixin="NewMountAlertFrameMixin" inherits="ItemAlertFrameTemplate">
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture parentKey="Background" atlas="MountToast-background" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Button>
</Ui>
