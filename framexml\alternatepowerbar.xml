<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="AlternatePowerBar.lua"/>
	<StatusBar name="AlternatePowerBarTemplate" inherits="TextStatusBar" virtual="true">
		<Size>
			<AbsDimension x="78" y="12"/>
		</Size>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentBackground" setAllPoints="true" parentKey="DefaultBackground">
					<Color r="0" g="0" b="0" a="0.5"/>
				</Texture>
				<Texture name="$parentBackground" file="Interface\PlayerFrame\MonkManaBar" parentKey="MonkBackground" hidden="true">
					<Size>
						<AbsDimension x="128" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" x="-17" y="0"/>
					</Anchors>
					<TexCoords left="0" right="1" top="0.5" bottom="1"/>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentBorder" file="Interface\CharacterFrame\UI-CharacterFrame-GroupIndicator" parentKey="DefaultBorder">
					<Size>
						<AbsDimension x="0" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" x="4" y="0"/>
						<Anchor point="TOPRIGHT" x="-4" y="0"/>
					</Anchors>
					<TexCoords left="0.125" right="0.250" top="1.0" bottom="0.0"/>
				</Texture>
				<Texture name="$parentLeftBorder" file="Interface\CharacterFrame\UI-CharacterFrame-GroupIndicator" parentKey="DefaultBorderLeft">
					<Size>
						<AbsDimension x="16" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="RIGHT" relativeTo="$parentBorder" relativePoint="LEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.125" top="1.0" bottom="0.0"/>
				</Texture>
				<Texture name="$parentRightBorder" file="Interface\CharacterFrame\UI-CharacterFrame-GroupIndicator" parentKey="DefaultBorderRight">
					<Size>
						<AbsDimension x="16" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentBorder" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.125" right="0" top="1.0" bottom="0.0"/>
				</Texture>
				<Texture name="$parentBorder" file="Interface\PlayerFrame\MonkManaBar" parentKey="MonkBorder" hidden="true">
					<Size>
						<AbsDimension x="128" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" x="-17" y="0"/>
					</Anchors>
					<TexCoords left="0" right="1" top="0" bottom="0.5"/>
				</Texture>
				<FontString name="$parentText" inherits="TextStatusBarText">
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				<FontString parentKey="LeftText" inherits="TextStatusBarText">
					<Anchors>
						<Anchor point="LEFT" x="0" y="0"/>
					</Anchors>
				</FontString>
				<FontString parentKey="RightText" inherits="TextStatusBarText">
					<Anchors>
						<Anchor point="RIGHT" x="0" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad function="AlternatePowerBar_OnLoad"/>
			<OnEvent>
				AlternatePowerBar_OnEvent(self, event, ...);
				TextStatusBar_OnEvent(self, event, ...);
			</OnEvent>
			<OnUpdate>
				AlternatePowerBar_OnUpdate(self, elapsed);
			</OnUpdate>
			<OnMouseUp>
				self:GetParent():Click(button);
			</OnMouseUp>
		</Scripts>
		<BarTexture file="Interface\TargetingFrame\UI-StatusBar"/>
		<BarColor r="0" g="0" b="1.0"/>
	</StatusBar>
	<StatusBar name="PlayerFrameAlternateManaBar" inherits="AlternatePowerBarTemplate" parent="PlayerFrame">	<!--For Druids in forms -->
		<Size x="104" y="12"/>
		<Anchors>
			<Anchor point="BOTTOMLEFT">
				<Offset>
					<AbsDimension x="114" y="23"/>
				</Offset>
			</Anchor>
		</Anchors>
	</StatusBar>
</Ui>
