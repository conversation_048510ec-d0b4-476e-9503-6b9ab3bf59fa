<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="ArtifactToasts.lua"/>

	<Frame name="ArtifactLevelUpToast" parent="UIParent" hidden="true" mixin="ArtifactLevelUpToastMixin">
		<Size x="128" y="128"/>
		<Anchors>
			<Anchor point="TOP" x="0" y="-123"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="ToastBG" alpha="0" atlas="AftLevelup-ToastBG" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.GlowLine" relativePoint="CENTER" y="-15"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="GlowLine" alpha="0" atlas="AftLevelup-GlowLine" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.IconFrame" x="0" y="-12"/>
					</Anchors>
				</Texture>
				<Texture parentKey="GlowLineBottom" alpha="0" atlas="AftLevelup-GlowLine" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine"/>
					</Anchors>
				</Texture>
				<Texture parentKey="GlowLineBottomBurst" alpha="0" alphaMode="ADD" atlas="AftLevelup-GlowLine" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="0" y="-77"/>
					</Anchors>
				</Texture>
				<Texture parentKey="LineBurst1" alpha="0" atlas="AftLevelup-Lines1" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="-30" y="-10"/>
					</Anchors>
				</Texture>
				<Texture parentKey="LineBurst2" alpha="0" atlas="AftLevelup-Lines2" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="20" y="-15"/>
					</Anchors>
				</Texture>
				<Texture parentKey="LineBurst3" alpha="0" atlas="AftLevelup-Lines1" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="50" y="15"/>
					</Anchors>
				</Texture>
				<Texture parentKey="LineBurst4" alpha="0" atlas="AftLevelup-Lines2" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="-50" y="15"/>
					</Anchors>
				</Texture>
				<Texture parentKey="LineBurst5" alpha="0" atlas="AftLevelup-Lines2" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="50" y="15"/>
					</Anchors>
				</Texture>
				<Texture parentKey="CloudyLineRight" alpha="0" atlas="AftLevelup-CloudyLineRight" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="20" y="-2"/>
					</Anchors>
				</Texture>
				<Texture parentKey="CloudyLineRMover" alpha="0" alphaMode="ADD" atlas="AftLevelup-CloudyLineLeft" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="-20" y="-2"/>
					</Anchors>
				</Texture>
				<Texture parentKey="CloudyLineLeft" alpha="0" atlas="AftLevelup-CloudyLineLeft" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="-20" y="-2"/>
					</Anchors>
				</Texture>
				<Texture parentKey="CloudyLineLMover" alpha="0" alphaMode="ADD" atlas="AftLevelup-CloudyLineRight" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="20" y="-2"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BottomLineLeft" alpha="0" atlas="AftLevelup-CloudyLineLeft">
					<Size x="255" y="24"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="-40" y="-77"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BottomLineRight" alpha="0" atlas="AftLevelup-CloudyLineRight">
					<Size x="255" y="24"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="40" y="-77"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Stars1" alpha="0" atlas="AftLevelup-Dots1" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="50" y="-5"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Stars2" alpha="0" atlas="AftLevelup-Dots2" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="-50" y="-5"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="1">
				<Texture parentKey="Icon" alpha="0">
					<Size x="48" y="48"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="2">
				<Texture parentKey="IconFrame" alpha="0" atlas="AftLevelup-IconFrame" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="3">
				<Texture parentKey="IconGlow" alpha="0" alphaMode="ADD" atlas="AftLevelup-IconGlow" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
				<Texture parentKey="IconGlowBurst" alpha="0" alphaMode="ADD" atlas="AftLevelup-IconGlow" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="IconStarBurst" alpha="0" alphaMode="ADD" atlas="ArtifactsFX-StarBurst" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.IconFrame" x="0" y="-16"/>
					</Anchors>
				</Texture>
				<Texture parentKey="WhiteIconGlow" alpha="0" atlas="AftLevelup-WhiteIconGlow" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
				<Texture parentKey="WhiteStarBurst" alpha="0" atlas="AftLevelup-WhiteStarBurst" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
				<FontString parentKey="ArtifactName" inherits="QuestFont_Enormous" justifyH="CENTER" justifyV="MIDDLE">
					<Size x="0" y="0"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.GlowLine" x="0" y="-37"/>
					</Anchors>
					<Color r=".901" g=".8" b=".501" />
					<Shadow>
						<Offset>
							<AbsDimension x="1" y="-1"/>
						</Offset>
						<Color r="0.0" g="0.0" b="0.0"/>
					</Shadow>
				</FontString>
				<FontString parentKey="NewTrait" text="ARTIFACT_TOAST_NEW_POWER_AVAILABLE" inherits="GameFontNormal" justifyH="CENTER" justifyV="MIDDLE">
					<Size x="0" y="0"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="-70"/>
					</Anchors>
					<Color r="0" g="1" b="0"/>
				</FontString>
				<FontString parentKey="UnlockTrait" text="ARTIFACT_TOAST_RETURN_TO_FORGE" inherits="GameFontNormal" justifyH="CENTER" justifyV="MIDDLE">
					<Size x="0" y="0"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="-104"/>
					</Anchors>
					<Color r="1" g="1" b="1"/>
				</FontString>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="ArtifactLevelUpAnim" setToFinalAlpha="true">
				<Alpha childKey="IconFrame" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Icon" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="IconGlow" duration="0.5" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="IconGlowBurst" startDelay="0.1" smoothing="IN" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="IconGlowBurst" smoothing="IN" duration="0.3" order="1" fromScaleX="0.5" fromScaleY="0.5" toScaleX="1.15" toScaleY="1.15"/>
				<Alpha childKey="IconGlowBurst" startDelay="0.45" duration="0.5" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="IconStarBurst" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="IconStarBurst" smoothing="IN" duration="0.4" order="1" fromScaleX="0.5" fromScaleY="0.5" toScaleX="4" toScaleY="1.5"/>
				<Alpha childKey="IconStarBurst" startDelay="0.25" duration="0.25" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="WhiteIconGlow" smoothing="IN" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="WhiteIconGlow" startDelay="0.3" smoothing="OUT" duration="0.25" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="WhiteStarBurst" smoothing="IN" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="WhiteStarBurst" startDelay="0.3" smoothing="OUT" duration="0.25" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="GlowLine" startDelay="0.25" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="GlowLine" startDelay="0.25" smoothing="OUT" duration="0.25" order="1" fromScaleX="0.001" fromScaleY="1" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="GlowLineBottom" startDelay="0.25" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="GlowLineBottom" startDelay="0.25" smoothing="OUT" duration="0.25" order="1" fromScaleX="0.001" fromScaleY="1" toScaleX="1" toScaleY="1"/>
				<Translation childKey="GlowLineBottom" startDelay="0.6" smoothing="IN_OUT" duration="0.35" order="1" offsetX="0" offsetY="-77"/>
				<Alpha childKey="ToastBG" startDelay="0.6" duration="0.35" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="ToastBG" startDelay="0.6" smoothing="IN_OUT" duration="0.35" order="1" fromScaleX="1" fromScaleY="0.001" toScaleX="1" toScaleY="1">
					<Origin point="TOP">
						<Offset x="0" y="0"/>
					</Origin>
				</Scale>
				<Alpha childKey="GlowLineBottomBurst" startDelay="0.9" smoothing="OUT" duration="0.3" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="GlowLineBottomBurst" startDelay="0.8" smoothing="OUT" duration="1" order="1" fromScaleX="0.75" fromScaleY="1" toScaleX="1.1" toScaleY="1"/>
				<Alpha childKey="GlowLineBottomBurst" startDelay="1.25" smoothing="IN" duration="0.75" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="LineBurst1" duration="0.5" order="1" fromAlpha="0" toAlpha="1"/>
				<Translation childKey="LineBurst1" smoothing="IN_OUT" duration="0.75" order="1" offsetX="0" offsetY="25"/>
				<Alpha childKey="LineBurst1" duration="0.75" order="1" fromAlpha="1" toAlpha="0"/>
				<Scale childKey="LineBurst1" smoothing="IN_OUT" duration="0.25" order="1" fromScaleX="1" fromScaleY="0.001" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="LineBurst2" startDelay="0.2" duration="0.5" order="1" fromAlpha="0" toAlpha="1"/>
				<Translation childKey="LineBurst2" startDelay="0.2" smoothing="IN_OUT" duration="0.75" order="1" offsetX="0" offsetY="20"/>
				<Alpha childKey="LineBurst2" startDelay="0.2" duration="0.75" order="1" fromAlpha="1" toAlpha="0"/>
				<Scale childKey="LineBurst2" startDelay="0.2" smoothing="IN_OUT" duration="0.25" order="1" fromScaleX="1" fromScaleY="0.001" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="LineBurst3" startDelay="0.15" duration="0.5" order="1" fromAlpha="0" toAlpha="1"/>
				<Translation childKey="LineBurst3" startDelay="0.15" smoothing="IN_OUT" duration="0.75" order="1" offsetX="0" offsetY="15"/>
				<Alpha childKey="LineBurst3" startDelay="0.15" duration="0.75" order="1" fromAlpha="1" toAlpha="0"/>
				<Scale childKey="LineBurst3" startDelay="0.15" smoothing="IN_OUT" duration="0.25" order="1" fromScaleX="1" fromScaleY="0.001" toScaleX="1" toScaleY="0.75"/>
				<Alpha childKey="LineBurst4" startDelay="0.5" duration="0.5" order="1" fromAlpha="0" toAlpha="1"/>
				<Translation childKey="LineBurst4" startDelay="0.5" smoothing="IN_OUT" duration="0.75" order="1" offsetX="0" offsetY="15"/>
				<Alpha childKey="LineBurst4" startDelay="0.5" duration="0.75" order="1" fromAlpha="1" toAlpha="0"/>
				<Scale childKey="LineBurst4" startDelay="0.5" smoothing="IN_OUT" duration="0.25" order="1" fromScaleX="1" fromScaleY="0.001" toScaleX="1" toScaleY="0.75"/>
				<Alpha childKey="LineBurst5" startDelay="0.5" duration="0.5" order="1" fromAlpha="0" toAlpha="1"/>
				<Translation childKey="LineBurst5" startDelay="0.5" smoothing="IN_OUT" duration="0.75" order="1" offsetX="0" offsetY="10"/>
				<Alpha childKey="LineBurst5" startDelay="0.5" duration="0.75" order="1" fromAlpha="1" toAlpha="0"/>
				<Scale childKey="LineBurst5" startDelay="0.5" smoothing="IN_OUT" duration="0.25" order="1" fromScaleX="1" fromScaleY="0.001" toScaleX="1" toScaleY="0.5"/>
				<Alpha childKey="Stars1" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="Stars1" duration="0.5" order="1" fromScaleX="1" fromScaleY="1" toScaleX="1.25" toScaleY="1.25"/>
				<Alpha childKey="Stars1" startDelay="0.75" duration="0.5" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Stars1" smoothing="IN_OUT" duration="1.5" order="1" offsetX="0" offsetY="40"/>
				<Alpha childKey="Stars2" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="Stars2" duration="0.5" order="1" fromScaleX="1" fromScaleY="1" toScaleX="1.25" toScaleY="1.25"/>
				<Alpha childKey="Stars2" startDelay="0.75" duration="0.5" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="Stars2" smoothing="IN_OUT" duration="1.5" order="1" offsetX="0" offsetY="45"/>
				<Alpha childKey="CloudyLineRight" smoothing="IN" duration="0.75" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="CloudyLineRight" smoothing="OUT" duration="0.5" order="1" fromScaleX="0.25" fromScaleY="0.001" toScaleX="0.75" toScaleY="1"/>
				<Alpha childKey="CloudyLineRMover" smoothing="IN" duration="0.75" order="1" fromAlpha="0" toAlpha="0.5"/>
				<Translation childKey="CloudyLineRMover" smoothing="OUT" duration="6" order="1" offsetX="40" offsetY="0"/>
				<Alpha childKey="CloudyLineLeft" smoothing="IN" duration="0.75" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="CloudyLineLeft" smoothing="OUT" duration="0.5" order="1" fromScaleX="0.25" fromScaleY="0.001" toScaleX="0.75" toScaleY="1"/>
				<Alpha childKey="CloudyLineLMover" smoothing="IN" duration="0.75" order="1" fromAlpha="0" toAlpha="0.5"/>
				<Translation childKey="CloudyLineLMover" smoothing="OUT" duration="6" order="1" offsetX="-40" offsetY="0"/>
				<Alpha childKey="BottomLineLeft" startDelay="0.95" smoothing="OUT" duration="0.3" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="BottomLineLeft" startDelay="0.9" smoothing="OUT" duration="2" order="1" fromScaleX="1" fromScaleY="1" toScaleX="1" toScaleY="1.75" />

				<Translation childKey="BottomLineLeft" startDelay="0.95" duration="3" order="1" offsetX="25" offsetY="0"/>
				<Alpha childKey="BottomLineRight" startDelay="0.95" smoothing="OUT" duration="0.3" order="1" fromAlpha="0" toAlpha="1"/>
				<Translation childKey="BottomLineRight" startDelay="0.95" duration="3" order="1" offsetX="-25" offsetY="0"/>
				<Scale childKey="BottomLineRight" startDelay="0.9" smoothing="OUT" duration="2" order="1" fromScaleX="1" fromScaleY="1" toScaleX="1" toScaleY="1.75" />

				<Alpha childKey="ArtifactName" startDelay="0.75" smoothing="OUT" duration="0.5" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="NewTrait" startDelay="0.85" smoothing="OUT" duration="0.5" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="UnlockTrait" startDelay="1.25" smoothing="OUT" duration="0.5" order="1" fromAlpha="0" toAlpha="1"/>
				
				<Alpha startDelay="4" duration="0.75" order="3" fromAlpha="1" toAlpha="0"/>

				<Scripts>
					<OnFinished>
						self:GetParent():OnAnimFinished();
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Scripts>
			<OnLoad method="OnLoad" />
			<OnEvent method="OnEvent" />
		</Scripts>
	</Frame>
</Ui>
