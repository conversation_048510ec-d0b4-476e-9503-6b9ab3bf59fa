<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<!-- if you change something here you probably want to change the glue version too -->

	<Script file="AudioOptionsFrame.lua"/>

	<Frame name="AudioOptionsFrame" inherits="OptionsFrameTemplate">
		<Frames>
			<Button name="$parentCancel" inherits="UIPanelButtonTemplate" text="CANCEL">
				<Size>
					<AbsDimension x="96" y="22"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="-16" y="16"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="AudioOptionsFrameCancel_OnClick"/>
				</Scripts>
			</Button>
			<Button name="$parentOkay" inherits="UIPanelButtonTemplate" text="OKAY">
				<Size>
					<AbsDimension x="96" y="22"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" relativeTo="$parentCancel" relativePoint="BOTTOMLEFT"/>
				</Anchors>
				<Scripts>
					<OnClick function="AudioOptionsFrameOkay_OnClick"/>
				</Scripts>
			</Button>
			<Button name="$parentDefaults" inherits="UIPanelButtonTemplate" text="DEFAULTS">
				<Size>
					<AbsDimension x="96" y="22"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="16" y="16"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
						AudioOptionsFrameDefault_OnClick(self, button);
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad function="AudioOptionsFrame_OnLoad"/>
			<OnEvent function="AudioOptionsFrame_OnEvent"/>
			<OnHide function="AudioOptionsFrame_OnHide"/>
		</Scripts>
	</Frame>
</Ui>
