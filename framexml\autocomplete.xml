<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="AutoComplete.lua"/>
	<EditBox name="AutoCompleteEditBoxTemplate" virtual="true">
		<Scripts>
			<OnTabPressed function="AutoCompleteEditBox_OnTabPressed"/>
			<OnEnterPressed function="AutoCompleteEditBox_OnEnterPressed"/>
			<OnTextChanged function="AutoCompleteEditBox_OnTextChanged"/>
			<OnChar function="AutoCompleteEditBox_OnChar"/>
			<OnEditFocusLost function="AutoCompleteEditBox_OnEditFocusLost"/>
			<OnEscapePressed function="AutoCompleteEditBox_OnEscapePressed"/>
			<OnArrowPressed function="AutoCompleteEditBox_OnArrowPressed"/>
		</Scripts>
	</EditBox>
	<Button name="AutoCompleteButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="120" y="14"/>
		</Size>
		<Scripts>
			<OnClick function="AutoCompleteButton_OnClick"/>
			<OnLoad>
				self:GetFontString():SetPoint("LEFT", self, "LEFT", 15, 0)
			</OnLoad>
		</Scripts>
		<ButtonText name="$parentText"/>
		<NormalFont style="GameFontNormal"/>
		<HighlightFont style="GameFontHighlight"/>
		<DisabledFont style="GameFontDisable"/>
		<HighlightTexture inherits="UIPanelButtonHighlightTexture"/>
	</Button>
	<Frame name="AutoCompleteBox" parent="UIParent" hidden="true">
		<Size>
			<AbsDimension x="5" y="5"/>
		</Size>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		<Backdrop bgFile="Interface\Tooltips\UI-Tooltip-Background" edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
			<EdgeSize>
				<AbsValue val="16"/>
			</EdgeSize>
			<TileSize>
				<AbsValue val="16"/>
			</TileSize>
			<BackgroundInsets>
				<AbsInset left="5" right="5" top="5" bottom="5"/>
			</BackgroundInsets>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="AutoCompleteInstructions" inherits="GameFontDisableSmall" text="PRESS_TAB">
					<Anchors>
						<Anchor point="BOTTOMLEFT">
							<Offset x="15" y="10"/>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="AutoCompleteButton1" inherits="AutoCompleteButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<!--This 10 is a magic number used in AutoComplete_Update when checking whether the AutoComplete box should be above or below the EditBox. Please update the number there if you change it here-->
							<AbsDimension x="0" y="-10"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="AutoCompleteButton2" inherits="AutoCompleteButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="AutoCompleteButton1" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Button>
			<Button name="AutoCompleteButton3" inherits="AutoCompleteButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="AutoCompleteButton2" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Button>
			<Button name="AutoCompleteButton4" inherits="AutoCompleteButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="AutoCompleteButton3" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Button>
			<Button name="AutoCompleteButton5" inherits="AutoCompleteButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="AutoCompleteButton4" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad function="AutoComplete_OnLoad"/>
		</Scripts>
	</Frame>
</Ui>