<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
    <Script file="BankFrame.lua"/>
	<Button name="BankItemButtonGenericTemplate" inherits="ItemButtonTemplate" virtual="true">
		<Layers>
			<Layer level="OVERLAY">
				<Texture parentKey="IconQuestTexture">
					<Size x="37" y="38"/>
					<Anchors>
						<Anchor point="TOP" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>	
		<Frames>
			<Cooldown name="$parentCooldown" parentKey="Cooldown" inherits="CooldownFrameTemplate"/>
		</Frames>
		<Scripts>
			<OnLoad function="BankFrameItemButton_OnLoad"/>
			<OnEnter function="BankFrameItemButton_OnEnter"/>
			<OnLeave>
				GameTooltip_Hide();
				ResetCursor();
			</OnLeave>
			<OnClick>
				if ( IsModifiedClick() ) then
					BankFrameItemButtonGeneric_OnModifiedClick(self, button);
				else
					BankFrameItemButtonGeneric_OnClick(self, button);
				end
			</OnClick>
            <OnDragStart>
                BankFrameItemButtonGeneric_OnClick(self, "LeftButton");
            </OnDragStart>
            <OnReceiveDrag>
                BankFrameItemButtonGeneric_OnClick(self, "LeftButton");
            </OnReceiveDrag>
		</Scripts>
	</Button>
	<Button name="BankItemButtonBagTemplate" inherits="ItemButtonTemplate" virtual="true">
		<Frames>
			<Cooldown name="$parentCooldown" parentKey="Cooldown" inherits="CooldownFrameTemplate"/>
			<Frame name="$parentHighlightFrame" parentKey="HighlightFrame" setAllPoints="true">
				<Layers>
					<Layer>
						<Texture name="$parentTexture" parentKey="HighlightTexture" alphaMode="ADD" file="Interface\Buttons\CheckButtonHilight" setAllPoints="true" hidden="true"/>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="BankFrameBagButton_OnLoad"/>
			<OnEvent function="BankFrameBagButton_OnEvent"/>
			<OnEnter function="BankFrameItemButton_OnEnter"/>
			<OnLeave>
				GameTooltip_Hide();
				ResetCursor();
			</OnLeave>
			<OnClick>
				if ( IsModifiedClick("PICKUPITEM") ) then
					BankFrameItemButtonBag_Pickup(self);
				else
					BankFrameItemButtonBag_OnClick(self, button);
				end
			</OnClick>
	        <OnDragStart>
				BankFrameItemButtonBag_Pickup(self, button);
			</OnDragStart>
	        <OnReceiveDrag>
				BankFrameItemButtonBag_OnClick(self, nil);
			</OnReceiveDrag>
		</Scripts>
	</Button>
	<Button name="ReagentBankItemButtonGenericTemplate" inherits="BankItemButtonGenericTemplate" virtual="true">
		<Scripts>
			<OnLoad function="ReagentBankFrameItemButton_OnLoad"/>
		</Scripts>
	</Button>
	<Button name="BankAutoSortButtonTemplate" virtual="true">
		<Size x="28" y="26"/>
		<NormalTexture atlas="bags-button-autosort-up"/>
		<PushedTexture atlas="bags-button-autosort-down"/>
		<HighlightTexture file="Interface\Buttons\ButtonHilight-Square" alphaMode="ADD">
			<Size x="24" y="23"/>
			<Anchors>
				<Anchor point="CENTER" x="0" y="0"/>
			</Anchors>
		</HighlightTexture>
		<Scripts>
			<OnEnter>
				GameTooltip:SetOwner(self);
				if(BankFrame.activeTabIndex == 1) then
					GameTooltip:SetText(BAG_CLEANUP_BANK);
				else
					GameTooltip:SetText(BAG_CLEANUP_REAGENT_BANK);
				end
				GameTooltip:Show();
			</OnEnter>
			<OnLeave function="GameTooltip_Hide"/>
		</Scripts>
	</Button>

	<Texture name="Bank-Slot-BG" file="Interface\BankFrame\Bank-Parts" virtual="true" >
		<Size x="51" y="50"/>	
		<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
	</Texture>
	<Texture name="Bank-Rivet" file="Interface\BankFrame\Bank-Parts" virtual="true" >
		<Size x="12" y="12"/>	
		<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
	</Texture>

	<Frame name="BankFrame" toplevel="true" movable="true" enableMouse="true" hidden="true" parent="UIParent" inherits="PortraitFrameTemplate">
		<Size x="386" y="415"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="BankPortraitTexture" >
					<Size x="60" y="60"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-5" y="5"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND">
				<Texture file="Interface\BankFrame\Bank-Background" horizTile="true" vertTile="true">
					<Size x="256" y="256"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="-20"/>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<FontString name="BankFrameTitleText" inherits="GameFontHighlight">
					<Anchors>
						<Anchor point="CENTER" x="-3" y="233"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="$parentTab1" inherits="CharacterFrameTabButtonTemplate" id="1" text="BANK">
                <Anchors>
					<Anchor point="TOPLEFT" relativePoint="BOTTOMLEFT" x="11" y ="2"/>
                </Anchors>
                <Scripts>
					<OnClick function="BankFrame_TabOnClick"/>
                </Scripts>
            </Button>
            <Button name="$parentTab2" inherits="CharacterFrameTabButtonTemplate" id="2" text="REAGENT_BANK">
                <Anchors>
                   <Anchor point="LEFT" relativeTo="$parentTab1" relativePoint="RIGHT" x="-15" y="0"/>
                </Anchors>
                <Scripts>
					<OnClick function="BankFrame_TabOnClick"/>
                </Scripts>
            </Button>

			<Frame name="ReagentBankHelpBox" parentKey="GlowBox" inherits="GlowBoxTemplate" hidden="false" frameStrata="DIALOG" >
				<Size x="220" y="65"/>
				<Anchors>
					<Anchor point="LEFT" relativePoint="RIGHT" relativeTo="$parentTab2" x="20" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString parentKey="BigText" inherits="GameFontHighlight" text="REAGENT_BANK_HELP" justifyH="LEFT">
							<Size x="176" y="0"/>
							<Anchors>
								<Anchor point="TOP" x="-10" y="-12"/>
							</Anchors>
						</FontString>
						<Texture parentKey="ArrowLEFT" inherits="HelpPlateArrowDOWN">
							<Size x="53" y="21"/>
							<Anchors>
								<Anchor point="RIGHT" relativePoint="LEFT" x="3" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BORDER">
						<Texture parentKey="ArrowGlowLEFT" inherits="HelpPlateArrow-GlowDOWN" alphaMode="ADD" alpha="0.5">
							<Size x="53" y="21"/>
							<Anchors>
								<Anchor point="RIGHT" relativePoint="LEFT" x="3" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						SetClampedTextureRotation(self.ArrowLEFT, 90);
						SetClampedTextureRotation(self.ArrowGlowLEFT, 90);
					</OnLoad>
				</Scripts>
				<Frames>
					<Button parentKey="CloseButton" inherits="UIPanelCloseButton">
						<Anchors>
							<Anchor point="TOPRIGHT" x="6" y="6"/>
						</Anchors>
						<Scripts>
							<OnClick>
								self:GetParent():Hide();
								SetCVarBitfield("closedInfoFrames", LE_FRAME_TUTORIAL_REAGENT_BANK_UNLOCK, true);
							</OnClick>
						</Scripts>
					</Button>
				</Frames>
			</Frame>
			
			
			<EditBox name="BankItemSearchBox" inherits="BagSearchBoxTemplate" letters="15">
	            <Size x="110" y="20"/>
	            <Anchors>
		            <Anchor point="TOPRIGHT" x="-48" y="-33"/>
	            </Anchors>
            </EditBox>
            
            <Button name="BankItemAutoSortButton" inherits="BankAutoSortButtonTemplate">
	            <Anchors>
		            <Anchor point="LEFT" relativeTo="BankItemSearchBox" relativePoint="RIGHT" x="8" y="-1"/>
	            </Anchors>
	            <Scripts>
		            <OnClick function="BankFrame_AutoSortButtonOnClick"/>
	            </Scripts>
            </Button>
			<!-- Bank Slots  -->
			<Frame name="BankSlotsFrame" setAllPoints="true" useParentLevel="true" hidden="false">
				<Layers>
					<Layer level="BORDER">
						<Texture parentKey="LeftTopCorner-Shadow" file="Interface\BankFrame\CornersShadow" virtual="true" >
							<Size x="44" y="44"/>	
							<Anchors>
								<Anchor point="TOPLEFT" x="2" y="-22"/>
							</Anchors>
							<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
						</Texture>
						<Texture parentKey="LeftBottomCorner-Shadow" file="Interface\BankFrame\CornersShadow">
							<Size x="44" y="44"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" x="2" y="27"/>
							</Anchors>
							<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
						</Texture>
						<Texture parentKey="RightTopCorner-Shadow" file="Interface\BankFrame\CornersShadow">
							<Size x="44" y="44"/>
							<Anchors>
								<Anchor point="TOPRIGHT" x="-3" y="-22"/>
							</Anchors>
							<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
						</Texture>
						<Texture parentKey="RightBottomCorner-Shadow" file="Interface\BankFrame\CornersShadow">
							<Size x="44" y="44"/>
							<Anchors>
								<Anchor point="BOTTOMRIGHT" x="-3" y="27"/>
							</Anchors>
							<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>
						</Texture>
						<Texture parentKey="Right-Shadow" file="Interface\BankFrame\VertShadow" virtual="true" vertTile="true" >
							<Size x="17" y="256"/>
							<Anchors>
								<Anchor point="TOPRIGHT" relativeKey="$parent.RightTopCorner-Shadow" relativePoint="BOTTOMRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.RightBottomCorner-Shadow" relativePoint="TOPRIGHT"/>
							</Anchors>
							<TexCoords left="0.********" right="0.********" top="0.********" bottom="1.********"/>	
						</Texture>
						<Texture parentKey="Left-Shadow" file="Interface\BankFrame\VertShadow" virtual="true" vertTile="true" >
							<Size x="17" y="256"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeKey="$parent.LeftTopCorner-Shadow" relativePoint="BOTTOMLEFT"/>
								<Anchor point="BOTTOMLEFT" relativeKey="$parent.LeftBottomCorner-Shadow" relativePoint="TOPLEFT"/>
							</Anchors>
							<TexCoords left="0.********" right="0.********" top="0.********" bottom="1.********"/>	
						</Texture>
						<Texture parentKey="Bottom-Shadow" file="Interface\BankFrame\HorizShadow" virtual="true" horizTile="true" >
							<Size x="256" y="17"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativeKey="$parent.LeftBottomCorner-Shadow" relativePoint="BOTTOMRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.RightBottomCorner-Shadow" relativePoint="BOTTOMLEFT"/>
							</Anchors>
							<TexCoords left="0.********" right="1.********" top="0.********" bottom="0.********"/>	
						</Texture>
						<Texture parentKey="Top-Shadow" file="Interface\BankFrame\HorizShadow" virtual="true" horizTile="true" >
							<Size x="256" y="17"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeKey="$parent.LeftTopCorner-Shadow" relativePoint="TOPRIGHT"/>
								<Anchor point="TOPRIGHT" relativeKey="$parent.RightTopCorner-Shadow" relativePoint="TOPLEFT"/>
							</Anchors>
							<TexCoords left="0.********" right="1.********" top="0.********" bottom="0.********"/>	
						</Texture>
						<FontString inherits="GameFontNormal" text="ITEMSLOTTEXT">
							<Anchors>
								<Anchor point="CENTER" x="-3" y="145"/>
							</Anchors>
						</FontString>
						<FontString inherits="GameFontNormal" text="BAGSLOTTEXT">
							<Anchors>
								<Anchor point="CENTER" x="-3" y="-55"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button parentKey="Item1" name="BankFrameItem1" inherits="BankItemButtonGenericTemplate" id="1">
						<Anchors>
							<Anchor point="TOPLEFT" x="28" y="-75"/>
						</Anchors>
					</Button>
					<!-- The other 27 items are created in BankFrame_OnLoad -->

					<Button parentKey="Bag1" inherits="BankItemButtonBagTemplate" id="1">
						<Anchors>
							<Anchor point="TOPLEFT" relativeKey="$parent.Item1" relativePoint="BOTTOMLEFT" x="0" y="-164"/>
						</Anchors>
					</Button>
					<Button parentKey="Bag2" inherits="BankItemButtonBagTemplate" id="2">
						<Anchors>
							<Anchor point="TOPLEFT" relativeKey="$parent.Bag1" relativePoint="TOPRIGHT" x="12" y="0"/>
						</Anchors>
					</Button>
					<Button parentKey="Bag3" inherits="BankItemButtonBagTemplate" id="3">
						<Anchors>
							<Anchor point="TOPLEFT" relativeKey="$parent.Bag2" relativePoint="TOPRIGHT" x="12" y="0"/>
						</Anchors>
					</Button>
					<Button parentKey="Bag4" inherits="BankItemButtonBagTemplate" id="4">
						<Anchors>
							<Anchor point="TOPLEFT" relativeKey="$parent.Bag3" relativePoint="TOPRIGHT" x="12" y="0"/>
						</Anchors>
					</Button>
					<Button parentKey="Bag5" inherits="BankItemButtonBagTemplate" id="5">
						<Anchors>
							<Anchor point="TOPLEFT" relativeKey="$parent.Bag4" relativePoint="TOPRIGHT" x="12" y="0"/>
						</Anchors>
					</Button>
					<Button parentKey="Bag6" inherits="BankItemButtonBagTemplate" id="6">
						<Anchors>
							<Anchor point="TOPLEFT" relativeKey="$parent.Bag5" relativePoint="TOPRIGHT" x="12" y="0"/>
						</Anchors>
					</Button>
					<Button parentKey="Bag7" inherits="BankItemButtonBagTemplate" id="7">
						<Anchors>
							<Anchor point="TOPLEFT" relativeKey="$parent.Bag6" relativePoint="TOPRIGHT" x="12" y="0"/>
						</Anchors>
					</Button>
					<Frame name="BankFramePurchaseInfo" hidden="false">
						<Size x="380" y="78"/>
						<Anchors>
							<Anchor point="CENTER" x="-18" y="-145"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<FontString inherits="GameFontHighlight" text="BANKSLOTPURCHASE_LABEL" justifyH="LEFT">
									<Size x="380" y="0"/>
									<Anchors>
										<Anchor point="CENTER" x="42" y="20"/>
									</Anchors>
								</FontString>
								<FontString name="BankFrameSlotCost" inherits="GameFontNormal" text="COSTS_LABEL">
									<Size x="0" y="12"/>
									<Anchors>
										<Anchor point="LEFT" relativePoint="LEFT" x="45" y="-10"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Button name="BankFramePurchaseButton" inherits="UIPanelButtonTemplate" virtual="true" text="BANKSLOTPURCHASE">
								<Size x="124" y="21"/>
								<Anchors>
									<Anchor point="RIGHT" relativePoint="RIGHT" x="-10" y="-10"/>
								</Anchors>
								<Scripts>
									<OnClick>
										PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
										StaticPopup_Show("CONFIRM_BUY_BANK_SLOT");
									</OnClick>
								</Scripts>
							</Button>
							<Frame name="BankFrameDetailMoneyFrame" inherits="SmallMoneyFrameTemplate" hidden="false">
								<Anchors>
									<Anchor point="LEFT" relativeTo="BankFrameSlotCost" relativePoint="RIGHT" x="10" y="0"/>
								</Anchors>
								<Scripts>
									<OnLoad>
										SmallMoneyFrame_OnLoad(self);
										MoneyFrame_SetType(self, "STATIC");
									</OnLoad>
								</Scripts>
							</Frame>
						</Frames>
					</Frame>
					<Frame name="BankFrameMoneyFrameInset" inherits="InsetFrameTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativePoint="BOTTOMLEFT" x="3" y="27"/>
							<Anchor point="BOTTOMRIGHT" x="-5" y="5"/>
						</Anchors>
					</Frame>
					<Frame name="BankFrameMoneyFrameBorder" inherits="ThinGoldEdgeTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativePoint="BOTTOMLEFT" x="5" y="25"/>
							<Anchor point="BOTTOMRIGHT" x="-8" y="6"/>
						</Anchors>
					</Frame>
					<Frame name="BankFrameMoneyFrame" inherits="SmallMoneyFrameTemplate" hidden="false">
						<Anchors>
							<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT" x="-5" y="8"/>
						</Anchors>
					</Frame>
				</Frames>
				<Scripts>
					<OnLoad function="BankSlotsFrame_OnLoad"/>
				</Scripts>
			</Frame>
			<!-- Reagent Bank Slots  -->
			<Frame name="ReagentBankFrame" setAllPoints="true" useParentLevel="true" hidden="true">
				<Layers>
					<Layer level="ARTWORK">
						<Texture parentKey="BG1" atlas="bank-slots" useAtlasSize="true" >
							<Anchors>
								<Anchor point="TOPLEFT" x="10" y="-60"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BACKGROUND">
						<Texture atlas="bank-slots-shadow" useAtlasSize="true">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.BG1" relativePoint="CENTER" x="0" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BORDER">
						<Texture parentKey="LeftTopCorner-Shadow" file="Interface\BankFrame\CornersShadow" virtual="true" >
							<Size x="44" y="44"/>	
							<Anchors>
								<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="2" y="-22"/>
							</Anchors>
							<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
						</Texture>
						<Texture parentKey="LeftBottomCorner-Shadow" file="Interface\BankFrame\CornersShadow">
							<Size x="44" y="44"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMLEFT" x="2" y="2"/>
							</Anchors>
							<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
						</Texture>
						<Texture parentKey="RightTopCorner-Shadow" file="Interface\BankFrame\CornersShadow">
							<Size x="44" y="44"/>
							<Anchors>
								<Anchor point="TOPRIGHT" relativePoint="TOPRIGHT" x="-3" y="-22"/>
							</Anchors>
							<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
						</Texture>
						<Texture parentKey="RightBottomCorner-Shadow" file="Interface\BankFrame\CornersShadow">
							<Size x="44" y="44"/>
							<Anchors>
								<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT" x="-3" y="2"/>
							</Anchors>
							<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>
						</Texture>
						<Texture parentKey="Right-Shadow" file="Interface\BankFrame\VertShadow" virtual="true" vertTile="true" >
							<Size x="17" y="256"/>
							<Anchors>
								<Anchor point="TOPRIGHT" relativeKey="$parent.RightTopCorner-Shadow" relativePoint="BOTTOMRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.RightBottomCorner-Shadow" relativePoint="TOPRIGHT"/>
							</Anchors>
							<TexCoords left="0.********" right="0.********" top="0.********" bottom="1.********"/>	
						</Texture>
						<Texture parentKey="Left-Shadow" file="Interface\BankFrame\VertShadow" virtual="true" vertTile="true" >
							<Size x="17" y="256"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeKey="$parent.LeftTopCorner-Shadow" relativePoint="BOTTOMLEFT"/>
								<Anchor point="BOTTOMLEFT" relativeKey="$parent.LeftBottomCorner-Shadow" relativePoint="TOPLEFT"/>
							</Anchors>
							<TexCoords left="0.********" right="0.********" top="0.********" bottom="1.********"/>	
						</Texture>
						<Texture parentKey="Bottom-Shadow" file="Interface\BankFrame\HorizShadow" virtual="true" horizTile="true" >
							<Size x="256" y="17"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativeKey="$parent.LeftBottomCorner-Shadow" relativePoint="BOTTOMRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.RightBottomCorner-Shadow" relativePoint="BOTTOMLEFT"/>
							</Anchors>
							<TexCoords left="0.********" right="1.********" top="0.********" bottom="0.********"/>	
						</Texture>
						<Texture parentKey="Top-Shadow" file="Interface\BankFrame\HorizShadow" virtual="true" horizTile="true" >
							<Size x="256" y="17"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeKey="$parent.LeftTopCorner-Shadow" relativePoint="TOPRIGHT"/>
								<Anchor point="TOPRIGHT" relativeKey="$parent.RightTopCorner-Shadow" relativePoint="TOPLEFT"/>
							</Anchors>
							<TexCoords left="0.********" right="1.********" top="0.********" bottom="0.********"/>	
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Button parentKey="DespositButton" inherits="UIPanelButtonTemplate" virtual="true" text="REAGENTBANK_DEPOSIT">
						<Size x="256" y="24"/>
						<Anchors>
							<Anchor point="BOTTOM" relativePoint="BOTTOM" x="0" y="12"/>
						</Anchors>
						<Scripts>
							<OnClick>
								PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
								DepositReagentBank();
							</OnClick>
						</Scripts>
					</Button>
					
					<Frame name="$parentUnlockInfo" parentKey="UnlockInfo" hidden="true">
						<Anchors>
							<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="0" y="-56"/>
							<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT" x="0" y="40"/>
						</Anchors>
						<Layers>
							<Layer level="BORDER">
								<Texture name="$parentBottomLeftInner" file="Interface\GuildBankFrame\Corners" >
									<Size x="32" y="32"/>
									<Anchors>
										<Anchor point="BOTTOMLEFT" x="4" y="0"/>
									</Anchors>
									<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
								</Texture>
								<Texture name="$parentBottomRightInner" file="Interface\GuildBankFrame\Corners" >
									<Size x="32" y="32"/>	
									<Anchors>
										<Anchor point="BOTTOMRIGHT" x="-4" y="0"/>
									</Anchors>
									<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
								</Texture>
								<Texture name="$parentTopRightInner" file="Interface\GuildBankFrame\Corners"  >
									<Size x="32" y="32"/>
									<Anchors>
										<Anchor point="TOPRIGHT" x="-4" y="0"/>
									</Anchors>
									<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
								</Texture>
								<Texture name="$parentTopLeftInner" file="Interface\GuildBankFrame\Corners">
									<Size x="32" y="32"/>	
									<Anchors>
										<Anchor point="TOPLEFT" x="4" y="0"/>
									</Anchors>
									<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
								</Texture>
								<Texture name="$parentLeftInner" file="Interface\GuildBankFrame\VertTile" vertTile="true">
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentTopLeftInner" relativePoint="BOTTOMLEFT" x="-3"/>
										<Anchor point="BOTTOMLEFT" relativeTo="$parentBottomLeftInner" relativePoint="TOPLEFT" x="-3"/>
									</Anchors>
								</Texture>
								<Texture name="$parentRightInner" file="Interface\GuildBankFrame\VertTile" vertTile="true">
									<Anchors>
										<Anchor point="TOPRIGHT" relativeTo="$parentTopRightInner" relativePoint="BOTTOMRIGHT" x="4"/>
										<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRightInner" relativePoint="TOPRIGHT" x="4"/>
									</Anchors>
								</Texture>
								<Texture name="$parentTopInner" file="Interface\GuildBankFrame\HorizTile" horizTile="true">
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentTopLeftInner" relativePoint="TOPRIGHT" y="3"/>
										<Anchor point="TOPRIGHT" relativeTo="$parentTopRightInner" relativePoint="TOPLEFT" y="3"/>
									</Anchors>
								</Texture>
								<Texture name="$parentBottomInner" file="Interface\GuildBankFrame\HorizTile" horizTile="true">
									<Anchors>
										<Anchor point="BOTTOMLEFT" relativeTo="$parentBottomLeftInner" relativePoint="BOTTOMRIGHT" y="-5"/>
										<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRightInner" relativePoint="BOTTOMLEFT" y="-5"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="BACKGROUND">
								<Texture name="$parentBlackBG">
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentTopLeftInner" x="4" y="-4"/>
										<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRightInner" x="-4" y="3"/>
									</Anchors>
									<Color r="0" g="0" b="0" a="1"/>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<FontString name="$parentText" inherits="GameFontHighlightMedium" text="REAGENTBANK_PURCHASE_TEXT" justifyV="BOTTOM">
									<Size x="512" y="32"/>
									<Anchors>
										<Anchor point="BOTTOM" relativePoint="CENTER" x="0" y="-8"/>
									</Anchors>
								</FontString>
								<FontString name="$parentTabCost" inherits="GameFontNormalMed3" text="COSTS_LABEL">
									<Size x="0" y="12"/>
									<Anchors>
										<Anchor point="CENTER" relativePoint="CENTER" x="-93" y="-48"/>
									</Anchors>
								</FontString>
								<FontString name="$parentTitle" inherits="QuestFont_Enormous" text="REAGENT_BANK" justifyV="BOTTOM">
									<Size x="384" y="0"/>
									<Anchors>
										<Anchor point="BOTTOM" relativeTo="$parentText" relativePoint="TOP" x="0" y="8"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Frame name="$parentCostMoneyFrame" parentKey="CostMoneyFrame" inherits="SmallMoneyFrameTemplate" hidden="false">
								<Anchors>
									<Anchor point="LEFT" relativeTo="$parentTabCost" relativePoint="RIGHT" x="0" y="0"/>
								</Anchors>
								<Scripts>
									<OnLoad>
										SmallMoneyFrame_OnLoad(self);
										MoneyFrame_SetType(self, "STATIC");
									</OnLoad>
								</Scripts>
							</Frame>
							<Button name="$parentPurchaseButton" inherits="UIPanelButtonTemplate" text="BANKSLOTPURCHASE">
								<Size x="124" y="21"/>
								<Anchors>
									<Anchor point="LEFT" relativeTo="$parentCostMoneyFrame" relativePoint="RIGHT" x="12" y="0"/>
								</Anchors>
								<Scripts>
									<OnClick>
										PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
										StaticPopup_Show("CONFIRM_BUY_REAGENTBANK_TAB");
									</OnClick>
								</Scripts>
							</Button>
						</Frames>
						<Scripts>
							<OnLoad function="RaiseFrameLevel"/>
						</Scripts>
					</Frame>
				</Frames>
				<Scripts>
					<OnLoad function="ReagentBankFrame_OnLoad"/>
					<OnEvent function="ReagentBankFrame_OnEvent"/>
					<OnShow function="ReagentBankFrame_OnShow"/>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="BankFrame_OnLoad"/>
			<OnEvent function="BankFrame_OnEvent"/>
			<OnShow function="BankFrame_OnShow"/>
			<OnHide function="BankFrame_OnHide"/>
		</Scripts>
	</Frame>
</Ui>
