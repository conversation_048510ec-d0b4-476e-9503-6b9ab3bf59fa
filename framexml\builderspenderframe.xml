<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="BuilderSpenderFrame.lua"/>
	
	<Frame name="BuilderSpenderFrame" virtual="true" mixin="BuilderSpender">
		<Layers>
			<Layer level="ARTWORK" textureSubLevel="0">
				<Texture parentKey="BarTexture" file="Interface\TargetingFrame\UI-StatusBar" hidden="true"/>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="1">
				<Texture file="Interface\TargetingFrame\UI-StatusBar-Glow" parentKey="LossGlowTexture" hidden="true" alphaMode="ADD"/>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="1">
				<Texture file="Interface\TargetingFrame\UI-StatusBar-Glow" parentKey="GainGlowTexture" hidden="true" alphaMode="ADD" alpha="0.75"/>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad method="OnLoad"/>
		</Scripts>
	</Frame>
	
	<Frame name="FullResourcePulseFrame" virtual="true" mixin="FullResourcePulse">
		<Size x="119" y="12"/>
		<Frames>
			<Frame parentKey="SpikeFrame">
				<Size x="119" y="12"/>
				<Anchors>
					<Anchor point="TOPRIGHT"/>
				</Anchors>
				<Layers>
					<Layer level="OVERLAY">
						<Texture parentKey="BigSpikeGlow" alpha="0" alphaMode="BLEND" atlas="FullAlert-BigSpike" useAtlasSize="true">
							<Anchors>
								<Anchor point="RIGHT" x="21" y="0"/>
							</Anchors>
						</Texture>
						<Texture parentKey="AlertSpikeStay" alpha="0" alphaMode="BLEND" atlas="FullAlert-FrameGlow" useAtlasSize="true">
							<Anchors>
								<Anchor point="RIGHT" x="11" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Animations>
					<AnimationGroup parentKey="SpikeAnim" setToFinalAlpha="true">
						<Alpha childKey="BigSpikeGlow" smoothing="IN" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
						<Scale childKey="BigSpikeGlow" smoothing="NONE" duration="0.1" order="1" fromScaleX="0.25" fromScaleY="0.75" toScaleX="1.4" toScaleY="0.75">
							<Origin point="LEFT">
								<Offset x="2" y="0"/>
							</Origin>
						</Scale>
						<Scale childKey="BigSpikeGlow" startDelay="0.2" smoothing="IN" duration="0.2" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.5" toScaleY="1">
							<Origin point="LEFT">
								<Offset x="6" y="0"/>
							</Origin>
						</Scale>
						<Alpha childKey="BigSpikeGlow" startDelay="0.3" duration="0.25" order="1" fromAlpha="1" toAlpha="0"/>
						<Alpha name="Spikey" childKey="AlertSpikeStay" smoothing="IN" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
					</AnimationGroup>
				</Animations>
			</Frame>
			<Frame parentKey="PulseFrame">
				<Size x="119" y="12"/>
				<Anchors>
					<Anchor point="TOPRIGHT"/>
				</Anchors>
				<Layers>
					<Layer level="OVERLAY">
						<Texture parentKey="YellowGlow" alpha="0" alphaMode="BLEND" atlas="FullAlert-YellowCurveGlow">
							<Size x="20" y="24"/>
							<Anchors>
								<Anchor point="RIGHT" x="-1" y="-1"/>
							</Anchors>
						</Texture>
						<Texture parentKey="SoftGlow" alpha="0" alphaMode="BLEND" atlas="FullAlert-SoftCurveGlow">
							<Size x="20" y="24"/>
							<Anchors>
								<Anchor point="RIGHT" x="-1" y="-1"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Animations>
					<AnimationGroup parentKey="PulseAnim" looping="REPEAT">
						<Alpha childKey="YellowGlow" duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
						<Translation childKey="YellowGlow" smoothing="OUT" duration="0.5" order="1" offsetX="20" offsetY="0"/>
						<Alpha childKey="YellowGlow" startDelay="0.4" duration="0.1" order="1" fromAlpha="1" toAlpha="0"/>
						<Alpha childKey="SoftGlow" duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
						<Translation childKey="SoftGlow" smoothing="OUT" duration="0.7" order="1" offsetX="20" offsetY="0"/>
						<Alpha childKey="SoftGlow" startDelay="0.6" duration="0.1" order="1" fromAlpha="1" toAlpha="0"/>
					</AnimationGroup>
				</Animations>
			</Frame>
		</Frames>
		<Animations>
			<AnimationGroup parentKey="FadeoutAnim" setToFinalAlpha="true">
				<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="1"/>
				<Scripts>
					<OnFinished>
						self:GetParent().PulseFrame.PulseAnim:Stop();
						self:GetParent().SpikeFrame.SpikeAnim:Stop();
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
	</Frame>
	
</Ui>
