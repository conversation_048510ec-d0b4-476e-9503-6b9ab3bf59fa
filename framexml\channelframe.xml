<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="ChannelFrame.lua"/>

	<Button name="ChannelRoleIconTemplate" virtual="true">
		<Size x="16" y="16"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentTexture" file="Interface\GroupFrame\UI-Group-LeaderIcon"/>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				RaiseFrameLevel(self);
			</OnLoad>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
	</Button>
	<Button name="ChannelButtonTemplate" virtual="true">
		<Size x="155" y="20"/>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentCollapsed" inherits="GameFontDisableLarge">
					<Anchors>
						<Anchor point="RIGHT" x="-5" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="$parentSpeakerFrame" inherits="VoiceChatSpeakerTemplate">
				<Size x="16" y="16"/>
				<Anchors>
					<Anchor point="LEFT" relativePoint="RIGHT" x="-20" y="1"/>
				</Anchors>
				<Scripts>
					<OnUpdate>
						VoiceChat_OnUpdate(self, elapsed);
					</OnUpdate>
					<OnClick>
						SetActiveVoiceChannel(self:GetParent():GetID());
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
				self:RegisterForDrag("LeftButton");
			</OnLoad>
			<OnClick>
				ChannelList_OnClick(self, button, down);
			</OnClick>
			<OnDragStart>
				ChannelListButton_OnDragStart(self, button);
			</OnDragStart>
			<OnDragStop>
				ChannelListButton_OnDragStop(self);
			</OnDragStop>
			<OnEnter>
				if ( self.voiceEnabled ) then
					GameTooltip_AddNewbieTip(self, DISPLAY_CHANNEL_PULLOUT, 1.0, 1.0, 1.0, string.format(NEWBIE_TOOLTIP_DISPLAY_CHANNEL_PULLOUT, (self.name or UNKNOWN)), 1);
				end
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
		<NormalTexture name="$parentNormalTexture" file="Interface\AuctionFrame\UI-AuctionFrame-FilterBg">
			<TexCoords left="0" right="0.53125" top="0" bottom="0.625"/>
		</NormalTexture>
		<HighlightTexture file="Interface\PaperDollInfoFrame\UI-Character-Tab-Highlight" alphaMode="ADD"/>
		<ButtonText name="$parentText">
			<Size x="135" y="8"/>
			<Anchors>
				<Anchor point="LEFT" x="5" y="0"/>
			</Anchors>
		</ButtonText>
		<NormalFont style="GameFontNormalSmallLeft"/>
		<HighlightFont style="GameFontHighlightSmallLeft"/>
	</Button>
	<Button name="ChannelRosterButtonTemplate" virtual="true">
		<Size x="130" y="15"/>
		<Frames>
			<Frame setAllPoints="true">
				<Layers>
					<Layer level="BORDER">
						<FontString name="$parentName" justifyH="LEFT" inherits="GameFontHighlightSmall">
							<Size y="15"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="13" y="-1"/>
								<Anchor point="TOPRIGHT" x="0" y="-1"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
			<Button name="$parentRank" inherits="ChannelRoleIconTemplate">
				<Size x="12" y="12"/>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parent" x="0" y="1"/>
				</Anchors>
				<Scripts>
					<OnEnter>
						local rank = self:GetParent().rank;
						local voice = self:GetParent().voice;
						-- Sets the Leader/Assistant Tooltip
						if ( rank ) then
							GameTooltip:SetOwner(self, "ANCHOR_LEFT");
							if ( rank == 2 ) then
								GameTooltip:SetText(RAID_LEADER, nil, nil, nil, nil, true);
							elseif ( rank == 1 ) then
								GameTooltip:SetText(RAID_ASSISTANT, nil, nil, nil, nil, true);
							end
							if ( voice == 2 ) then
								GameToolTip:AddLine("("..MUTED..")", HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b);
							end
						else
							GameTooltip:Hide();
						end
					</OnEnter>
				</Scripts>
			</Button>
			<Button name="$parentSpeakerFrame" inherits="VoiceChatSpeakerTemplate">
				<Size x="12" y="12"/>
				<Anchors>
					<Anchor point="RIGHT" x="-5" y="0"/>
				</Anchors>
				<Scripts>
					<OnUpdate>
						VoiceChat_OnUpdate(self, elapsed);
					</OnUpdate>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
				if ( ChannelRosterScrollFrameScrollBar:IsShown() ) then
					self:SetWidth(115);
				else
					self:SetWidth(135);
				end
			</OnLoad>
			<OnClick>
				ChannelRoster_OnClick(self, button, down);
			</OnClick>
			<OnEnter>
				if (_G[self:GetName().."Name"]:IsTruncated() and self.name ) then
					GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
					GameTooltip:SetText(self.name, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b);
				end
			</OnEnter>
			<OnLeave function="GameTooltip_Hide"/>
		</Scripts>
		<HighlightTexture file="Interface\QuestFrame\UI-QuestTitleHighlight" alphaMode="ADD"/>
	</Button>
	<!-- /Templates -->
	<!-- Channel Frame -->
	<Frame name="ChannelFrame" parent="FriendsFrame" hidden="true">
		<Size x="340" y="450"/>
		<Anchors>
			<Anchor point="TOPLEFT" x="0" y="0"/>
		</Anchors>
		<Frames>
			<Frame name="$parentLeftInset" inherits="InsetFrameTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" x="4" y="-60"/>
					<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMLEFT" x="167" y="52"/>
				</Anchors>
			</Frame>
			<Frame name="$parentRightInset" inherits="InsetFrameTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" x="167" y="-60"/>
					<Anchor point="BOTTOMRIGHT" x="-8" y="52"/>
				</Anchors>
			</Frame>
			<Frame name="$parentAutoJoin">
				<Size x="265" y="14"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="ChannelFrame" relativePoint="TOP" x="-100" y="-40"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString inherits="GameFontHighlightSmall" text="VOICE_CHAT">
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<CheckButton name="$parentParty" inherits="UICheckButtonTemplate">
						<Size x="24" y="24"/>
						<Anchors>
							<Anchor point="TOPLEFT" x="60" y="5"/>
						</Anchors>
						<HitRectInsets>
							<AbsInset left="0" right="-60" top="0" bottom="0"/>
						</HitRectInsets>
						<Scripts>
							<OnLoad>
								local text = _G[self:GetName().."Text"];
								text:SetText(VOICE_CHAT_PARTY_RAID);
								text:SetPoint("LEFT", self, "RIGHT", 2, 1);

								self:RegisterEvent("PLAYER_ENTERING_WORLD");
							</OnLoad>
							<OnEvent>
								if ( event == "PLAYER_ENTERING_WORLD" ) then
									self:SetChecked(GetCVar("autojoinPartyVoice"));
								end
							</OnEvent>
							<OnEnter>
								GameTooltip:SetOwner(self:GetParent(), "ANCHOR_RIGHT", -20);
								GameTooltip:SetText(AUTO_JOIN_VOICE);
								GameTooltip_AddNewbieTip(self, AUTO_JOIN_VOICE, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_AUTO_JOIN_VOICE, 1);
								GameTooltip:Show();
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
							<OnClick>
								SetCVar("autojoinPartyVoice", self:GetChecked());
								if ( self:GetChecked() ) then
									PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_OFF);
								else
									PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
								end
							</OnClick>
						</Scripts>
					</CheckButton>
					<CheckButton name="$parentBattleground" inherits="UICheckButtonTemplate">
						<Size x="24" y="24"/>
						<Anchors>
							<Anchor point="LEFT" relativeTo="$parentParty" relativePoint="RIGHT" x="70" y="0"/>
						</Anchors>
						<HitRectInsets>
							<AbsInset left="0" right="-60" top="0" bottom="0"/>
						</HitRectInsets>
						<Scripts>
							<OnLoad>
								local text = _G[self:GetName().."Text"];
								text:SetText(VOICE_CHAT_BATTLEGROUND);
								text:SetPoint("LEFT", self, "RIGHT", 2, 1);

								self:RegisterEvent("PLAYER_ENTERING_WORLD");
							</OnLoad>
							<OnEvent>
								if ( event == "PLAYER_ENTERING_WORLD" ) then
									self:SetChecked(GetCVarBool("autojoinBGVoice"));
								end
							</OnEvent>
							<OnEnter>
								GameTooltip:SetOwner(self:GetParent(), "ANCHOR_RIGHT", -20);
								GameTooltip:SetText(AUTO_JOIN_VOICE);
								GameTooltip_AddNewbieTip(self, AUTO_JOIN_VOICE, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_AUTO_JOIN_VOICE, 1);
								GameTooltip:Show();
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
							<OnClick>
								SetCVar("autojoinBGVoice", self:GetChecked());
								if ( self:GetChecked() ) then
									PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_OFF);
								else
									PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
								end
							</OnClick>
						</Scripts>
					</CheckButton>
				</Frames>
				<Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT", -20);
						GameTooltip:SetText(AUTO_JOIN_VOICE);
						GameTooltip_AddNewbieTip(self, AUTO_JOIN_VOICE, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_AUTO_JOIN_VOICE, 1);
						GameTooltip:Show();
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Frame>
			<Button name="$parentNewButton" inherits="UIPanelButtonTemplate" text="ADD">
				<Size x="80" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" x="-7" y="30"/>
				</Anchors>
				<Scripts>
					<OnClick function="ChannelFrame_New_OnClick"/>
				</Scripts>
			</Button>
<!-- Waiting for Chat System Changes
			<Button name="$parentJoinButton" inherits="UIPanelButtonTemplate" text="JOIN">
				<Size>
					<AbsDimension x="80" y="22"/>
				</Size>
				<Anchors>
					<Anchor point="RIGHT" relativeTo="$parentNewButton" relativePoint="LEFT">
						<Offset x="-3" y="0"/>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						ChannelFrame_Join_OnClick();
					</OnClick>
				</Scripts>
			</Button> 
			-->
			<!-- Channel List -->
			<ScrollFrame name="ChannelListScrollFrame" inherits="UIPanelScrollFrameTemplate">
				<Size x="158" y="327"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="7" y="-65"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture name="$parentTop" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
							<Size x="31" y="256"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="-2" y="6"/>
							</Anchors>
							<TexCoords left="0" right="0.484375" top="0" bottom="1.0"/>
						</Texture>
						<Texture name="$parentBottom" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
							<Size x="31" y="106"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="-2" y="-7"/>
							</Anchors>
							<TexCoords left="0.515625" right="1.0" top="0" bottom="0.4140625"/>
						</Texture>
					</Layer>
				</Layers>
				<ScrollChild>
					<Frame name="ChannelListScrollChildFrame">
						<Size x="145" y="400"/>
						<Anchors>
							<Anchor point="TOPLEFT" x="0" y="0"/>
						</Anchors>
						<Frames>
							<Button name="ChannelButton1" inherits="ChannelButtonTemplate" id="1">
								<Anchors>
									<Anchor point="TOPLEFT"/>
								</Anchors>
							</Button>
							<!-- Other 19 ChannelButtons are made in ChannelFrame.lua -->
						</Frames>
					</Frame>
				</ScrollChild>
			</ScrollFrame>
			<!-- /Channel List -->
			<!-- Channel Roster -->
			<Frame name="ChannelRoster">
				<Size x="158" y="327"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="ChannelFrame" relativePoint="TOP" x="121" y="-79"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString name="$parentChannelName" inherits="GameFontNormalSmall" text="Channel Name" justifyH="LEFT">
							<Anchors>
								<Anchor point="TOPLEFT" x="-115" y="20"/>
							</Anchors>
						</FontString>
						<FontString name="$parentChannelCount" inherits="GameFontNormalSmall" justifyH="LEFT">
							<Size x="30" y="13"/>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentChannelName" relativePoint="RIGHT"/>
							</Anchors>
						</FontString>
						<FontString name="$parentHiddenText" inherits="GameFontNormalSmall" hidden="true">
							<Size x="0" y="13"/>
							<Anchors>
								<Anchor point="TOPLEFT"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<ScrollFrame name="$parentScrollFrame" inherits="FauxScrollFrameTemplate">
						<Size x="158" y="333"/>
						<Anchors>
							<Anchor point="TOPRIGHT" relativeTo="ChannelFrame" x="-34" y="-63"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentTop" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
									<Size x="31" y="256"/>
									<Anchors>
										<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="-2" y="4"/>
									</Anchors>
									<TexCoords left="0" right="0.484375" top="0" bottom="1.0"/>
								</Texture>
								<Texture name="$parentBottom" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
									<Size x="31" y="106"/>
									<Anchors>
										<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="-2" y="-2"/>
									</Anchors>
									<TexCoords left="0.515625" right="1.0" top="0" bottom="0.4140625"/>
								</Texture>
							</Layer>
						</Layers>
						<Scripts>
							<OnVerticalScroll>
								FauxScrollFrame_OnVerticalScroll(self, offset, CHANNEL_ROSTER_HEIGHT, ChannelRoster_Update)
							</OnVerticalScroll>
						</Scripts>
					</ScrollFrame>
					<Button name="ChannelMemberButton1" inherits="ChannelRosterButtonTemplate" id="1">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="ChannelFrame" relativePoint="TOPLEFT" x="200" y="-65"/>
						</Anchors>
					</Button>
					<!-- Other 21 ChannelMemberButtons created in ChannelFrame.lua -->
					<Frame name="$parentDropDown" inherits="UIDropDownMenuTemplate"/>
				</Frames>
			</Frame>
			<!-- /Channel Roster -->
			<!-- Channel Create/Join Pane -->
			<Frame name="$parentDaughterFrame" hidden="true" enableKeyboard="true" enableMouse="true">
				<Size x="212" y="200"/>
				<Anchors>
					<Anchor point="CENTER" relativeTo="UIParent" x="100" y="100"/>
				</Anchors>
				<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
					<BackgroundInsets>
						<AbsInset left="11" right="12" top="12" bottom="11"/>
					</BackgroundInsets>
					<TileSize>
						<AbsValue val="32"/>
					</TileSize>
					<EdgeSize>
						<AbsValue val="32"/>
					</EdgeSize>
				</Backdrop>
				<Layers>
					<Layer level="BORDER">
						<FontString name="$parentName" inherits="GameFontNormal" text="CHANNEL_NEW_CHANNEL">
							<Size x="0" y="12"/>
							<Anchors>
								<Anchor point="TOP" x="-5" y="-13"/>
							</Anchors>
						</FontString>
						<Texture name="$parentTitlebar" file="Interface\FriendsFrame\UI-ChannelFrame-Titlebar">
							<Size x="256" y="32"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="8" y="-8"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture name="$parentCorner" file="Interface\DialogFrame\UI-DialogBox-Corner">
							<Size x="32" y="32"/>
							<Anchors>
								<Anchor point="TOPRIGHT" x="-6" y="-7"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<EditBox name="$parentChannelName" letters="31" multiLine="false" enableMouse="true" autoFocus="true" inherits="InputBoxTemplate">
						<Size x="170" y="16"/>
						<Anchors>
							<Anchor point="TOPLEFT" x="23" y="-60"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<FontString name="$parentLabel" inherits="GameFontNormal" text="CHANNEL_CHANNEL_NAME">
									<Anchors>
										<Anchor point="BOTTOMLEFT" relativePoint="TOPLEFT" x="0" y="5"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnEnterPressed>
								if ( self:GetText() ) then
									ChannelFrameDaughterFrame_Okay(self);
								else
									ChannelFrameDaughterFrame_Cancel(self);
								end
							</OnEnterPressed>
							<OnEscapePressed function="ChannelFrameDaughterFrame_Cancel"/>
							<OnTabPressed>
								EditBox_HandleTabbing(self, CHAT_CHANNEL_TABBING);
							</OnTabPressed>
						</Scripts>
					</EditBox>
					<EditBox name="$parentChannelPassword" letters="31" multiLine="false" enableMouse="true" autoFocus="false" inherits="InputBoxTemplate">
						<Size x="170" y="16"/>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentChannelName" relativePoint="BOTTOMLEFT" x="0" y="-30"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<FontString name="$parentLabel" inherits="GameFontNormal" text="PASSWORD">
									<Anchors>
										<Anchor point="BOTTOMLEFT" relativePoint="TOPLEFT" x="0" y="5"/>
									</Anchors>
								</FontString>
								<FontString name="$parentOptional" inherits="GameFontHighlightSmall" text="OPTIONAL_PARENS">
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentLabel" relativePoint="RIGHT" x="3" y="0"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnEnterPressed function="ChannelFrameDaughterFrame_Okay"/>
							<OnEscapePressed function="ChannelFrameDaughterFrame_Cancel"/>
							<OnTabPressed>
								EditBox_HandleTabbing(self, CHAT_CHANNEL_TABBING);
							</OnTabPressed>
						</Scripts>
					</EditBox>
					<!--
					<CheckButton name="$parentVoiceChat" inherits="UICheckButtonTemplate">
						<Size>
							<AbsDimension x="24" y="24"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentChannelPassword" relativePoint="BOTTOMLEFT">
								<Offset>
									<AbsDimension x="-7" y="-14"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								local text = _G[self:GetName().."Text"];
								text:SetText(ENABLE_VOICECHAT);
								text:SetPoint("LEFT", self, "RIGHT", 2, 2);
							</OnLoad>
						</Scripts>
					</CheckButton> -->
					<Button name="$parentDetailCloseButton" inherits="UIPanelCloseButton">
						<Anchors>
							<Anchor point="TOPRIGHT" x="-2" y="-3"/>
						</Anchors>
					</Button>
					<Button name="$parentOkayButton" inherits="UIPanelButtonTemplate" text="OKAY">
						<Size x="96" y="22"/>
						<Anchors>
							<Anchor point="BOTTOMLEFT" x="10" y="12"/>
						</Anchors>
						<Scripts>
							<OnClick function="ChannelFrameDaughterFrame_Okay"/>
						</Scripts>
						<NormalFont style="GameFontNormalSmall"/>
						<HighlightFont style="GameFontHighlightSmall"/>
						<DisabledFont style="GameFontDisableSmall"/>
					</Button>
					<Button name="$parentCancelButton" inherits="UIPanelButtonTemplate" text="CANCEL">
						<Size x="96" y="22"/>
						<Anchors>
							<Anchor point="LEFT" relativeTo="$parentOkayButton" relativePoint="RIGHT" x="1" y="0"/>
						</Anchors>
						<Scripts>
							<OnClick function="ChannelFrameDaughterFrame_Cancel"/>
						</Scripts>
						<NormalFont style="GameFontNormalSmall"/>
						<HighlightFont style="GameFontHighlightSmall"/>
						<DisabledFont style="GameFontDisableSmall"/>
					</Button>
				</Frames>
				<Scripts>
					<OnHide function="ChannelFrameDaughterFrame_OnHide"/>
				</Scripts>
			</Frame>
			<Frame name="ChannelListDropDown" inherits="UIDropDownMenuTemplate"/>
		</Frames>
		<Scripts>
			<OnLoad function="ChannelFrame_OnLoad"/>
			<OnShow function="ChannelFrame_Update"/>
			<OnEvent function="ChannelFrame_OnEvent"/>
			<OnUpdate function="ChannelFrame_OnUpdate"/>
			<OnHide>
				if ( ChannelFrameDaughterFrame:IsShown() ) then
					ChannelFrameDaughterFrame:Hide();		
				end
			</OnHide>
		</Scripts>
	</Frame>
	
	<Button name="ChannelPulloutRosterButtonTemplate" virtual="true">
		<Size x="150" y="14"/>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		<Frames>
			<Button name="$parentSpeaker" inherits="VoiceChatSpeakerTemplate" hidden="true">
				<Size x="13" y="13"/>
				<Anchors>
					<Anchor point="LEFT" x="4" y="0"/>
				</Anchors>
			</Button>
		</Frames>		
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentName" inherits="GameFontHighlightSmall" justifyH="LEFT" justifyV="TOP" text="">
					<Anchors>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				self.speaker = _G[self:GetName() .. "Speaker"];
				self.name = _G[self:GetName() .. "Name"];
				self.name:SetPoint("TOPLEFT", "$parentSpeaker", "TOPRIGHT", 8, -1);
				self:EnableMouseWheel();
				self:RegisterEvent("VOICE_PLATE_START");
				self:RegisterEvent("VOICE_PLATE_STOP");
				self:SetScript("OnEvent", ChannelPulloutRosterButton_OnEvent);
			</OnLoad>
			<OnMouseWheel>
				ChannelPulloutRoster_Scroll(self:GetParent(), delta);
			</OnMouseWheel>
		</Scripts>
	</Button>
	
	
	<Button name="ChannelPulloutTab" frameStrata="LOW" parent="UIParent" movable="true" hidden="true">
		<Anchors>
			<Anchor point="TOPLEFT" relativePoint="RIGHT" x="-264" y="77"/>
		</Anchors>
		<Size x="64" y="32"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentLeft" file="Interface\ChatFrame\ChatFrameTab">
					<Size x="16" y="32"/>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0.0" right="0.25" top="0.0" bottom="1.0"/>
				</Texture>
				<Texture name="$parentMiddle" file="Interface\ChatFrame\ChatFrameTab">
					<Size x="44" y="32"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.25" right="0.75" top="0.0" bottom="1.0"/>
				</Texture>
				<Texture name="$parentRight" file="Interface\ChatFrame\ChatFrameTab">
					<Size x="16" y="32"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentMiddle" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.75" right="1.0" top="0.0" bottom="1.0"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentFlash" hidden="true">
				<Size x="5" y="32"/>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentLeft" x="0" y="-7"/>
					<Anchor point="RIGHT" relativeTo="$parentRight" x="0" y="-7"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture file="Interface\PaperDollInfoFrame\UI-Character-Tab-Highlight" alphaMode="ADD" setAllPoints="true"/>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="$parentDropDown" inherits="UIDropDownMenuTemplate" id="1" hidden="true">
				<Anchors>
					<Anchor point="TOP" x="-80" y="-35"/>
				</Anchors>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self:RegisterForClicks("LeftButtonDown", "LeftButtonUp", "RightButtonUp");
				self:RegisterForDrag("LeftButton");
				self:SetAlpha(0);
				UIDropDownMenu_Initialize(_G[self:GetName().."DropDown"], ChannelPulloutTabDropDown_Initialize, "MENU");
			</OnLoad>
			<OnShow>
				PanelTemplates_TabResize(self, 0);
			</OnShow>
			<OnClick>
				ChannelPulloutTab_ReanchorLeft();
				ChannelPulloutTab_OnClick(self, button);
				PlaySound(SOUNDKIT.U_CHAT_SCROLL_BUTTON);
			</OnClick>
			<OnEnter>
				GameTooltip_AddNewbieTip(self, CHANNELPULLOUT_OPTIONS_LABEL, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_CHANNELPULLOUT_OPTIONS, 1);
			</OnEnter>
			<OnLeave function="GameTooltip_Hide"/>
		</Scripts>
		<ButtonText name="$parentText" text="Channel Roster">
			<Size x="0" y="8"/>
			<Anchors>
				<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT" x="0" y="-5"/>
			</Anchors>
		</ButtonText>
		<NormalFont style="GameFontNormalSmall"/>
		<HighlightTexture file="Interface\PaperDollInfoFrame\UI-Character-Tab-Highlight" alphaMode="ADD">
			<Anchors>
				<Anchor point="LEFT" relativeTo="$parentLeft" x="0" y="-7"/>
				<Anchor point="RIGHT" relativeTo="$parentRight" x="0" y="-7"/>
			</Anchors>
		</HighlightTexture>
	</Button>
	<Frame name="ChannelPullout" movable="true" frameStrata="DIALOG" parent="UIParent" hidden="true" resizable="true">
		<Size x="140" y="420"/>
		<Anchors>
			<Anchor point="TOPLEFT" relativeTo="ChannelPulloutTab" relativePoint="BOTTOMLEFT" x="-5" y="3"/>
		</Anchors>
		<Scripts>
			<OnUpdate function="ChannelPullout_OnUpdate"/>
			<OnLoad function="ChannelPullout_OnLoad"/>
		</Scripts>		
		<Frames>
			<Frame name="$parentBackground" setAllPoints="true">
				<Backdrop bgFile="Interface\Tooltips\UI-Tooltip-Background" edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
					<EdgeSize>	
						<AbsValue val="16"/>
					</EdgeSize>
					<TileSize>
						<AbsValue val="16"/>
					</TileSize>
					<BackgroundInsets>
						<AbsInset left="4" right="4" top="4" bottom="4"/>
					</BackgroundInsets>		
				</Backdrop>	
				<Scripts>
					<OnLoad>
						self:SetBackdropBorderColor(0.5, 0.5, 0.5, 0.5);
						self:SetBackdropColor(TOOLTIP_DEFAULT_BACKGROUND_COLOR.r, TOOLTIP_DEFAULT_BACKGROUND_COLOR.g, TOOLTIP_DEFAULT_BACKGROUND_COLOR.b, 0.5);
					</OnLoad>					
				</Scripts>
			</Frame>
			<Button name="$parentCloseButton">
				<Size x="24" y="24"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="-2" y="-2"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_OFF);
						ChannelPullout_ToggleDisplay(self);
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface/BUTTONS/UI-Panel-MinimizeButton-Up"/>
				<PushedTexture file="Interface/BUTTONS/UI-Panel-MinimizeButton-Down"/>
				<DisabledTexture file="Interface/BUTTONS/UI-Panel-MinimizeButton-Disabled"/>
				<HighlightTexture file="Interface/BUTTONS/UI-Panel-MinimizeButton-Highlight" alphaMode="ADD"/>
			</Button>
			<Frame name="$parentRoster">
				<Anchors>
					<Anchor point="TOPLEFT" x="4" y="-8"/>
					<Anchor point="BOTTOMRIGHT" x="-24" y="8"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						ChannelPulloutRoster_OnLoad(self);
						self:EnableMouseWheel();
						self.downArrow = _G[self:GetParent():GetName() .. "DownArrow"];
						self.upArrow = _G[self:GetParent():GetName() .. "UpArrow"];						
					</OnLoad>
					<OnShow function="ChannelPulloutRoster_Update"/>
					<OnMouseWheel function="ChannelPulloutRoster_Scroll"/>
				</Scripts>
				<Frames>
					<Frame name="$parentScroll">
						<Size x="16" y="0"/>
						<Anchors>
							<Anchor point="TOPRIGHT" relativeTo="ChannelPullout" x="-6" y="-24"/>
							<Anchor point="BOTTOMRIGHT" relativeTo="ChannelPullout" x="-4" y="8"/>
						</Anchors>
						<Frames>
							<Button name="$parentUpBtn">
								<Size x="18" y="18"/>
								<Anchors>
									<Anchor point="TOP"/>
								</Anchors>		
								<Scripts>
									<OnClick>
										ChannelPulloutRoster_Scroll(self:GetParent():GetParent(), 1);
									</OnClick>
								</Scripts>
								<NormalTexture file="Interface\ChatFrame\UI-ChatIcon-ScrollUp-Up"/>
								<PushedTexture file="Interface\ChatFrame\UI-ChatIcon-ScrollUp-Down"/>
								<DisabledTexture file="Interface\ChatFrame\UI-ChatIcon-ScrollUp-Disabled"/>
								<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
							</Button>
							<Button name="$parentDownBtn">
								<Size x="18" y="18"/>
								<Anchors>
									<Anchor point="BOTTOM"/>
								</Anchors>
								<Scripts>
									<OnClick>
										ChannelPulloutRoster_Scroll(self:GetParent():GetParent(), -1);
									</OnClick>
								</Scripts>
								<NormalTexture file="Interface\ChatFrame\UI-ChatIcon-ScrollDown-Up"/>
								<PushedTexture file="Interface\ChatFrame\UI-ChatIcon-ScrollDown-Down"/>
								<DisabledTexture file="Interface\ChatFrame\UI-ChatIcon-ScrollDown-Disabled"/>
								<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
							</Button>
						</Frames>
					</Frame>
				</Frames>
			</Frame>
		</Frames>
	</Frame>
			
</Ui>
