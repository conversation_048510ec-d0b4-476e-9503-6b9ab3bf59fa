<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
    <Script file="CharacterFrame.lua"/>

	<Texture name="Char-Stat-Bottom" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="169" y="46"/>
		<TexCoords left="0.00390625" right="0.66406250" top="0.00781250" bottom="0.36718750"/>
	</Texture>
	<Texture name="Char-Stat-Minimized" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="169" y="14"/>
		<TexCoords left="0.00390625" right="0.66406250" top="0.38281250" bottom="0.49218750"/>
	</Texture>
	<Texture name="Char-Stat-Top" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="169" y="9"/>
		<TexCoords left="0.00390625" right="0.66406250" top="0.50781250" bottom="0.57812500"/>
	</Texture>
	<Texture name="Char-RightSlot" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="50" y="44"/>
		<TexCoords left="0.00390625" right="0.19921875" top="0.59375000" bottom="0.93750000"/>
	</Texture>
	<Texture name="Char-Stat-Minus" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="7" y="3"/>
		<TexCoords left="0.00390625" right="0.03125000" top="0.95312500" bottom="0.97656250"/>
	</Texture>
	<Texture name="Char-LeftSlot" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="49" y="44"/>
		<TexCoords left="0.20703125" right="0.39843750" top="0.59375000" bottom="0.93750000"/>
	</Texture>
	<Texture name="Char-Corner-LowerRight" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="7" y="7"/>
		<TexCoords left="0.40625000" right="0.43359375" top="0.59375000" bottom="0.64843750"/>
	</Texture>
	<Texture name="Char-Corner-LowerLeft" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="7" y="7"/>
		<TexCoords left="0.40625000" right="0.43359375" top="0.66406250" bottom="0.71875000"/>
	</Texture>
	<Texture name="Char-Corner-UpperRight" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="7" y="7"/>
		<TexCoords left="0.40625000" right="0.43359375" top="0.73437500" bottom="0.78906250"/>
	</Texture>
	<Texture name="Char-Corner-UpperLeft" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="7" y="7"/>
		<TexCoords left="0.40625000" right="0.43359375" top="0.80468750" bottom="0.85937500"/>
	</Texture>
	<Texture name="Char-Stat-Plus" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="7" y="7"/>
		<TexCoords left="0.40625000" right="0.43359375" top="0.87500000" bottom="0.92968750"/>
	</Texture>
	<Texture name="Char-BottomSlot" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="42" y="53"/>
		<TexCoords left="0.67187500" right="0.83593750" top="0.00781250" bottom="0.42187500"/>
	</Texture>
	<Texture name="Char-Slot-Bottom-Right" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="7" y="54"/>
		<TexCoords left="0.67187500" right="0.69921875" top="0.43750000" bottom="0.85937500"/>
	</Texture>
	<Texture name="Char-Slot-Bottom-Left" file="Interface\CharacterFrame\Char-Paperdoll-Parts" virtual="true" >
		<Size x="6" y="54"/>
		<TexCoords left="0.70703125" right="0.73046875" top="0.43750000" bottom="0.85937500"/>
	</Texture>

	<Texture name="Char-Inner-Bottom" file="Interface\CharacterFrame\Char-Paperdoll-Horizontal" horizTile="true" virtual="true">
		<Size x="32" y="5"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.06250000" bottom="0.37500000"/>
	</Texture>
	<Texture name="Char-Inner-Top" file="Interface\CharacterFrame\Char-Paperdoll-Horizontal" horizTile="true" virtual="true">
		<Size x="32" y="5"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.50000000" bottom="0.81250000"/>
	</Texture>
	<Texture name="Char-Inner-Left" file="Interface\CharacterFrame\Char-Paperdoll-Vertical" vertTile="true" virtual="true">
		<Size x="5" y="32"/>
		<TexCoords left="0.06250000" right="0.37500000" top="0.00000000" bottom="1.00000000"/>
	</Texture>
	<Texture name="Char-Inner-Right" file="Interface\CharacterFrame\Char-Paperdoll-Vertical" vertTile="true" virtual="true">
		<Size x="5" y="32"/>
		<TexCoords left="0.50000000" right="0.81250000" top="0.00000000" bottom="1.00000000"/>
	</Texture>

	<Frame name="CharacterStatFrameCategoryTemplate" virtual="true">
		<Size x="197" y="40"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Background" atlas="UI-Character-Info-Title" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Title" inherits="GameFontHighlight">
					<Anchors>
						<Anchor point="CENTER" x="0" y="1"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				self.Title:SetText(self.titleText);
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="CharacterStatFrameTemplate" virtual="true">
		<Size x="187" y="15"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Background" atlas="UI-Character-Info-Line-Bounce" useAtlasSize="true" alpha="0.3">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Label" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="LEFT" x="11" y="0"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Value" inherits="GameFontHighlightSmall">
					<Anchors>
						<Anchor point="RIGHT" x="-8" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnEnter>
				if ( self.onEnterFunc ) then
					self:onEnterFunc();
				else
					PaperDollStatTooltip(self);
				end
			</OnEnter>
			<OnLeave function="GameTooltip_Hide"/>
			<OnLoad>
				if (STATFRAME_STATTEXT_FONT_OVERRIDE) then
					self.Value:SetFontObject(STATFRAME_STATTEXT_FONT_OVERRIDE);
				end
			</OnLoad>
		</Scripts>
	</Frame>

    <Frame name="CharacterFrame" toplevel="true" movable="true" parent="UIParent" hidden="true" inherits="ButtonFrameTemplate">
        <Frames>
            <Button name="CharacterFrameTab1" inherits="CharacterFrameTabButtonTemplate" id="1" text="CHARACTER">
                <Anchors>
					<Anchor point="TOPLEFT" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="11" y="2"/>
						</Offset>
					</Anchor>
                </Anchors>
                <Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText(MicroButtonTooltipText(CHARACTER_INFO, "TOGGLECHARACTER0"), 1.0,1.0,1.0 );
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
                </Scripts>
            </Button>
			<Button name="CharacterFrameTab2" inherits="CharacterFrameTabButtonTemplate" id="2" text="REPUTATION_ABBR">
                <Anchors>
                   <Anchor point="LEFT" relativeTo="CharacterFrameTab1" relativePoint="RIGHT">
                        <Offset>
                            <AbsDimension x="-15" y="0"/>
                        </Offset>
                    </Anchor>
                </Anchors>
                <Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText(MicroButtonTooltipText(REPUTATION, "TOGGLECHARACTER2"), 1.0,1.0,1.0 );
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
                </Scripts>
            </Button>
			<Frame parentKey="ReputationTabHelpBox" inherits="GlowBoxTemplate" hidden="true" frameStrata="DIALOG" >
				<Size x="230" y="65"/>
				<Anchors>
					<Anchor point="TOP" relativePoint="BOTTOM" relativeTo="CharacterFrameTab2" x="0" y="-14"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString parentKey="BigText" inherits="GameFontHighlight" text="REPUTATION_EXALTED_PLUS_HELP" justifyH="LEFT">
							<Size x="186" y="0"/>
							<Anchors>
								<Anchor point="TOP" x="-10" y="-12"/>
							</Anchors>
						</FontString>
						<Texture parentKey="ArrowUp" inherits="HelpPlateArrowUP">
							<Size x="53" y="21"/>
							<Anchors>
								<Anchor point="BOTTOM" relativePoint="TOP" x="0" y="-3"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BORDER">
						<Texture parentKey="ArrowGlowUp" inherits="HelpPlateArrow-GlowUP" alphaMode="ADD" alpha="0.5">
							<Size x="53" y="21"/>
							<Anchors>
								<Anchor point="BOTTOM" relativePoint="TOP" x="0" y="-3"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Button parentKey="CloseButton" inherits="UIPanelCloseButton">
						<Anchors>
							<Anchor point="TOPRIGHT" x="6" y="6"/>
						</Anchors>
						<Scripts>
							<OnClick>
								self:GetParent():Hide();
								SetCVarBitfield("closedInfoFrames",	LE_FRAME_TUTORIAL_REPUTATION_EXALTED_PLUS, true);
							</OnClick>
						</Scripts>
					</Button>
				</Frames>
				<Scripts>
					<OnLoad>
						self:SetHeight(self.BigText:GetHeight() + HELPTIP_HEIGHT_PADDING);
					</OnLoad>
				</Scripts>
			</Frame>
			<Button name="CharacterFrameTab3" inherits="CharacterFrameTabButtonTemplate" id="3" text="CURRENCY" hidden="true">
                <Anchors>
                   <Anchor point="LEFT" relativeTo="CharacterFrameTab2" relativePoint="RIGHT">
                        <Offset>
                            <AbsDimension x="-15" y="0"/>
                        </Offset>
                    </Anchor>
                </Anchors>
                <Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText(MicroButtonTooltipText(CURRENCY, "TOGGLECURRENCY"), 1.0,1.0,1.0 );
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
                </Scripts>
            </Button>
			<Frame name="CharacterFrameInsetRight" inherits="InsetFrameTemplate" hidden="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="CharacterFrameInset" relativePoint="TOPRIGHT" x="1" y="0"/>
					<Anchor point="BOTTOMRIGHT"  x="-4" y="4"/>
				</Anchors>
			</Frame>

			<Frame name="CharacterStatsPane" hidden="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="CharacterFrameInsetRight" x="3" y="-3"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="CharacterFrameInsetRight" x="-3" y="2"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture parentKey="ClassBackground">
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Frame parentKey="ItemLevelCategory" inherits="CharacterStatFrameCategoryTemplate">
						<KeyValues>
							<KeyValue key="titleText" value="STAT_AVERAGE_ITEM_LEVEL" type="global"/>
						</KeyValues>
						<Anchors>
							<Anchor point="TOP" x="0" y="-2"/>
						</Anchors>
					</Frame>
					<Frame parentKey="ItemLevelFrame">
						<Size x="187" y="29"/>
						<Anchors>
							<Anchor point="TOP" relativeKey="$parent.ItemLevelCategory" relativePoint="BOTTOM" x="0" y="0"/>
						</Anchors>
						<Layers>
							<Layer level="BORDER">
								<Texture parentKey="Background" atlas="UI-Character-Info-ItemLevel-Bounce" useAtlasSize="true" alpha="0.3">
									<Anchors>
										<Anchor point="CENTER"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<FontString parentKey="Value" inherits="Game15Font_o1">
									<Anchors>
										<Anchor point="CENTER" relativeKey="$parent.ItemLevelBackground" x="0" y="-1"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnEnter>
								if ( self.onEnterFunc ) then
									self:onEnterFunc();
								else
									PaperDollStatTooltip(self);
								end
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</Frame>
					<Frame parentKey="AttributesCategory" inherits="CharacterStatFrameCategoryTemplate">
						<KeyValues>
							<KeyValue key="titleText" value="STAT_CATEGORY_ATTRIBUTES" type="global"/>
						</KeyValues>
						<Anchors>
							<Anchor point="TOP" x="0" y="-81"/>
						</Anchors>
					</Frame>
					<Frame parentKey="EnhancementsCategory" inherits="CharacterStatFrameCategoryTemplate">
						<KeyValues>
							<KeyValue key="titleText" value="STAT_CATEGORY_ENHANCEMENTS" type="global"/>
						</KeyValues>
					</Frame>
				</Frames>
				<Scripts>
					<OnLoad>
						self.statsFramePool = CreateFramePool("FRAME", self, "CharacterStatFrameTemplate");
						local _, class = UnitClass("player");
						self.ClassBackground:SetAtlas("UI-Character-Info-"..class.."-BG", true);
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="CharacterFrame_OnLoad"/>
            <OnEvent function="CharacterFrame_OnEvent"/>
            <OnShow function="CharacterFrame_OnShow"/>
			<OnHide function="CharacterFrame_OnHide"/>
        </Scripts>
    </Frame>
</Ui>
