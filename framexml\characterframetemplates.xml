<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
    <Button name="CharacterFrameTabButtonTemplate" motionScriptsWhileDisabled="true" virtual="true">
        <Size>
            <AbsDimension x="10" y="32"/>
        </Size>
        <Layers>
        	<Layer level="BACKGROUND">
        		<Texture name="$parentLeftDisabled" file="Interface\PaperDollInfoFrame\UI-Character-ActiveTab">
        			<Size>
        				<AbsDimension x="20" y="35"/>
        			</Size>
        			<Anchors>
        				<Anchor point="TOPLEFT">
        					<Offset>
        						<AbsDimension x="0" y="0"/>
        					</Offset>
        				</Anchor>
        			</Anchors>
					<TexCoords left="0" right="0.15625" top="0" bottom="0.546875"/>
        		</Texture>
				<Texture name="$parentMiddleDisabled" file="Interface\PaperDollInfoFrame\UI-Character-ActiveTab">
        			<Size>
        				<AbsDimension x="88" y="35"/>
        			</Size>
        			<Anchors>
        				<Anchor point="LEFT" relativeTo="$parentLeftDisabled" relativePoint="RIGHT"/>
        			</Anchors>
					<TexCoords left="0.15625" right="0.84375" top="0" bottom="0.546875"/>
        		</Texture>
				<Texture name="$parentRightDisabled" file="Interface\PaperDollInfoFrame\UI-Character-ActiveTab">
        			<Size>
        				<AbsDimension x="20" y="35"/>
        			</Size>
        			<Anchors>
        				<Anchor point="LEFT" relativeTo="$parentMiddleDisabled" relativePoint="RIGHT"/>
        			</Anchors>
					<TexCoords left="0.84375" right="1.0" top="0" bottom="0.546875"/>
        		</Texture>
				<Texture name="$parentLeft" file="Interface\PaperDollInfoFrame\UI-Character-InActiveTab">
        			<Size>
        				<AbsDimension x="20" y="32"/>
        			</Size>
        			<Anchors>
        				<Anchor point="TOPLEFT" x="0" y="-1"/>
        			</Anchors>
					<TexCoords left="0" right="0.15625" top="0" bottom="1.0"/>
        		</Texture>
				<Texture name="$parentMiddle" file="Interface\PaperDollInfoFrame\UI-Character-InActiveTab">
        			<Size>
        				<AbsDimension x="88" y="32"/>
        			</Size>
        			<Anchors>
        				<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.15625" right="0.84375" top="0" bottom="1.0"/>
        		</Texture>
				<Texture name="$parentRight" file="Interface\PaperDollInfoFrame\UI-Character-InActiveTab">
        			<Size>
        				<AbsDimension x="20" y="32"/>
        			</Size>
        			<Anchors>
        				<Anchor point="LEFT" relativeTo="$parentMiddle" relativePoint="RIGHT"/>
        			</Anchors>
					<TexCoords left="0.84375" right="1.0" top="0" bottom="1.0"/>
        		</Texture>
        	</Layer>
        </Layers>
		<Scripts>
			<OnLoad>
				self:SetFrameLevel(self:GetFrameLevel() + 4);
				self:RegisterEvent("DISPLAY_SIZE_CHANGED");
			</OnLoad>
			<OnEvent>
				if (self:IsVisible()) then
					PanelTemplates_TabResize(self, 0, nil, 36, self:GetParent().maxTabWidth or 88);
				end
			</OnEvent>
			<OnClick>
				PanelTemplates_Tab_OnClick(self, CharacterFrame);
				CharacterFrameTab_OnClick(self, button);
			</OnClick>
			<OnShow>
				PanelTemplates_TabResize(self, 0, nil, 36, self:GetParent().maxTabWidth or 88);
				CharacterFrame_TabBoundsCheck(self);
			</OnShow>
			<OnEnter>
				local buttonText = _G[self:GetName().."Text"];
				if (buttonText and buttonText:IsTruncated()) then
					GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
					GameTooltip:SetText(buttonText:GetText());
				end
			</OnEnter>
			<OnLeave>
				GameTooltip_Hide();
			</OnLeave>
		</Scripts>
		<ButtonText name="$parentText">
			<Size>
				<AbsDimension x="0" y="10"/>
			</Size>
			<Anchors>
				<Anchor point="CENTER">
					<Offset>
						<AbsDimension x="0" y="2"/>
					</Offset>
				</Anchor>
			</Anchors>
		</ButtonText>
		<NormalFont style="GameFontNormalSmall"/>
		<HighlightFont style="GameFontHighlightSmall"/>
		<DisabledFont style="GameFontHighlightSmall"/>
		<HighlightTexture name="$parentHighlightTexture" file="Interface\PaperDollInfoFrame\UI-Character-Tab-RealHighlight" alphaMode="ADD">
			<Anchors>
				<Anchor point="TOPLEFT">
					<Offset>
						<AbsDimension x="3" y="5"/>
					</Offset>
				</Anchor>
				<Anchor point="BOTTOMRIGHT">
					<Offset>
						<AbsDimension x="-3" y="0"/>
					</Offset>
				</Anchor>
			</Anchors>
		</HighlightTexture>
    </Button>
</Ui>
