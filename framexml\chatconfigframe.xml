<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
<Script file="ChatConfigFrame.lua"/>
	<Button name="ConfigCategoryButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="10" y="16"/>
		</Size>
		<Scripts>
			<OnLoad>
				_G[self:GetName().."Highlight"]:SetVertexColor(0.11, 0.325, 0.48);
			</OnLoad>
			<OnClick>
				ChatConfigCategory_OnClick(self, button, down);
			</OnClick>
		</Scripts>
		<ButtonText name="$parentNormalText">
			<Size>
				<AbsDimension x="0" y="16"/>
			</Size>
			<Anchors>
				<Anchor point="LEFT">
					<Offset>
						<AbsDimension x="2" y="1"/>
					</Offset>
				</Anchor>
			</Anchors>
		</ButtonText>
		<NormalFont style="GameFontNormalLeft"/>
		<DisabledFont style="GameFontDisableLeft"/>
		<HighlightFont style="GameFontHighlightLeft"/>
		<HighlightTexture name="$parentHighlight" file="Interface\Buttons\UI-Listbox-Highlight2" alphaMode="ADD"/>
	</Button>
	<Button name="ConfigFilterButtonTemplate" inherits="ConfigCategoryButtonTemplate" virtual="true">
		<Scripts>
			<OnClick>
				ChatConfigFilter_OnClick(self:GetID()+FauxScrollFrame_GetOffset(ChatConfigCombatSettingsFiltersScrollFrame));
			</OnClick>
		</Scripts>
	</Button>
	<Frame name="ChatConfigBoxTemplate" virtual="true">
		<Backdrop bgFile="Interface\Tooltips\UI-Tooltip-Background" edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
			<EdgeSize>
				<AbsValue val="16"/>
			</EdgeSize>
			<TileSize>
				<AbsValue val="16"/>
			</TileSize>
			<BackgroundInsets>
				<AbsInset left="5" right="5" top="5" bottom="5"/>
			</BackgroundInsets>
		</Backdrop>
		<Scripts>
			<OnLoad>
				self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b, 0.5);
				self:SetBackdropColor(TOOLTIP_DEFAULT_BACKGROUND_COLOR.r, TOOLTIP_DEFAULT_BACKGROUND_COLOR.g, TOOLTIP_DEFAULT_BACKGROUND_COLOR.b);
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="ChatConfigBoxWithHeaderTemplate" inherits="ChatConfigBoxTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND">
				<FontString name="$parentTitle" inherits="GameFontNormal" parentKey="header">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativePoint="TOPLEFT">
							<Offset x="0" y="2"/>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Frame>
	<Frame name="ChatConfigBoxWithHeaderAndClassColorsTemplate" inherits="ChatConfigBoxWithHeaderTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND">
				<FontString name="$parentColorHeader" inherits="GameFontNormalSmall" text="COLOR">
					<Anchors>
						<Anchor point="BOTTOM" relativePoint="TOPLEFT">
							<Offset x="191" y="2"/>
						</Anchor>
					</Anchors>
				</FontString>
				<FontString name="$parentClassColorHeader" inherits="GameFontNormalSmall" text="SHOW_CLASS_COLOR">
					<Anchors>
						<Anchor point="BOTTOM" relativePoint="TOPLEFT">
							<Offset x="275" y="2"/>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Frame>
	<FontString name="ClassColorLegendFontStringTemplate" inherits="GameFontNormal" justifyH="LEFT" virtual="true">
		<Size>
			<AbsDimension x="100" y="11"/>
		</Size>
	</FontString>
	<Frame name="ClassColorLegendTemplate" inherits="ChatConfigBoxTemplate" virtual="true">
		<Size>
			<AbsDimension x="130" y="1"/>
		</Size>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentTitle" inherits="GameFontNormal" text="CLASS_COLORS" parentKey="header">
					<Anchors>
						<Anchor point="TOPLEFT">
							<Offset x="10" y="-11"/>
						</Anchor>
					</Anchors>
				</FontString>
				<FontString inherits="ClassColorLegendFontStringTemplate" parentKey="firstClass">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTitle" relativePoint="BOTTOMLEFT">
							<Offset x="10" y="-5"/>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b, 0.5);
				self:SetBackdropColor(0.27, 0.27, 0.27);
				
				local fontStringOffset = 2;
				self.classStrings = {};
				tinsert(self.classStrings, self.firstClass);
				for key, value in ipairs(CLASS_SORT_ORDER) do
					local fontString = self.classStrings[key];
					if ( not fontString ) then
						fontString = self:CreateFontString(nil, "ARTWORK", "ClassColorLegendFontStringTemplate");
						tinsert(self.classStrings, fontString);
						fontString:SetPoint("TOPLEFT", self.classStrings[key - 1], "BOTTOMLEFT", 0, -fontStringOffset);
					end
					local classColor = RAID_CLASS_COLORS[value];
					fontString:SetFormattedText("|cff%.2x%.2x%.2x%s|r\n", classColor.r*255, classColor.g*255, classColor.b*255, LOCALIZED_CLASS_NAMES_MALE[value]);
				end
				
				self:SetHeight((self.firstClass:GetHeight() + fontStringOffset) * #self.classStrings + self.header:GetHeight() + 31);
			</OnLoad>
		</Scripts>
	</Frame>
	<CheckButton name="ChatConfigBaseCheckButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="32" y="32"/>
		</Size>
		<Scripts>
			<OnClick>
				local checked = self:GetChecked();
				if ( self.func ) then
					self.func(self, checked);
				end
				
				ChatConfigFrame_PlayCheckboxSound(checked);
			</OnClick>
			<OnEnter>
				if (self.tooltip) then
					GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
					GameTooltip:SetText(self.tooltip, nil, nil, nil, nil, (self.tooltipStyle or true));
				end
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
		<NormalTexture file="Interface\Buttons\UI-CheckBox-Up"/>
		<PushedTexture file="Interface\Buttons\UI-CheckBox-Down"/>
		<HighlightTexture file="Interface\Buttons\UI-CheckBox-Highlight" alphaMode="ADD"/>
		<CheckedTexture file="Interface\Buttons\UI-CheckBox-Check"/>
		<DisabledCheckedTexture file="Interface\Buttons\UI-CheckBox-Check-Disabled"/>
	</CheckButton>
	<CheckButton name="ChatConfigCheckButtonTemplate" inherits="ChatConfigBaseCheckButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="24" y="24"/>
		</Size>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentText" inherits="GameFontHighlight" justifyH="LEFT">
					<Anchors>
						<Anchor point="LEFT" relativePoint="RIGHT">
							<Offset>
								<AbsDimension x="-2" y="2"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<HitRectInsets>
			<AbsInset left="0" right="-145" top="0" bottom="0"/>
		</HitRectInsets>
	</CheckButton>
	<CheckButton name="ChatConfigSmallCheckButtonTemplate" inherits="ChatConfigBaseCheckButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="20" y="20"/>
		</Size>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentText" inherits="GameFontHighlightSmall">
					<Anchors>
						<Anchor point="LEFT" relativePoint="RIGHT">
							<Offset>
								<AbsDimension x="-2" y="2"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<HitRectInsets>
			<AbsInset left="0" right="-55" top="0" bottom="0"/>
		</HitRectInsets>
	</CheckButton>
	<Frame name="ChatConfigCheckBoxTemplate" virtual="true">
		<Size>
			<AbsDimension x="200" y="26"/>
		</Size>
		<Backdrop edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
			<EdgeSize>
				<AbsValue val="16"/>
			</EdgeSize>
			<TileSize>
				<AbsValue val="16"/>
			</TileSize>
		</Backdrop>
		<Frames>
			<CheckButton name="$parentCheck" inherits="ChatConfigCheckButtonTemplate">
				<Size>
					<AbsDimension x="24" y="24"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT">
						<Offset x="5" y="0"/>
					</Anchor>
				</Anchors>
			</CheckButton>
		</Frames>
		<Scripts>
			<OnLoad>
				self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b, 0.5);
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="ChatConfigCheckBoxWithSwatchTemplate" inherits="ChatConfigCheckBoxTemplate" virtual="true">
		<Frames>
			<Button name="$parentColorSwatch" hidden="false">
				<Size>
					<AbsDimension x="16" y="16"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT">
						<Offset>
							<AbsDimension x="178" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentSwatchBg">
							<Size>
								<AbsDimension x="14" y="14"/>
							</Size>
							<Anchors>
								<Anchor point="CENTER">
									<Offset>
										<AbsDimension x="0" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<Color r="1.0" g="1.0" b="1.0"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnClick>
						MessageTypeColor_OpenColorPicker(self, button, down);
					</OnClick>
				</Scripts>
				<NormalTexture name="$parentNormalTexture" file="Interface\ChatFrame\ChatFrameColorSwatch"/>
			</Button>
		</Frames>
	</Frame>
	<Frame name="ChatConfigCheckBoxWithSwatchAndClassColorTemplate" inherits="ChatConfigCheckBoxWithSwatchTemplate" virtual="true">
		<Size>
			<AbsDimension x="328" y="25"/>
		</Size>
		<Frames>
			<CheckButton name="$parentColorClasses" inherits="ChatConfigBaseCheckButtonTemplate">
				<Size>
					<AbsDimension x="24" y="23"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentColorSwatch" relativePoint="RIGHT">
						<Offset x="65" y="0"/>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						self.func = ColorClassesCheckBox_OnClick;
					</OnLoad>
				</Scripts>
			</CheckButton>
		</Frames>
	</Frame>
	<Frame name="ChatConfigSwatchTemplate" virtual="true">
		<Size>
			<AbsDimension x="200" y="26"/>
		</Size>
		<Backdrop edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
			<EdgeSize>
				<AbsValue val="16"/>
			</EdgeSize>
			<TileSize>
				<AbsValue val="16"/>
			</TileSize>
		</Backdrop>
		<Layers>
			<Layer level="BACKGROUND">
				<FontString name="$parentText" inherits="GameFontHighlight">
					<Anchors>
						<Anchor point="LEFT">
							<Offset x="7" y="1"/>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="$parentColorSwatch">
				<Size>
					<AbsDimension x="16" y="16"/>
				</Size>
				<Anchors>
					<Anchor point="RIGHT">
						<Offset>
							<AbsDimension x="-6" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentSwatchBg">
							<Size>
								<AbsDimension x="14" y="14"/>
							</Size>
							<Anchors>
								<Anchor point="CENTER">
									<Offset>
										<AbsDimension x="0" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<Color r="1.0" g="1.0" b="1.0"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnClick>
						ChatUnitColor_OpenColorPicker(self, button, down);
					</OnClick>
				</Scripts>
				<NormalTexture name="$parentNormalTexture" file="Interface\ChatFrame\ChatFrameColorSwatch"/>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b, 0.5);
			</OnLoad>
		</Scripts>
	</Frame>
	<Button name="ChatConfigTabTemplate" virtual="true" hidden="true">
		<Size>
			<AbsDimension x="64" y="32"/>
		</Size>
		<Anchors>
			<Anchor point="BOTTOMLEFT">
				<Offset>
					<AbsDimension x="0" y="2"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentLeft" file="Interface\ChatFrame\ChatFrameTab">
					<Size>
						<AbsDimension x="16" y="32"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0.0" right="0.25" top="0.0" bottom="1.0"/>
				</Texture>
				<Texture name="$parentMiddle" file="Interface\ChatFrame\ChatFrameTab">
					<Size>
						<AbsDimension x="44" y="32"/>
					</Size>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.25" right="0.75" top="0.0" bottom="1.0"/>
				</Texture>
				<Texture name="$parentRight" file="Interface\ChatFrame\ChatFrameTab">
					<Size>
						<AbsDimension x="16" y="32"/>
					</Size>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentMiddle" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.75" right="1.0" top="0.0" bottom="1.0"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick>
				PlaySound(SOUNDKIT.U_CHAT_SCROLL_BUTTON);
				ChatConfig_UpdateCombatTabs(self:GetID());
			</OnClick>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
		<ButtonText name="$parentText">
			<Size>
				<AbsDimension x="0" y="8"/>
			</Size>
			<Anchors>
				<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT">
					<Offset>
						<AbsDimension x="0" y="-5"/>
					</Offset>
				</Anchor>
			</Anchors>
		</ButtonText>
		<NormalFont style="GameFontNormalSmall"/>
		<HighlightTexture file="Interface\PaperDollInfoFrame\UI-Character-Tab-Highlight" alphaMode="ADD">
			<Anchors>
				<Anchor point="LEFT" relativeTo="$parentLeft">
					<Offset>
						<AbsDimension x="0" y="-7"/>
					</Offset>
				</Anchor>
				<Anchor point="RIGHT" relativeTo="$parentRight">
					<Offset>
						<AbsDimension x="0" y="-7"/>
					</Offset>
				</Anchor>
			</Anchors>
		</HighlightTexture>
	</Button>

	<Frame name="ChatConfigFrame" toplevel="true" parent="UIParent" hidden="true" enableMouse="true">
		<Size>
			<AbsDimension x="645" y="595"/>
		</Size>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="ChatConfigFrameHeader" file="Interface\DialogFrame\UI-DialogBox-Header">
					<Size x="256" y="64"/>
					<Anchors>
						<Anchor point="TOP">
							<Offset x="0" y="12"/>
						</Anchor>
					</Anchors>
				</Texture>
				<FontString name="ChatConfigFrameHeaderText" inherits="GameFontNormal">
					<Size>
						<AbsDimension x="185" y="12"/>
					</Size>
					<Anchors>
						<Anchor point="TOP" relativeTo="ChatConfigFrameHeader">
							<Offset x="0" y="-14"/>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="ChatConfigCategoryFrame" inherits="ChatConfigBoxTemplate">
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset x="12" y="-47"/>
					</Anchor>
					<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMLEFT">
						<Offset x="137" y="35"/>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString name="$parentTitle" inherits="GameFontHighlight" text="CATEGORIES">
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativePoint="TOPLEFT">
									<Offset x="7" y="0"/>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentButton1" inherits="ConfigCategoryButtonTemplate" text="CHAT" id="1">
						<Anchors>
							<Anchor point="TOPLEFT">
								<Offset x="5" y="-7"/>
							</Anchor>
							<Anchor point="TOPRIGHT">
								<Offset x="-5" y="-7"/>
							</Anchor>
						</Anchors>
					</Button>
					<Button name="$parentButton2" inherits="ConfigCategoryButtonTemplate" text="COMBAT" id="2">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentButton1" relativePoint="BOTTOMLEFT">
								<Offset x="0" y="-1"/>
							</Anchor>
							<Anchor point="TOPRIGHT" relativeTo="$parentButton1" relativePoint="BOTTOMRIGHT">
								<Offset x="0" y="-1"/>
							</Anchor>
						</Anchors>
					</Button>
					<Button name="$parentButton3" inherits="ConfigCategoryButtonTemplate" text="GLOBAL_CHANNELS" id="3">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentButton2" relativePoint="BOTTOMLEFT">
								<Offset x="0" y="-1"/>
							</Anchor>
							<Anchor point="TOPRIGHT" relativeTo="$parentButton2" relativePoint="BOTTOMRIGHT">
								<Offset x="0" y="-1"/>
							</Anchor>
						</Anchors>
					</Button>
					<Button name="$parentButton4" inherits="ConfigCategoryButtonTemplate" text="OTHER" id="4">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentButton3" relativePoint="BOTTOMLEFT">
								<Offset x="0" y="-1"/>
							</Anchor>
							<Anchor point="TOPRIGHT" relativeTo="$parentButton3" relativePoint="BOTTOMRIGHT">
								<Offset x="0" y="-1"/>
							</Anchor>
						</Anchors>
					</Button>
				</Frames>
				<Scripts>
					<OnShow>
						if ( IsCombatLog(FCF_GetCurrentChatFrame()) ) then
							ChatConfigCategoryFrameButton2:Show();
							ChatConfigCategoryFrameButton3:SetPoint("TOPLEFT", ChatConfigCategoryFrameButton2, "BOTTOMLEFT", 0, -1);
							ChatConfigCategoryFrameButton3:SetPoint("TOPRIGHT", ChatConfigCategoryFrameButton2, "BOTTOMRIGHT", 0, -1);
							ChatConfigCategory_OnClick(ChatConfigCategoryFrameButton2);
						else
							ChatConfigCategoryFrameButton2:Hide();
							ChatConfigCategoryFrameButton3:SetPoint("TOPLEFT", ChatConfigCategoryFrameButton1, "BOTTOMLEFT", 0, -1);
							ChatConfigCategoryFrameButton3:SetPoint("TOPRIGHT", ChatConfigCategoryFrameButton1, "BOTTOMRIGHT", 0, -1);
							ChatConfigCategory_OnClick(ChatConfigCategoryFrameButton1);
						end
						ChatConfigFrameHeaderText:SetText(format(CHATCONFIG_HEADER, FCF_GetCurrentChatFrame().name));
						ChatConfigFrameHeader:SetWidth(ChatConfigFrameHeaderText:GetWidth()+200);
						ChatConfigCategory_UpdateEnabled();
					</OnShow>
				</Scripts>
			</Frame>
			<Frame name="ChatConfigBackgroundFrame" inherits="ChatConfigBoxTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="ChatConfigCategoryFrame" relativePoint="TOPRIGHT">
						<Offset x="1" y="0"/>
					</Anchor>
					<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT">
						<Offset x="-12" y="35"/>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="ChatConfigChatSettings">
				<Size x="10" y="10"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="ChatConfigBackgroundFrame"/>
				</Anchors>
				<Frames>
					<Frame name="$parentLeft" inherits="ChatConfigBoxWithHeaderAndClassColorsTemplate">
						<Size x="10" y="10"/>
						<Anchors>
							<Anchor point="TOPLEFT">
								<Offset x="13" y="-25"/>
							</Anchor>
						</Anchors>
					</Frame>
					<Frame name="$parentClassColorLegend" inherits="ClassColorLegendTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT">
								<Offset x="5" y="0"/>
							</Anchor>
						</Anchors>
					</Frame>
				</Frames>
				<Scripts>
					<OnShow function="ChatConfigChatSettings_OnShow"/>
				</Scripts>
			</Frame>
			<Frame name="ChatConfigChannelSettings" hidden="true">
				<Size x="10" y="10"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="ChatConfigBackgroundFrame"/>
				</Anchors>
				<Frames>
					<Frame name="$parentLeft" inherits="ChatConfigBoxWithHeaderAndClassColorsTemplate">
						<Size x="10" y="10"/>
						<Anchors>
							<Anchor point="TOPLEFT">
								<Offset x="13" y="-25"/>
							</Anchor>
						</Anchors>
					</Frame>
					<Frame name="$parentClassColorLegend" inherits="ClassColorLegendTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT">
								<Offset x="5" y="0"/>
							</Anchor>
						</Anchors>
					</Frame>
				</Frames>
				<Scripts>
					<OnShow function="ChatConfigChannelSettings_OnShow"/>
				</Scripts>
			</Frame>
			<Frame name="ChatConfigOtherSettings">
				<Size x="10" y="10"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="ChatConfigBackgroundFrame"/>
				</Anchors>
				<Frames>
					<Frame name="$parentCombat" inherits="ChatConfigBoxWithHeaderTemplate">
						<Size x="10" y="10"/>
						<Anchors>
							<Anchor point="TOPLEFT">
								<Offset x="13" y="-25"/>
							</Anchor>
						</Anchors>
					</Frame>
					<Frame name="$parentPVP" inherits="ChatConfigBoxWithHeaderTemplate">
						<Size x="10" y="10"/>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentCombat" relativePoint="BOTTOMLEFT">
								<Offset x="0" y="-25"/>
							</Anchor>
						</Anchors>
					</Frame>
					<Frame name="$parentSystem" inherits="ChatConfigBoxWithHeaderTemplate">
						<Size x="10" y="10"/>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentCombat" relativePoint="TOPRIGHT">
								<Offset x="50" y="0"/>
							</Anchor>
						</Anchors>
					</Frame>
					<Frame name="$parentCreature" inherits="ChatConfigBoxWithHeaderTemplate">
						<Size x="10" y="10"/>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentSystem" relativePoint="BOTTOMLEFT">
								<Offset x="0" y="-25"/>
							</Anchor>
						</Anchors>
					</Frame>
				</Frames>
				<Scripts>
					<OnShow function="ChatConfigOtherSettings_OnShow"/>
				</Scripts>
			</Frame>
			<Frame name="ChatConfigCombatSettings">
				<Size>
					<AbsDimension x="10" y="10"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="ChatConfigCategoryFrame" relativePoint="TOPRIGHT">
						<Offset x="1" y="0"/>
					</Anchor>
					<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT">
						<Offset x="-12" y="35"/>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString inherits="GameFontHighlight" text="FILTERS">
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativePoint="TOPLEFT">
									<Offset x="0" y="2"/>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Frame name="$parentFilters" inherits="ChatConfigBoxTemplate">
						<Size>
							<AbsDimension x="0" y="0"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT">
								<Offset x="0" y="0"/>
							</Anchor>
							<Anchor point="BOTTOMRIGHT" relativePoint="TOPRIGHT">
								<Offset x="0" y="-85"/>
							</Anchor>
						</Anchors>
						<Frames>
							<ScrollFrame name="$parentScrollFrame" inherits="FauxScrollFrameTemplateLight">
								<Anchors>
									<Anchor point="TOPLEFT">
										<Offset x="5" y="-6"/>
									</Anchor>
									<Anchor point="BOTTOMRIGHT">
										<Offset x="-28" y="4"/>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnVerticalScroll>
										FauxScrollFrame_OnVerticalScroll(self, offset, CHATCONFIG_FILTER_HEIGHT, ChatConfig_UpdateFilterList);
									</OnVerticalScroll>
								</Scripts>
							</ScrollFrame>
							<Button name="$parentButton1" inherits="ConfigFilterButtonTemplate" id="1">
								<Anchors>
									<Anchor point="TOPLEFT">
										<Offset x="5" y="-7"/>
									</Anchor>
									<Anchor point="TOPRIGHT">
										<Offset x="-5" y="-7"/>
									</Anchor>
								</Anchors>
							</Button>
							<Button name="$parentButton2" inherits="ConfigFilterButtonTemplate" id="2">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentButton1" relativePoint="BOTTOMLEFT">
										<Offset x="0" y="-2"/>
									</Anchor>
									<Anchor point="TOPRIGHT" relativeTo="$parentButton1" relativePoint="BOTTOMRIGHT">
										<Offset x="0" y="-2"/>
									</Anchor>
								</Anchors>
							</Button>
							<Button name="$parentButton3" inherits="ConfigFilterButtonTemplate" id="3">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentButton2" relativePoint="BOTTOMLEFT">
										<Offset x="0" y="-2"/>
									</Anchor>
									<Anchor point="TOPRIGHT" relativeTo="$parentButton2" relativePoint="BOTTOMRIGHT">
										<Offset x="0" y="-2"/>
									</Anchor>
								</Anchors>
							</Button>
							<Button name="$parentButton4" inherits="ConfigFilterButtonTemplate" id="4">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentButton3" relativePoint="BOTTOMLEFT">
										<Offset x="0" y="-2"/>
									</Anchor>
									<Anchor point="TOPRIGHT" relativeTo="$parentButton3" relativePoint="BOTTOMRIGHT">
										<Offset x="0" y="-2"/>
									</Anchor>
								</Anchors>
							</Button>
							<Button name="$parentDeleteButton" inherits="UIPanelButtonTemplate" text="DELETE">
								<Size>
									<AbsDimension x="60" y="22"/>
								</Size>
								<Anchors>
									<Anchor point="TOPRIGHT" relativePoint="BOTTOMRIGHT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnClick>
										StaticPopup_Show("CONFIRM_COMBAT_FILTER_DELETE");
									</OnClick>
								</Scripts>
							</Button>
							<Button name="$parentAddFilterButton" inherits="UIPanelButtonTemplate" text="ADD_FILTER">
								<Size>
									<AbsDimension x="80" y="22"/>
								</Size>
								<Anchors>
									<Anchor point="RIGHT" relativeTo="$parentDeleteButton" relativePoint="LEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnClick>
										StaticPopup_Show("CREATE_COMBAT_FILTER");
									</OnClick>
								</Scripts>
							</Button>
							<Button name="$parentCopyFilterButton" inherits="UIPanelButtonTemplate" text="COPY_FILTER">
								<Size>
									<AbsDimension x="80" y="22"/>
								</Size>
								<Anchors>
									<Anchor point="RIGHT" relativeTo="$parentAddFilterButton" relativePoint="LEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnClick>
										local dialog = StaticPopup_Show("COPY_COMBAT_FILTER");
										dialog.data = CHATCONFIG_SELECTED_FILTER;
									</OnClick>
								</Scripts>
							</Button>
							<Button name="ChatConfigMoveFilterUpButton">
								<Size>
									<AbsDimension x="28" y="28"/>
								</Size>
								<Anchors>
									<Anchor point="TOPLEFT" relativePoint="BOTTOMLEFT">
										<Offset x="0" y="0"/>
									</Anchor>
								</Anchors>
								<HitRectInsets>
									<AbsInset left="6" right="6" top="7" bottom="7"/>
								</HitRectInsets>
								<Scripts>
									<OnClick>
										ChatConfig_MoveFilterUp();
										PlaySound(SOUNDKIT.U_CHAT_SCROLL_BUTTON);
									</OnClick>
									<OnEnter>
										GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
										GameTooltip_AddNewbieTip(self, MOVE_FILTER_UP, 1.0, 1.0, 1.0);
									</OnEnter>
									<OnLeave function="GameTooltip_Hide"/>
								</Scripts>
								<NormalTexture file="Interface\MainMenuBar\UI-MainMenu-ScrollUpButton-Up"/>
								<PushedTexture file="Interface\MainMenuBar\UI-MainMenu-ScrollUpButton-Down"/>
								<DisabledTexture file="Interface\MainMenuBar\UI-MainMenu-ScrollUpButton-Disabled"/>
								<HighlightTexture alphaMode="ADD" file="Interface\MainMenuBar\UI-MainMenu-ScrollUpButton-Highlight"/>
							</Button>
							<Button name="ChatConfigMoveFilterDownButton">
								<Size>
									<AbsDimension x="28" y="28"/>
								</Size>
								<Anchors>
									<Anchor point="LEFT" relativeTo="ChatConfigMoveFilterUpButton" relativePoint="RIGHT">
										<Offset>
											<AbsDimension x="-8" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
								<HitRectInsets>
									<AbsInset left="6" right="6" top="7" bottom="7"/>
								</HitRectInsets>
								<Scripts>
									<OnClick>
										ChatConfig_MoveFilterDown();
										PlaySound(SOUNDKIT.U_CHAT_SCROLL_BUTTON);
									</OnClick>
									<OnEnter>
										GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
										GameTooltip_AddNewbieTip(self, MOVE_FILTER_DOWN, 1.0, 1.0, 1.0);
									</OnEnter>
									<OnLeave function="GameTooltip_Hide"/>
								</Scripts>
								<NormalTexture file="Interface\MainMenuBar\UI-MainMenu-ScrollDownButton-Up"/>
								<PushedTexture file="Interface\MainMenuBar\UI-MainMenu-ScrollDownButton-Down"/>
								<DisabledTexture file="Interface\MainMenuBar\UI-MainMenu-ScrollDownButton-Disabled"/>
								<HighlightTexture alphaMode="ADD" file="Interface\MainMenuBar\UI-MainMenu-ScrollDownButton-Highlight"/>
							</Button>
						</Frames>
						<Scripts>
							<OnShow>
								ChatConfig_UpdateFilterList();
								ChatConfigFilter_OnClick(1);
							</OnShow>
						</Scripts>
					</Frame>
					<Frame name="CombatConfigMessageSources">
						<Size>
							<AbsDimension x="10" y="10"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="ChatConfigBackgroundFrame">
								<Offset x="0" y="0"/>
							</Anchor>
						</Anchors>
						<Frames>
							<Frame name="$parentDoneBy" inherits="ChatConfigBoxWithHeaderTemplate">
								<Size x="10" y="10"/>
								<Anchors>
									<Anchor point="TOPLEFT">
										<Offset x="13" y="-25"/>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentDoneTo" inherits="ChatConfigBoxWithHeaderTemplate">
								<Size x="10" y="10"/>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentDoneBy" relativePoint="TOPRIGHT">
										<Offset x="50" y="0"/>
									</Anchor>
								</Anchors>
							</Frame>
						</Frames>
					</Frame>
					<Frame name="CombatConfigMessageTypes">
						<Size>
							<AbsDimension x="10" y="10"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="ChatConfigBackgroundFrame">
								<Offset x="0" y="0"/>
							</Anchor>
						</Anchors>
						<Frames>
							<Frame name="$parentLeft">
								<Size x="10" y="10"/>
								<Anchors>
									<Anchor point="TOPLEFT">
										<Offset x="11" y="-10"/>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentRight">
								<Size x="10" y="10"/>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT">
										<Offset x="230" y="0"/>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentMisc">
								<Size x="10" y="10"/>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentRight" relativePoint="BOTTOMLEFT">
										<Offset x="0" y="-20"/>
									</Anchor>
								</Anchors>
								<Layers>
									<Layer level="BACKGROUND">
										<FontString inherits="GameFontHighlight" text="MISCELLANEOUS">
											<Anchors>
												<Anchor point="BOTTOMLEFT" relativePoint="TOPLEFT">
													<Offset x="0" y="0"/>
												</Anchor>
											</Anchors>
										</FontString>
									</Layer>
								</Layers>
							</Frame>
						</Frames>
					</Frame>
					<Frame name="CombatConfigColors">
						<Size>
							<AbsDimension x="10" y="10"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="ChatConfigBackgroundFrame">
								<Offset x="0" y="0"/>
							</Anchor>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<FontString name="$parentExampleString1" inherits="GameFontNormal" justifyH="LEFT" justifyV="TOP">
									<Size x="450" y="0"/>
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="CombatConfigColors" relativePoint="TOPLEFT">
											<Offset x="25" y="-27"/>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentExampleString2" inherits="GameFontNormal" justifyH="LEFT" justifyV="TOP">
									<Size x="450" y="0"/>
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentExampleString1" relativePoint="BOTTOMLEFT">
											<Offset x="0" y="0"/>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentExampleTitle" inherits="GameFontNormalSmall" text="EXAMPLE_TEXT">
									<Size x="0" y="13"/>
									<Anchors>
										<Anchor point="BOTTOM" relativeTo="$parentExampleString1" relativePoint="TOP">
											<Offset x="0" y="3"/>
										</Anchor>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Frame name="$parentUnitColors" inherits="ChatConfigBoxWithHeaderTemplate">
								<Size x="10" y="10"/>
								<Anchors>
									<Anchor point="BOTTOMLEFT" relativeTo="ChatConfigBackgroundFrame" relativePoint="BOTTOMLEFT">
										<Offset x="13" y="89"/>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentHighlighting">
								<Size>
									<AbsDimension x="200" y="53"/>
								</Size>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentUnitColors" relativePoint="BOTTOMLEFT">
										<Offset x="0" y="-25"/>
									</Anchor>
								</Anchors>
								<Backdrop edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
									<EdgeSize>
										<AbsValue val="16"/>
									</EdgeSize>
									<TileSize>
										<AbsValue val="16"/>
									</TileSize>
								</Backdrop>
								<Layers>
									<Layer level="BACKGROUND">
										<FontString name="$parentTitle" inherits="GameFontNormal" text="HIGHLIGHTING">
											<Anchors>
												<Anchor point="BOTTOMLEFT" relativePoint="TOPLEFT">
													<Offset x="0" y="2"/>
												</Anchor>
											</Anchors>
										</FontString>
									</Layer>
								</Layers>
								<Frames>
									<CheckButton name="$parentLine" inherits="ChatConfigSmallCheckButtonTemplate">
										<Size>
											<AbsDimension x="24" y="24"/>
										</Size>
										<Anchors>
											<Anchor point="TOPLEFT" relativeTo="CombatConfigColorsHighlighting" relativePoint="TOPLEFT">
												<Offset x="6" y="-4"/>
											</Anchor>
										</Anchors>
										<Scripts>
											<OnLoad>
												_G[self:GetName().."Text"]:SetText(COMBATLOG_HIGHLIGHT_KILL);
												self.tooltip = HIGHLIGHT_KILL_COMBATLOG_TOOLTIP;
											</OnLoad>
											<OnClick>
												local checked = self:GetChecked()
												if ( checked ) then
													CHATCONFIG_SELECTED_FILTER.settings.lineHighlighting = true;
												else
													CHATCONFIG_SELECTED_FILTER.settings.lineHighlighting = false;
												end
												CombatConfig_Colorize_Update();
												ChatConfigFrame_PlayCheckboxSound(checked);
											</OnClick>
										</Scripts>
									</CheckButton>
									<CheckButton name="$parentAbility" inherits="ChatConfigSmallCheckButtonTemplate">
										<Size>
											<AbsDimension x="24" y="24"/>
										</Size>
										<Anchors>
											<Anchor point="LEFT" relativeTo="$parentLine" relativePoint="RIGHT">
												<Offset x="60" y="0"/>
											</Anchor>
										</Anchors>
										<Scripts>
											<OnLoad>
												_G[self:GetName().."Text"]:SetText(COMBATLOG_HIGHLIGHT_ABILITY);
												self.tooltip = HIGHLIGHT_ABILITY_COMBATLOG_TOOLTIP;
											</OnLoad>
											<OnClick>
												local checked = self:GetChecked()
												if ( checked ) then
													CHATCONFIG_SELECTED_FILTER.settings.abilityHighlighting = true;
												else
													CHATCONFIG_SELECTED_FILTER.settings.abilityHighlighting = false;
												end
												CombatConfig_Colorize_Update();
												ChatConfigFrame_PlayCheckboxSound(checked);
											</OnClick>
										</Scripts>
									</CheckButton>
									<CheckButton name="$parentDamage" inherits="ChatConfigSmallCheckButtonTemplate">
										<Size>
											<AbsDimension x="24" y="24"/>
										</Size>
										<Anchors>
											<Anchor point="TOPLEFT" relativeTo="CombatConfigColorsHighlightingLine" relativePoint="BOTTOMLEFT">
												<Offset x="0" y="3"/>
											</Anchor>
										</Anchors>
										<Scripts>
											<OnLoad>
												_G[self:GetName().."Text"]:SetText(COMBATLOG_HIGHLIGHT_DAMAGE);
												self.tooltip = HIGHLIGHT_DAMAGE_COMBATLOG_TOOLTIP;
											</OnLoad>
											<OnClick>
												local checked = self:GetChecked()
												if ( checked ) then
													CHATCONFIG_SELECTED_FILTER.settings.amountHighlighting = true;
												else
													CHATCONFIG_SELECTED_FILTER.settings.amountHighlighting = false;
												end
												CombatConfig_Colorize_Update();
												ChatConfigFrame_PlayCheckboxSound(checked);
											</OnClick>
										</Scripts>
									</CheckButton>
									<CheckButton name="$parentSchool" inherits="ChatConfigSmallCheckButtonTemplate">
										<Size>
											<AbsDimension x="24" y="24"/>
										</Size>
										<Anchors>
											<Anchor point="LEFT" relativeTo="$parentDamage" relativePoint="RIGHT">
												<Offset x="60" y="0"/>
											</Anchor>
										</Anchors>
										<Scripts>
											<OnLoad>
												_G[self:GetName().."Text"]:SetText(COMBATLOG_HIGHLIGHT_SCHOOL);
												self.tooltip = HIGHLIGHT_SCHOOL_COMBATLOG_TOOLTIP;
											</OnLoad>
											<OnClick>
												local checked = self:GetChecked()
												if ( checked ) then
													CHATCONFIG_SELECTED_FILTER.settings.schoolNameHighlighting = true;
												else
													CHATCONFIG_SELECTED_FILTER.settings.schoolNameHighlighting = false;
												end
												CombatConfig_Colorize_Update();
												ChatConfigFrame_PlayCheckboxSound(checked);											
											</OnClick>
										</Scripts>
									</CheckButton>
								</Frames>
								<Scripts>
									<OnLoad>
										self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b, 0.5);
									</OnLoad>
								</Scripts>
							</Frame>
							<Frame name="$parentColorize">
								<Size x="10" y="10"/>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentUnitColors" relativePoint="TOPRIGHT">
										<Offset x="50" y="0"/>
									</Anchor>
								</Anchors>
								<Frames>
									<Frame name="$parentUnitName">
										<Size>
											<AbsDimension x="200" y="32"/>
										</Size>
										<Anchors>
											<Anchor point="TOPLEFT">
												<Offset x="0" y="0"/>
											</Anchor>
										</Anchors>
										<Layers>
											<Layer level="BACKGROUND">
												<FontString inherits="GameFontNormal" text="COLORIZE">
													<Anchors>
														<Anchor point="BOTTOMLEFT" relativePoint="TOPLEFT">
															<Offset x="0" y="2"/>
														</Anchor>
													</Anchors>
												</FontString>
											</Layer>
										</Layers>
										<Backdrop edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
											<EdgeSize>
												<AbsValue val="16"/>
											</EdgeSize>
											<TileSize>
												<AbsValue val="16"/>
											</TileSize>
										</Backdrop>
										<Frames>
											<CheckButton name="$parentCheck" inherits="ChatConfigCheckButtonTemplate">
												<Size>
													<AbsDimension x="24" y="24"/>
												</Size>
												<Anchors>
													<Anchor point="LEFT">
														<Offset x="7" y="0"/>
													</Anchor>
												</Anchors>
												<Scripts>
													<OnLoad>
														_G[self:GetName().."Text"]:SetText(UNIT_NAMES);
														self.tooltip = UNIT_NAMES_COMBATLOG_TOOLTIP;
													</OnLoad>
													<OnClick>
														local checked = self:GetChecked()
														if ( checked ) then
															CHATCONFIG_SELECTED_FILTER.settings.unitColoring = true;
															CHATCONFIG_SELECTED_FILTER.settings.sourceColoring = true;
															CHATCONFIG_SELECTED_FILTER.settings.destColoring = true;
														else
															CHATCONFIG_SELECTED_FILTER.settings.unitColoring = false;
															CHATCONFIG_SELECTED_FILTER.settings.sourceColoring = false;
															CHATCONFIG_SELECTED_FILTER.settings.destColoring = false;
														end
														CombatConfig_Colorize_Update();
														ChatConfigFrame_PlayCheckboxSound(checked);
													</OnClick>
												</Scripts>
											</CheckButton>
										</Frames>
										<Scripts>
											<OnLoad>
												self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b, 0.5);
											</OnLoad>
										</Scripts>
									</Frame>
									<Frame name="$parentSpellNames">
										<Size>
											<AbsDimension x="200" y="52"/>
										</Size>
										<Anchors>
											<Anchor point="TOPLEFT" relativeTo="$parentUnitName" relativePoint="BOTTOMLEFT">
												<Offset x="0" y="0"/>
											</Anchor>
										</Anchors>
										<Backdrop edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
											<EdgeSize>
												<AbsValue val="16"/>
											</EdgeSize>
											<TileSize>
												<AbsValue val="16"/>
											</TileSize>
										</Backdrop>
										<Frames>
											<CheckButton name="$parentCheck" inherits="ChatConfigCheckButtonTemplate">
												<Size>
													<AbsDimension x="24" y="24"/>
												</Size>
												<Anchors>
													<Anchor point="TOPLEFT">
														<Offset x="7" y="-4"/>
													</Anchor>
												</Anchors>
												<Scripts>
													<OnLoad>
														_G[self:GetName().."Text"]:SetText(SPELL_NAMES);
														self.tooltip = SPELL_NAMES_COMBATLOG_TOOLTIP;
													</OnLoad>
													<OnClick>
														local checked = self:GetChecked()
														if ( checked ) then
															CHATCONFIG_SELECTED_FILTER.settings.abilityColoring = true;
														else
															CHATCONFIG_SELECTED_FILTER.settings.abilityColoring = false;
														end
														CombatConfig_Colorize_Update();
														ChatConfigFrame_PlayCheckboxSound(checked);
													</OnClick>
												</Scripts>
											</CheckButton>
											<CheckButton name="$parentSchoolColoring" inherits="ChatConfigSmallCheckButtonTemplate">
												<Anchors>
													<Anchor point="TOPLEFT" relativeTo="$parentCheck" relativePoint="BOTTOMLEFT">
														<Offset x="10" y="0"/>
													</Anchor>
												</Anchors>
												<Scripts>
													<OnLoad>
														_G[self:GetName().."Text"]:SetText(COLOR_BY_SCHOOL);
														self.tooltip = SPELL_COLOR_BY_SCHOOL_COMBATLOG_TOOLTIP;
													</OnLoad>
													<OnClick>
														local checked = self:GetChecked()
														if ( checked ) then
															CHATCONFIG_SELECTED_FILTER.settings.abilitySchoolColoring = true;
														else
															CHATCONFIG_SELECTED_FILTER.settings.abilitySchoolColoring = false;
														end
														CombatConfig_Colorize_Update();
														ChatConfigFrame_PlayCheckboxSound(checked);
													</OnClick>
												</Scripts>
											</CheckButton>
											<Button name="$parentColorSwatch">
												<Size>
													<AbsDimension x="16" y="16"/>
												</Size>
												<Anchors>
													<Anchor point="TOPRIGHT">
														<Offset>
															<AbsDimension x="-8" y="-8"/>
														</Offset>
													</Anchor>
												</Anchors>
												<Layers>
													<Layer level="BACKGROUND">
														<Texture name="$parentSwatchBg">
															<Size>
																<AbsDimension x="14" y="14"/>
															</Size>
															<Anchors>
																<Anchor point="CENTER">
																	<Offset>
																		<AbsDimension x="0" y="0"/>
																	</Offset>
																</Anchor>
															</Anchors>
															<Color r="1.0" g="1.0" b="1.0"/>
														</Texture>
													</Layer>
												</Layers>
												<Scripts>
													<OnClick function="SpellColor_OpenColorPicker"/>
												</Scripts>
												<NormalTexture name="$parentNormalTexture" file="Interface\ChatFrame\ChatFrameColorSwatch"/>
											</Button>
										</Frames>
										<Scripts>
											<OnLoad>
												self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b, 0.5);
											</OnLoad>
										</Scripts>
									</Frame>
									<Frame name="$parentDamageNumber">
										<Size>
											<AbsDimension x="200" y="52"/>
										</Size>
										<Anchors>
											<Anchor point="TOPLEFT" relativeTo="$parentSpellNames" relativePoint="BOTTOMLEFT">
												<Offset x="0" y="0"/>
											</Anchor>
										</Anchors>
										<Backdrop edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
											<EdgeSize>
												<AbsValue val="16"/>
											</EdgeSize>
											<TileSize>
												<AbsValue val="16"/>
											</TileSize>
										</Backdrop>
										<Frames>
											<CheckButton name="$parentCheck" inherits="ChatConfigCheckButtonTemplate">
												<Size>
													<AbsDimension x="24" y="24"/>
												</Size>
												<Anchors>
													<Anchor point="TOPLEFT">
														<Offset x="7" y="-4"/>
													</Anchor>
												</Anchors>
												<Scripts>
													<OnLoad>
														_G[self:GetName().."Text"]:SetText(DAMAGE_NUMBER);
														self.tooltip = SPELL_DAMAGE_NUMBER_COMBATLOG_TOOLTIP;
													</OnLoad>
													<OnClick>
														local checked = self:GetChecked()
														if ( checked ) then
															CHATCONFIG_SELECTED_FILTER.settings.amountColoring = true;
														else
															CHATCONFIG_SELECTED_FILTER.settings.amountColoring = false;
														end
														CombatConfig_Colorize_Update();
														ChatConfigFrame_PlayCheckboxSound(checked);
													</OnClick>
												</Scripts>
											</CheckButton>
											<CheckButton name="$parentSchoolColoring" inherits="ChatConfigSmallCheckButtonTemplate">
												<Anchors>
													<Anchor point="TOPLEFT" relativeTo="$parentCheck" relativePoint="BOTTOMLEFT">
														<Offset x="10" y="0"/>
													</Anchor>
												</Anchors>
												<Scripts>
													<OnLoad>
														_G[self:GetName().."Text"]:SetText(COLOR_BY_SCHOOL);
														self.tooltip = SPELL_COLOR_BY_SCHOOL_COMBATLOG_TOOLTIP;
													</OnLoad>
													<OnClick>
														local checked = self:GetChecked()
														if ( checked ) then
															CHATCONFIG_SELECTED_FILTER.settings.amountSchoolColoring = true;
														else
															CHATCONFIG_SELECTED_FILTER.settings.amountSchoolColoring = false;
														end
														CombatConfig_Colorize_Update();
														ChatConfigFrame_PlayCheckboxSound(checked);
													</OnClick>
												</Scripts>
											</CheckButton>
											<Button name="$parentColorSwatch">
												<Size>
													<AbsDimension x="16" y="16"/>
												</Size>
												<Anchors>
													<Anchor point="TOPRIGHT">
														<Offset>
															<AbsDimension x="-8" y="-8"/>
														</Offset>
													</Anchor>
												</Anchors>
												<Layers>
													<Layer level="BACKGROUND">
														<Texture name="$parentSwatchBg">
															<Size>
																<AbsDimension x="14" y="14"/>
															</Size>
															<Anchors>
																<Anchor point="CENTER">
																	<Offset>
																		<AbsDimension x="0" y="0"/>
																	</Offset>
																</Anchor>
															</Anchors>
															<Color r="1.0" g="1.0" b="1.0"/>
														</Texture>
													</Layer>
												</Layers>
												<Scripts>
													<OnClick function="DamageColor_OpenColorPicker"/>
												</Scripts>
												<NormalTexture name="$parentNormalTexture" file="Interface\ChatFrame\ChatFrameColorSwatch"/>
											</Button>
										</Frames>
										<Scripts>
											<OnLoad>
												self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b, 0.5);
											</OnLoad>
										</Scripts>
									</Frame>
									<Frame name="$parentDamageSchool">
										<Size>
											<AbsDimension x="200" y="32"/>
										</Size>
										<Anchors>
											<Anchor point="TOPLEFT" relativeTo="$parentDamageNumber" relativePoint="BOTTOMLEFT">
												<Offset x="0" y="0"/>
											</Anchor>
										</Anchors>
										<Backdrop edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
											<EdgeSize>
												<AbsValue val="16"/>
											</EdgeSize>
											<TileSize>
												<AbsValue val="16"/>
											</TileSize>
										</Backdrop>
										<Frames>
											<CheckButton name="$parentCheck" inherits="ChatConfigCheckButtonTemplate">
												<Size>
													<AbsDimension x="24" y="24"/>
												</Size>
												<Anchors>
													<Anchor point="LEFT">
														<Offset x="7" y="0"/>
													</Anchor>
												</Anchors>
												<Scripts>
													<OnLoad>
														_G[self:GetName().."Text"]:SetText(DAMAGE_SCHOOL_TEXT);
														self.tooltip = SPELL_DAMAGE_SCHOOL_COMBATLOG_TOOLTIP;
													</OnLoad>
													<OnClick>
														local checked = self:GetChecked()
														if ( checked ) then
															CHATCONFIG_SELECTED_FILTER.settings.schoolNameColoring = true;
														else
															CHATCONFIG_SELECTED_FILTER.settings.schoolNameColoring = false;
														end
														CombatConfig_Colorize_Update();
														ChatConfigFrame_PlayCheckboxSound(checked);
													</OnClick>
												</Scripts>
											</CheckButton>
										</Frames>
										<Scripts>
											<OnLoad>
												self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b, 0.5);
											</OnLoad>
										</Scripts>
									</Frame>
									<Frame name="$parentEntireLine">
										<Size>
											<AbsDimension x="200" y="52"/>
										</Size>
										<Anchors>
											<Anchor point="TOPLEFT" relativeTo="$parentDamageSchool" relativePoint="BOTTOMLEFT">
												<Offset x="0" y="0"/>
											</Anchor>
										</Anchors>
										<Backdrop edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
											<EdgeSize>
												<AbsValue val="16"/>
											</EdgeSize>
											<TileSize>
												<AbsValue val="16"/>
											</TileSize>
										</Backdrop>
										<Frames>
											<CheckButton name="$parentCheck" inherits="ChatConfigCheckButtonTemplate">
												<Size>
													<AbsDimension x="24" y="24"/>
												</Size>
												<Anchors>
													<Anchor point="TOPLEFT">
														<Offset x="7" y="-4"/>
													</Anchor>
												</Anchors>
												<Scripts>
													<OnLoad>
														_G[self:GetName().."Text"]:SetText(ENTIRE_LINE);
														self.tooltip = ENTIRE_LINE_COMBATLOG_TOOLTIP;
													</OnLoad>
													<OnClick>
														local checked = self:GetChecked()
														if ( checked ) then
															CHATCONFIG_SELECTED_FILTER.settings.lineColoring = true;
														else
															CHATCONFIG_SELECTED_FILTER.settings.lineColoring = false;
														end
														CombatConfig_Colorize_Update();
														ChatConfigFrame_PlayCheckboxSound(checked);
													</OnClick>
												</Scripts>
											</CheckButton>
											<CheckButton name="$parentBySource" inherits="UIRadioButtonTemplate">
												<Anchors>
													<Anchor point="TOPLEFT" relativeTo="$parentCheck" relativePoint="BOTTOMLEFT">
														<Offset x="10" y="0"/>
													</Anchor>
												</Anchors>
												<Scripts>
													<OnLoad>
														_G[self:GetName().."Text"]:SetText(BY_SOURCE);
														self.tooltip = BY_SOURCE_COMBATLOG_TOOLTIP;
													</OnLoad>
													<OnClick>
														local checked = self:GetChecked();
														CHATCONFIG_SELECTED_FILTER.settings.lineColorPriority = 1;
														CombatConfig_Colorize_Update();
														ChatConfigFrame_PlayCheckboxSound(checked);
													</OnClick>
												</Scripts>
											</CheckButton>
											<CheckButton name="$parentByTarget" inherits="UIRadioButtonTemplate">
												<Anchors>
													<Anchor point="LEFT" relativeTo="$parentBySourceText" relativePoint="RIGHT">
														<Offset x="10" y="0"/>
													</Anchor>
												</Anchors>
												<Scripts>
													<OnLoad>
														_G[self:GetName().."Text"]:SetText(BY_TARGET);
														self.tooltip = BY_TARGET_COMBATLOG_TOOLTIP;
													</OnLoad>
													<OnClick>
														local checked = self:GetChecked();
														CHATCONFIG_SELECTED_FILTER.settings.lineColorPriority = 2;
														CombatConfig_Colorize_Update();
														ChatConfigFrame_PlayCheckboxSound(checked);
													</OnClick>
												</Scripts>
											</CheckButton>
										</Frames>
										<Scripts>
											<OnLoad>
												self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b, 0.5);
											</OnLoad>
										</Scripts>
									</Frame>
								</Frames>
							</Frame>
						</Frames>
						<Scripts>
							<OnShow>
								ChatConfig_UpdateSwatches(CombatConfigColorsUnitColors);
								CombatConfig_Colorize_Update();
							</OnShow>
						</Scripts>
					</Frame>
					<Frame name="CombatConfigFormatting">
						<Size>
							<AbsDimension x="10" y="10"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="ChatConfigBackgroundFrame">
								<Offset x="10" y="0"/>
							</Anchor>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<FontString name="$parentExampleString1" inherits="GameFontNormal" justifyH="LEFT" justifyV="TOP">
									<Size x="450" y="0"/>
									<Anchors>
										<Anchor point="TOPLEFT">
											<Offset x="15" y="-27"/>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentExampleString2" inherits="GameFontNormal" justifyH="LEFT" justifyV="TOP">
									<Size x="450" y="0"/>
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentExampleString1" relativePoint="BOTTOMLEFT">
											<Offset x="0" y="0"/>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentExampleTitle" inherits="GameFontNormalSmall" text="EXAMPLE_TEXT">
									<Size x="0" y="13"/>
									<Anchors>
										<Anchor point="BOTTOM" relativeTo="$parentExampleString1" relativePoint="TOP">
											<Offset x="0" y="3"/>
										</Anchor>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<CheckButton name="$parentShowTimeStamp" inherits="ChatConfigCheckButtonTemplate">
								<Size>
									<AbsDimension x="24" y="24"/>
								</Size>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentExampleString2" relativePoint="BOTTOMLEFT">
										<Offset x="0" y="-20"/>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnLoad>
										_G[self:GetName().."Text"]:SetText(SHOW_TIMESTAMP);
										self.tooltip = TIMESTAMP_COMBATLOG_TOOLTIP;
									</OnLoad>
									<OnClick>
										local checked = self:GetChecked()
										if ( checked ) then
											CHATCONFIG_SELECTED_FILTER.settings.timestamp = true;
										else
											CHATCONFIG_SELECTED_FILTER.settings.timestamp = false;
										end
										CombatConfig_Formatting_Update();
										ChatConfigFrame_PlayCheckboxSound(checked);
									</OnClick>
								</Scripts>
							</CheckButton>
							<CheckButton name="$parentShowBraces" inherits="ChatConfigCheckButtonTemplate">
								<Size>
									<AbsDimension x="24" y="24"/>
								</Size>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentShowTimeStamp" relativePoint="BOTTOMLEFT">
										<Offset x="0" y="-15"/>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnLoad>
										_G[self:GetName().."Text"]:SetText(SHOW_BRACES);
										self.tooltip = SHOW_BRACES_COMBATLOG_TOOLTIP;
									</OnLoad>
									<OnClick>
										local checked = self:GetChecked()
										if ( checked ) then
											CHATCONFIG_SELECTED_FILTER.settings.braces = true;
										else
											CHATCONFIG_SELECTED_FILTER.settings.braces = false;
										end
										CombatConfig_Formatting_Update();
										ChatConfigFrame_PlayCheckboxSound(checked);
									</OnClick>
								</Scripts>
							</CheckButton>
							<CheckButton name="$parentUnitNames" inherits="ChatConfigSmallCheckButtonTemplate">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentShowBraces" relativePoint="BOTTOMLEFT">
										<Offset x="10" y="0"/>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnLoad>
										_G[self:GetName().."Text"]:SetText(UNIT_NAMES);
										self.tooltip = UNIT_NAMES_SHOW_BRACES_COMBATLOG_TOOLTIP;
									</OnLoad>
									<OnClick>
										local checked = self:GetChecked()
										if ( checked ) then
											CHATCONFIG_SELECTED_FILTER.settings.unitBraces = true;
											CHATCONFIG_SELECTED_FILTER.settings.sourceBraces = true;
											CHATCONFIG_SELECTED_FILTER.settings.destBraces = true;
										else
											CHATCONFIG_SELECTED_FILTER.settings.unitBraces = false;
											CHATCONFIG_SELECTED_FILTER.settings.sourceBraces = false;
											CHATCONFIG_SELECTED_FILTER.settings.destBraces = false;
										end
										CombatConfig_Formatting_Update();
										ChatConfigFrame_PlayCheckboxSound(checked);
									</OnClick>
								</Scripts>
							</CheckButton>
							<CheckButton name="$parentSpellNames" inherits="ChatConfigSmallCheckButtonTemplate">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentUnitNames" relativePoint="BOTTOMLEFT">
										<Offset x="0" y="-2"/>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnLoad>
										_G[self:GetName().."Text"]:SetText(SPELL_NAMES);
										self.tooltip = SPELL_NAMES_SHOW_BRACES_COMBATLOG_TOOLTIP;
									</OnLoad>
									<OnClick>
										local checked = self:GetChecked()
										if ( checked ) then
											CHATCONFIG_SELECTED_FILTER.settings.spellBraces = true;
										else
											CHATCONFIG_SELECTED_FILTER.settings.spellBraces = false;
										end
										CombatConfig_Formatting_Update();
										ChatConfigFrame_PlayCheckboxSound(checked);
									</OnClick>
								</Scripts>
							</CheckButton>
							<CheckButton name="$parentItemNames" inherits="ChatConfigSmallCheckButtonTemplate">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentSpellNames" relativePoint="BOTTOMLEFT">
										<Offset x="0" y="-2"/>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnLoad>
										_G[self:GetName().."Text"]:SetText(ITEM_NAMES);
										self.tooltip = ITEM_NAMES_SHOW_BRACES_COMBATLOG_TOOLTIP;
									</OnLoad>
									<OnClick>
										local checked = self:GetChecked()
										if ( checked ) then
											CHATCONFIG_SELECTED_FILTER.settings.itemBraces = true;
										else
											CHATCONFIG_SELECTED_FILTER.settings.itemBraces = false;
										end
										CombatConfig_Formatting_Update();
										ChatConfigFrame_PlayCheckboxSound(checked);
									</OnClick>
								</Scripts>
							</CheckButton>
							<CheckButton name="$parentFullText" inherits="ChatConfigCheckButtonTemplate">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentItemNames" relativePoint="BOTTOMLEFT">
										<Offset x="-10" y="-13"/>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnLoad>
										_G[self:GetName().."Text"]:SetText(USE_FULL_TEXT_MODE);
										self.tooltip = FULL_TEXT_COMBATLOG_TOOLTIP;
									</OnLoad>
									<OnClick>
										local checked = self:GetChecked()
										if ( checked ) then
											CHATCONFIG_SELECTED_FILTER.settings.fullText = true;
										else
											CHATCONFIG_SELECTED_FILTER.settings.fullText = false;
										end
										CombatConfig_Formatting_Update();
										ChatConfigFrame_PlayCheckboxSound(checked);
									</OnClick>
								</Scripts>
							</CheckButton>
						</Frames>
						<Scripts>
							<OnShow function="CombatConfig_Formatting_Update"/>
						</Scripts>
					</Frame>
					<Frame name="CombatConfigSettings">
						<Size x="10" y="10"/>
						<Anchors>
							<Anchor point="TOPLEFT">
								<Offset x="13" y="-25"/>
							</Anchor>
						</Anchors>
						<Frames>
							<EditBox name="$parentNameEditBox" letters="32" autoFocus="false" inherits="InputBoxTemplate">
								<Size>
									<AbsDimension x="150" y="20"/>
								</Size>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="CombatConfigFormatting" relativePoint="TOPLEFT">
										<Offset x="27" y="-26"/>
									</Anchor>
								</Anchors>
								<Layers>
									<Layer level="BACKGROUND">
										<FontString inherits="GameFontNormalSmall" text="FILTER_NAME">
											<Anchors>
												<Anchor point="BOTTOMLEFT" relativePoint="TOPLEFT">
													<Offset x="0" y="2"/>
												</Anchor>
											</Anchors>
										</FontString>
									</Layer>
								</Layers>
								<Scripts>
									<OnShow>
										self:SetText(CHATCONFIG_SELECTED_FILTER.name);
									</OnShow>
									<OnEnterPressed>
										CombatConfig_SetFilterName(self:GetText());
										self:HighlightText(0, -1);
									</OnEnterPressed>
									<OnEditFocusGained function="EditBox_HighlightText"/>
								</Scripts>
							</EditBox>
							<Button name="$parentSaveButton" inherits="UIPanelButtonTemplate" text="SAVE">
								<Size>
									<AbsDimension x="78" y="22"/>
								</Size>
								<Anchors>
									<Anchor point="LEFT" relativeTo="$parentNameEditBox" relativePoint="RIGHT">
										<Offset>
											<AbsDimension x="8" y="-1"/>
										</Offset>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnClick>
										CombatConfig_SetFilterName(CombatConfigSettingsNameEditBox:GetText());
									</OnClick>
								</Scripts>
							</Button>
							<CheckButton name="$parentShowQuickButton" inherits="ChatConfigCheckButtonTemplate">
								<Size>
									<AbsDimension x="24" y="24"/>
								</Size>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="CombatConfigSettingsNameEditBox" relativePoint="BOTTOMLEFT">
										<Offset x="-6" y="-19"/>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnLoad>
										_G[self:GetName().."Text"]:SetText(SHOW_QUICK_BUTTON);
										self.tooltip = QUICK_BUTTON_COMBATLOG_TOOLTIP;
									</OnLoad>
									<OnClick>
										local checked = self:GetChecked()
										if ( checked ) then
											CHATCONFIG_SELECTED_FILTER.hasQuickButton = true;
										else
											CHATCONFIG_SELECTED_FILTER.hasQuickButton = false;
										end
										Blizzard_CombatLog_Update_QuickButtons();
										CombatConfig_Settings_Update();
										ChatConfigFrame_PlayCheckboxSound(checked);
									</OnClick>
								</Scripts>
							</CheckButton>
							<CheckButton name="$parentSolo" inherits="ChatConfigSmallCheckButtonTemplate">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentShowQuickButton" relativePoint="BOTTOMLEFT">
										<Offset x="20" y="0"/>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnLoad>
										_G[self:GetName().."Text"]:SetText(SOLO);
									</OnLoad>
									<OnClick>
										local checked = self:GetChecked()
										if ( checked ) then
											CHATCONFIG_SELECTED_FILTER.quickButtonDisplay.solo = true;
										else
											CHATCONFIG_SELECTED_FILTER.quickButtonDisplay.solo = false;
										end
										Blizzard_CombatLog_Update_QuickButtons();
										ChatConfigFrame_PlayCheckboxSound(checked);
									</OnClick>
								</Scripts>
							</CheckButton>
							<CheckButton name="$parentParty" inherits="ChatConfigSmallCheckButtonTemplate">
								<Anchors>
									<Anchor point="LEFT" relativeTo="$parentSolo" relativePoint="RIGHT">
										<Offset x="60" y="0"/>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnLoad>
										_G[self:GetName().."Text"]:SetText(PARTY);
									</OnLoad>
									<OnClick>
										local checked = self:GetChecked()
										if ( checked ) then
											CHATCONFIG_SELECTED_FILTER.quickButtonDisplay.party = true;
										else
											CHATCONFIG_SELECTED_FILTER.quickButtonDisplay.party = false;
										end
										Blizzard_CombatLog_Update_QuickButtons();
										ChatConfigFrame_PlayCheckboxSound(checked);
									</OnClick>
								</Scripts>
							</CheckButton>
							<CheckButton name="$parentRaid" inherits="ChatConfigSmallCheckButtonTemplate">
								<Anchors>
									<Anchor point="LEFT" relativeTo="$parentParty" relativePoint="RIGHT">
										<Offset x="60" y="0"/>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnLoad>
										_G[self:GetName().."Text"]:SetText(RAID);
									</OnLoad>
									<OnClick>
										local checked = self:GetChecked()
										if ( checked ) then
											CHATCONFIG_SELECTED_FILTER.quickButtonDisplay.raid = true;
										else
											CHATCONFIG_SELECTED_FILTER.quickButtonDisplay.raid = false;
										end
										Blizzard_CombatLog_Update_QuickButtons();
										ChatConfigFrame_PlayCheckboxSound(checked);
									</OnClick>
								</Scripts>
							</CheckButton>
						</Frames>
						<Scripts>
							<OnLoad>
								self:RegisterEvent("GROUP_ROSTER_UPDATE");
							</OnLoad>
							<OnEvent>
								if ( Blizzard_CombatLog_Update_QuickButtons ) then	--The function is in an AddOn, so we want to make sure it is loaded before calling it.
									Blizzard_CombatLog_Update_QuickButtons();
								end
							</OnEvent>
							<OnShow function="CombatConfig_Settings_Update"/>
						</Scripts>
					</Frame>
				</Frames>
				<Scripts>
					<OnLoad function="ChatConfigCombat_OnLoad"/>
					<OnShow function="ChatConfigCombat_OnShow"/>
					<OnHide>
						ChatConfigBackgroundFrame:SetPoint("TOPLEFT", ChatConfigCategoryFrame, "TOPRIGHT", 1, 0);
						ChatConfig_HideCombatTabs();
					</OnHide>
				</Scripts>
			</Frame>
			<Button name="$parentDefaultButton" parentKey="DefaultButton" inherits="UIPanelButtonTemplate" text="CHAT_DEFAULTS">
				<Size x="130" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT" x="11" y="12"/>
				</Anchors>
				<Scripts>
					<OnClick function="ChatConfigFrameDefaultButton_OnClick"/>
				</Scripts>
			</Button>
			<Button name="$parentRedockButton" parentKey="RedockButton" inherits="UIPanelButtonTemplate" text="RESET_CHAT_WINDOW_POSITIONS">
				<Size x="148" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativeKey="$parent.DefaultButton" relativePoint="BOTTOMRIGHT"/>
				</Anchors>
				<Scripts>
					<OnClick function="ChatConfigFrameRedockButton_OnClick"/>
					<OnLoad function="ChatConfigFrameRedockButton_OnLoad"/>
				</Scripts>
			</Button>
			<Button name="CombatLogDefaultButton" inherits="UIPanelButtonTemplate" text="COMBATLOG_DEFAULTS">
				<Size>
					<AbsDimension x="170" y="22"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativeTo="$parentDefaultButton">
						<Offset x="0" y="0"/>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						StaticPopup_Show("CONFIRM_COMBAT_FILTER_DEFAULTS");
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentCancelButton" inherits="UIPanelButtonTemplate" text="CANCEL" hidden="true">
				<Size>
					<AbsDimension x="78" y="22"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="-11" y="13"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						ChatConfigCancel_OnClick();
						PlaySound(SOUNDKIT.GS_TITLE_OPTION_OK);
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentOkayButton" inherits="UIPanelButtonTemplate" text="OKAY">
				<Size>
					<AbsDimension x="78" y="22"/>
				</Size>
				<Anchors>
					<Anchor point="RIGHT" relativeTo="$parentCancelButton" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="-11" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						HideUIPanel(ChatConfigFrame);
						if ( IsCombatLog(FCF_GetCurrentChatFrame()) ) then
							Blizzard_CombatLog_RefreshGlobalLinks();
							Blizzard_CombatLog_ApplyFilters(Blizzard_CombatLog_CurrentSettings);
							Blizzard_CombatLog_Refilter();
						end
						PlaySound(SOUNDKIT.GS_TITLE_OPTION_OK);
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad function="ChatConfigFrame_OnLoad"/>
			<OnEvent function="ChatConfigFrame_OnEvent"/>
			<OnShow>
				CHATCONFIG_SELECTED_FILTER = Blizzard_CombatLog_Filters.filters[ChatConfigCombatSettingsFilters.selectedFilter];
				CHATCONFIG_SELECTED_FILTER_OLD_SETTINGS = CopyTable(Blizzard_CombatLog_Filters);
			</OnShow>
		</Scripts>
	</Frame>
</Ui>
