<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="CinematicFrame.lua"/>
	<Button name="CinematicDialogButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="119" y="21"/>
		</Size>
		<NormalTexture file="Interface\Buttons\UI-DialogBox-Button-Up">
			<TexCoords left="0.0" right="1.0" top="0.0" bottom="0.71875"/>
		</NormalTexture>
		<PushedTexture file="Interface\Buttons\UI-DialogBox-Button-Down">
			<TexCoords left="0.0" right="1.0" top="0.0" bottom="0.71875"/>
		</PushedTexture>
		<DisabledTexture file="Interface\Buttons\UI-DialogBox-Button-Disabled">
			<TexCoords left="0.0" right="1.0" top="0.0" bottom="0.71875"/>
		</DisabledTexture>
		<HighlightTexture file="Interface\Buttons\UI-DialogBox-Button-Highlight" alphaMode="ADD">
			<TexCoords left="0.0" right="1.0" top="0.0" bottom="0.71875"/>
		</HighlightTexture>
		<NormalFont style="GameFontNormal"/>
		<DisabledFont style="GameFontDisable"/>
		<HighlightFont style="GameFontHighlight"/>
	</Button>
	<Frame name="CinematicFrame" setAllPoints="true" hidden="true" enableKeyboard="true" enableMouse="true">
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="UpperBlackBar" name="UpperBlackBar">
					<Size>
						<AbsDimension x="0" y="128"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT"/>
						<Anchor point="TOPRIGHT"/>
					</Anchors>
					<Color r="0.0" g="0.0" b="0.0"/>
				</Texture>
				<Texture parentKey="LowerBlackBar" name="LowerBlackBar">
					<Size>
						<AbsDimension x="0" y="128"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOMLEFT"/>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
					<Color r="0.0" g="0.0" b="0.0"/>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<FontString parentKey="Subtitle1" parentArray="Subtitles" inherits="GameFontHighlightLarge" justifyH="CENTER" justifyV="MIDDLE" hidden="true">
					<Size x="512" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.LowerBlackBar" relativePoint="LEFT" x="5" y="0"/>
						<Anchor point="RIGHT" relativeKey="$parent.LowerBlackBar" relativePoint="RIGHT" x="-5" y="0"/>
					</Anchors>
				</FontString>
				<!--<FontString parentKey="Subtitle2" parentArray="Subtitles" inherits="GameFontHighlightLarge" justifyH="CENTER" justifyV="TOP" hidden="true">
					<Size x="512" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Subtitle1" relativePoint="BOTTOMLEFT" x="0" y="-15"/>
						<Anchor point="TOPRIGHT" relativeKey="$parent.Subtitle1" relativePoint="BOTTOMRIGHT" x="0" y="-15"/>
					</Anchors>
				</FontString>-->
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentRaidBossEmoteFrame" inherits="RaidBossEmoteFrameTemplate" parentKey="raidBossEmoteFrame">
				<Anchors>
					<Anchor point="TOP" x="0" y="-20"/>
				</Anchors>
			</Frame>
			<Frame name="$parentCloseDialog" parentKey="closeDialog">
				<Size x="320" y="84"/>
				<Anchors>
					<Anchor point="CENTER"/>
				</Anchors>
				<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
					<BackgroundInsets>
						<AbsInset left="11" right="12" top="12" bottom="11"/>
					</BackgroundInsets>
					<TileSize>
						<AbsValue val="32"/>
					</TileSize>
					<EdgeSize>
						<AbsValue val="32"/>
					</EdgeSize>
				</Backdrop>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentText" inherits="GameFontHighlight" text="CONFIRM_CLOSE_CINEMATIC">
							<Size>
								<AbsDimension x="290" y="0"/>
							</Size>
							<Anchors>
								<Anchor point="TOP">
									<Offset>
										<AbsDimension x="0" y="-16"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentConfirmButton" inherits="CinematicDialogButtonTemplate" text="YES">
						<Anchors>
							<Anchor point="BOTTOMRIGHT" relativeTo="$parent" relativePoint="BOTTOM" x="-6" y="16"/>
						</Anchors>
						<Scripts>
							<OnClick>
								CinematicFrame_CancelCinematic();
							</OnClick>
						</Scripts>	
					</Button>
					<Button name="$parentResumeButton" inherits="CinematicDialogButtonTemplate" text="NO">
						<Anchors>
							<Anchor point="LEFT" relativeTo="$parentConfirmButton" relativePoint="RIGHT" x="13" y="0"/>
						</Anchors>
						<Scripts>
							<OnClick>
								self:GetParent():Hide();
							</OnClick>
						</Scripts>	
					</Button>
				</Frames>
				<Scripts>
					<OnShow>
						MouseOverrideCinematicDisable(true);
					</OnShow>
					<OnHide>
						MouseOverrideCinematicDisable(false);
					</OnHide>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="CinematicFrame_OnLoad"/>
			<OnShow function="CinematicFrame_OnShow"/>
			<OnEvent function="CinematicFrame_OnEvent"/>
			<OnKeyDown function="CinematicFrame_OnKeyDown"/>
		</Scripts>
	</Frame>
</Ui>
