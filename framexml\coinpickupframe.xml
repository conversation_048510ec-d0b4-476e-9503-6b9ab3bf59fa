<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="CoinPickupFrame.lua"/>
	<Frame name="CoinPickupFrame" frameStrata="HIGH" parent="UIParent" toplevel="true" enableMouse="true" enableKeyboard="true" hidden="true">
		<Size>
			<AbsDimension x="172" y="96"/>
		</Size>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture file="Interface\MoneyFrame\UI-MoneyFrame">
					<Size>
						<AbsDimension x="256" y="32"/>
					</Size>
					<TexCoords left="0" right="0.671875" top="0" bottom="0.75"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture name="CoinPickupGoldIcon" file="Interface\MoneyFrame\UI-MoneyIcons" hidden="true">
					<Size>
						<AbsDimension x="16" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="RIGHT">
							<Offset>
								<AbsDimension x="-32" y="19"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0" right="0.25" top="0" bottom="1"/>
				</Texture>
				<Texture name="CoinPickupSilverIcon" file="Interface\MoneyFrame\UI-MoneyIcons" hidden="true">
					<Size>
						<AbsDimension x="16" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="RIGHT">
							<Offset>
								<AbsDimension x="-32" y="19"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.25" right="0.5" top="0" bottom="1"/>
				</Texture>
				<Texture name="CoinPickupCopperIcon" file="Interface\MoneyFrame\UI-MoneyIcons" hidden="true">
					<Size>
						<AbsDimension x="16" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="RIGHT">
							<Offset>
								<AbsDimension x="-32" y="19"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.5" right="0.75" top="0" bottom="1"/>
				</Texture>
				<FontString name="CoinPickupLabel" text="COPPER_AMOUNT_SYMBOL" font="GameFontHighlight" hidden="true">
					<Size x="14" y="14"/>
					<Anchors>
						<Anchor point="RIGHT">
							<Offset x="-32" y="19"/>
						</Anchor>
					</Anchors>
				</FontString>
				<FontString name="CoinPickupText" inherits="GameFontHighlight" justifyH="RIGHT">
					<Anchors>
						<Anchor point="RIGHT">
							<Offset>
								<AbsDimension x="-50" y="18"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="CoinPickupLeftButton">
				<Size>
					<AbsDimension x="16" y="16"/>
				</Size>
				<Anchors>
					<Anchor point="RIGHT" relativePoint="CENTER">
						<Offset>
							<AbsDimension x="-59" y="18"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="CoinPickupFrameLeft_Click"/>
				</Scripts>
				<NormalTexture file="Interface\MoneyFrame\Arrow-Left-Up"/>
				<PushedTexture file="Interface\MoneyFrame\Arrow-Left-Down"/>
				<DisabledTexture file="Interface\MoneyFrame\Arrow-Left-Disabled"/>
			</Button>
			<Button name="CoinPickupRightButton">
				<Size>
					<AbsDimension x="16" y="16"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativePoint="CENTER">
						<Offset>
							<AbsDimension x="64" y="18"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="CoinPickupFrameRight_Click"/>
				</Scripts>
				<NormalTexture file="Interface\MoneyFrame\Arrow-Right-Up"/>
				<PushedTexture file="Interface\MoneyFrame\Arrow-Right-Down"/>
				<DisabledTexture file="Interface\MoneyFrame\Arrow-Right-Disabled"/>
			</Button>
			<Button name="CoinPickupOkayButton" inherits="UIPanelButtonTemplate" text="OKAY">
				<Size>
					<AbsDimension x="64" y="24"/>
				</Size>
				<Anchors>
					<Anchor point="RIGHT" relativePoint="BOTTOM">
						<Offset>
							<AbsDimension x="-3" y="32"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="CoinPickupFrameOkay_Click"/>
				</Scripts>
			</Button>
			<Button name="CoinPickupCancelButton" inherits="UIPanelButtonTemplate" text="COINPICKUP_CANCEL">
				<Size>
					<AbsDimension x="64" y="24"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativePoint="BOTTOM">
						<Offset>
							<AbsDimension x="5" y="32"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="CoinPickupFrameCancel_Click"/>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad function="DropCursorMoney"/>
			<OnShow>
				PlaySound(SOUNDKIT.MONEY_FRAME_OPEN);
			</OnShow>
			<OnHide function="CoinPickupFrame_OnHide"/>
			<OnChar function="CoinPickupFrame_OnChar"/>
			<OnKeyDown function="CoinPickupFrame_OnKeyDown"/>
			<OnKeyUp function="CoinPickupFrame_OnKeyUp"/>
		</Scripts>
	</Frame>
</Ui>
