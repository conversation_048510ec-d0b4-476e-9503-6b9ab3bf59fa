<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
  <Script file="ComboFramePlayer.lua"/>
  
	<Frame name="ComboPointPlayerTemplate" virtual="true">
		<Size x="20" y="21"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="PointOff" atlas="ComboPoints-PointBg" useAtlasSize="false">
					<Size x="20" y="21"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Point" atlas="ComboPoints-ComboPoint" useAtlasSize="false" alpha="0" alphaMode="BLEND">
					<Size x="20" y="21"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
				<Texture parentKey="CircleBurst" alpha="0" alphaMode="BLEND" atlas="ComboPoints-FX-Circle" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="Star" alpha="0" alphaMode="ADD" atlas="ComboPoints-FX-Star" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="PointAnim" setToFinalAlpha="true">
				<Alpha childKey="Point" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="Point" smoothing="OUT" duration="0.25" order="1" fromScaleX="0.8" fromScaleY="0.8" toScaleX="1" toScaleY="1"/>
			</AnimationGroup>
			<AnimationGroup parentKey="AnimIn" setToFinalAlpha="true">
				<Scale childKey="Star" startDelay="0.1" smoothing="OUT" duration="0.5" order="1" fromScaleX="0.25" fromScaleY="0.25" toScaleX="0.9" toScaleY="0.9"/>
				<Rotation childKey="Star" startDelay="0.1" smoothing="OUT" duration="0.8" order="1" degrees="-60"/>
				<Alpha childKey="Star" startDelay="0.5" smoothing="IN" duration="0.4" order="1" fromAlpha="0.75" toAlpha="0"/>
				<Alpha childKey="CircleBurst" duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="CircleBurst" smoothing="OUT" duration="0.25" order="1" fromScaleX="1.25" fromScaleY="1.25" toScaleX="0.75" toScaleY="0.75"/>
				<Alpha childKey="CircleBurst" startDelay="0.25" smoothing="IN" duration="0.25" order="1" fromAlpha="1" toAlpha="0"/>
			</AnimationGroup>
			<AnimationGroup parentKey="AnimOut" setToFinalAlpha="true">
				<Alpha childKey="CircleBurst" duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="CircleBurst" smoothing="OUT" duration="0.4" order="1" fromScaleX="0.8" fromScaleY="0.8" toScaleX="0.6" toScaleY="0.6"/>
				<Alpha childKey="CircleBurst" startDelay="0.25" smoothing="IN" duration="0.25" order="1" fromAlpha="1" toAlpha="0"/>
			</AnimationGroup>
		</Animations>
	</Frame>
  
	<Frame name="ComboPointBonusPlayerTemplate" virtual="true">
		<Size x="9" y="10"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="PointOff" atlas="ComboPoints-ComboPointDash-Bg" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Point" atlas="ComboPoints-ComboPointDash" useAtlasSize="true" alpha="0" alphaMode="BLEND">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="2">
				<Texture parentKey="DashBurst" alpha="0" alphaMode="BLEND" atlas="ComboPoints-FX-Dash" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="0" y="-1"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="AnimIn" setToFinalAlpha="true">
				<Alpha childKey="Point" duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="Point" smoothing="OUT" duration="0.25" order="1" fromScaleX="0.8" fromScaleY="0.8" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="DashBurst" duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="DashBurst" smoothing="OUT" duration="0.25" order="1" fromScaleX="0.5" fromScaleY="1" toScaleX="1.1" toScaleY="1"/>
				<Alpha childKey="DashBurst" startDelay="0.25" smoothing="IN" duration="0.25" order="1" fromAlpha="1" toAlpha="0"/>
			</AnimationGroup>
			<AnimationGroup parentKey="AnimOut" setToFinalAlpha="true">
				<Alpha parentKey="AlphaAnim" childKey="Point" fromAlpha="1" toAlpha="0" duration="0.2"/>
			</AnimationGroup>
		</Animations>
	</Frame>
		
	<Frame name="ComboPointPlayerFrame" inherits="ClassPowerBarFrame" mixin="ClassPowerBar, ComboPointPowerBar">
		<Size x="126" y="18"/>
		<Anchors>
			<Anchor point="TOP" relativePoint="BOTTOM" x="50" y="38"/>
		</Anchors>
		<Layers>
			<Layer level="OVERLAY">
				<Texture parentKey="Background" atlas="ComboPoints-AllPointsBG" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="Combo1" parentArray="ComboPoints" inherits="ComboPointPlayerTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" x="12" y="-2"/>
				</Anchors>
			</Frame>
			<Frame parentKey="Combo2" parentArray="ComboPoints" inherits="ComboPointPlayerTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.Combo1" relativePoint="RIGHT" x="1" y="0"/>
				</Anchors>
			</Frame>
			<Frame parentKey="Combo3" parentArray="ComboPoints" inherits="ComboPointPlayerTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.Combo2" relativePoint="RIGHT" x="1" y="0"/>
				</Anchors>
			</Frame>
			<Frame parentKey="Combo4" parentArray="ComboPoints" inherits="ComboPointPlayerTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.Combo3" relativePoint="RIGHT" x="1" y="0"/>
				</Anchors>
			</Frame>
			<Frame parentKey="Combo5" parentArray="ComboPoints" inherits="ComboPointPlayerTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.Combo4" relativePoint="RIGHT" x="1" y="0"/>
				</Anchors>
			</Frame>
			<Frame parentKey="Combo6" parentArray="ComboPoints" inherits="ComboPointPlayerTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.Combo5" relativePoint="RIGHT" x="1" y="0"/>
				</Anchors>
			</Frame>
			<Frame parentKey="ComboBonus1" parentArray="ComboBonus" inherits="ComboPointBonusPlayerTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" x="7" y="0"/>
				</Anchors>
			</Frame>
			<Frame parentKey="ComboBonus2" parentArray="ComboBonus" inherits="ComboPointBonusPlayerTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.ComboBonus1" relativePoint="RIGHT" x="12" y="0"/>
				</Anchors>
			</Frame>
			<Frame parentKey="ComboBonus3" parentArray="ComboBonus" inherits="ComboPointBonusPlayerTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.ComboBonus2" relativePoint="RIGHT" x="12" y="0"/>
				</Anchors>
			</Frame>
			<Frame parentKey="ComboBonus4" parentArray="ComboBonus" inherits="ComboPointBonusPlayerTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.ComboBonus3" relativePoint="RIGHT" x="12" y="0"/>
				</Anchors>
			</Frame>
			<Frame parentKey="ComboBonus5" parentArray="ComboBonus" inherits="ComboPointBonusPlayerTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.ComboBonus4" relativePoint="RIGHT" x="12" y="0"/>
				</Anchors>
			</Frame>
		</Frames>
	</Frame>	
</Ui>


