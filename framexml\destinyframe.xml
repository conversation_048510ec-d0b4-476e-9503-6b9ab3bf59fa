<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
<!-- Patchwerk
size=1024,1024
<Texture name="Destiny_BG.png" >
	<Size x="1020" y="626"/>	
	<TexCoords left="0.00097656" right="0.99707031" top="0.00097656" bottom="0.61230469"/>	
</Texture>
<Texture name="ButtonBG-Glow.png" >
	<Size x="279" y="207"/>	
	<TexCoords left="0.00097656" right="0.27343750" top="0.61425781" bottom="0.81640625"/>	
</Texture>
<Texture name="AllianceButton-Up.png" >
	<Size x="191" y="121"/>	
	<TexCoords left="0.00097656" right="0.18750000" top="0.81835938" bottom="0.93652344"/>	
</Texture>
<Texture name="OrnateBit.png" >
	<Size x="80" y="14"/>	
	<TexCoords left="0.00097656" right="0.07910156" top="0.93847656" bottom="0.95214844"/>	
</Texture>
<Texture name="ButtonHighlight.png" >
	<Size x="191" y="121"/>	
	<TexCoords left="0.27539063" right="0.46191406" top="0.61425781" bottom="0.73242188"/>	
</Texture>
<Texture name="AllianceButton-Down.png" >
	<Size x="191" y="121"/>	
	<TexCoords left="0.27539063" right="0.46191406" top="0.73437500" bottom="0.85253906"/>	
</Texture>
<Texture name="HordeButton-Down.png" >
	<Size x="191" y="121"/>	
	<TexCoords left="0.27539063" right="0.46191406" top="0.85449219" bottom="0.97265625"/>	
</Texture>
<Texture name="HordeButton-Up.png" >
	<Size x="191" y="121"/>	
	<TexCoords left="0.46386719" right="0.65039063" top="0.61425781" bottom="0.73242188"/>	
</Texture>
-->
	<Script file="DestinyFrame.lua"/>
	<Button name="DestinyButtonTemplate" virtual="true">
		<Size x="191" y="121"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture file="Interface\Destiny\UI-Destiny">
					<Size x="279" y="207"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
					<TexCoords left="0.00097656" right="0.27343750" top="0.61425781" bottom="0.81640625"/>							
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString inherits="GameFontNormalLarge" setAllPoints="true" spacing="4" parentKey="label"/>
			</Layer>
		</Layers>
		<NormalTexture file="Interface\Destiny\UI-Destiny" parentKey="normalTex">
			<Size x="191" y="121"/>
			<TexCoords left="0.46386719" right="0.65039063" top="0.61425781" bottom="0.73242188"/>					
		</NormalTexture>
		<PushedTexture file="Interface\Destiny\UI-Destiny" parentKey="pushedTex">
			<Size x="191" y="121"/>
			<TexCoords left="0.27539063" right="0.46191406" top="0.85449219" bottom="0.97265625"/>				
		</PushedTexture>
		<HighlightTexture file="Interface\Destiny\UI-Destiny">
			<Size x="191" y="121"/>
			<TexCoords left="0.27539063" right="0.46191406" top="0.61425781" bottom="0.73242188"/>
		</HighlightTexture>
	</Button>
	
	<Frame name="DestinyFrame" frameStrata="FULLSCREEN" hidden="true" enableMouse="true" enableKeyboard="true" setAllPoints="true">
		<Frames>
			<Button name="DestinyAllianceButton" inherits="DestinyButtonTemplate" parentKey="allianceButton">
				<Anchors>
					<Anchor point="BOTTOM" x="-152" y="160"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						self.label:SetText(JOIN_THE_ALLIANCE);
						self.normalTex:SetTexCoord(0.00097656, 0.18750000, 0.81835938, 0.93652344);
						self.pushedTex:SetTexCoord(0.27539063, 0.46191406, 0.73437500, 0.85253906);
					</OnLoad>
					<OnClick>
						PlaySound(SOUNDKIT.IG_CHARACTER_INFO_OPEN);
						NeutralPlayerSelectFaction(2);
						DestinyFrame:Hide()
					</OnClick>
				</Scripts>
			</Button>
			<Button name="DestinyHordeButton" inherits="DestinyButtonTemplate" parentKey="hordeButton">
				<Anchors>
					<Anchor point="BOTTOM" x="152" y="160"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						self.label:SetText(JOIN_THE_HORDE);
					</OnLoad>
					<OnClick>
						PlaySound(SOUNDKIT.IG_CHARACTER_INFO_OPEN);
						NeutralPlayerSelectFaction(1);
						DestinyFrame:Hide()
					</OnClick>
				</Scripts>
			</Button>
			<Frame parentKey="RecruitAFriendSuggestion" frameStrata="FULLSCREEN" inherits="RecruitInfoDialogTemplate"/>
		</Frames>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture setAllPoints="true" parentKey="alphaLayer">
					<Color r="0" g="0" b="0" a="0.5"/>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture file="Interface\Destiny\UI-Destiny" parentKey="background">
					<Size x="1020" y="626"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
					<TexCoords left="0.00097656" right="0.99707031" top="0.00097656" bottom="0.61230469"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString inherits="QuestFont_Super_Huge" text="CHOOSE_FACTION" parentKey="frameHeader">
					<Anchors>
						<Anchor point="TOP" x="0" y="-164"/>
					</Anchors>
					<Color r="0" g="0" b="0"/>
				</FontString>
				<FontString name="$parentAllianceLabel" inherits="DestinyFontLarge" text="THE_ALLIANCE">
					<Anchors>
						<Anchor point="TOP" x="-152" y="-214"/>
					</Anchors>
				</FontString>
				<FontString name="$parentHordeLabel" inherits="DestinyFontLarge" text="THE_HORDE">
					<Anchors>
						<Anchor point="TOP" x="152" y="-214"/>
					</Anchors>
				</FontString>
				<Texture name="$parentLeftOrnament" file="Interface\Destiny\UI-Destiny">
					<Size x="80" y="14"/>
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentAllianceLabel" relativePoint="BOTTOM" x="0" y="-10"/>
					</Anchors>
					<TexCoords left="0.00097656" right="0.07910156" top="0.93847656" bottom="0.95214844"/>
				</Texture>
				<Texture name="$parentRightOrnament" file="Interface\Destiny\UI-Destiny">
					<Size x="80" y="14"/>
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentHordeLabel" relativePoint="BOTTOM" x="0" y="-10"/>
					</Anchors>
					<TexCoords left="0.00097656" right="0.07910156" top="0.93847656" bottom="0.95214844"/>
				</Texture>
				<FontString name="$parentAllianceText" inherits="GameFontBlack" text="CHOOSE_THE_ALLIANCE" parentKey="allianceText">
					<Size x="180" y="0"/>
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentLeftOrnament" relativePoint="BOTTOM" x="0" y="-10"/>
					</Anchors>
					<Color r="0.10" g="0.10" b="0.10"/>
				</FontString>
				<FontString name="$parentHordeText" inherits="GameFontBlack" text="CHOOSE_THE_HORDE" parentKey="hordeText">
					<Size x="180" y="0"/>
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentRightOrnament" relativePoint="BOTTOM" x="0" y="-10"/>
					</Anchors>
					<Color r="0.10" g="0.10" b="0.10"/>
				</FontString>
				<FontString name="$parentAllianceFinalText" inherits="GameFontBlackMedium" text="ALLIANCE_CHEER">
					<Size x="180" y="0"/>
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentAllianceText" relativePoint="BOTTOM" x="0" y="-10"/>
						<Anchor point="BOTTOM" relativeTo="DestinyAllianceButton" relativePoint="TOP" x="0" y="0"/>
					</Anchors>
					<Color r="0.10" g="0.10" b="0.10"/>
				</FontString>
				<FontString name="$parentHordeFinalText" inherits="GameFontBlackMedium" text="HORDE_CHEER">
					<Size x="180" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentAllianceFinalText" relativePoint="RIGHT" x="124" y="0"/>
					</Anchors>
					<Color r="0.10" g="0.10" b="0.10"/>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				self:RegisterEvent("SHOW_FACTION_SELECT_UI");
			</OnLoad>
			<OnEvent function="DestinyFrame_OnEvent"/>
			<OnKeyDown>
				if ( GetBindingFromClick(key) == "TOGGLEGAMEMENU" ) then
					self:Hide();
				end
			</OnKeyDown>
		</Scripts>
	</Frame>
</Ui>
