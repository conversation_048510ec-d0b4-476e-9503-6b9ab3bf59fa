<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="DressUpFrames.lua"/>
	<Frame name="SideDressUpFrame" toplevel="true" parent="UIParent" enableMouse="true" hidden="true">
		<Size>
			<AbsDimension x="187" y="389"/>
		</Size>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentTop" file="Interface\AuctionFrame\AuctionHouseDressUpFrame-Top">
					<Size>
						<AbsDimension x="256" y="256"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture file="Interface\AuctionFrame\AuctionHouseDressUpFrame-Bottom">
					<Size>
						<AbsDimension x="256" y="256"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTop" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="BGTopLeft" name="$parentBackgroundTop">
					<Size>
						<AbsDimension x="171" y="282"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT">
							<Offset>
								<AbsDimension x="5" y="-14"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0" right="0.61" top="0" bottom="1.0"/>
				</Texture>
				<Texture parentKey="BGBottomLeft" name="$parentBackgroundBot">
					<Size>
						<AbsDimension x="171" y="83"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentBackgroundTop" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.61" top="0" bottom="0.588"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<DressUpModel name="SideDressUpModel" scale="1.0" inherits="ModelWithControlsTemplate">
				<Size>
					<AbsDimension x="172" y="400"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT" x="5" y="-13"/>
					<Anchor point="BOTTOMRIGHT" x="-11" y="11"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						Model_OnLoad(self, MODELFRAME_MAX_PLAYER_ZOOM);
					</OnLoad>
				</Scripts>
				<Frames>
					<Button parentKey="ResetButton" name="$parentResetButton" inherits="UIPanelButtonTemplate" text="RESET">
						<Size>
							<AbsDimension x="80" y="22"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOM">
								<Offset>
									<AbsDimension x="0" y="40"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnClick>
								SideDressUpModel:Dress();
								PlaySound(SOUNDKIT.GS_TITLE_OPTION_OK);
							</OnClick>
						</Scripts>
					</Button>
					<Button name="$parentCloseButton" inherits="UIPanelCloseButton">
						<Anchors>
							<Anchor point="CENTER" relativeTo="SideDressUpFrame" relativePoint="TOPRIGHT">
								<Offset>
									<AbsDimension x="-15" y="-16"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture file="Interface\AuctionFrame\AuctionHouseDressUpFrame-Corner">
									<Size>
										<AbsDimension x="32" y="32"/>
									</Size>
									<Anchors>
										<Anchor point="TOPRIGHT" relativeTo="SideDressUpFrame">
											<Offset>
												<AbsDimension x="-5" y="-5"/>
											</Offset>
										</Anchor>
									</Anchors>
								</Texture>
							</Layer>
						</Layers>
						<Scripts>
							<OnClick>
								HideUIPanel(self:GetParent():GetParent());
							</OnClick>
						</Scripts>
					</Button>
				</Frames>
			</DressUpModel>
		</Frames>
		<Scripts>
			<OnLoad>
				self.ResetButton = SideDressUpModel.ResetButton;
			</OnLoad>
			<OnShow function="SideDressUpFrame_OnShow"/>
			<OnHide function="SideDressUpFrame_OnHide"/>
		</Scripts>
	</Frame> 
	<Frame name="DressUpFrame" toplevel="true" enableMouse="true" parent="UIParent" hidden="true" inherits="ButtonFrameTemplate">
		<Size x="450" y="545"/>
		<Anchors>
			<Anchor point="TOPLEFT" x="70" y="-104"/>
		</Anchors>
		<HitRectInsets>
			<AbsInset left="0" right="30" top="0" bottom="45"/>
		</HitRectInsets>
		<Frames>
			<Frame parentKey="OutfitDropDown" name="$parentOutfitDropDown" inherits="WardrobeOutfitDropDownTemplate" mixin="DressUpOutfitMixin">
				<KeyValues>
					<KeyValue key="width" value="163" type="number"/>
					<KeyValue key="minMenuStringWidth" value="127" type="number"/>
					<KeyValue key="maxMenuStringWidth" value="190" type="number"/>
				</KeyValues>
				<Anchors>
					<Anchor point="TOP" x="-23" y="-28"/>
				</Anchors>
			</Frame>
			<Frame name="MaximizeMinimizeFrame" inherits="MaximizeMinimizeButtonFrameTemplate">
				<Anchors>
					<Anchor point="RIGHT" relativeKey="$parent.CloseButton" relativePoint="LEFT" x="10" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture atlas="UI-OuterBorderButtonPatch" useAtlasSize="true">
							<Anchors>
								<Anchor point="CENTER" relativePoint="LEFT" x="6" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>						
						function OnMaximize(frame)
							frame:GetParent():SetSize(450, 545);
							PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						end
						
						self:SetOnMaximizedCallback(OnMaximize);
						
						function OnMinimize(frame)
							frame:GetParent():SetSize(334, 423);
							PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						end
						
						self:SetOnMinimizedCallback(OnMinimize);
						
						self:SetMinimizedCVar("miniDressUpFrame");
					</OnLoad>
				</Scripts>
			</Frame>
			<Button name="DressUpFrameCancelButton" inherits="UIPanelButtonTemplate" text="CLOSE">
				<Size x="80" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT" x="-7" y="4"/>
				</Anchors>
				<Scripts>
					<OnClick function="HideParentPanel"/>
				</Scripts>
			</Button>
			<Button parentKey="ResetButton" name="DressUpFrameResetButton" inherits="UIPanelButtonTemplate" text="RESET">
				<Size x="80" y="22"/>
				<Anchors>
					<Anchor point="RIGHT" relativeTo="DressUpFrameCancelButton" relativePoint="LEFT"/>
				</Anchors>
				<Scripts>
					<OnClick>
						self:GetParent().DressUpModel:Dress();
						PlaySound(SOUNDKIT.GS_TITLE_OPTION_OK);
					</OnClick>
				</Scripts>
			</Button>
			<DressUpModel name="DressUpModel" parentKey="DressUpModel" inherits="ModelWithControlsTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" x="7" y="-63"/>
					<Anchor point="BOTTOMRIGHT" x="-9" y="28"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						Model_OnLoad(self, MODELFRAME_MAX_PLAYER_ZOOM);
					</OnLoad>
					<OnDressModel function="DressUpFrame_OnDressModel"/>
					<OnHide>
						self:SetSheathed(false);
					</OnHide>
				</Scripts>
			</DressUpModel>
		</Frames>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="ModelBackground" atlas="dressingroom-background-warrior">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.DressUpModel"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.DressUpModel"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				self.TitleText:SetText(DRESSUP_FRAME);
			</OnLoad>
			<OnShow>
				SetPortraitTexture(DressUpFramePortrait, "player");
				PlaySound(SOUNDKIT.IG_CHARACTER_INFO_OPEN);
			</OnShow>
			<OnHide>
				PlaySound(SOUNDKIT.IG_CHARACTER_INFO_CLOSE);
			</OnHide>
		</Scripts>
	</Frame>
</Ui>