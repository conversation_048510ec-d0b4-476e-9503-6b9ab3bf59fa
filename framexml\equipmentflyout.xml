<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
    <Script file="EquipmentFlyout.lua"/>

	<Texture name="EquipmentFlyoutTexture" file="Interface\PaperDollInfoFrame\UI-GearManager-Flyout" hidden="true" virtual="true"/>
	<Button name="EquipmentFlyoutButtonTemplate" inherits="ItemButtonTemplate" virtual="true">
		<Frames>
			<Cooldown name="$parentCooldown" parentKey="cooldown" inherits="CooldownFrameTemplate"/>
		</Frames>
		<Layers>
			<Layer level="OVERLAY">
				<Texture parentKey="UpgradeIcon" atlas="bags-greenarrow" useAtlasSize="true" hidden="true">
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnEnter function="EquipmentFlyoutButton_OnEnter"/>
			<OnLeave>
				GameTooltip:Hide();
				ResetCursor();
			</OnLeave>
			<OnClick function="EquipmentFlyoutButton_OnClick"/>
		</Scripts>
	</Button>
	<Button name="EquipmentFlyoutPopoutButtonTemplate" hidden="true" virtual="true">
		<Size>
			<AbsDimension x="16" y="32"/>
		</Size>
		<Anchors>
			<Anchor point="LEFT" relativePoint="RIGHT"/>
		</Anchors>
		<Scripts>
			<OnLoad function="EquipmentFlyoutPopoutButton_OnLoad"/>
			<OnClick function="EquipmentFlyoutPopoutButton_OnClick"/>
		</Scripts>
		<NormalTexture file="Interface\PaperDollInfoFrame\UI-GearManager-FlyoutButton"/>
		<HighlightTexture file="Interface\PaperDollInfoFrame\UI-GearManager-FlyoutButton"/>
	</Button>	
	<Frame name="EquipmentFlyoutFrame" hidden="true" enableMouse="false" frameStrata="HIGH">
		<Size x="43" y="43"/>
		<Layers>
			<Layer level="OVERLAY">
				<Texture name="$parentHighlight" file="Interface\PaperDollInfoFrame\UI-GearManager-ItemButton-Highlight">
					<Size x="50" y="50"/>
					<Anchors>
						<Anchor point="LEFT">
							<Offset x="-4" y="0"/>
						</Anchor>
					</Anchors>
					<TexCoords left="0" right="0.78125" top="0" bottom="0.78125"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentButtons" frameStrata="HIGH" enableMouse="true" parentKey="buttonFrame" clampedToScreen="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativePoint="TOPRIGHT">
						<Offset x="0" y="0"/>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture parentKey="bg1" inherits="EquipmentFlyoutTexture">
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset x="-5" y="4"/>
								</Anchor>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						self.numBGs = 1;
					</OnLoad>
				</Scripts>
			</Frame>
			<Frame parentKey="NavigationFrame" frameStrata="HIGH">
				<Size x="214" y="41"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.buttonFrame" relativePoint="BOTTOMLEFT" x="-5" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="Test2" file="Interface\PaperDollInfoFrame\UI-GearManager-Flyout" setAllPoints="true">
							<TexCoords left="0" right="0.8359375" top="0.7890625" bottom="0.94921875"/>
						</Texture>
					</Layer>
					<Layer level="ARTWORK">
						<FontString inherits="GameFontHighlightSmall" text="PREVIOUS">
							<Anchors>
								<Anchor point="LEFT" x="36" y="1"/>
							</Anchors>
						</FontString>
						<FontString inherits="GameFontHighlightSmall" text="NEXT">
							<Anchors>
								<Anchor point="RIGHT" x="-33" y="1"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button parentKey="PrevButton">
						<Size>
							<AbsDimension x="32" y="32"/>
						</Size>
						<Anchors>
							<Anchor point="LEFT" x="6" y="1"/>
						</Anchors>
						<Scripts>
							<OnClick>
								EquipmentFlyout_ChangePage(-1);
							</OnClick>
						</Scripts>
						<NormalTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Up"/>
						<PushedTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Down"/>
						<DisabledTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Disabled"/>
						<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
					</Button>
					<Button parentKey="NextButton">
						<Size>
							<AbsDimension x="32" y="32"/>
						</Size>
						<Anchors>
							<Anchor point="RIGHT" x="-3" y="1"/>
						</Anchors>
						<Scripts>
							<OnClick>
								EquipmentFlyout_ChangePage(1);
							</OnClick>
						</Scripts>
						<NormalTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Up"/>
						<PushedTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Down"/>
						<DisabledTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Disabled"/>
						<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
					</Button>
				</Frames>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="EquipmentFlyout_OnLoad"/>
			<OnUpdate function="EquipmentFlyout_OnUpdate"/>
			<OnShow function="EquipmentFlyout_OnShow"/>
			<OnHide function="EquipmentFlyout_OnHide"/>
			<OnEvent function="EquipmentFlyout_OnEvent"/>
		</Scripts>
	</Frame>
</Ui>