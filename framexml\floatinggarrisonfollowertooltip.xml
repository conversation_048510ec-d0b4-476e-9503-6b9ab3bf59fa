<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
UI.xsd">
	<Script file="FloatingGarrisonFollowerTooltip.lua"/>

	<Frame name="GarrisonFollowerAbilityTemplate" virtual="true">
		<Size x="200" y="20"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="Icon">
					<Size x="20" y="20"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="2"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Name" inherits="GameFontNormal" justifyH="LEFT">
					<Size x="200" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Icon" relativePoint="RIGHT" x="4"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Description" inherits="GameFontHighlight" justifyH="LEFT" wordwrap="true">
					<Size x="200" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Name" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
					</Anchors>
				</FontString>
				<Texture parentKey="CounterIcon">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Description" relativePoint="BOTTOMLEFT" x="0" y="-8"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Details" inherits="GameFontHighlight" justifyH="LEFT">
					<Size x="176" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.CounterIcon" relativePoint="TOPRIGHT" x="8" y="-1"/>
					</Anchors>
					<Color r="0.698" g="0.941" b="1" a="1"/>
				</FontString>

				<Texture atlas="GarrMission_EncounterAbilityBorder" useAtlasSize="true" parentKey="CounterIconBorder" hidden="true">
					<Size x="15" y="15"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.CounterIcon"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="1">
				<Texture parentKey="Border" atlas="GarrMission_EncounterAbilityBorder" hidden="true">
					<Size x="30" y="30"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Icon"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>
	
	<Frame name="GarrisonFollowerTooltipTemplate" parent="UIParent" frameStrata="TOOLTIP" clampedToScreen="true" virtual="true" hidden="true" inherits="TooltipBorderedFrameTemplate">
		<Size x="260" y="80"/>
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="2">
				<Texture parentKey="Class" atlas="GarrMission_ClassIcon-DeathKnight" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPRIGHT" y="-5" x="-5"/>
					</Anchors>
					<Color r="1" g="1" b="1" a=".2"/>
				</Texture>
				<Texture parentKey="XPBarBackground">
					<Size x="180" y="4"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="66" y="-55"/>
					</Anchors>
					<Color r="0.1" g="0.1" b="0.1" a="1"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Name" inherits="GameFontNormal" justifyH="LEFT">
					<Size x="140" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativePoint="TOPLEFT" x="66" y="-20"/>
					</Anchors>
				</FontString>
				<FontString parentKey="ClassSpecName" inherits="GameFontHighlightSmall" justifyH="LEFT">
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="66" y="-35"/>
					</Anchors>
					<Color r="1" g="1" b="1" a="1"/>
				</FontString>
				<FontString parentKey="ILevel" inherits="GameFontHighlightSmall" justifyH="LEFT">
					<Size x="190" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="66" y="-50"/>
					</Anchors>
					<Color r="0.745" g="0.745" b="0.745" a="1"/>
				</FontString>
				<FontString parentKey="Quality" inherits="GameFontHighlightSmall" justifyH="LEFT">
					<Size x="190" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="12" y="-72"/>
					</Anchors>
				</FontString>
				<Texture parentKey="XPBar">
					<Size x="180" y="4"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="66" y="-55"/>
					</Anchors>
					<Color r="0.212" g="0" b="0.337" a="1"/>
				</Texture>
				<FontString parentKey="XP" inherits="GameFontHighlightSmall" justifyH="CENTER">
					<Size x="180"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.XPBar" relativePoint="BOTTOMLEFT" y="-3"/>
					</Anchors>
					<Color r="1" g="1" b="1" a="1"/>
				</FontString>

				<FontString parentKey="SpecializationLabel" inherits="GameFontNormal" justifyH="LEFT" text="SPECIALIZATION">
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="15" y="-85" />
					</Anchors>
				</FontString>

				<FontString parentKey="AbilitiesLabel" inherits="GameFontNormal" justifyH="LEFT" text="ABILITIES">
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="15" y="-85" />
					</Anchors>
				</FontString>

				<FontString parentKey="TraitsLabel" inherits="GameFontNormal" justifyH="LEFT" text="GARRISON_TRAITS">
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="15" y="-85" />
					</Anchors>
				</FontString>

				<FontString parentKey="UnderBiased" inherits="GameFontNormal" justifyH="LEFT" wordwrap="true">
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="15" y="-85" />
						<Anchor point="TOPRIGHT" relativePoint="TOPRIGHT" x="-15" y="-85" />
					</Anchors>
				</FontString>
			</Layer>
		</Layers>

		<Frames>

			<Frame parentKey="PortraitFrame" inherits="GarrisonFollowerPortraitTemplate" hidden="false">
				<Anchors>
					<Anchor point="TOPLEFT" x="10" y="-10"/>
				</Anchors>
			</Frame>
			<Frame parentArray="Abilities" inherits="GarrisonFollowerAbilityTemplate" hidden="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.AbilitiesLabel" x="2" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Frame>

			<Frame parentArray="Traits" inherits="GarrisonFollowerAbilityTemplate" hidden="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.TraitsLabel" x="2" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="GarrisonFollowerTooltip_OnLoad"/>
		</Scripts>
	</Frame>

	<Frame name="GarrisonShipyardFollowerTooltipTemplate" parent="UIParent" frameStrata="TOOLTIP" clampedToScreen="true" virtual="true" hidden="true" inherits="TooltipBorderedFrameTemplate">
		<Size x="260" y="80"/>
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="2">
				<Texture parentKey="XPBarBackground">
					<Size x="231" y="4"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="15" y="-55"/>
					</Anchors>
					<Color r="0.1" g="0.1" b="0.1" a="1"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Name" inherits="GameFontNormal" justifyH="LEFT">
					<Size x="140" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativePoint="TOPLEFT" x="15" y="-20"/>
					</Anchors>
				</FontString>
				<FontString parentKey="ClassSpecName" inherits="GameFontHighlightSmall" justifyH="LEFT">
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="15" y="-35"/>
					</Anchors>
					<Color r="1" g="1" b="1" a="1"/>
				</FontString>
				<FontString parentKey="Quality" inherits="GameFontHighlightSmall" justifyH="LEFT">
					<Size x="190" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="15" y="-50"/>
					</Anchors>
				</FontString>
				<Texture parentKey="XPBar">
					<Size x="231" y="4"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="15" y="-55"/>
					</Anchors>
					<Color r="0.212" g="0" b="0.337" a="1"/>
				</Texture>
				<FontString parentKey="XP" inherits="GameFontHighlightSmall" justifyH="CENTER">
					<Size x="231"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.XPBar" relativePoint="BOTTOMLEFT" y="-3"/>
					</Anchors>
					<Color r="1" g="1" b="1" a="1"/>
				</FontString>
			</Layer>
		</Layers>

		<Frames>
			<Frame parentKey="Property1" parentArray="Properties" inherits="GarrisonFollowerAbilityTemplate" hidden="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.ClassSpecName" x="2" y="-10" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Frame>
			<Frame parentKey="Property2" parentArray="Properties" inherits="GarrisonFollowerAbilityTemplate" hidden="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.Property1" x="0" y="-10" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Frame>
			<Frame parentKey="Property3" parentArray="Properties" inherits="GarrisonFollowerAbilityTemplate" hidden="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.Property2" x="0" y="-10" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Frame>
			<Frame parentKey="Property4" parentArray="Properties" inherits="GarrisonFollowerAbilityTemplate" hidden="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.Property3" x="0" y="-10" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="GarrisonFollowerTooltip_OnLoad"/>
		</Scripts>
	</Frame>

	<Frame name="FloatingGarrisonFollowerTooltip" inherits="GarrisonFollowerTooltipTemplate" movable="true" toplevel="true">
		<Size x="260" y="150"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="80"/>
		</Anchors>
		<Frames>
			<Button parentKey="CloseButton">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="1" y="0"/>
				</Anchors>
				<Scripts>
					<OnClick>
						self:GetParent():Hide();
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Up"/>
				<PushedTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Down"/>
				<HighlightTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Highlight" alphaMode="ADD"/>
			</Button>
		</Frames>
		<Scripts>
			<OnMouseDown>
				self:StartMoving();
			</OnMouseDown>
			<OnMouseUp>
				self:StopMovingOrSizing();
			</OnMouseUp>
		</Scripts>
	</Frame>

	<Frame name="FloatingGarrisonShipyardFollowerTooltip" inherits="GarrisonShipyardFollowerTooltipTemplate" movable="true" toplevel="true">
		<Size x="260" y="150"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="80"/>
		</Anchors>
		<Frames>
			<Button parentKey="CloseButton">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="1" y="0"/>
				</Anchors>
				<Scripts>
					<OnClick>
						self:GetParent():Hide();
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Up"/>
				<PushedTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Down"/>
				<HighlightTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Highlight" alphaMode="ADD"/>
			</Button>
		</Frames>
		<Scripts>
			<OnMouseDown>
				self:StartMoving();
			</OnMouseDown>
			<OnMouseUp>
				self:StopMovingOrSizing();
			</OnMouseUp>
		</Scripts>
	</Frame>


	<!-- 6.x style -->
	<Frame name="GarrisonFollowerAbilityTooltipTemplate" parent="UIParent" frameStrata="TOOLTIP" clampedToScreen="true" virtual="true" hidden="true" inherits="TooltipBorderedFrameTemplate">
		<Size x="240" y="40"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="Icon">
					<Size x="24" y="24"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="10" y="-10"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Name" inherits="GameFontNormal" justifyH="LEFT">
					<Size x="190" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Icon" relativePoint="RIGHT" x="6"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Description" inherits="GameFontHighlight" justifyH="LEFT" wordwrap="true">
					<Size x="190" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Name" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
					</Anchors>
				</FontString>
				<FontString parentKey="CountersLabel" inherits="GameFontHighlight" justifyH="LEFT" text="GARRISON_ABILITY_COUNTERS">
					<Size x="190" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Description" relativePoint="BOTTOMLEFT" x="0" y="-8"/>
					</Anchors>
					<Color r="0.698" g="0.941" b="1" a="1"/>
				</FontString>
				<Texture parentKey="CounterIcon">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.CountersLabel" relativePoint="BOTTOMLEFT" x="4" y="-8"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Details" inherits="GameFontHighlight" justifyH="LEFT">
					<Size x="176" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.CounterIcon" relativePoint="TOPRIGHT" x="8" y="-1"/>
					</Anchors>
				</FontString>
				<Texture atlas="GarrMission_EncounterAbilityBorder" useAtlasSize="true" parentKey="CounterIconBorder" hidden="true">
					<Size x="15" y="15"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.CounterIcon"/>
					</Anchors>
				</Texture>

			</Layer>
		</Layers>
		<KeyValues>
			<KeyValue key="abilityFrameHeightBase" value="30" type="number"/>
			<KeyValue key="spacingBetweenNameAndDescription" value="4" type="number"/>
			<KeyValue key="spacingBetweenDescriptionAndDetails" value="8" type="number"/>
		</KeyValues>
	</Frame>

	<!-- 7.0 style -->
	<Frame name="GarrisonFollowerAbilityWithoutCountersTooltipTemplate" parent="UIParent" frameStrata="TOOLTIP" clampedToScreen="true" virtual="true" hidden="true" inherits="TooltipBorderedFrameTemplate">
		<Size x="240" y="40"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="Icon">
					<Size x="24" y="24"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="12" y="-12"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Name" inherits="GameTooltipHeaderText" justifyH="LEFT">
					<Size x="190" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Icon" relativePoint="RIGHT" x="8"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Description" inherits="GameTooltipText" justifyH="LEFT" wordwrap="true">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Icon" relativePoint="BOTTOMLEFT" x="0" y="-6"/>
					</Anchors>
					<Size x="210" y="0"/>
					<Color r="1.0" g="0.82" b="0.0"/>
				</FontString>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="1">
				<Texture atlas="GarrMission_EncounterAbilityBorder-Lg" useAtlasSize="false" parentKey="AbilityBorder" hidden="false">
					<Size x="40" y="40"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Icon"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<KeyValues>
			<KeyValue key="abilityFrameHeightBase" value="30" type="number"/>
			<KeyValue key="spacingBetweenNameAndDescription" value="8" type="number"/>
			<KeyValue key="spacingBetweenDescriptionAndDetails" value="0" type="number"/>
		</KeyValues>
	</Frame>

	<!-- 7.0 style for the mission page -->
	<Frame name="GarrisonFollowerMissionAbilityWithoutCountersTooltipTemplate" parent="UIParent" frameStrata="TOOLTIP" clampedToScreen="true" virtual="true" hidden="true" inherits="TooltipBorderedFrameTemplate">
		<Size x="240" y="40"/>
		<Layers>
			<Layer level="ARTWORK">
				<FontString parentKey="Header" inherits="GameTooltipText" text="GARRISON_MISSION_COUNTER_FROM">
					<Anchors>
						<Anchor point="TOPLEFT" x="12" y="-12"/>
					</Anchors>
					<Color r="0.7" g="1.0" b="1.0"/>
				</FontString>
				<Texture parentKey="Icon">
					<Size x="24" y="24"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="-8" relativeKey="$parent.Header" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Name" inherits="GameTooltipHeaderText" justifyH="LEFT">
					<Size x="190" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Icon" relativePoint="RIGHT" x="8"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Description" inherits="GameTooltipText" justifyH="LEFT" wordwrap="true">
					<Size x="210" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Icon" relativePoint="BOTTOMLEFT" x="0" y="-8"/>
					</Anchors>
					<Color r="1.0" g="0.82" b="0.0"/>
				</FontString>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="1">
				<Texture atlas="GarrMission_EncounterAbilityBorder-Lg" useAtlasSize="false" parentKey="AbilityBorder" hidden="false">
					<Size x="40" y="40"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Icon"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<KeyValues>
			<KeyValue key="abilityFrameHeightBase" value="46" type="number"/>
			<KeyValue key="spacingBetweenNameAndDescription" value="8" type="number"/>
			<KeyValue key="spacingBetweenDescriptionAndDetails" value="0" type="number"/>
		</KeyValues>
	</Frame>


	<Frame name="FloatingGarrisonFollowerAbilityTooltip" inherits="GarrisonFollowerAbilityTooltipTemplate" movable="true" toplevel="true">
		<Size x="245" y="45"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="80"/>
		</Anchors>
		<Frames>
			<Button parentKey="CloseButton">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="1" y="0"/>
				</Anchors>
				<Scripts>
					<OnClick>
						self:GetParent():Hide();
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Up"/>
				<PushedTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Down"/>
				<HighlightTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Highlight" alphaMode="ADD"/>
			</Button>
		</Frames>
		<Scripts>
			<OnMouseDown>
				self:StartMoving();
			</OnMouseDown>
			<OnMouseUp>
				self:StopMovingOrSizing();
			</OnMouseUp>
		</Scripts>
	</Frame>

	<Frame name="FloatingGarrisonMissionTooltip" movable="true" toplevel="true" parent="UIParent" frameStrata="TOOLTIP" clampedToScreen="true" hidden="true" inherits="TooltipBorderedFrameTemplate">
		<Size x="240" y="70"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="80"/>
		</Anchors>
		<Layers>
			<Layer level="ARTWORK">
				<FontString parentKey="Name" inherits="GameFontNormal" justifyH="LEFT">
					<Anchors>
						<Anchor point="TOPLEFT" x="10" y="-10"/>
					</Anchors>
				</FontString>
				<FontString parentKey="FollowerRequirement" inherits="GameFontHighlight" justifyH="LEFT">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Name" relativePoint="BOTTOMLEFT" y="-3"/>
					</Anchors>
				</FontString>
				<FontString parentKey="RewardsLabel" inherits="GameFontNormal" justifyH="LEFT" text="REWARDS">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.FollowerRequirement" relativePoint="BOTTOMLEFT" y="-12"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Rewards" inherits="GameFontHighlight" justifyH="LEFT">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.RewardsLabel" relativePoint="BOTTOMLEFT" />
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="CloseButton">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="1" y="0"/>
				</Anchors>
				<Scripts>
					<OnClick>
						self:GetParent():Hide();
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Up"/>
				<PushedTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Down"/>
				<HighlightTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Highlight" alphaMode="ADD"/>
			</Button>
		</Frames>
		<Scripts>
			<OnMouseDown>
				self:StartMoving();
			</OnMouseDown>
			<OnMouseUp>
				self:StopMovingOrSizing();
			</OnMouseUp>
			<OnShow function="FloatingGarrisonMissionTooltip_OnShow"/>
			<OnHide function="FloatingGarrisonMissionTooltip_OnHide"/>
			<OnEvent function="FloatingGarrisonMissionTooltip_OnEvent"/>
		</Scripts>
	</Frame>


</Ui>
