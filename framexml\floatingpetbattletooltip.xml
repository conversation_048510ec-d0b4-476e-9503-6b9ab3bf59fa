<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
UI.xsd">
	<Script file="FloatingPetBattleTooltip.lua"/>
	<Frame name="FloatingPetBattleAbilityTooltip" inherits="SharedPetBattleAbilityTooltipTemplate" movable="true" toplevel="true">
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="80"/>
		</Anchors>
		<Frames>
			<Button parentKey="CloseButton">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="1" y="0"/>
				</Anchors>
				<Scripts>
					<OnClick>
						self:GetParent():Hide();
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Up"/>
				<PushedTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Down"/>
				<HighlightTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Highlight" alphaMode="ADD"/>
			</Button>
		</Frames>
		<Scripts>
			<OnMouseDown>
				self:StartMoving();
			</OnMouseDown>
			<OnMouseUp>
				self:StopMovingOrSizing();
			</OnMouseUp>
		</Scripts>
	</Frame>
	
	<Frame name="BattlePetTooltipTemplate" parent="UIParent" frameStrata="TOOLTIP" clampedToScreen="true" virtual="true" hidden="true" inherits="TooltipBorderedFrameTemplate">
		<Size x="260" y="150"/>
		<Layers>	
			<Layer level="ARTWORK">
				<FontString parentKey="Name" wordwrap="false" inherits="GameFontNormalLargeLeft">
					<Size x="238" y="0"/>
					<Anchors>
						<Anchor point="TOP" x="0" y="-10"/>
					</Anchors>
				</FontString>
				
				<FontString parentKey="BattlePet" inherits="GameFontHighlight" justifyH="LEFT" text="TOOLTIP_BATTLE_PET">
					<Size x="238" y="0"/>
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.Name" relativePoint="BOTTOM" x="0" y="-5"/>
					</Anchors>
				</FontString>
				<FontString parentKey="PetType" inherits="GameFontHighlight" justifyH="RIGHT" text="TOOLTIP_BATTLE_PET">
					<Size x="238" y="0"/>
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.Name" relativePoint="BOTTOM" x="0" y="-5"/>
					</Anchors>
				</FontString>
				<Texture parentKey="PetTypeTexture">
					<Size x="33" y="33"/>
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.PetType" relativePoint="BOTTOMRIGHT" x="0" y="-5"/>
					</Anchors>
					<TexCoords left="0.79687500" right="0.49218750" top="0.50390625" bottom="0.65625000"/>
				</Texture>
				<FontString parentKey="Level" inherits="GameFontHighlight" justifyH="LEFT" text="BATTLE_PET_CAGE_TOOLTIP_LEVEL">
					<Size x="238" y="0"/>
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.BattlePet" relativePoint="BOTTOM" x="0" y="-2"/>
					</Anchors>
				</FontString>
				
				<Texture parentKey="HealthTexture" file="Interface\PetBattles\PetBattle-StatIcons">
					<Size x="16" y="16"/>
					<TexCoords left="0.5" right="1.0" top="0.5" bottom="1.0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Level" relativePoint="BOTTOMLEFT" x="0" y="-2"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Health" inherits="GameFontHighlight" justifyH="LEFT" text="100">
					<Size x="0" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.HealthTexture" relativePoint="RIGHT" x="2" y="0"/>
					</Anchors>
				</FontString>
				
				<Texture parentKey="PowerTexture" file="Interface\PetBattles\PetBattle-StatIcons">
					<Size x="16" y="16"/>
					<TexCoords left="0.0" right="0.5" top="0.0" bottom="0.5"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.HealthTexture" relativePoint="BOTTOMLEFT" x="0" y="-2"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Power" inherits="GameFontHighlight" justifyH="LEFT" text="100">
					<Size x="0" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.PowerTexture" relativePoint="RIGHT" x="2" y="0"/>
					</Anchors>
				</FontString>
				
				<Texture parentKey="SpeedTexture" file="Interface\PetBattles\PetBattle-StatIcons">
					<Size x="16" y="16"/>
					<TexCoords left="0.0" right="0.5" top="0.5" bottom="1.0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.PowerTexture" relativePoint="BOTTOMLEFT" x="0" y="-2"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Speed" inherits="GameFontHighlight" justifyH="LEFT" text="100">
					<Size x="0" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.SpeedTexture" relativePoint="RIGHT" x="2" y="0"/>
					</Anchors>
				</FontString>

				<FontString parentKey="Owned" wordwrap="false" inherits="GameFontHighlight" justifyH="LEFT">
					<Size x="238" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.SpeedTexture" relativePoint="BOTTOMLEFT" x="0" y="-2"/>
					</Anchors>
					<Color r="1.0" g="0.82" b="0.0" a="1.0"/>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad function="BattlePetTooltip_OnLoad"/>
		</Scripts>
	</Frame>
	
	<Frame name="FloatingBattlePetTooltip" inherits="BattlePetTooltipTemplate" movable="true" toplevel="true">
		<Size x="260" y="150"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="80"/>
		</Anchors>
		<Layers>	
			<Layer level="ARTWORK">
				<Texture parentKey="Delimiter">
					<Size x="251" y="2"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.SpeedTexture" relativePoint="BOTTOMLEFT" x="-6" y="-5"/>
					</Anchors>
					<Color r="0.2" g="0.2" b="0.2"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="CloseButton">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="1" y="0"/>
				</Anchors>
				<Scripts>
					<OnClick>
						self:GetParent():Hide();
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Up"/>
				<PushedTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Down"/>
				<HighlightTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Highlight" alphaMode="ADD"/>
			</Button>
			<Button parentKey="JournalClick" text="BATTLE_PET_TOOLTIP_CLICK">
				<Size x="238" y="20"/>
				<Anchors>
					<Anchor point="TOP" relativeKey="$parent.Delimiter" relativePoint="BOTTOM" x="0" y="-3"/>
				</Anchors>
				<NormalFont style="GameFontNormalLeft"/>
				<Scripts>
					<OnClick function="BattlePetTooltipJournalClick_OnClick"/>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnMouseDown>
				self:StartMoving();
			</OnMouseDown>
			<OnMouseUp>
				self:StopMovingOrSizing();
			</OnMouseUp>
		</Scripts>
	</Frame>
</Ui>
