# Do not delete the following line!
## Interface: 70300

## add C namespace augmentations here
..\SharedXML\C_TimerAugment.lua

## add snippets independent of modules here
..\SharedXML\Util.lua
..\SharedXML\Pools.lua
..\SharedXML\LoopingSoundEffect.lua
..\SharedXML\CircularBuffer.lua
..\SharedXML\FontableFrameMixin.lua
..\SharedXML\Vector2D.lua
..\SharedXML\Vector3D.lua
..\SharedXML\Spline.lua
..\SharedXML\LayoutFrame.xml
..\SharedXML\ManagedLayoutFrame.xml
..\SharedXML\BulletPoint.xml
MixinUtil.lua

## intrinsics
..\SharedXML\ScrollingMessageFrame.xml

..\SharedXML\ModelSceneTemplates.xml
..\SharedXML\ModelSceneMixin.xml
..\SharedXML\ModelSceneActorMixin.xml
..\SharedXML\FrameStack.lua

AnimatedStatusBar.xml
FlowContainer.lua
FrameLocks.lua


..\SharedXML\SoundKitConstants.lua
Constants.lua
Localization.xml
Fonts.xml
FontStyles.xml


## add new modules below here
WorldFrame.xml
UIParent.xml
QuestUtils.lua
## IME.xml needs to be loaded after UIParent.xml
..\SharedXML\IME.xml
MoneyFrame.lua
MoneyFrame.xml
MoneyInputFrame.lua
MoneyInputFrame.xml
..\SharedXML\SharedUIPanelTemplates.xml
..\SharedXML\SharedBasicControls.xml
GameTooltip.xml
UIMenu.xml
UIDropDownMenu.xml
UIPanelTemplates.lua
UIPanelTemplates.xml
SecureTemplates.xml
SecureHandlerTemplates.xml
SecureGroupHeaders.xml
..\SharedXML\ModelFrames.xml
WardrobeOutfits.xml
DressUpFrames.xml
ItemButtonTemplate.xml
NavigationBar.xml
SparkleFrame.xml
..\SharedXML\HybridScrollFrame.lua
..\SharedXML\HybridScrollFrame.xml
GameMenuFrame.xml
CharacterFrameTemplates.xml
TextStatusBar.lua
TextStatusBar.xml
UIErrorsFrame.xml
AutoComplete.xml
StaticPopup.xml
Sound.lua
OptionsFrameTemplates.xml
OptionsPanelTemplates.xml
UIOptions.xml
VideoOptionsFrame.xml
..\SharedXML\VideoOptionsPanels.xml
MacOptionsPanel.xml
AudioOptionsFrame.xml
AudioOptionsPanels.xml
InterfaceOptionsFrame.xml
InterfaceOptionsPanels.xml
..\SharedXML\AddonList.xml
GarrisonBaseUtils.xml
AlertFrames.xml
AlertFrameSystems.xml
MirrorTimer.xml
CoinPickupFrame.xml
StackSplitFrame.xml
FadingFrame.xml
ZoneText.xml
BuilderSpenderFrame.xml
UnitFrame.xml
Cooldown.xml
PVPHonorSystem.xml
ActionBarController.xml
TutorialFrame.xml
Minimap.xml
GameTime.xml
##ActionWindow.xml
EquipmentFlyout.xml
BuffFrame.xml
LowHealthFrame.xml
CombatFeedback.xml
UnitPowerBarAlt.xml
CastingBarFrame.xml
UnitPopup.xml
BNet.xml
HistoryKeeper.lua
ChatFrame.xml
FloatingChatFrame.xml
VoiceChat.xml
ReadyCheck.xml
PlayerFrame.xml
PartyFrame.xml
TargetFrame.xml
TotemFrame.xml
PetFrame.xml
StatsFrame.xml
SpellBookFrame.xml
CharacterFrame.xml
PaperDollFrame.xml
ReputationFrame.xml
QuestFrame.xml
QuestPOI.xml
QuestInfo.xml
MerchantFrame.xml
TradeFrame.xml
ContainerFrame.xml
LootFrame.xml
ItemTextFrame.xml
TaxiFrame.xml
BankFrame.xml
ScrollOfResurrection.xml
RoleSelectionTemplate.xml
ItemRef.xml
SocialQueue.xml
QuickJoinToast.xml
FriendsFrame.xml
QuickJoin.xml
RaidFrame.xml
ChannelFrame.xml
PetActionBarFrame.xml
MultiCastActionBarFrame.xml
MainMenuBarBagButtons.xml
UnitPositionFrameTemplates.xml
WorldMapFrame.xml
QuestMapFrame.xml
RaidWarning.xml
CinematicFrame.xml
ComboFrame.xml
TabardFrame.xml
GuildRegistrarFrame.xml
PetitionFrame.xml
HelpFrame.xml
ColorPickerFrame.xml
GossipFrame.xml
MailFrame.xml
PetStable.xml
VehicleSeatIndicator.xml
DurabilityFrame.xml
WorldStateFrame.xml
PVEFrame.xml
LFGFrame.xml
LFDFrame.xml
LFRFrame.xml
RaidFinder.xml
ScenarioFinder.xml
LFGList.xml
RatingMenuFrame.xml
TalentFrameBase.lua
TalentFrameTemplates.xml
ClassPowerBar.xml
RuneFrame.xml
ShardBar.xml
ComboFramePlayer.xml
InsanityBar.xml
EasyMenu.lua
ChatConfigFrame.xml
MovieFrame.xml
AlternatePowerBar.xml
MonkStaggerBar.xml
LevelUpDisplay.xml
AnimationSystem.lua
EquipmentManager.lua
CompactUnitFrame.xml
CompactRaidGroup.xml
CompactPartyFrame.xml
RolePoll.xml
SpellFlyout.xml
PaladinPowerBar.xml
MageArcaneChargesBar.xml
SpellActivationOverlay.xml
GuildInviteFrame.xml
GhostFrame.xml
StreamingFrame.xml
IconIntroAnimation.xml
Timer.xml
MonkHarmonyBar.xml
RecruitAFriendFrame.xml
DestinyFrame.xml
PriestBar.xml
SharedPetBattleTemplates.xml
LootHistory.xml
FloatingPetBattleTooltip.xml
QueueStatusFrame.xml
BattlePetTooltip.xml
FloatingGarrisonFollowerTooltip.xml
GarrisonFollowerTooltip.xml
StaticPopupSpecial.xml
LossOfControlFrame.xml
PVPHelper.xml
MapBar.xml
ProductChoice.xml
ZoneAbility.xml
ArtifactToasts.xml
..\SharedXML\ModelPreviewFrame.xml
SplashFrame.xml
QuestChoiceFrameMixin.lua

#Save off whatever we need available unmodified.
SecureCapsule.lua

## add new modules above here
LocalizationPost.xml
