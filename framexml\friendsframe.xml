<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="FriendsFrame.lua"/>

<!-- Patchwerk
TRAVEL PASS
<Texture name="TravelPass-Pressed.png" >
	<Size x="24" y="32"/>
	<TexCoords left="0.01562500" right="0.39062500" top="0.00781250" bottom="0.25781250"/>
</Texture>
<Texture name="TravelPass-Normal.png" >
	<Size x="24" y="32"/>
	<TexCoords left="0.42187500" right="0.79687500" top="0.00781250" bottom="0.25781250"/>
</Texture>
<Texture name="TravelPass-Hover.png" >
	<Size x="24" y="32"/>
	<TexCoords left="0.01562500" right="0.39062500" top="0.27343750" bottom="0.52343750"/>
</Texture>
<Texture name="TravelPass-Disabled.png" >
	<Size x="24" y="32"/>
	<TexCoords left="0.42187500" right="0.79687500" top="0.27343750" bottom="0.52343750"/>
</Texture>
-->

	<Frame name="FriendsFrameHeaderTemplate" virtual="true" enableMouse="true">
		<Size x="302" y="16"/>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentTitle" inherits="GameFontHighlightLeft">
					<Anchors>
						<Anchor point="LEFT" x="5" y="1"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="FriendsFrameFriendDividerTemplate" virtual="true" enableMouse="true">
		<Size x="298" y="16"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture file="Interface\FriendsFrame\UI-FriendsFrame-OnlineDivider" setAllPoints="true"/>
			</Layer>
		</Layers>
	</Frame>
	<Frame name="FriendsFrameFriendInviteTemplate" virtual="true">
		<Size x="298" y="34"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Background">
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="0"/>
						<Anchor point="BOTTOMRIGHT" x="0" y="2"/>
					</Anchors>
					<Color r="0" g="0.694" b="0.941" a="0.050"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Name" inherits="FriendsFont_Normal" justifyH="LEFT">
					<Size x="180" y="0"/>
					<Anchors>
						<Anchor point="LEFT" x="8" y="2"/>
					</Anchors>
					<Color r="0.510" g="0.773" b="1" />
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="DeclineButton" inherits="UIMenuButtonStretchTemplate" motionScriptsWhileDisabled="true">
				<Size x="24" y="22"/>
				<Anchors>
					<Anchor point="RIGHT" x="-7" y="1"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture parentKey="Icon" atlas="groupfinder-icon-redx" useAtlasSize="true">
							<Anchors>
								<Anchor point="CENTER" x="0" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						FriendsListPendingInviteDropDown.inviteID = self:GetParent().inviteID;
						FriendsListPendingInviteDropDown.inviteIndex = self:GetParent().inviteIndex;
						ToggleDropDownMenu(1, nil, FriendsListPendingInviteDropDown, self, -6, -3);
					</OnClick>
				</Scripts>
				<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
			</Button>
			<Button parentKey="AcceptButton" inherits="UIMenuButtonStretchTemplate" text="ACCEPT">
				<Size x="0" y="22"/>
				<Anchors>
					<Anchor point="RIGHT" relativeKey="$parent.DeclineButton" relativePoint="LEFT" x="-2" y="0"/>
				</Anchors>
				<NormalFont style="GameFontHighlightSmall"/>
				<HighlightFont style="GameFontHighlightSmall"/>
				<Scripts>
					<OnLoad>
						self:SetWidth(self.Text:GetStringWidth() + 40);
						self.Text:SetPoint("CENTER", 0, 0);
					</OnLoad>
					<OnClick>
						FriendsList_ClosePendingInviteDialogs();
						BNAcceptFriendInvite(self:GetParent().inviteID);
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
	</Frame>
	<Button name="FriendsFrameButtonTemplate" hidden="true" virtual="true">
		<Size x="298" y="34"/>
		<Layers>
			<Layer level="BACKGROUND">
				<FontString inherits="GameFontNormal" parentKey="text" />
				<Texture name="$parentBackground" parentKey="background">
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="-1"/>
						<Anchor point="BOTTOMRIGHT" x="0" y="1"/>
					</Anchors>
					<Color r="0" g="0.694" b="0.941" a="0.050"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture name="$parentStatus" file="Interface\FriendsFrame\StatusIcon-Online" parentKey="status">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="4" y="-3"/>
					</Anchors>
				</Texture>
				<Texture name="$parentGameIcon" file="Interface\FriendsFrame\Battlenet-WoWicon" parentKey="gameIcon">
					<Size x="28" y="28"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="-21" y="-3"/>
					</Anchors>
				</Texture>
				<FontString name="$parentName" inherits="FriendsFont_Normal" justifyH="LEFT" parentKey="name">
					<Size x="226" y="12"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="20" y="-4"/>
					</Anchors>
					<Color r="0.510" g="0.773" b="1" />
				</FontString>
				<FontString name="$parentInfo" inherits="FriendsFont_Small" justifyH="LEFT" parentKey="info">
					<Size x="260" y="10"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentName" relativePoint="BOTTOMLEFT" x="0" y="-3"/>
					</Anchors>
					<Color r="0.486" g="0.518" b="0.541" />
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="$parentTravelPassButton" hidden="true" motionScriptsWhileDisabled="true" parentKey="travelPassButton">
				<Size x="24" y="32"/>
				<Anchors>
					<Anchor point="TOPRIGHT">
						<Offset x="1" y="-1"/>
					</Anchor>
				</Anchors>
				<NormalTexture name="$parentNormalTexture" file="Interface\FriendsFrame\TravelPass-Invite">
					<Size x="24" y="32"/>
					<TexCoords left="0.01562500" right="0.39062500" top="0.27343750" bottom="0.52343750"/>
				</NormalTexture>
				<PushedTexture name="$parentPushedTexture" file="Interface\FriendsFrame\TravelPass-Invite">
					<Size x="24" y="32"/>
					<TexCoords left="0.42187500" right="0.79687500" top="0.27343750" bottom="0.52343750"/>
				</PushedTexture>
				<DisabledTexture name="$parentDisabledTexture" file="Interface\FriendsFrame\TravelPass-Invite">
					<Size x="24" y="32"/>
					<TexCoords left="0.01562500" right="0.39062500" top="0.00781250" bottom="0.25781250"/>
				</DisabledTexture>
				<HighlightTexture name="$parentHighlightTexture" file="Interface\FriendsFrame\TravelPass-Invite" alphaMode="ADD">
					<Size x="24" y="32"/>
					<TexCoords left="0.42187500" right="0.79687500" top="0.00781250" bottom="0.25781250"/>
				</HighlightTexture>
				<Scripts>
					<OnEnter function="TravelPassButton_OnEnter"/>
					<OnLeave>
						GameTooltip:Hide();
					</OnLeave>
					<OnClick>
						FriendsFrame_BattlenetInvite(self:GetParent());
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentSummonButton" inherits="ActionButtonTemplate" hidden="true" parentKey="summonButton">
				<Size x="24" y="32"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="1" y="-1"/>
				</Anchors>
				<HighlightTexture name="$parentHighlightTexture" file="Interface\FriendsFrame\TravelPass-Invite" alphaMode="ADD">
					<Size x="24" y="32"/>
					<TexCoords left="0.42187500" right="0.79687500" top="0.00781250" bottom="0.25781250"/>
				</HighlightTexture>
				<Scripts>
					<OnLoad>
						local normalTexture = _G[self:GetName().."NormalTexture"];
						normalTexture:ClearAllPoints();
						normalTexture:SetPoint("CENTER");
						normalTexture:SetSize(self:GetSize());
						normalTexture:SetAtlas("socialqueuing-friendlist-summonbutton-up");

						local pushedTexture = self:GetPushedTexture();
						pushedTexture:ClearAllPoints();
						pushedTexture:SetPoint("CENTER");
						pushedTexture:SetSize(self:GetSize());
						pushedTexture:SetAtlas("socialqueuing-friendlist-summonbutton-down");

						self.cooldown:SetSize(self:GetSize());
						self.cooldown:SetHideCountdownNumbers(true);
						self.cooldown:SetSwipeColor(0, 0, 0);
					</OnLoad>
					<OnShow>
						FriendsFrame_SummonButton_OnShow(self);
					</OnShow>
					<OnClick>
						FriendsFrame_ClickSummonButton(self, button, down);
					</OnClick>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:AddLine(RAF_SUMMON_LINKED, 1, 1, 1, true);
						if ( self.duration ) then
							GameTooltip:AddLine(COOLDOWN_REMAINING .. " " .. SecondsToTime(self.duration - (GetTime() - self.start)), 1, 1, 1, true);
						end
						if ( SHOW_NEWBIE_TIPS == "1" ) then
							GameTooltip:AddLine(NEWBIE_TOOLTIP_RAF_SUMMON_LINKED, NORMAL_FONT_COLOR.r, NORMAL_FONT_COLOR.g, NORMAL_FONT_COLOR.b, true)
						end
						GameTooltip:Show();
					</OnEnter>
					<OnLeave>
						GameTooltip:Hide();
					</OnLeave>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self.highlight:SetVertexColor(0.243, 0.570, 1);
				self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
			</OnLoad>
			<OnEnter function="FriendsFrameTooltip_Show" />
			<OnLeave>
				FriendsTooltip.button = nil;
				FriendsTooltip:Hide();
			</OnLeave>
			<OnClick>
				FriendsFrameFriendButton_OnClick(self, button);
			</OnClick>
		</Scripts>
		<HighlightTexture file="Interface\QuestFrame\UI-QuestLogTitleHighlight" alphaMode="ADD" parentKey="highlight">
			<Anchors>
				<Anchor point="TOPLEFT" x="0" y="-1"/>
				<Anchor point="BOTTOMRIGHT" x="0" y="1"/>
			</Anchors>
		</HighlightTexture>
	</Button>
	<!--
	<Button name="FriendsFrameButtonTemplate" virtual="true">
		<Size x="298" y="31"/>
		<Frames>
			<Frame name="$parentButtonText" setAllPoints="true">
				<Layers>
					<Layer level="BORDER">
						<FontString name="$parentName" inherits="GameFontNormal" justifyH="LEFT">
							<Anchors>
								<Anchor point="TOPLEFT" x="10" y="-3"/>
							</Anchors>
						</FontString>
						<Texture name="$parentLink" file="Interface/FriendsFrame/UI-FriendsFrame-Link" hidden="true">
							<Size x="16" y="16"/>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentName" relativePoint="RIGHT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<FontString name="$parentLocation" inherits="GameFontNormal" justifyH="LEFT">
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentName" relativePoint="RIGHT"/>
								<Anchor point="TOPRIGHT" x="10" y="-3"/>
							</Anchors>
						</FontString>
						<FontString name="$parentInfo" inherits="GameFontHighlightSmall" justifyH="LEFT">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentName" relativePoint="BOTTOMLEFT"/>
							</Anchors>
						</FontString>
						<FontString name="$parentNoteHiddenText" inherits="GameFontNormalSmall" justifyH="LEFT" hidden="true">
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentInfo" relativePoint="RIGHT" x="5" y="0"/>
							</Anchors>
						</FontString>
						<FontString name="$parentNoteText" inherits="GameFontNormalSmall" justifyH="LEFT">
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentInfo" relativePoint="RIGHT" x="2" y="0"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentSummonButton" inherits="ActionButtonTemplate">
						<Size x="24" y="24"/>
						<Anchors>
							<Anchor point="LEFT" x="12" y="1"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentIcon"/>
							</Layer>
							<Layer level="ARTWORK">
								<Texture name="$parentFlash" file="Interface\Buttons\UI-QuickslotRed" hidden="true"/>
								<FontString name="$parentHotKey" inherits="NumberFontNormalSmallGray" justifyH="RIGHT">
									<Size x="32" y="10"/>
									<Anchors>
										<Anchor point="TOPLEFT" x="-2" y="-2"/>
									</Anchors>
								</FontString>
								<FontString name="$parentCount" inherits="NumberFontNormal" justifyH="RIGHT">
									<Anchors>
										<Anchor point="BOTTOMRIGHT" x="-2" y="2"/>
									</Anchors>
								</FontString>
							</Layer>
							<Layer level="OVERLAY">
								<FontString name="$parentName" inherits="GameFontHighlightSmallOutline">
									<Size x="32" y="10"/>
									<Anchors>
										<Anchor point="BOTTOM" x="0" y="2"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Cooldown name="$parentCooldown" inherits="CooldownFrameTemplate">
								<Size x="28" y="28"/>
								<Anchors>
									<Anchor point="CENTER" x="0" y="-1"/>
								</Anchors>
							</Cooldown>
						</Frames>
						<Scripts>
							<OnLoad>
								_G[self:GetName().."Icon"]:SetTexture("Interface\\Icons\\Spell_Shadow_Teleport");
								self:RegisterEvent("SPELL_UPDATE_COOLDOWN");
								local normalTexture = _G[self:GetName().."NormalTexture"];
								normalTexture:SetWidth(40);
								normalTexture:SetHeight(40);
							</OnLoad>
							<OnEvent>
								FriendsFrame_SummonButton_OnEvent(self, event, ...);
							</OnEvent>
							<OnShow>
								FriendsFrame_SummonButton_OnShow(self);
							</OnShow>
							<OnClick>
								FriendsFrame_ClickSummonButton(self, button, down);
							</OnClick>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								GameTooltip:AddLine(RAF_SUMMON_LINKED, 1, 1, 1, true);
								if ( self.duration ) then
									GameTooltip:AddLine(COOLDOWN_REMAINING .. " " .. SecondsToTime(self.duration - (GetTime() - self.start)), 1, 1, 1, true);
								end
								if ( SHOW_NEWBIE_TIPS == "1" ) then
									GameTooltip:AddLine(NEWBIE_TOOLTIP_RAF_SUMMON_LINKED, NORMAL_FONT_COLOR.r, NORMAL_FONT_COLOR.g, NORMAL_FONT_COLOR.b, true)
								end
								GameTooltip:Show();
							</OnEnter>
							<OnLeave>
								GameTooltip:Hide();
							</OnLeave>
						</Scripts>
					</Button>
					<Button name="$parentNote">
						<Size x="7" y="8"/>
						<Anchors>
							<Anchor point="RIGHT" relativeTo="$parentName" relativePoint="LEFT" x="0" y="0"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentIcon" file="Interface/FriendsFrame/UI-FriendsFrame-Note">
									<Size x="7" y="8"/>
									<Anchors>
										<Anchor point="TOPLEFT"/>
									</Anchors>
								</Texture>
							</Layer>
						</Layers>
						<Scripts>
							<OnLoad>
								self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
							</OnLoad>
							<OnClick>
								FriendsFrame.NotesID = self:GetParent():GetParent():GetID();
								local dialog = StaticPopup_Show("SET_FRIENDNOTE", GetFriendInfo(FriendsFrame.NotesID));
								PlaySound(SOUNDKIT.IG_CHARACTER_INFO_CLOSE);
							</OnClick>
						</Scripts>
						<HighlightTexture file="Interface\QuestFrame\UI-QuestTitleHighlight" alphaMode="ADD">
							<Size x="7" y="8"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="-1" y="0"/>
							</Anchors>
						</HighlightTexture>
					</Button>
				</Frames>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
			</OnLoad>
			<OnClick>
				FriendsFrameFriendButton_OnClick(self, button);
				PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
			</OnClick>
		</Scripts>
		<HighlightTexture file="Interface\QuestFrame\UI-QuestTitleHighlight" alphaMode="ADD"/>
	</Button>
	-->
	<Button name="FriendsFrameIgnoreButtonTemplate" virtual="true">
		<Size x="298" y="16"/>
		<Layers>
			<Layer level="BORDER">
				<FontString name="$parentName" inherits="GameFontNormal" justifyH="LEFT" parentKey="name">
					<Size x="288" y="12"/>
					<Anchors>
						<Anchor point="LEFT" x="10" y="1"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick>
				FriendsFrameIgnoreButton_OnClick(self);
				PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
			</OnClick>
		</Scripts>
		<HighlightTexture file="Interface\QuestFrame\UI-QuestTitleHighlight" alphaMode="ADD"/>
	</Button>
	<Button name="FriendsFriendsButtonTemplate" virtual="true">
		<Size x="248" y="16"/>
		<Layers>
			<Layer level="BORDER">
				<FontString name="$parentName" inherits="FriendsFont_Normal" justifyH="LEFT" parentKey="name">
					<Size x="240" y="16"/>
					<Anchors>
						<Anchor point="LEFT" x="6" y="1"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick function="FriendsFriendsButton_OnClick"/>
		</Scripts>
		<HighlightTexture file="Interface\QuestFrame\UI-QuestTitleHighlight" alphaMode="ADD"/>
	</Button>
	<Button name="FriendsFrameWhoButtonTemplate" virtual="true">
		<Size x="298" y="16"/>
		<Layers>
			<Layer level="BORDER">
				<FontString name="$parentName" inherits="GameFontNormalSmall" justifyH="LEFT">
					<Size x="88" y="14"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="10" y="-3"/>
					</Anchors>
				</FontString>
				<FontString name="$parentVariable" inherits="GameFontHighlightSmall" justifyH="LEFT">
					<Size x="95" y="14"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentName" relativePoint="RIGHT" x="0" y="0"/>
					</Anchors>
				</FontString>
				<FontString name="$parentLevel" inherits="GameFontHighlightSmall" justifyH="CENTER" wordwrap="false">
					<Size x="24" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentVariable" relativePoint="RIGHT" x="2" y="0"/>
					</Anchors>
				</FontString>
				<FontString name="$parentClass" inherits="GameFontHighlightSmall" justifyH="LEFT">
					<Size x="80" y="8"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentLevel" relativePoint="RIGHT" x="8" y="0"/>
					</Anchors>
				</FontString>
				<!--
				<FontString name="$parentGroup" inherits="GameFontHighlightSmall" justifyH="CENTER">
					<Size>
						<AbsDimension x="45" y="14"/>
					</Size>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentClass" relativePoint="RIGHT">
							<Offset>
								<AbsDimension x="-2" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				-->
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
			</OnLoad>
			<OnClick>
				FriendsFrameWhoButton_OnClick(self, button);
				PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
			</OnClick>
			<OnEnter>
				if (self.tooltip1 and self.tooltip2) then
					GameTooltip:SetOwner(self, "ANCHOR_LEFT");
					GameTooltip:SetText(self.tooltip1);
					GameTooltip:AddLine(self.tooltip2, 1, 1, 1);
					GameTooltip:Show();
				end
			</OnEnter>
			<OnLeave function="GameTooltip_Hide"/>
		</Scripts>
		<HighlightTexture file="Interface\QuestFrame\UI-QuestTitleHighlight" alphaMode="ADD">
			<Size x="298" y="16"/>
			<Anchors>
				<Anchor point="TOP" x="5" y="-2"/>
			</Anchors>
		</HighlightTexture>
	</Button>
	<Button name="WhoFrameColumnHeaderTemplate" virtual="true">
		<Size x="10" y="24"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Left" name="$parentLeft" file="Interface\FriendsFrame\WhoFrame-ColumnTabs">
					<Size x="5" y="24"/>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.078125" top="0" bottom="0.75"/>
				</Texture>
				<Texture parentKey="Middle" name="$parentMiddle" file="Interface\FriendsFrame\WhoFrame-ColumnTabs">
					<Size x="53" y="24"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Left" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.078125" right="0.90625" top="0" bottom="0.75"/>
				</Texture>
				<Texture parentKey="Right" name="$parentRight" file="Interface\FriendsFrame\WhoFrame-ColumnTabs">
					<Size x="4" y="24"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Middle" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.90625" right="0.96875" top="0" bottom="0.75"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick>
				if ( self.sortType ) then
					SortWho(self.sortType);
				end
				PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
			</OnClick>
		</Scripts>
		<ButtonText>
			<Anchors>
				<Anchor point="LEFT" x="8" y="0"/>
			</Anchors>
		</ButtonText>
		<NormalFont style="GameFontHighlightSmall"/>
		<HighlightTexture parentKey="HighlightTexture" name="$parentHighlightTexture" file="Interface\PaperDollInfoFrame\UI-Character-Tab-Highlight" alphaMode="ADD">
			<Anchors>
				<Anchor point="TOPLEFT" relativeKey="$parent.Left" x="-2" y="5"/>
				<Anchor point="BOTTOMRIGHT" relativeKey="$parent.Right" x="2" y="-7"/>
			</Anchors>
		</HighlightTexture>
	</Button>

	<Button name="FriendsFrameTabTemplate" inherits="CharacterFrameTabButtonTemplate" virtual="true">
		<Scripts>
			<OnClick>
				PanelTemplates_Tab_OnClick(self, FriendsFrame);
				FriendsFrame_OnShow();
			</OnClick>
		</Scripts>
	</Button>
	<Frame name="FriendsFrame" toplevel="true" parent="UIParent" movable="true" enableMouse="true" hidden="true" inherits="ButtonFrameTemplate">
		<Layers>
			<Layer level="OVERLAY" textureSubLevel="-1">
				<Texture name="$parentIcon" file="Interface\FriendsFrame\Battlenet-Portrait">
					<Size x="60" y="60"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-5" y="7"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<FontString name="FriendsFrameTitleText" inherits="GameFontNormal">
					<Size x="250" y="16"/>
					<Anchors>
						<Anchor point="BOTTOM" x="3" y="-16"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="FriendsDropDown" inherits="UIDropDownMenuTemplate"/>
			<Frame name="TravelPassDropDown" inherits="UIDropDownMenuTemplate" hidden="true">
				<Scripts>
					<OnLoad function="TravelPassDropDown_OnLoad"/>
				</Scripts>
			</Frame>
			<Frame name="FriendsTabHeader" parentKey="FriendsTabHeader" setAllPoints="true">
				<Frames>
					<Frame name="FriendsFrameBattlenetFrame">
						<Size x="190" y="29"/>
						<Anchors>
							<Anchor point="TOPLEFT" x="109" y="-26"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture file="Interface\FriendsFrame\battlenet-friends-main" setAllPoints="true">
									<TexCoords left="0.00390625" right="0.74609375" top="0.00195313" bottom="0.05859375"/>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<FontString parentKey="Tag" inherits="GameFontNormal">
									<Anchors>
										<Anchor point="CENTER"/>
									</Anchors>
									<Color r="0.345" g="0.667" b="0.867" />
								</FontString>
								<FontString parentKey="UnavailableLabel" inherits="FriendsFont_Normal" text="BATTLENET_UNAVAILABLE" hidden="true">
									<Anchors>
										<Anchor point="CENTER"/>
									</Anchors>
									<Color r="1" g="0.82" b="0"/>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnHide>
								FriendsFrameBattlenetFrame_HideSubFrames();
							</OnHide>
						</Scripts>
						<Frames>
							<Button parentKey="BroadcastButton">
								<Size x="32" y="32"/>
								<Anchors>
									<Anchor point="LEFT" relativeTo="$parent" relativePoint="RIGHT" x="2" y="0"/>
								</Anchors>
								<Scripts>
									<OnClick>
										PlaySound(SOUNDKIT.IG_CHAT_EMOTE_BUTTON);
										if ( self:GetParent().BroadcastFrame:IsShown() ) then
											FriendsFrameBattlenetFrame_HideBroadcastFrame();
										else
											FriendsFrameBattlenetFrame_ShowBroadcastFrame();
										end
									</OnClick>
									<OnEnter>
										GameTooltip_AddNewbieTip(self, CHAT_LABEL, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_CHATMENU, 1);
									</OnEnter>
									<OnLeave function="GameTooltip_Hide"/>
								</Scripts>
								<NormalTexture file="Interface\FriendsFrame\broadcast-normal"/>
								<PushedTexture file="Interface\FriendsFrame\broadcast-press"/>
								<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
							</Button>
							<Button parentKey="UnavailableInfoButton" inherits="UIPanelInfoButton" hidden="true">
								<Anchors>
									<Anchor point="LEFT" relativeTo="$parent" relativePoint="RIGHT" x="4" y="0"/>
								</Anchors>
								<Scripts>
									<OnClick>
										local infoFrame = self:GetParent().UnavailableInfoFrame;
										if ( infoFrame:IsShown() ) then
											infoFrame:Hide();
										else
											infoFrame:SetHeight(86 + infoFrame.Text:GetHeight());
											infoFrame:Show();
										end
									</OnClick>
								</Scripts>
							</Button>
							<Frame parentKey="BroadcastFrame" frameStrata="HIGH" enableMouse="true" hidden="true">
								<Size x="338" y="100"/>
								<Anchors>
									<Anchor point="TOPLEFT" x="-110" y="-24"/>
								</Anchors>
								<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
									<BackgroundInsets>
										<AbsInset left="11" right="12" top="12" bottom="11"/>
									</BackgroundInsets>
									<TileSize>
										<AbsValue val="32"/>
									</TileSize>
									<EdgeSize>
										<AbsValue val="32"/>
									</EdgeSize>
								</Backdrop>
								<Layers>
									<Layer level="BACKGROUND" textureSubLevel="1">
										<Texture>
											<Anchors>
												<Anchor point="TOPLEFT" x="10" y="-10"/>
												<Anchor point="BOTTOMRIGHT" x="-10" y="10"/>
											</Anchors>
											<Color r="0" g="0" b="0" a="0.7"/>
										</Texture>
									</Layer>
									<Layer level="ARTWORK">
										<FontString text="BATTLENET_BROADCAST" inherits="GameFontNormalMed3">
											<Anchors>
												<Anchor point="TOP" x="0" y="-20"/>
											</Anchors>
										</FontString>
									</Layer>
								</Layers>
								<Scripts>
									<OnShow>
										FriendsFrameBattlenetFrame_UpdateBroadcast();
									</OnShow>
								</Scripts>
								<Frames>
									<ScrollFrame name="$parentScrollFrame" parentKey="ScrollFrame" inherits="UIPanelScrollFrameTemplate">
										<Size x="263" y="0"/>
										<Anchors>
											<Anchor point="TOP" x="0" y="-44"/>
											<Anchor point="BOTTOM" x="0" y="60"/>
										</Anchors>
										<Layers>
											<Layer level="BACKGROUND">
												<Texture parentKey="TopLeftBorder" file="Interface\Common\Common-Input-Border-TL">
													<Size x="8" y="8"/>
													<Anchors>
														<Anchor point="TOPLEFT" x="-4" y="4"/>
													</Anchors>
												</Texture>
												<Texture parentKey="TopRightBorder" file="Interface\Common\Common-Input-Border-TR">
													<Size x="8" y="8"/>
													<Anchors>
														<Anchor point="TOPRIGHT" x="4" y="4"/>
													</Anchors>
												</Texture>
												<Texture parentKey="TopBorder" file="Interface\Common\Common-Input-Border-T">
													<Anchors>
														<Anchor point="TOPLEFT" relativeKey="$parent.TopLeftBorder" relativePoint="TOPRIGHT"/>
														<Anchor point="BOTTOMRIGHT" relativeKey="$parent.TopRightBorder" relativePoint="BOTTOMLEFT"/>
													</Anchors>
												</Texture>
												<Texture parentKey="BottomLeftBorder" file="Interface\Common\Common-Input-Border-BL">
													<Size x="8" y="8"/>
													<Anchors>
														<Anchor point="BOTTOMLEFT" x="-4" y="-3"/>
													</Anchors>
												</Texture>
												<Texture parentKey="BottomRightBorder" file="Interface\Common\Common-Input-Border-BR">
													<Size x="8" y="8"/>
													<Anchors>
														<Anchor point="BOTTOMRIGHT" x="4" y="-3"/>
													</Anchors>
												</Texture>
												<Texture parentKey="BottomBorder" file="Interface\Common\Common-Input-Border-B">
													<Anchors>
														<Anchor point="TOPLEFT" relativeKey="$parent.BottomLeftBorder" relativePoint="TOPRIGHT"/>
														<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomRightBorder" relativePoint="BOTTOMLEFT"/>
													</Anchors>
												</Texture>
												<Texture parentKey="LeftBorder" file="Interface\Common\Common-Input-Border-L">
													<Anchors>
														<Anchor point="TOPLEFT" relativeKey="$parent.TopLeftBorder" relativePoint="BOTTOMLEFT"/>
														<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomLeftBorder" relativePoint="TOPRIGHT"/>
													</Anchors>
												</Texture>
												<Texture parentKey="RightBorder" file="Interface\Common\Common-Input-Border-R">
													<Anchors>
														<Anchor point="TOPLEFT" relativeKey="$parent.TopRightBorder" relativePoint="BOTTOMLEFT"/>
														<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomRightBorder" relativePoint="TOPRIGHT"/>
													</Anchors>
												</Texture>
												<Texture parentKey="MiddleBorder" file="Interface\Common\Common-Input-Border-M">
													<Anchors>
														<Anchor point="TOPLEFT" relativeKey="$parent.LeftBorder" relativePoint="TOPRIGHT"/>
														<Anchor point="BOTTOMRIGHT" relativeKey="$parent.RightBorder" relativePoint="BOTTOMLEFT"/>
													</Anchors>
												</Texture>
											</Layer>
										</Layers>
										<Frames>
											<Button inherits="UIPanelButtonTemplate" parentKey="UpdateButton" text="UPDATE">
												<Size x="110" y="24"/>
												<Anchors>
													<Anchor point="TOPLEFT" relativeKey="$parent" relativePoint="BOTTOMLEFT" x="-2" y="-12"/>
												</Anchors>
												<Scripts>
													<OnClick>
														FriendsFrameBattlenetFrame_SetBroadcast();
													</OnClick>
												</Scripts>
											</Button>
											<Button inherits="UIPanelButtonTemplate" parentKey="CancelButton" text="CANCEL">
												<Size x="110" y="24"/>
												<Anchors>
													<Anchor point="TOPRIGHT" relativeKey="$parent" relativePoint="BOTTOMRIGHT" x="2" y="-12"/>
												</Anchors>
												<Scripts>
													<OnClick>
														FriendsFrameBattlenetFrame_UpdateBroadcast();
														FriendsFrameBattlenetFrame_HideBroadcastFrame();
													</OnClick>
												</Scripts>
											</Button>
										</Frames>
										<Scripts>
											<OnLoad>
												self.scrollBarHideable = 1;
												self.ScrollBar:Hide();

												self.minHeight = 113 + self.EditBox.PromptText:GetHeight() * 2
												self:GetParent():SetHeight(self.minHeight);
												self.EditBox:SetPoint("TOPLEFT", 4, -4);
											</OnLoad>
											<OnMouseDown>
												self.EditBox:SetFocus();
											</OnMouseDown>
										</Scripts>
										<ScrollChild>
											<EditBox parentKey="EditBox" multiLine="false" letters="127" countInvisibleLetters="true" autoFocus="false">
												<Size x="256" y="1"/>
												<Layers>
													<Layer level="BORDER">
														<FontString parentKey="PromptText" inherits="FriendsFont_Small" justifyH="LEFT" justifyV="MIDDLE" text="FRIENDS_LIST_ENTER_TEXT">
															<Anchors>
																<Anchor point="LEFT" x="0" y="0"/>
															</Anchors>
															<Color r="0.35" g="0.35" b="0.35"/>
														</FontString>
													</Layer>
												</Layers>
												<Scripts>
													<OnTextChanged>
														if ( self:GetText() ~= "" ) then
															self.PromptText:Hide();
														else
															self.PromptText:Show();
														end
														local scrollFrame = self:GetParent();
														scrollFrame:GetParent():SetHeight(max(scrollFrame.minHeight, 111 + self:GetHeight()));
													</OnTextChanged>
													<OnCursorChanged function="ScrollingEdit_OnCursorChanged"/>
													<OnEscapePressed>
														self:ClearFocus();
													</OnEscapePressed>
													<OnEnterPressed>
														FriendsFrameBattlenetFrame_SetBroadcast();
													</OnEnterPressed>
												</Scripts>
												<FontString inherits="FriendsFont_Small" spacing="2"/>
											</EditBox>
										</ScrollChild>
									</ScrollFrame>
								</Frames>
							</Frame>
							<Frame parentKey="UnavailableInfoFrame" hidden="true">
								<Size x="306" y="100"/>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="FriendsFrame" relativePoint="TOPRIGHT" x="-2" y="-18"/>
								</Anchors>
								<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
									<BackgroundInsets>
										<AbsInset left="11" right="12" top="12" bottom="11"/>
									</BackgroundInsets>
									<TileSize>
										<AbsValue val="32"/>
									</TileSize>
									<EdgeSize>
										<AbsValue val="32"/>
									</EdgeSize>
								</Backdrop>
								<Layers>
									<Layer level="ARTWORK">
										<FontString parentKey="Label" inherits="FriendsFont_Large" text="BATTLENET_UNAVAILABLE">
											<Anchors>
												<Anchor point="TOP" x="17" y="-26"/>
											</Anchors>
											<Color r="1" g="0.82" b="0"/>
										</FontString>
										<Texture file="Interface\FriendsFrame\PlusManz-BattleNet">
											<Size x="32" y="32"/>
											<Anchors>
												<Anchor point="RIGHT" relativeKey="$parent.Label" relativePoint="LEFT" x="-2" y="0"/>
											</Anchors>
										</Texture>
										<FontString parentKey="Text" inherits="FriendsFont_Normal" text="BATTLENET_UNAVAILABLE_ALERT" justifyH="LEFT" spacing="2">
											<Size x="250" y="0"/>
											<Anchors>
												<Anchor point="TOP" x="0" y="-58"/>
											</Anchors>
										</FontString>
									</Layer>
								</Layers>
							</Frame>
						</Frames>
					</Frame>
					<Frame name="FriendsFrameStatusDropDown" inherits="UIDropDownMenuTemplate" hidden="false">
						<Anchors>
							<Anchor point="TOPLEFT" x="43" y="-27"/>
						</Anchors>
						<Layers>
							<Layer level="OVERLAY">
								<Texture name="$parentStatus" file="Interface\FriendsFrame\StatusIcon-Online">
									<Anchors>
										<Anchor point="LEFT" x="24" y="1"/>
									</Anchors>
								</Texture>
							</Layer>
						</Layers>
						<Frames>
							<Frame name="$parentMouseOver" enableMouse="true">
								<Size x="22" y="23"/>
								<Anchors>
									<Anchor point="TOPLEFT" x="16" y="-2"/>
								</Anchors>
								<Scripts>
									<OnEnter function="FriendsFrameStatusDropDown_ShowTooltip"/>
									<OnLeave>
										GameTooltip:Hide();
									</OnLeave>
								</Scripts>
							</Frame>
						</Frames>
						<Scripts>
							<OnLoad function="FriendsFrameStatusDropDown_OnLoad"/>
							<OnShow function="FriendsFrameStatusDropDown_OnShow"/>
						</Scripts>
					</Frame>
					<EditBox name="FriendsFrameBroadcastInput" letters="127" countInvisibleLetters="true" autoFocus="false" hidden="true">
						<Size x="190" y="20"/>
						<Anchors>
							<Anchor point="LEFT" relativeTo="FriendsFrameStatusDropDown" relativePoint="RIGHT" x="14" y="2"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border">
									<Size x="8" y="20"/>
									<Anchors>
										<Anchor point="TOPLEFT" x="-21" y="0"/>
									</Anchors>
									<TexCoords left="0" right="0.0625" top="0" bottom="0.625"/>
								</Texture>
								<Texture name="$parentRight" file="Interface\Common\Common-Input-Border">
									<Size x="8" y="20"/>
									<Anchors>
										<Anchor point="RIGHT" x="0" y="0"/>
									</Anchors>
									<TexCoords left="0.9375" right="1.0" top="0" bottom="0.625"/>
								</Texture>
								<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border">
									<Size x="0" y="20"/>
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
										<Anchor point="RIGHT" relativeTo="$parentRight" relativePoint="LEFT"/>
									</Anchors>
									<TexCoords left="0.0625" right="0.9375" top="0" bottom="0.625"/>
								</Texture>
							</Layer>
							<Layer level="BORDER">
								<FontString name="$parentFill" inherits="FriendsFont_UserText" justifyH="LEFT" justifyV="MIDDLE" text="FRIENDS_LIST_ENTER_TEXT">
									<Anchors>
										<Anchor point="LEFT" x="3" y="0"/>
									</Anchors>
									<Color r="0.35" g="0.35" b="0.35"/>
								</FontString>
							</Layer>
							<Layer level="ARTWORK">
								<Texture file="Interface\FriendsFrame\BroadcastIcon" parentKey="icon">
									<Size x="16" y="16"/>
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT" x="-5" y="-1"/>
									</Anchors>
								</Texture>
							</Layer>
						</Layers>
						<Scripts>
							<OnLoad>
								self.icon:SetAlpha(0.35);
							</OnLoad>
							<OnShow>
							</OnShow>
							<OnEnterPressed function="FriendsFrameBroadcastInput_OnEnterPressed"/>
							<OnEscapePressed function="FriendsFrameBroadcastInput_UpdateDisplay"/>
							<OnEditFocusGained>
								self.icon:SetAlpha(1);
								self.clear:Hide();
								self:SetTextInsets(0, 10, 0, 0);
							</OnEditFocusGained>
							<OnEditFocusLost>
								FriendsFrameBroadcastInputTooltipButton:Show();
							</OnEditFocusLost>
							<OnTextChanged>
								if ( self:GetText() ~= "" ) then
									FriendsFrameBroadcastInputFill:Hide();
								else
									FriendsFrameBroadcastInputFill:Show();
								end
							</OnTextChanged>
							<OnHide function="FriendsFrameBroadcastInput_UpdateDisplay"/>
						</Scripts>
						<Frames>
							<Button name="$parentTooltipButton" setAllPoints="true">
								<Scripts>
									<OnClick>
										local editBox = FriendsFrameBroadcastInput;
										editBox:SetFocus();
										editBox:SetCursorPosition(strlen(editBox:GetText()));
										editBox:HighlightText(0, -1);  -- highlight all
										self:Hide();
									</OnClick>
									<OnEnter>
										GameTooltip:SetOwner(FriendsFrameBroadcastInput, "ANCHOR_RIGHT");
										GameTooltip:SetText(BN_BROADCAST_TOOLTIP, nil, nil, nil, nil, true);
									</OnEnter>
									<OnLeave>
										GameTooltip:Hide();
									</OnLeave>
								</Scripts>
							</Button>
							<Button name="$parentClearButton" parentKey="clear" hidden="true">
								<Size x="16" y="16"/>
								<Anchors>
									<Anchor point="RIGHT" x="-2" y="0"/>
								</Anchors>
								<Layers>
									<Layer level="ARTWORK">
										<Texture file="Interface\FriendsFrame\ClearBroadcastIcon" parentKey="icon" setAllPoints="true" alpha="0.60" />
									</Layer>
								</Layers>
								<Scripts>
									<OnEnter>self.icon:SetAlpha(1.0);</OnEnter>
									<OnShow>self:SetPoint("RIGHT", -2, 0);</OnShow>
									<OnLeave>self.icon:SetAlpha(0.60);</OnLeave>
									<OnMouseDown>self:SetPoint("RIGHT", -1, -1);</OnMouseDown>
									<OnMouseUp>self:SetPoint("RIGHT", -2, 0);</OnMouseUp>
									<OnClick function="FriendsFrameBroadcastInput_OnClearPressed"/>
								</Scripts>
							</Button>
						</Frames>
						<Scripts>
							<OnLoad>
								local frameLevel = self:GetFrameLevel();
								FriendsFrameBroadcastInputTooltipButton:SetFrameLevel(frameLevel + 1);
								FriendsFrameBroadcastInputClearButton:SetFrameLevel(frameLevel + 2);
							</OnLoad>
						</Scripts>
						<FontString inherits="FriendsFont_UserText">
							<Color r="0.345" g="0.667" b="0.867" />
						</FontString>
					</EditBox>
					<Button name="FriendsTabHeaderTab1" inherits="TabButtonTemplate" text="FRIENDS" id="1">
						<Anchors>
							<Anchor point="TOPLEFT" x="18" y="-51"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								PanelTemplates_TabResize(self, 0);
								_G[self:GetName().."HighlightTexture"]:SetWidth(self:GetTextWidth() + 31);
							</OnLoad>
							<OnClick function="FriendsTabHeader_ClickTab"/>
						</Scripts>
					</Button>
					<Button name="FriendsTabHeaderTab2" inherits="TabButtonTemplate" text="QUICK_JOIN" id="2">
						<Anchors>
							<Anchor point="LEFT" relativeTo="FriendsTabHeaderTab1" relativePoint="RIGHT" x="0" y="0"/>
						</Anchors>
						<Scripts>
							<OnClick function="FriendsTabHeader_ClickTab"/>
						</Scripts>
					</Button>
					<Button name="FriendsTabHeaderTab3" inherits="TabButtonTemplate" text="IGNORE" id="3">
						<Anchors>
							<Anchor point="LEFT" relativeTo="FriendsTabHeaderTab2" relativePoint="RIGHT" x="0" y="0"/>
						</Anchors>
						<Scripts>
							<OnClick function="FriendsTabHeader_ClickTab"/>
						</Scripts>
					</Button>
					<Frame parentKey="FriendsFrameQuickJoinHelpTip" inherits="GlowBoxTemplate" enableMouse="true" hidden="true" frameStrata="DIALOG">
						<Size x="220" y="100"/>
						<Anchors>
							<Anchor point="LEFT" relativeTo="FriendsTabHeaderTab2" relativePoint="RIGHT" x="9" y="-6"/>
						</Anchors>
						<Layers>
							<Layer level="OVERLAY">
								<FontString parentKey="Text" inherits="GameFontHighlightLeft" justifyV="TOP" text="SOCIAL_QUICK_JOIN_TAB_HELP_TIP">
									<Size x="188" y="0"/>
									<Anchors>
										<Anchor point="TOPLEFT" x="16" y="-20"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Button parentKey="CloseButton" inherits="UIPanelCloseButton">
								<Anchors>
									<Anchor point="TOPRIGHT" x="6" y="6"/>
								</Anchors>
								<Scripts>
									<OnClick inherit="append">
										FriendsFrame_CloseQuickJoinHelpTip();
									</OnClick>
								</Scripts>
							</Button>
							<Frame parentKey="Arrow" inherits="GlowBoxArrowTemplate">
								<Anchors>
									<Anchor point="RIGHT" relativePoint="LEFT" x="36" y="16"/>
								</Anchors>
							</Frame>
						</Frames>
						<Scripts>
							<OnLoad>
								SetClampedTextureRotation(self.Arrow.Arrow, 90);
								SetClampedTextureRotation(self.Arrow.Glow, 90);
								self.Arrow.Glow:ClearAllPoints();
								self.Arrow.Glow:SetPoint("RIGHT", self, "LEFT", 3, 0);
							</OnLoad>
							<OnShow>
								self:SetHeight(self.Text:GetHeight() + 42);
							</OnShow>
						</Scripts>
					</Frame>
					<Button name="$parentRecruitAFriendButton" motionScriptsWhileDisabled="true" parentKey="RaFButton" hidden="true">
						<Size x="26" y="26"/>
						<Anchors>
							<Anchor point="TOPRIGHT" x="-8" y="-55"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentIcon" parentKey="Icon"/>
							</Layer>
						</Layers>
						<Scripts>
							<OnLoad>
								self:RegisterEvent("RECRUIT_A_FRIEND_SYSTEM_STATUS");
								self:RegisterEvent("PLAYER_ENTERING_WORLD");
								self:RegisterEvent("PRODUCT_CHOICE_UPDATE");
								RAFButton_Update(self);
							</OnLoad>
							<OnClick>
								if ( #C_ProductChoice.GetChoices() > 0 ) then
									ProductChoiceFrame_SetUp(ProductChoiceFrame);
									ProductChoiceFrame:Show();
								else
									StaticPopupSpecial_Show(RecruitAFriendFrame);
								end
							</OnClick>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								if ( self.rewards ) then
									GameTooltip:SetText(RAF_BUTTON_REWARD_TOOLTIP_TITLE);
									GameTooltip:AddLine(RAF_BUTTON_REWARD_TOOLTIP_DESCRIPTION, 1, 1, 1, 1);
								else
									GameTooltip:SetText(RAF_BUTTON_TOOLTIP_TITLE);
									GameTooltip:AddLine(RAF_BUTTON_TOOLTIP_DESCRIPTION, 1, 1, 1, 1);
									if ( self.suppressedRewards and self.suppressedRewards > 0 ) then
										local color = RED_FONT_COLOR;
										local text = string.format(RAF_REWARD_SUPPRESSED, self.suppressedRewards);
										GameTooltip:AddLine(text, color.r, color.g, color.b, 1);
									end
									if ( not self:IsEnabled() ) then
										local color = RED_FONT_COLOR;
										GameTooltip:AddLine(RAF_BUTTON_DISABLED_FACTION, color.r, color.g, color.b, 1);
									end
								end
								GameTooltip:Show();
							</OnEnter>
							<OnLeave>
								GameTooltip:Hide();
							</OnLeave>
							<OnEvent>
								RAFButton_Update(self);
							</OnEvent>
						</Scripts>
						<PushedTexture file="Interface\Buttons\UI-Quickslot-Depress"/>
						<HighlightTexture alphaMode="ADD" file="Interface\Buttons\ButtonHilight-Square" parentKey="highlight"/>
					</Button>
					<Button name="$parentSoRButton" parentKey="soRButton" hidden="true">
						<Size x="26" y="26"/>
						<Anchors>
							<Anchor point="RIGHT" relativeKey="$parent.RaFButton" relativePoint="LEFT" x="-4" y="0"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentIcon" parentKey="icon"/>
							</Layer>
						</Layers>
						<Scripts>
							<OnLoad>
								self.icon:SetTexture("Interface\\Icons\\SOR-mail");
								self:RegisterEvent("SOR_BY_TEXT_UPDATED");
								if ( CanSendSoRByText() ) then
									self:Show();
								else
									self:Hide();
								end
							</OnLoad>
							<OnClick>
								ScrollOfResurrectionButton_OnClick(self);
							</OnClick>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								GameTooltip:SetText(SEND_SCROLL_OF_RESURRECTION);
								GameTooltip:AddLine(SCROLL_OF_RESURRECTION_TOOLTIP, 1, 1, 1, true);
								GameTooltip:Show();
							</OnEnter>
							<OnLeave>
								GameTooltip:Hide();
							</OnLeave>
							<OnEvent>
								if ( CanSendSoRByText() ) then
									self:Show();
								else
									self:Hide();
								end
							</OnEvent>
							<OnShow function="FriendsTabHeader_ResizeTabs"/>
							<OnHide function="FriendsTabHeader_ResizeTabs"/>
						</Scripts>
						<PushedTexture file="Interface\Buttons\UI-Quickslot-Depress"/>
						<HighlightTexture alphaMode="ADD" file="Interface\Buttons\ButtonHilight-Square" parentKey="highlight"/>
					</Button>
				</Frames>
				<Scripts>
					<OnLoad>
						PanelTemplates_SetNumTabs(self, 3);
						PanelTemplates_SetTab(self, 1);
						FriendsTabHeader_ResizeTabs();
					</OnLoad>
				</Scripts>
			</Frame>
			<Frame name="FriendsListFrame" hidden="true" setAllPoints="true">
				<Frames>
					<Button name="FriendsFrameAddFriendButton" inherits="UIPanelButtonTemplate" text="ADD_FRIEND">
						<Size x="131" y="21"/>
						<Anchors>
							<Anchor point="BOTTOMLEFT" relativeTo="FriendsFrame" x="4" y="4"/>
						</Anchors>
						<Scripts>
							<OnClick function="FriendsFrameAddFriendButton_OnClick"/>
							<OnEnter>
								GameTooltip_AddNewbieTip(self, ADD_FRIEND, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_ADDFRIEND, 1);
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</Button>
					<Button name="FriendsFrameSendMessageButton" inherits="UIPanelButtonTemplate" text="SEND_MESSAGE">
						<Size x="131" y="21"/>
						<Anchors>
							<Anchor point="BOTTOMRIGHT" relativeTo="FriendsFrame" x="-6" y="4"/>
						</Anchors>
						<Scripts>
							<OnClick function="FriendsFrameSendMessageButton_OnClick"/>
							<OnEnter>
								GameTooltip_AddNewbieTip(self, SEND_MESSAGE, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_SENDMESSAGE, 1);
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</Button>
					<Frame name="FriendsListPendingInviteDropDown" parentKey="FilterDropDown" inherits="UIDropDownMenuTemplate">
						<Scripts>
							<OnLoad>
								UIDropDownMenu_Initialize(self, FriendsList_InitializePendingInviteDropDown, "MENU");
							</OnLoad>
						</Scripts>
					</Frame>
					<Frame parentKey="RIDWarning" enableMouse="true" hidden="true" frameStrata="DIALOG">
						<Size x="298" y="309"/>
						<Anchors>
							<Anchor point="TOPLEFT" x="7" y="-87"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture setAllPoints="true">
									<Color r="0" g="0" b="0" a="0.85"/>
								</Texture>
							</Layer>
							<Layer level="BORDER">
								<Texture name="$parentLeft" file="Interface\FriendsFrame\PlusManz-BattleNetBG" hidden="true">
									<Anchors>
										<Anchor point="TOPLEFT" x="-8" y="12" />
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<Texture name="$parentBattlenetIcon" file="Interface\FriendsFrame\PlusManz-BattleNet" alpha="0.3">
									<Anchors>
										<Anchor point="CENTER" x="10" y="100" />
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="OVERLAY">
								<Texture name="$parentPlayerIcon" file="Interface\FriendsFrame\PlusManz-PlusManz">
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentBattlenetIcon" x="-20" y="-10" />
									</Anchors>
								</Texture>
								<FontString name="$parentTitle" inherits="GameFontNormal" text="BATTLENET_FRIEND_REQUEST_RECEIVED">
									<Size x="200" y="0"/>
									<Anchors>
										<Anchor point="TOP" relativeTo="$parentPlayerIcon" relativePoint="BOTTOM" x="0" y="2"/>
									</Anchors>
								</FontString>
								<FontString inherits="GameFontHighlight" justifyH="LEFT" text="RID_FRIEND_REQUEST_INFO">
									<Size x="200" y="0"/>
									<Anchors>
										<Anchor point="TOP" relativeTo="$parentTitle" relativePoint="BOTTOM" x="0" y="-4"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Button name="$parentContinueButton" inherits="UIPanelButtonTemplate" text="CONTINUE">
								<Size x="131" y="22"/>
								<Anchors>
									<Anchor point="BOTTOM" x="0" y="30"/>
								</Anchors>
								<Scripts>
									<OnClick>
										SetCVar("pendingInviteInfoShown", 1);
										FriendsList_Update();
									</OnClick>
								</Scripts>
							</Button>
						</Frames>
						<Scripts>
							<OnMouseWheel function="nop"/>
						</Scripts>
					</Frame>
					<ScrollFrame name="FriendsFrameFriendsScrollFrame" inherits="HybridScrollFrameTemplate">
						<Size x="302" y="307"/>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="FriendsFrame" x="8" y="-87"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentTop" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
									<Size x="29" y="102"/>
									<Anchors>
										<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="-6" y="5"/>
									</Anchors>
									<TexCoords left="0" right="0.445" top="0" bottom="0.4"/>
								</Texture>
								<Texture name="$parentBottom" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
									<Size x="29" y="106"/>
									<Anchors>
										<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="-6" y="-5"/>
									</Anchors>
									<TexCoords left="0.515625" right="0.960625" top="0" bottom="0.4140625"/>
								</Texture>
								<Texture name="$parentMiddle" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
									<Size x="29" y="1"/>
									<Anchors>
										<Anchor point="TOP" relativeTo="$parentTop" relativePoint="BOTTOM"/>
										<Anchor point="BOTTOM" relativeTo="$parentBottom" relativePoint="TOP"/>
									</Anchors>
									<TexCoords left="0" right="0.445" top=".75" bottom="1.0"/>
								</Texture>
							</Layer>
						</Layers>
						<Frames>
							<Button parentKey="PendingInvitesHeaderButton" inherits="UIMenuButtonStretchTemplate" hidden="true">
								<Size x="295" y="30"/>
								<NormalFont style="GameFontHighlightSmall2"/>
								<HighlightFont style="GameFontHighlightSmall2"/>
								<Layers>
									<Layer level="BACKGROUND" textureSubLevel="-1">
										<Texture parentKey="BG" file="Interface\FrameGeneral\UI-Background-Rock" horizTile="true" vertTile="true">
											<Anchors>
												<Anchor point="TOPLEFT" x="2" y="-2"/>
												<Anchor point="BOTTOMRIGHT" x="-2" y="2"/>
											</Anchors>
										</Texture>
									</Layer>
									<Layer level="ARTWORK">
										<Texture parentKey="RightArrow" atlas="friendslist-categorybutton-arrow-right" useAtlasSize="true" hidden="true">
											<Anchors>
												<Anchor point="LEFT" x="11" y="-2"/>
											</Anchors>
										</Texture>
										<Texture parentKey="DownArrow" atlas="friendslist-categorybutton-arrow-down" useAtlasSize="true" hidden="true">
											<Anchors>
												<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="8" y="-12"/>
											</Anchors>
										</Texture>
									</Layer>
									<Layer level="OVERLAY">
										<Texture parentKey="Flash" file="Interface\PaperDollInfoFrame\UI-Character-Tab-Highlight-yellow" setAllPoints="true" alpha="0" alphaMode="ADD">
											<Animations>
												<AnimationGroup parentKey="Anim" looping="REPEAT">
													<Alpha fromAlpha="0" toAlpha="0.4" duration="1" order="1"/>
													<Alpha fromAlpha="0.4" toAlpha="0" duration="1" order="2"/>
												</AnimationGroup>
											</Animations>
										</Texture>
									</Layer>
								</Layers>
								<ButtonText>
									<Anchors>
										<Anchor point="LEFT" x="25" y="0"/>
									</Anchors>
								</ButtonText>
								<Scripts>
									<OnMouseDown>
										self.DownArrow:SetPoint("LEFT", 10, -5);
										self.RightArrow:SetPoint("LEFT", 13, -4);
									</OnMouseDown>
									<OnMouseUp>
										self.DownArrow:SetPoint("LEFT", 8, -3);
										self.RightArrow:SetPoint("LEFT", 11, -2);
									</OnMouseUp>
									<OnClick>
										PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
										FriendsListFrame_ToggleInvites();
									</OnClick>
								</Scripts>
							</Button>
							<Slider name="$parentScrollBar" inherits="MinimalHybridScrollBarTemplate">
								<Anchors>
									<Anchor point="TOPRIGHT" relativeTo="FriendsFrame" relativePoint="TOPRIGHT" x="-14" y="-101"/>
									<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="6" y="12"/>
								</Anchors>
							</Slider>
						</Frames>
					</ScrollFrame>
				</Frames>
				<Scripts>
					<OnShow function="FriendsListFrame_OnShow"/>
					<OnHide function="FriendsListFrame_OnHide"/>
				</Scripts>
			</Frame>
			<Frame name="IgnoreListFrame" hidden="true" setAllPoints="true">
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentTop" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
							<Size x="29" y="102"/>
							<Anchors>
								<Anchor point="TOPRIGHT" relativePoint="TOPRIGHT" x="-5" y="-82"/>
							</Anchors>
							<TexCoords left="0" right="0.445" top="0" bottom="0.4"/>
						</Texture>
						<Texture name="$parentBottom" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
							<Size x="29" y="106"/>
							<Anchors>
								<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT" x="-5" y="25"/>
							</Anchors>
							<TexCoords left="0.515625" right="0.960625" top="0" bottom="0.4140625"/>
						</Texture>
						<Texture name="$parentMiddle" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
							<Size x="29" y="1"/>
							<Anchors>
								<Anchor point="TOP" relativeTo="$parentTop" relativePoint="BOTTOM"/>
								<Anchor point="BOTTOM" relativeTo="$parentBottom" relativePoint="TOP"/>
							</Anchors>
							<TexCoords left="0" right="0.445" top=".75" bottom="1.0"/>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Button name="FriendsFrameIgnorePlayerButton" inherits="UIPanelButtonTemplate" text="IGNORE_PLAYER">
						<Size x="131" y="21"/>
						<Anchors>
							<Anchor point="BOTTOMLEFT" relativeTo="FriendsFrame" x="4" y="4"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								self:SetFrameLevel( self:GetFrameLevel() + 3 );
							</OnLoad>
							<OnClick>
								if ( UnitCanCooperate("player", "target") and UnitIsPlayer("target") ) then
									local name, server = UnitName("target");
									local fullname = name;
									if ( server and UnitRealmRelationship("target") ~= LE_REALM_RELATION_SAME ) then
										fullname = name.."-"..server;
									end
									AddIgnore(fullname);
									PlaySound(SOUNDKIT.U_CHAT_SCROLL_BUTTON);
								else
									StaticPopup_Show("ADD_IGNORE");
								end
							</OnClick>
							<OnEnter>
								GameTooltip_AddNewbieTip(self, IGNORE_PLAYER, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_IGNOREPLAYER, 1);
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</Button>
					<Button name="FriendsFrameUnsquelchButton" inherits="UIPanelButtonTemplate" text="REMOVE_PLAYER">
						<Size x="134" y="21"/>
						<Anchors>
							<Anchor point="BOTTOMRIGHT" relativeTo="FriendsFrame" x="-6" y="4"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								self:SetFrameLevel( self:GetFrameLevel() + 3 );
							</OnLoad>
							<OnClick function="FriendsFrameUnsquelchButton_OnClick"/>
							<OnEnter>
								GameTooltip_AddNewbieTip(self, REMOVE_PLAYER, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_REMOVEPLAYER, 1);
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</Button>
					<Button name="FriendsFrameMutePlayerButton" inherits="UIPanelButtonTemplate" text="MUTE_PLAYER">
						<Size x="110" y="21"/>
						<Anchors>
							<Anchor point="LEFT" relativeTo="FriendsFrameIgnorePlayerButton" relativePoint="RIGHT"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								self:SetFrameLevel( self:GetFrameLevel() + 3 );
							</OnLoad>
							<OnClick>
								if ( UnitCanCooperate("player", "target") and UnitIsPlayer("target") ) then
									local name, server = UnitName("target");
									local fullname = name;
									if ( server and not UnitIsSameServer("target") ) then
										fullname = name.."-"..server;
									end
									AddMute(fullname);
									PlaySound(SOUNDKIT.U_CHAT_SCROLL_BUTTON);
								else
									StaticPopup_Show("ADD_MUTE");
								end
							</OnClick>
							<OnEnter>
								GameTooltip_AddNewbieTip(self, MUTE_PLAYER, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_MUTEPLAYER, 1);
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</Button>
					<Frame name="FriendsFrameIgnoredHeader" inherits="FriendsFrameHeaderTemplate" hidden="true"/>
					<Frame name="FriendsFrameBlockedInviteHeader" inherits="FriendsFrameHeaderTemplate" hidden="true"/>
					<Frame name="FriendsFrameMutedHeader" inherits="FriendsFrameHeaderTemplate" hidden="true"/>
					<Button name="FriendsFrameIgnoreButton1" inherits="FriendsFrameIgnoreButtonTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="FriendsFrame" x="8" y="-89"/>
						</Anchors>
					</Button>
					<!-- Other 18 IgnoreButtons are made in FriendsFrame.lua -->
					<ScrollFrame name="FriendsFrameIgnoreScrollFrame" inherits="FauxScrollFrameTemplate">
						<Size x="298" y="309"/>
						<Anchors>
							<Anchor point="TOPRIGHT" relativeTo="FriendsFrame" x="-32" y="-87"/>
						</Anchors>
						<Scripts>
							<OnVerticalScroll>
								FauxScrollFrame_OnVerticalScroll(self, offset, FRIENDS_FRAME_IGNORE_HEIGHT, IgnoreList_Update);
							</OnVerticalScroll>
						</Scripts>
					</ScrollFrame>
				</Frames>
				<Scripts>
					<OnLoad>
						FriendsFrameIgnoredHeaderTitle:SetText(IGNORED);
						FriendsFrameBlockedInviteHeaderTitle:SetText(BLOCKED_INVITES);
						FriendsFrameMutedHeaderTitle:SetText(MUTED);
					</OnLoad>
				</Scripts>
			</Frame>
			<Frame name="WhoFrame" setAllPoints="true" hidden="true">
				<Frames>
					<Frame name="WhoFrameListInset" inherits="InsetFrameTemplate" frameStrata="MEDIUM">
						<Anchors>
							<Anchor point="TOPLEFT" x="5" y="-80"/>
							<Anchor point="BOTTOMRIGHT" x="-5" y="46"/>
						</Anchors>
						<Layers>
							<Layer level="ARTWORK">
								<FontString name="WhoFrameTotals" inherits="GameFontNormalSmall">
									<Size x="298" y="16"/>
									<Anchors>
										<Anchor point="BOTTOM" x="0" y="5"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
					</Frame>
					<Button name="WhoFrameColumnHeader1" inherits="WhoFrameColumnHeaderTemplate" text="NAME">
						<Anchors>
							<Anchor point="TOPLEFT" x="7" y="-57"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								WhoFrameColumn_SetWidth(self, 83);
								self.sortType = "name";
							</OnLoad>
						</Scripts>
					</Button>
					<Button name="WhoFrameColumnHeader2" inherits="WhoFrameColumnHeaderTemplate">
						<Anchors>
							<Anchor point="LEFT" relativeTo="WhoFrameColumnHeader1" relativePoint="RIGHT" x="-2" y="0"/>
						</Anchors>
						<Frames>
							<Frame name="WhoFrameDropDown" inherits="UIDropDownMenuTemplate" id="1">
								<Anchors>
									<Anchor point="TOPLEFT" x="-15" y="0"/>
								</Anchors>
								<Layers>
									<Layer level="OVERLAY">
										<Texture name="WhoFrameDropDownHighlightTexture" file="Interface\PaperDollInfoFrame\UI-Character-Tab-Highlight" alphaMode="ADD" hidden="true">
											<Anchors>
												<Anchor point="TOPLEFT" x="15" y="3"/>
												<Anchor point="BOTTOMRIGHT" x="-30" y="-2"/>
											</Anchors>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnLoad function="WhoFrameDropDown_OnLoad"/>
									<OnShow>
										UIDropDownMenu_Initialize(self, WhoFrameDropDown_Initialize);
										UIDropDownMenu_SetSelectedID(self, 1);
									</OnShow>
									<OnEnter>
										WhoFrameDropDownHighlightTexture:Show();
									</OnEnter>
									<OnLeave>
										WhoFrameDropDownHighlightTexture:Hide();
									</OnLeave>
									<OnMouseUp>
										if ( WHOFRAME_DROPDOWN_LIST[UIDropDownMenu_GetSelectedID(self)].sortType ) then
											SortWho(WHOFRAME_DROPDOWN_LIST[UIDropDownMenu_GetSelectedID(self)].sortType);
											PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
										end
									</OnMouseUp>
								</Scripts>
							</Frame>
						</Frames>
						<Scripts>
							<OnLoad>
								WhoFrameColumn_SetWidth(self, 105);
							</OnLoad>
						</Scripts>
					</Button>
					<Button name="WhoFrameColumnHeader3" inherits="WhoFrameColumnHeaderTemplate" text="LEVEL_ABBR">
						<Anchors>
							<Anchor point="LEFT" relativeTo="WhoFrameColumnHeader2" relativePoint="RIGHT" x="-2" y="0"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								WhoFrameColumn_SetWidth(self, 32);
								self.sortType = "level";
							</OnLoad>
						</Scripts>
					</Button>
					<Button name="WhoFrameColumnHeader4" inherits="WhoFrameColumnHeaderTemplate" text="CLASS">
						<Anchors>
							<Anchor point="LEFT" relativeTo="WhoFrameColumnHeader3" relativePoint="RIGHT" x="-2" y="0"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								WhoFrameColumn_SetWidth(self, 92);
								self.sortType = "class";
							</OnLoad>
						</Scripts>
					</Button>
					<!--
					<Button name="WhoFrameColumnHeader5" inherits="WhoFrameColumnHeaderTemplate" text="GROUP">
						<Anchors>
							<Anchor point="LEFT" relativeTo="WhoFrameColumnHeader4" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="-2" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								WhoFrameColumn_SetWidth(53);
								self.sortType = "group";
							</OnLoad>
						</Scripts>
					</Button>
					-->
					<Button name="WhoFrameButton1" inherits="FriendsFrameWhoButtonTemplate" id="1">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="WhoFrame" x="2" y="-82"/>
						</Anchors>
					</Button>
					<!-- Other 16 WhoFrameButtons are made in FriendsFrame.lua -->
					<Button name="WhoFrameGroupInviteButton" inherits="UIPanelButtonTemplate" text="GROUP_INVITE">
						<Size x="120" y="22"/>
						<Anchors>
							<Anchor point="BOTTOMRIGHT" x="-6" y="4"/>
						</Anchors>
						<Scripts>
							<OnClick>
								InviteToGroup(WhoFrame.selectedName);
							</OnClick>
						</Scripts>
					</Button>
					<Button name="WhoFrameAddFriendButton" inherits="UIPanelButtonTemplate" text="ADD_FRIEND">
						<Size x="120" y="22"/>
						<Anchors>
							<Anchor point="RIGHT" relativeTo="WhoFrameGroupInviteButton" relativePoint="LEFT" x="0" y="0"/>
						</Anchors>
						<Scripts>
							<OnClick>
								AddFriend(WhoFrame.selectedName);
							</OnClick>
						</Scripts>
					</Button>
					<Button name="WhoFrameWhoButton" inherits="UIPanelButtonTemplate" text="REFRESH">
						<Size x="85" y="22"/>
						<Anchors>
							<Anchor point="RIGHT" relativeTo="WhoFrameAddFriendButton" relativePoint="LEFT" x="0" y="0"/>
						</Anchors>
						<Scripts>
							<OnClick>
								WhoFrameEditBox_OnEnterPressed(WhoFrameEditBox);
								WhoFrame.selectedWho = nil;
								PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
							</OnClick>
						</Scripts>
					</Button>
					<Frame name="WhoFrameEditBoxInset" inherits="InsetFrameTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativePoint="BOTTOMLEFT" x="5" y="46"/>
							<Anchor point="BOTTOMRIGHT" x="-5" y="26"/>
						</Anchors>
					</Frame>
					<EditBox name="WhoFrameEditBox" autoFocus="false" frameStrata="HIGH" historyLines="32">
						<Size x="296" y="32"/>
						<Anchors>
							<Anchor point="BOTTOM" x="-10" y="20"/>
						</Anchors>
						<Scripts>
							<OnShow function="EditBox_ClearFocus"/>
							<OnEnterPressed function="WhoFrameEditBox_OnEnterPressed"/>
							<OnEscapePressed function="EditBox_ClearFocus"/>
						</Scripts>
						<FontString inherits="ChatFontNormal" bytes="256"/>
					</EditBox>
					<ScrollFrame name="WhoListScrollFrame" inherits="FauxScrollFrameTemplate">
						<Size x="296" y="287"/>
						<Anchors>
							<Anchor point="TOPRIGHT" relativeTo="WhoFrame" relativePoint="TOPRIGHT" x="-33" y="-87"/>
						</Anchors>
						<Scripts>
							<OnVerticalScroll>
								FauxScrollFrame_OnVerticalScroll(self, offset, FRIENDS_FRAME_WHO_HEIGHT, WhoList_Update);
							</OnVerticalScroll>
						</Scripts>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
									<Size x="31" y="256"/>
									<Anchors>
										<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="-2" y="5"/>
									</Anchors>
									<TexCoords left="0" right="0.484375" top="0" bottom="1.0"/>
								</Texture>
								<Texture file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
									<Size x="31" y="106"/>
									<Anchors>
										<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="-2" y="-2"/>
									</Anchors>
									<TexCoords left="0.515625" right="1.0" top="0" bottom="0.4140625"/>
								</Texture>
							</Layer>
						</Layers>
					</ScrollFrame>
				</Frames>
				<Scripts>
					<OnLoad>
						SetWhoToUI(false);
					</OnLoad>
					<OnShow>
						SetWhoToUI(true);
					</OnShow>
					<OnHide>
						SetWhoToUI(false);
					</OnHide>
				</Scripts>
			</Frame>
			<Button name="FriendsFrameTab1" inherits="FriendsFrameTabTemplate" id="1" text="FRIENDS">
				<Anchors>
					<Anchor point="BOTTOMLEFT" x="5" y="-30"/>
				</Anchors>
				<Scripts>
					<OnEnter>
						GameTooltip_AddNewbieTip(self, MicroButtonTooltipText(FRIENDS, "TOGGLEFRIENDSTAB"), 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_FRIENDSTAB, 1);
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Button>
			<!--
			<Button name="FriendsFrameTab2" inherits="FriendsFrameTabTemplate" id="2" text="IGNORE">
				<Anchors>
					<Anchor point="LEFT" relativeTo="FriendsFrameTab1" relativePoint="RIGHT" x="-14" y="0"/>
				</Anchors>
				<Scripts>
					<OnEnter>
						GameTooltip_AddNewbieTip(nil, nil, nil, nil, NEWBIE_TOOLTIP_IGNORETAB, 1);
					</OnEnter>
					<OnLeave>
						GameTooltip:Hide();
					</OnLeave>
				</Scripts>
			</Button>
			-->
			<Button name="FriendsFrameTab2" inherits="FriendsFrameTabTemplate" id="2" text="WHO">
				<Anchors>
					<Anchor point="LEFT" relativeTo="FriendsFrameTab1" relativePoint="RIGHT" x="-15" y="0"/>
				</Anchors>
				<Scripts>
					<OnEnter>
						GameTooltip_AddNewbieTip(self, MicroButtonTooltipText(WHO, "TOGGLEWHOTAB"), 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_WHOTAB, 1);
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Button>
			<Button name="FriendsFrameTab3" inherits="FriendsFrameTabTemplate" id="3" text="CHAT">
				<Anchors>
					<Anchor point="LEFT" relativeTo="FriendsFrameTab2" relativePoint="RIGHT" x="-15" y="0"/>
				</Anchors>
				<Scripts>
					<OnLoad function="FriendsFrame_Update"/>
					<OnEvent function="FriendsFrame_Update"/>
					<OnEnter>
						GameTooltip_AddNewbieTip(self, MicroButtonTooltipText(CHAT_CHANNELS, "TOGGLECHANNELTAB"), 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_CHANNELTAB, 1);
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Button>
			<Button name="FriendsFrameTab4" inherits="FriendsFrameTabTemplate" id="4" text="RAID">
				<Anchors>
					<Anchor point="LEFT" relativeTo="FriendsFrameTab3" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="-15" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad function="FriendsFrame_Update"/>
					<OnEvent function="FriendsFrame_Update"/>
					<OnEnter>
						GameTooltip_AddNewbieTip(self, RAID, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_RAID, 1);
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Button>
			<Frame name="FriendsTooltip" frameStrata="TOOLTIP" hidden="true" clampedToScreen="true">
				<Size x="200" y="200"/>
				<Backdrop bgFile="Interface\Tooltips\UI-Tooltip-Background" edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
					<Color r="0" b="0" g="0"/>
					<EdgeSize>
						<AbsValue val="16"/>
					</EdgeSize>
					<TileSize>
						<AbsValue val="16"/>
					</TileSize>
					<BackgroundInsets>
						<AbsInset left="4" right="4" top="4" bottom="4"/>
					</BackgroundInsets>
				</Backdrop>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentHeader" inherits="FriendsFont_Normal" justifyH="LEFT">
							<Size x="176" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="12" y="-12"/>
							</Anchors>
							<Color r="0.510" g="0.773" b="1" />
						</FontString>
						<FontString name="$parentGameAccount1Name" inherits="FriendsFont_Small" justifyH="LEFT">
							<Size x="176" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="12" y="0"/>
								<Anchor point="TOP" relativeTo="$parentHeader" relativePoint="BOTTOM" x="0" y="-4"/>
							</Anchors>
						</FontString>
						<FontString name="$parentGameAccount1Info" inherits="FriendsFont_Small" justifyH="LEFT">
							<Size x="168" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="20" y="0"/>
								<Anchor point="TOP" relativeTo="$parentGameAccount1Name" relativePoint="BOTTOM" x="0" y="-4"/>
							</Anchors>
							<Color r="0.486" g="0.518" b="0.541" />
						</FontString>
						<FontString name="$parentNoteText" inherits="FriendsFont_Small" justifyH="LEFT" justifyV="TOP">
							<Size x="158" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="30" y="0"/>
								<Anchor point="TOP" relativeTo="$parentGameAccount1Info" relativePoint="BOTTOM" x="0" y="-4"/>
							</Anchors>
							<Color r="1.0" g="0.82" b="0"/>
						</FontString>
						<Texture name="$parentNoteIcon" file="Interface/FriendsFrame/UI-FriendsFrame-Note">
							<Size x="14" y="12"/>
							<Anchors>
								<Anchor point="TOPRIGHT" relativeTo="$parentNoteText" relativePoint="TOPLEFT" x="-1" y="0"/>
							</Anchors>
							<Color r="1.0" g="0.82" b="0"/>
						</Texture>
						<FontString name="$parentBroadcastText" inherits="FriendsFont_UserText" justifyH="LEFT" justifyV="TOP" nonspacewrap="true">
							<Size x="158" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="30" y="0"/>
								<Anchor point="TOP" relativeTo="$parentNoteText" relativePoint="BOTTOM" x="0" y="-4"/>
							</Anchors>
							<Color r="0.345" g="0.667" b="0.867" />
						</FontString>
						<Texture name="$parentBroadcastIcon" file="Interface\FriendsFrame\BroadcastIcon">
							<Size x="16" y="16"/>
							<Anchors>
								<Anchor point="TOPRIGHT" relativeTo="$parentBroadcastText" relativePoint="TOPLEFT" x="-2" y="1"/>
							</Anchors>
						</Texture>
						<FontString name="$parentLastOnline" inherits="FriendsFont_Small" justifyH="LEFT" justifyV="TOP">
							<Size x="176" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="12" y="0"/>
								<Anchor point="TOP" relativeTo="$parentBroadcastText" relativePoint="BOTTOM" x="0" y="-4"/>
							</Anchors>
							<Color r="0.486" g="0.518" b="0.541" />
						</FontString>
						<FontString name="$parentOtherGameAccounts" inherits="FriendsFont_Normal" justifyH="LEFT" text="FRIENDS_LIST_PLAYING" hidden="true">
							<Anchors>
								<Anchor point="LEFT" x="12" y="0"/>
								<Anchor point="TOP" relativeTo="$parentBroadcastText" relativePoint="BOTTOM" x="0" y="-12"/>
							</Anchors>
							<Color r="0.996" g="0.882" b="0.361"/>
						</FontString>
						<FontString name="$parentGameAccount2Name" inherits="FriendsFont_Small" justifyH="LEFT" hidden="true">
							<Size x="176" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="12" y="0"/>
								<Anchor point="TOP" relativeTo="$parentOtherGameAccounts" relativePoint="BOTTOM" x="0" y="-4"/>
							</Anchors>
						</FontString>
						<FontString name="$parentGameAccount2Info" inherits="FriendsFont_Small" justifyH="LEFT" hidden="true">
							<Size x="168" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="20" y="0"/>
								<Anchor point="TOP" relativeTo="$parentGameAccount2Name" relativePoint="BOTTOM" x="0" y="-1"/>
							</Anchors>
							<Color r="0.486" g="0.518" b="0.541" />
						</FontString>
						<FontString name="$parentGameAccount3Name" inherits="FriendsFont_Small" justifyH="LEFT" hidden="true">
							<Size x="176" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="12" y="0"/>
								<Anchor point="TOP" relativeTo="$parentGameAccount2Info" relativePoint="BOTTOM" x="0" y="-4"/>
							</Anchors>
						</FontString>
						<FontString name="$parentGameAccount3Info" inherits="FriendsFont_Small" justifyH="LEFT" hidden="true">
							<Size x="168" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="20" y="0"/>
								<Anchor point="TOP" relativeTo="$parentGameAccount3Name" relativePoint="BOTTOM" x="0" y="-1"/>
							</Anchors>
							<Color r="0.486" g="0.518" b="0.541" />
						</FontString>
						<FontString name="$parentGameAccount4Name" inherits="FriendsFont_Small" justifyH="LEFT" hidden="true">
							<Size x="176" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="12" y="0"/>
								<Anchor point="TOP" relativeTo="$parentGameAccount3Info" relativePoint="BOTTOM" x="0" y="-4"/>
							</Anchors>
						</FontString>
						<FontString name="$parentGameAccount4Info" inherits="FriendsFont_Small" justifyH="LEFT" hidden="true">
							<Size x="168" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="20" y="0"/>
								<Anchor point="TOP" relativeTo="$parentGameAccount4Name" relativePoint="BOTTOM" x="0" y="-1"/>
							</Anchors>
							<Color r="0.486" g="0.518" b="0.541" />
						</FontString>
						<FontString name="$parentGameAccount5Name" inherits="FriendsFont_Small" justifyH="LEFT" hidden="true">
							<Size x="176" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="12" y="0"/>
								<Anchor point="TOP" relativeTo="$parentGameAccount4Info" relativePoint="BOTTOM" x="0" y="-4"/>
							</Anchors>
						</FontString>
						<FontString name="$parentGameAccount5Info" inherits="FriendsFont_Small" justifyH="LEFT" hidden="true">
							<Size x="168" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="20" y="0"/>
								<Anchor point="TOP" relativeTo="$parentGameAccount5Name" relativePoint="BOTTOM" x="0" y="-1"/>
							</Anchors>
							<Color r="0.486" g="0.518" b="0.541" />
						</FontString>
						<FontString name="$parentGameAccountMany" inherits="FriendsFont_Small" justifyH="LEFT" hidden="true">
							<Size x="176" y="0"/>
							<Anchors>
								<Anchor point="LEFT" x="12" y="0"/>
								<Anchor point="TOP" relativeTo="$parentGameAccount5Info" relativePoint="BOTTOM" x="0" y="-4"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnUpdate>
						-- need to update in case of alternate alphabet
						if ( self.hasBroadcast ) then
							FriendsFrameTooltip_Show(self.button);
						end
					</OnUpdate>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="FriendsFrame_OnLoad"/>
			<OnShow function="FriendsFrame_OnShow"/>
			<OnHide function="FriendsFrame_OnHide"/>
			<OnEvent>
				FriendsFrame_OnEvent(self, event, ...);
			</OnEvent>
			<OnMouseWheel function=""/>
		</Scripts>
	</Frame>
	<Frame name="AddFriendFrame" parent="UIParent" hidden="true" frameStrata="DIALOG">
		<Size x="446" y="272"/>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Frames>
			<Frame name="AddFriendInfoFrame" hidden="true">
				<Size x="446" y="272"/>
				<Anchors>
					<Anchor point="TOPLEFT"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture file="Interface\FriendsFrame\PlusManz-BattleNet" alpha="0.3">
							<Anchors>
								<Anchor point="TOPLEFT" x="62" y="-32" />
							</Anchors>
						</Texture>
						<Texture name="$parentFactionIcon" file="Interface\FriendsFrame\PlusManz-Horde" alpha="0.5" hidden="true">
							<Anchors>
								<Anchor point="TOPRIGHT" x="-114" y="-32" />
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture name="$parentLeftFriend" file="Interface\FriendsFrame\PlusManz-PlusManz">
							<Anchors>
								<Anchor point="TOPLEFT" x="42" y="-42" />
							</Anchors>
						</Texture>
						<Texture name="$parentRightFriend" file="Interface\FriendsFrame\PlusManz-PlusManz">
							<Anchors>
								<Anchor point="TOPRIGHT" x="-134" y="-42" />
							</Anchors>
						</Texture>
						<FontString name="$parentLeftTitle" inherits="GameFontNormal" justifyH="LEFT" text="BATTLENET_FRIEND">
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativeTo="$parentLeftFriend" x="-2" y="-6"/>
							</Anchors>
						</FontString>
						<FontString name="$parentRightTitle" inherits="GameFontNormal" justifyH="LEFT" text="CHARACTER_FRIEND">
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativeTo="$parentRightFriend" x="-2" y="-6"/>
							</Anchors>
						</FontString>
						<FontString inherits="GameFontHighlightSmall" justifyH="LEFT" text="BATTLENET_FRIEND_INFO">
							<Size x="160" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentLeftTitle" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
							</Anchors>
						</FontString>
						<FontString inherits="GameFontHighlightSmall" justifyH="LEFT" text="CHARACTER_FRIEND_INFO">
							<Size x="160" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentRightTitle" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentContinueButton" inherits="UIPanelButtonTemplate" text="CONTINUE">
						<Size x="131" y="22"/>
						<Anchors>
							<Anchor point="BOTTOM" relativeTo="$parent" relativePoint="BOTTOM" x="0" y="30"/>
						</Anchors>
						<Scripts>
							<OnClick>
								SetCVar("addFriendInfoShown", 1);
								AddFriendFrame_ShowEntry();
							</OnClick>
						</Scripts>
					</Button>
				</Frames>
			</Frame>
			<Frame name="AddFriendEntryFrame" hidden="true">
				<Size x="384" y="218"/>
				<Anchors>
					<Anchor point="TOPLEFT"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture name="$parentLeftIcon" file="Interface\FriendsFrame\PlusManz-BattleNet" alpha="0.3">
							<Size x="40" y="40"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="92" y="-50" />
							</Anchors>
						</Texture>
						<Texture name="$parentRightIcon" file="Interface\FriendsFrame\PlusManz-Horde" alpha="0.5" hidden="true">
							<Size x="40" y="40"/>
							<Anchors>
								<Anchor point="TOPRIGHT" x="-82" y="-50" />
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="ARTWORK">
						<Texture name="$parentLeftFriend" file="Interface\FriendsFrame\PlusManz-PlusManz">
							<Size x="36" y="36"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativeTo="$parentLeftIcon" x="-10" y="-4" />
							</Anchors>
						</Texture>
						<Texture name="$parentRightFriend" file="Interface\FriendsFrame\PlusManz-PlusManz">
							<Size x="36" y="36"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativeTo="$parentRightIcon" x="-10" y="-4" />
							</Anchors>
						</Texture>
						<FontString name="$parentTopTitle" inherits="GameFontNormalLarge" text="ADD_FRIEND" justifyH="CENTER">
							<Anchors>
								<Anchor point="TOP" relativePoint="TOP" x="-12" y="-24"/>
							</Anchors>
						</FontString>
						<FontString name="$parentOrLabel" inherits="GameFontNormalLarge" text="OR_CAPS" justifyH="CENTER">
							<Anchors>
								<Anchor point="TOP" relativePoint="TOP" x="0" y="-66"/>
							</Anchors>
							<Color r="1" g="1" b="1"/>
						</FontString>
						<FontString name="$parentLeftTitle" inherits="GameFontNormal" text="BATTLENET_FRIEND" justifyH="CENTER">
							<Anchors>
								<Anchor point="TOP" relativeTo="$parentLeftFriend" relativePoint="BOTTOM" x="8" y="4"/>
							</Anchors>
						</FontString>
						<FontString name="$parentRightTitle" inherits="GameFontNormal" text="CHARACTER_FRIEND" justifyH="CENTER">
							<Anchors>
								<Anchor point="TOP" relativeTo="$parentRightFriend" relativePoint="BOTTOM" x="8" y="4"/>
							</Anchors>
						</FontString>
						<FontString name="$parentLeftDescription" inherits="GameFontHighlightSmall" text="BATTLENET_FRIEND_LABEL" justifyH="CENTER">
							<Anchors>
								<Anchor point="TOP" relativeTo="$parentLeftTitle" relativePoint="BOTTOM" x="0" y="-4"/>
							</Anchors>
						</FontString>
						<FontString name="$parentRightDescription" inherits="GameFontHighlightSmall" text="CHARACTER_FRIEND_LABEL" justifyH="CENTER">
							<Anchors>
								<Anchor point="TOP" relativeTo="$parentRightTitle" relativePoint="BOTTOM" x="0" y="-4"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentInfoButton" inherits="UIPanelInfoButton">
						<Anchors>
							<Anchor point="LEFT" relativePoint="RIGHT" relativeTo="$parentTopTitle" x="8" y="0" />
						</Anchors>
						<Scripts>
							<OnClick>
								if ( AddFriendNameEditBox:HasFocus() ) then
									AddFriendFrame.editFocus = AddFriendNameEditBox;
								elseif ( AddFriendNoteEditBox:HasFocus() ) then
									AddFriendFrame.editFocus = AddFriendNoteEditBox;
								else
									AddFriendFrame.editFocus = nil;
								end
								AddFriendFrame_ShowInfo();
							</OnClick>
						</Scripts>
					</Button>
					<EditBox name="AddFriendNameEditBox" autoFocus="false" letters="255" inherits="AutoCompleteEditBoxTemplate">
						<Size x="280" y="20"/>
						<Anchors>
							<Anchor point="TOP" x="0" y="-130"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border">
									<Size x="8" y="20"/>
									<Anchors>
										<Anchor point="TOPLEFT" x="-5" y="0"/>
									</Anchors>
									<TexCoords left="0" right="0.0625" top="0" bottom="0.625"/>
								</Texture>
								<Texture name="$parentRight" file="Interface\Common\Common-Input-Border">
									<Size x="8" y="20"/>
									<Anchors>
										<Anchor point="RIGHT" x="0" y="0"/>
									</Anchors>
									<TexCoords left="0.9375" right="1.0" top="0" bottom="0.625"/>
								</Texture>
								<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border">
									<Size x="0" y="20"/>
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
										<Anchor point="RIGHT" relativeTo="$parentRight" relativePoint="LEFT"/>
									</Anchors>
									<TexCoords left="0.0625" right="0.9375" top="0" bottom="0.625"/>
								</Texture>
							</Layer>
							<Layer level="BORDER">
								<FontString name="$parentFill" inherits="FriendsFont_Small" justifyH="LEFT" justifyV="MIDDLE" text="ENTER_NAME_OR_EMAIL">
									<Anchors>
										<Anchor point="LEFT" x="3" y="0"/>
									</Anchors>
									<Color r="0.35" g="0.35" b="0.35"/>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnLoad>
								self.autoCompleteParams = AUTOCOMPLETE_LIST.ADDFRIEND;
							</OnLoad>
							<OnEnterPressed>
								if ( not AutoCompleteEditBox_OnEnterPressed(self) and AddFriendEntryFrameAcceptButton:IsEnabled() ) then
									AddFriendFrame_Accept();
								end
							</OnEnterPressed>
							<OnTabPressed>
								if ( AddFriendNoteEditBox:IsShown() and not AutoCompleteEditBox_OnTabPressed(self) ) then
									AddFriendNoteEditBox:SetFocus();
								end
							</OnTabPressed>
							<OnEscapePressed>
								self:ClearFocus();
							</OnEscapePressed>
							<OnTextChanged function="AddFriendNameEditBox_OnTextChanged"/>
						</Scripts>
						<FontString inherits="FriendsFont_Small">
							<Color r="1" g="1" b="1" />
						</FontString>
					</EditBox>
					<Frame name="AddFriendNoteFrame" hidden="true">
						<Size x="286" y="60"/>
						<Anchors>
							<Anchor point="TOP" x="-2" y="-164"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentTopLeft" file="Interface\Common\Common-Input-Border-TL">
									<Size x="8" y="8"/>
									<Anchors>
										<Anchor point="TOPLEFT" x="0" y="0"/>
									</Anchors>
								</Texture>
								<Texture name="$parentTopRight" file="Interface\Common\Common-Input-Border-TR">
									<Size x="8" y="8"/>
									<Anchors>
										<Anchor point="TOPRIGHT" x="0" y="0"/>
									</Anchors>
								</Texture>
								<Texture name="$parentTop" file="Interface\Common\Common-Input-Border-T">
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentTopLeft" relativePoint="TOPRIGHT"/>
										<Anchor point="BOTTOMRIGHT" relativeTo="$parentTopRight" relativePoint="BOTTOMLEFT"/>
									</Anchors>
								</Texture>
								<Texture name="$parentBottomLeft" file="Interface\Common\Common-Input-Border-BL">
									<Size x="8" y="8"/>
									<Anchors>
										<Anchor point="BOTTOMLEFT" x="0" y="0"/>
									</Anchors>
								</Texture>
								<Texture name="$parentBottomRight" file="Interface\Common\Common-Input-Border-BR">
									<Size x="8" y="8"/>
									<Anchors>
										<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
									</Anchors>
								</Texture>
								<Texture name="$parentBottom" file="Interface\Common\Common-Input-Border-B">
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentBottomLeft" relativePoint="TOPRIGHT"/>
										<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRight" relativePoint="BOTTOMLEFT"/>
									</Anchors>
								</Texture>
								<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border-L">
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentTopLeft" relativePoint="BOTTOMLEFT"/>
										<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomLeft" relativePoint="TOPRIGHT"/>
									</Anchors>
								</Texture>
								<Texture name="$parentRight" file="Interface\Common\Common-Input-Border-R">
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentTopRight" relativePoint="BOTTOMLEFT"/>
										<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRight" relativePoint="TOPRIGHT"/>
									</Anchors>
								</Texture>
								<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border-M">
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT"/>
										<Anchor point="BOTTOMRIGHT" relativeTo="$parentRight" relativePoint="BOTTOMLEFT"/>
									</Anchors>
								</Texture>
							</Layer>
						</Layers>
						<Frames>
							<ScrollFrame name="$parentScrollFrame" inherits="UIPanelScrollFrameTemplate">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parent" relativePoint="TOPLEFT" x="6" y="-6"/>
									<Anchor point="BOTTOMRIGHT" relativeTo="$parent" relativePoint="BOTTOMRIGHT" x="0" y="6"/>
								</Anchors>
								<Scripts>
									<OnLoad>
										local scrollBar = _G[self:GetName().."ScrollBar"];
										scrollBar:ClearAllPoints();
										scrollBar:SetPoint("TOPLEFT", self, "TOPRIGHT", -18, -10);
										scrollBar:SetPoint("BOTTOMLEFT", self, "BOTTOMRIGHT", -18, 8);
										-- reposition the up and down buttons
										_G[self:GetName().."ScrollBarScrollDownButton"]:SetPoint("TOP", scrollBar, "BOTTOM", 0, 4);
										_G[self:GetName().."ScrollBarScrollUpButton"]:SetPoint("BOTTOM", scrollBar, "TOP", 0, -4);
										-- make the scroll bar hideable and force it to start off hidden so positioning calculations can be done
										-- as soon as it needs to be shown
										self.scrollBarHideable = 1;
										scrollBar:Hide();
									</OnLoad>
									<OnMouseDown>
										AddFriendNoteEditBox:SetFocus();
									</OnMouseDown>
								</Scripts>
								<ScrollChild>
									<EditBox name="AddFriendNoteEditBox" multiLine="true" letters="127" countInvisibleLetters="true" autoFocus="false">
										<Size x="258" y="1"/>
										<Layers>
											<Layer level="BORDER">
												<FontString name="$parentFill" inherits="FriendsFont_Small" justifyH="LEFT" justifyV="MIDDLE" text="ENTER_INVITE_NOTE">
													<Anchors>
														<Anchor point="LEFT" x="3" y="0"/>
													</Anchors>
													<Color r="0.35" g="0.35" b="0.35"/>
												</FontString>
											</Layer>
										</Layers>
										<Scripts>
											<OnTabPressed>
												AddFriendNameEditBox:SetFocus();
											</OnTabPressed>
											<OnTextChanged>
												ScrollingEdit_OnTextChanged(self, self:GetParent());
												if ( self:GetText() ~= "" ) then
													AddFriendNoteEditBoxFill:Hide();
												else
													AddFriendNoteEditBoxFill:Show();
												end
											</OnTextChanged>
											<OnCursorChanged function="ScrollingEdit_OnCursorChanged"/>
											<OnUpdate>
												ScrollingEdit_OnUpdate(self, elapsed, self:GetParent());
											</OnUpdate>
											<OnEscapePressed>
												self:ClearFocus();
											</OnEscapePressed>
										</Scripts>
										<FontString inherits="FriendsFont_Small"/>
									</EditBox>
								</ScrollChild>
							</ScrollFrame>
						</Frames>
					</Frame>
					<Button name="$parentAcceptButton" inherits="UIPanelButtonTemplate" text="ADD_FRIEND">
						<Size x="118" y="22"/>
						<Anchors>
							<Anchor point="BOTTOMLEFT" x="45" y="30"/>
						</Anchors>
						<Scripts>
							<OnClick function="AddFriendFrame_Accept"/>
						</Scripts>
					</Button>
					<Button name="$parentCancelButton" inherits="UIPanelButtonTemplate" text="CANCEL">
						<Size x="118" y="22"/>
						<Anchors>
							<Anchor point="BOTTOMRIGHT" x="-50" y="30"/>
						</Anchors>
						<Scripts>
							<OnClick>
								StaticPopupSpecial_Hide(AddFriendFrame);
							</OnClick>
						</Scripts>
					</Button>
				</Frames>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self.exclusive = true;
				self.hideOnEscape = true;
			</OnLoad>
			<OnShow function="AddFriendFrame_OnShow"/>
			<OnHide>
				AddFriendFrame.editFocus = nil;
				PlaySound(SOUNDKIT.IG_MAINMENU_CLOSE);
			</OnHide>
		</Scripts>
	</Frame>
	<Frame name="FriendsFriendsFrame" parent="UIParent" hidden="true" frameStrata="DIALOG">
		<Size x="314" y="345"/>
		<Anchors>
			<Anchor point="CENTER" x="0" y="50"/>
		</Anchors>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentTitle" inherits="FriendsFont_Large" justifyH="LEFT">
					<Size x="270" y="16"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="26" y="-20"/>
					</Anchors>
					<Color r="1.0" g="0.82" b="0"/>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="FriendsFriendsFrameDropDown" inherits="UIDropDownMenuTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" x="8" y="-40"/>
				</Anchors>
			</Frame>
			<Frame name="FriendsFriendsList">
				<Size x="266" y="215"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="24" y="-74"/>
				</Anchors>
				<Backdrop bgFile="Interface\Tooltips\UI-Tooltip-Background" edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
					<EdgeSize>
						<AbsValue val="16"/>
					</EdgeSize>
					<TileSize>
						<AbsValue val="16"/>
					</TileSize>
					<BackgroundInsets>
						<AbsInset left="5" right="5" top="5" bottom="5"/>
					</BackgroundInsets>
				</Backdrop>
				<Scripts>
					<OnLoad>
						self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b);
						self:SetBackdropColor(0, 0, 0);
					</OnLoad>
				</Scripts>
			</Frame>
			<Button name="FriendsFriendsButton1" inherits="FriendsFriendsButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="FriendsFriendsList" x="4" y="-8"/>
				</Anchors>
			</Button>
			<Button name="FriendsFriendsButton2" inherits="FriendsFriendsButtonTemplate">
				<Anchors>
					<Anchor point="TOP" relativeTo="FriendsFriendsButton1" relativePoint="BOTTOM" x="0" y="0"/>
				</Anchors>
			</Button>
			<Button name="FriendsFriendsButton3" inherits="FriendsFriendsButtonTemplate">
				<Anchors>
					<Anchor point="TOP" relativeTo="FriendsFriendsButton2" relativePoint="BOTTOM" x="0" y="0"/>
				</Anchors>
			</Button>
			<Button name="FriendsFriendsButton4" inherits="FriendsFriendsButtonTemplate">
				<Anchors>
					<Anchor point="TOP" relativeTo="FriendsFriendsButton3" relativePoint="BOTTOM" x="0" y="0"/>
				</Anchors>
			</Button>
			<Button name="FriendsFriendsButton5" inherits="FriendsFriendsButtonTemplate">
				<Anchors>
					<Anchor point="TOP" relativeTo="FriendsFriendsButton4" relativePoint="BOTTOM" x="0" y="0"/>
				</Anchors>
			</Button>
			<Button name="FriendsFriendsButton6" inherits="FriendsFriendsButtonTemplate">
				<Anchors>
					<Anchor point="TOP" relativeTo="FriendsFriendsButton5" relativePoint="BOTTOM" x="0" y="0"/>
				</Anchors>
			</Button>
			<Button name="FriendsFriendsButton7" inherits="FriendsFriendsButtonTemplate">
				<Anchors>
					<Anchor point="TOP" relativeTo="FriendsFriendsButton6" relativePoint="BOTTOM" x="0" y="0"/>
				</Anchors>
			</Button>
			<Button name="FriendsFriendsButton8" inherits="FriendsFriendsButtonTemplate">
				<Anchors>
					<Anchor point="TOP" relativeTo="FriendsFriendsButton7" relativePoint="BOTTOM" x="0" y="0"/>
				</Anchors>
			</Button>
			<Button name="FriendsFriendsButton9" inherits="FriendsFriendsButtonTemplate">
				<Anchors>
					<Anchor point="TOP" relativeTo="FriendsFriendsButton8" relativePoint="BOTTOM" x="0" y="0"/>
				</Anchors>
			</Button>
			<Button name="FriendsFriendsButton10" inherits="FriendsFriendsButtonTemplate">
				<Anchors>
					<Anchor point="TOP" relativeTo="FriendsFriendsButton9" relativePoint="BOTTOM" x="0" y="0"/>
				</Anchors>
			</Button>
			<Button name="FriendsFriendsButton11" inherits="FriendsFriendsButtonTemplate">
				<Anchors>
					<Anchor point="TOP" relativeTo="FriendsFriendsButton10" relativePoint="BOTTOM" x="0" y="0"/>
				</Anchors>
			</Button>
			<ScrollFrame name="FriendsFriendsScrollFrame" inherits="FauxScrollFrameTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="FriendsFriendsList" x="0" y="-4"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="FriendsFriendsList" x="-26" y="3"/>
				</Anchors>
				<Scripts>
					<OnVerticalScroll>
						FauxScrollFrame_OnVerticalScroll(self, offset, FRIENDS_FRAME_IGNORE_HEIGHT, FriendsFriendsList_Update);
					</OnVerticalScroll>
				</Scripts>
			</ScrollFrame>
			<Frame name="FriendsFriendsWaitFrame">
				<Size x="20" y="20"/>
				<Anchors>
					<Anchor point="CENTER" relativeTo="FriendsFriendsScrollFrame"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString inherits="ChatFontNormal" text="FRIENDS_FRIENDS_WAITING">
							<Anchors>
								<Anchor point="CENTER" x="12" y="0"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnShow>
						if ( UIFrameIsFlashing(self) ) then
							UIFrameFlashStop(self);
						end
						UIFrameFlash(self, 1, 0.5, 60, true, 1)
					</OnShow>
				</Scripts>
			</Frame>
			<Button name="FriendsFriendsSendRequestButton" inherits="UIPanelButtonTemplate" text="SEND_REQUEST">
				<Size x="118" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT" x="30" y="24"/>
				</Anchors>
				<Scripts>
					<OnClick function="FriendsFriendsFrame_SendRequest"/>
				</Scripts>
			</Button>
			<Button name="FriendsFriendsCloseButton" inherits="UIPanelButtonTemplate" text="CLOSE">
				<Size x="118" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" x="-30" y="24"/>
				</Anchors>
				<Scripts>
					<OnClick function="FriendsFriendsFrame_Close"/>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self:RegisterEvent("BN_REQUEST_FOF_SUCCEEDED");
				self:RegisterEvent("BN_DISCONNECTED");
				self:RegisterEvent("BN_REQUEST_FOF_FAILED");
				self.requested = { };
				self.hideOnEscape = true;
				self.exclusive = true;
				UIDropDownMenu_SetWidth(FriendsFriendsFrameDropDown, 120);
			</OnLoad>
			<OnShow>
				PlaySound(SOUNDKIT.IG_MAINMENU_OPEN);
			</OnShow>
			<OnHide>
				PlaySound(SOUNDKIT.IG_MAINMENU_CLOSE);
			</OnHide>
			<OnEvent function="FriendsFriendsFrame_OnEvent"/>
		</Scripts>
	</Frame>

	<Frame name="BattleTagInviteFrame" parent="UIParent" frameStrata="DIALOG" hidden="true">
		<Size x="357" y="150"/>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="12" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString inherits="FriendsFont_Normal" text="BATTLE_TAG_REQUEST">
					<Anchors>
						<Anchor point="TOP" x="0" y="-20"/>
					</Anchors>
				</FontString>
				<FontString parentKey="BattleTag" inherits="FriendsFont_Large" text="">
					<Anchors>
						<Anchor point="TOP" x="0" y="-36"/>
					</Anchors>
					<Color r="1" g="0.82" b="0"/>
				</FontString>
				<FontString parentKey="InfoText" inherits="GameFontHighlightSmall" spacing="2" text="BATTLE_TAG_REQUEST_INFO">
					<Size x="241" y="0"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="-10"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button inherits="UIPanelButtonTemplate" text="SEND_REQUEST">
				<Size x="128" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT" x="43" y="15"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						BNSendVerifiedBattleTagInvite();	-- unit should have been set with BNCheckBattleTagInviteToUnit or BNCheckBattleTagInviteToGuildMember
						StaticPopupSpecial_Hide(self:GetParent());
					</OnClick>
				</Scripts>
			</Button>
			<Button inherits="UIPanelButtonTemplate" text="CANCEL">
				<Size x="98" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" x="-43" y="15"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						StaticPopupSpecial_Hide(self:GetParent());
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self.exclusive = true;
				self.hideOnEscape = true;
			</OnLoad>
		</Scripts>
	</Frame>
</Ui>
