<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="GameMenuFrame.lua"/>
	<Frame name="GameMenuFrame" toplevel="true" frameStrata="DIALOG" enableMouse="true" hidden="true" parent="UIParent">
		<Size x="195" y="312"/>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="GameMenuFrameHeader" file="Interface\DialogFrame\UI-DialogBox-Header">
					<Size x="256" y="64"/>
					<Anchors>
						<Anchor point="TOP" x="0" y="12"/>
					</Anchors>
				</Texture>
				<FontString inherits="GameFontNormal" text="MAINMENU_BUTTON">
					<Anchors>
						<Anchor point="TOP" relativeTo="GameMenuFrameHeader" x="0" y="-14"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="GameMenuButtonHelp" inherits="GameMenuButtonTemplate" text="GAMEMENU_HELP">
				<Anchors>
					<Anchor point="CENTER" relativePoint="TOP" x="0" y="-42"/>
				</Anchors>
				<Scripts>
					<OnShow>
						self:SetEnabled(not IsKioskModeEnabled());
					</OnShow>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
						ToggleHelpFrame()
					</OnClick>
				</Scripts>
			</Button>
			<Button name="GameMenuButtonStore" inherits="GameMenuButtonTemplate" text="BLIZZARD_STORE" motionScriptsWhileDisabled="true">
				<Anchors>
					<Anchor point="TOP" relativeTo="GameMenuButtonHelp" relativePoint="BOTTOM" x="0" y="-1"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						self:RegisterEvent("STORE_STATUS_CHANGED");
						self:RegisterEvent("TRIAL_STATUS_UPDATE");
						GameMenuFrame_UpdateStoreButtonState(self);
					</OnLoad>
					<OnShow function="GameMenuFrame_UpdateStoreButtonState"/>
					<OnEvent function="GameMenuFrame_UpdateStoreButtonState"/>
					<OnEnter>
						if ( self.disabledTooltip ) then
							GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
							GameTooltip:SetText(self:GetText());
							GameTooltip:AddLine(self.disabledTooltip, RED_FONT_COLOR.r, RED_FONT_COLOR.g, RED_FONT_COLOR.b, true);
							GameTooltip:Show();
						end
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
						ToggleStoreUI()
					</OnClick>
				</Scripts>
			</Button>
			<Button name="GameMenuButtonWhatsNew" inherits="GameMenuButtonTemplate" text="GAMEMENU_NEW_BUTTON" motionScriptsWhileDisabled="true">
				<Anchors>
					<Anchor point="TOP" relativeTo="GameMenuButtonStore" relativePoint="BOTTOM" x="0" y="-1"/>
				</Anchors>
				<Scripts>
					<OnShow>
						self:SetEnabled(not IsKioskModeEnabled());
					</OnShow>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
						HideUIPanel(GameMenuFrame);
						SplashFrame_Open();
					</OnClick>
				</Scripts>
			</Button>
			<Button name="GameMenuButtonOptions" inherits="GameMenuButtonTemplate" text="SYSTEMOPTIONS_MENU">
				<Anchors>
					<Anchor point="TOP" relativeTo="GameMenuButtonWhatsNew" relativePoint="BOTTOM" x="0" y="-16"/>
				</Anchors>
				<Scripts>
					<OnShow>
						self:SetEnabled(not IsKioskModeEnabled());
					</OnShow>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
						ShowUIPanel(VideoOptionsFrame);
						VideoOptionsFrame.lastFrame = GameMenuFrame;
					</OnClick>
				</Scripts>
			</Button>
			<Button name="GameMenuButtonUIOptions" inherits="GameMenuButtonTemplate" text="UIOPTIONS_MENU">
				<Anchors>
					<Anchor point="TOP" relativeTo="GameMenuButtonOptions" relativePoint="BOTTOM">
						<Offset>
							<AbsDimension x="0" y="-1"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
						ShowUIPanel(InterfaceOptionsFrame);
						InterfaceOptionsFrame.lastFrame = GameMenuFrame;
					</OnClick>
				</Scripts>
			</Button>
			<Button name="GameMenuButtonKeybindings" inherits="GameMenuButtonTemplate" text="KEY_BINDINGS">
				<Anchors>
					<Anchor point="TOP" relativeTo="GameMenuButtonUIOptions" relativePoint="BOTTOM" x="0" y="-1"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
						KeyBindingFrame_LoadUI();
						KeyBindingFrame.mode = 1;
						ShowUIPanel(KeyBindingFrame);
					</OnClick>
				</Scripts>
			</Button>
			<Button name="GameMenuButtonMacros" inherits="GameMenuButtonTemplate" text="MACROS">
				<Anchors>
					<Anchor point="TOP" relativeTo="GameMenuButtonKeybindings" relativePoint="BOTTOM" x="0" y="-1"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
						HideUIPanel(GameMenuFrame);
						ShowMacroFrame();
					</OnClick>
				</Scripts>
			</Button>
			<Button name="GameMenuButtonAddons" inherits="GameMenuButtonTemplate" hidden="true" text="ADDONS">
				<Anchors>
					<Anchor point="TOP" relativeTo="GameMenuButtonMacros" relativePoint="BOTTOM" x="0" y="-1"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						if ( GetNumAddOns() == 0 ) then
							GameMenuButtonAddons:Hide();
						else
							GameMenuButtonAddons:Show();
						end
					</OnLoad>
					<OnShow>
						self:SetEnabled(not IsKioskModeEnabled() and not ScriptsDisallowedForBeta());
					</OnShow>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
						HideUIPanel(GameMenuFrame);
						ShowUIPanel(AddonList);
					</OnClick>
				</Scripts>
			</Button>
			<Button name="GameMenuButtonRatings" inherits="GameMenuButtonTemplate" hidden="true" text="RATINGS_MENU">
				<Anchors>
					<Anchor point="TOP" relativeTo="GameMenuButtonAddons" relativePoint="BOTTOM" x="0" y="-1"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						if ( GetNumAddOns() == 0 ) then
							self:SetParent(GameMenuButtonMacros);
							self:SetPoint("TOP", GameMenuButtonMacros, "BOTTOM", 0, -1);
							GameMenuFrame:SetHeight(270);
						end
					</OnLoad>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION);
						HideUIPanel(GameMenuFrame);
						ShowUIPanel(RatingMenuFrame);
					</OnClick>
				</Scripts>
			</Button>
			<Button name="GameMenuButtonLogout" inherits="GameMenuButtonTemplate" text="LOGOUT">
				<Anchors>
					<Anchor point="TOP" relativeTo="GameMenuButtonAddons" relativePoint="BOTTOM" x="0" y="-16"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						if ( GetNumAddOns() == 0 ) then
							self:SetParent(GameMenuButtonMacros);
							self:SetPoint("TOP", GameMenuButtonMacros, "BOTTOM", 0, -16);
							GameMenuFrame:SetHeight(270);
						end
					</OnLoad>
					<OnShow>
						if ( not StaticPopup_Visible("CAMP") and not StaticPopup_Visible("QUIT") ) then
							self:Enable();
						else
							self:Disable();
						end
					</OnShow>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_LOGOUT);
						Logout();
						HideUIPanel(GameMenuFrame);
					</OnClick>
				</Scripts>
			</Button>
			<Button name="GameMenuButtonQuit" inherits="GameMenuButtonTemplate" text="EXIT_GAME">
				<Anchors>
					<Anchor point="TOP" relativeTo="GameMenuButtonLogout" relativePoint="BOTTOM" x="0" y="-1"/>
				</Anchors>
				<Scripts>
					<OnShow>
						if ( not StaticPopup_Visible("CAMP") and not StaticPopup_Visible("QUIT") and not IsKioskModeEnabled() ) then
							self:Enable();
						else
							self:Disable();
						end
					</OnShow>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_QUIT);
						Quit();
						HideUIPanel(GameMenuFrame);
					</OnClick>
				</Scripts>
			</Button>
			<Button name="GameMenuButtonContinue" inherits="GameMenuButtonTemplate" text="RETURN_TO_GAME">
				<Anchors>
					<Anchor point="TOP" relativeTo="GameMenuButtonQuit" relativePoint="BOTTOM" x="0" y="-16"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_CONTINUE);
						HideUIPanel(GameMenuFrame);
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnShow function="GameMenuFrame_OnShow"/>
			<OnHide>
				UpdateMicroButtons();
				Enable_BagButtons();
			</OnHide>
		</Scripts>
	</Frame>
</Ui>
