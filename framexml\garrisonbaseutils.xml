<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">

	<Script file="GarrisonBaseUtils.lua"/>

	<Frame name="GarrisonFollowerPortraitTemplate" mixin="GarrisonFollowerPortraitMixin" virtual="true">
		<Size x="52" y="60"/>
		<Layers>
			<Layer level="BORDER">
				<Texture parentKey="PortraitRing" atlas="GarrMission_PortraitRing" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOP"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="-1">
				<Texture parentKey="Portrait">
					<Size x="44" y="44"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.PortraitRing" y="5"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="1">
				<Texture parentKey="PortraitRingQuality" atlas="GarrMission_PortraitRing_Quality" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOP"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="2">
				<Texture parentKey="LevelBorder" atlas="GarrMission_PortraitRing_LevelBorder" useAtlasSize="true">
					<Anchors>
						<Anchor point="BOTTOM" y="-3"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Level" inherits="GameFontHighlightSmall" justifyH="CENTER" text="95">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.LevelBorder"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="PortraitRingCover" atlas="GarrMission_PortraitRing_Darkener" useAtlasSize="true" hidden="true" alpha="0.6">
					<Anchors>
						<Anchor point="TOP" x="0" y="-1"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>
</Ui>
