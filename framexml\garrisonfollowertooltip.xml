<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
UI.xsd">
	<Script file="GarrisonFollowerTooltip.lua"/>
		<Frame name="GarrisonFollowerTooltip" inherits="GarrisonFollowerTooltipTemplate" movable="false" toplevel="true">
		<Size x="260" y="122"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="80"/>
		</Anchors>
		<Scripts>
			<OnLoad>
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="GarrisonFollowerAbilityTooltip" inherits="GarrisonFollowerAbilityTooltipTemplate" movable="false" toplevel="true">
		<Size x="245" y="45"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="80"/>
		</Anchors>
		<Scripts>
			<OnLoad>
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="GarrisonFollowerAbilityWithoutCountersTooltip" inherits="GarrisonFollowerAbilityWithoutCountersTooltipTemplate" movable="false" toplevel="true">
		<Size x="245" y="45"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="80"/>
		</Anchors>
		<Scripts>
			<OnLoad>
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="GarrisonFollowerMissionAbilityWithoutCountersTooltip" inherits="GarrisonFollowerMissionAbilityWithoutCountersTooltipTemplate" movable="false" toplevel="true">
		<Size x="245" y="45"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="80"/>
		</Anchors>
		<Scripts>
			<OnLoad>
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="GarrisonShipyardFollowerTooltip" inherits="GarrisonShipyardFollowerTooltipTemplate" movable="false" toplevel="true">
		<Size x="260" y="122"/>
		<Anchors>
			<Anchor point="BOTTOM" x="0" y="80"/>
		</Anchors>
		<Scripts>
			<OnLoad>
			</OnLoad>
		</Scripts>
	</Frame>
</Ui>
