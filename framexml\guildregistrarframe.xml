 <Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="GuildRegistrarFrame.lua"/>
	<Frame name="GuildRegistrarFrame" toplevel="true" parent="UIParent" movable="true" enableMouse="true" hidden="true" inherits="ButtonFrameTemplate">
		<Layers>
			<Layer level="BACKGROUND">
                <Texture name="GuildRegistrarFramePortrait">
                    <Size x="60" y="60"/>
                    <Anchors>
                        <Anchor point="TOPLEFT" x="7" y="-6"/>
                    </Anchors>
                </Texture>
				<Texture name="GuildRegistrarFrameBg" file="Interface\QuestFrame\QuestBG">
					<Size x="512" y="512"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="7" y="-62"/>
					</Anchors>
				</Texture>
            </Layer>
			<Layer level="ARTWORK">
				<FontString name="GuildRegistrarText" inherits="QuestFont" justifyH="LEFT">
					<Size x="270" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="10" y="-10"/>
					</Anchors>
				</FontString>
				<Texture name="$parentTop" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
					<Size x="31" y="102"/>
					<Anchors>
						<Anchor point="TOPRIGHT" relativePoint="TOPRIGHT" x="-3" y="-59"/>
					</Anchors>
					<TexCoords left="0" right="0.484375" top="0" bottom="0.4"/>
				</Texture>
				<Texture name="$parentBottom" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
					<Size x="31" y="106"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT" x="-3" y="25"/>
					</Anchors>
					<TexCoords left="0.515625" right="1.0" top="0" bottom="0.4140625"/>
				</Texture>
				<Texture name="$parentMiddle" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
					<Size x="31" y="1"/>
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentTop" relativePoint="BOTTOM"/>
						<Anchor point="BOTTOM" relativeTo="$parentBottom" relativePoint="TOP"/>
					</Anchors>
					<TexCoords left="0" right="0.484375" top=".75" bottom="1.0"/>
				</Texture>
			</Layer>
		</Layers> 
 		<Frames>
			<Frame name="GuildRegistrarNpcNameFrame">
				<Size x="300" y="14"/>
				<Anchors>
					<Anchor point="TOP" relativeTo="GuildRegistrarFrame" relativePoint="TOP" x="0" y="-4"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString name="GuildRegistrarFrameNpcNameText" inherits="GameFontHighlight"/>
					</Layer>
				</Layers>
				<Scripts>
                     <OnLoad function="RaiseFrameLevelByTwo"/>
                </Scripts>
			</Frame>
			<Frame name="GuildRegistrarGreetingFrame" setAllPoints="true" hidden="true">
				<Layers>
					<Layer level="BACKGROUND">
						<FontString name="AvailableServicesText" inherits="QuestTitleFont" text="AVAILABLE_SERVICES" justifyH="LEFT">
							<Size x="300" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="20" y="-70"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="GuildRegistrarFrameGoodbyeButton" inherits="UIPanelButtonTemplate" text="CANCEL">
						<Size x="75" y="22"/>
						<Anchors>
							<Anchor point="BOTTOMRIGHT" relativeTo="GuildRegistrarFrame" relativePoint="BOTTOMRIGHT" x="-6" y="4"/>
						</Anchors>
						<Scripts>
							<OnClick>
								HideUIPanel(GuildRegistrarFrame);
							</OnClick>
						</Scripts>
					</Button>
					<Button name="GuildRegistrarButton1" inherits="QuestTitleButtonTemplate" text="GUILD_CHARTER_PURCHASE">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="AvailableServicesText" relativePoint="BOTTOMLEFT" x="-10" y="-10"/>
						</Anchors>
						<Scripts>
							<OnClick function="GuildRegistrar_ShowPurchaseFrame"/>
						</Scripts>
					</Button>
					<Button name="GuildRegistrarButton2" inherits="QuestTitleButtonTemplate" text="GUILD_CHARTER_REGISTER">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="GuildRegistrarButton1" relativePoint="BOTTOMLEFT"/>
						</Anchors>
						<Scripts>
							<OnClick function="TurnInGuildCharter"/>
						</Scripts>
					</Button>
<!--
					<Button name="GuildRegistrarButton3" inherits="QuestTitleButtonTemplate" text="GUILD_CREST_DESIGN">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="GuildRegistrarButton2" relativePoint="BOTTOMLEFT"/>
						</Anchors>
						<Scripts>
							<OnClick>
								GetTabardInfo();
							</OnClick>
						</Scripts>
					</Button>
-->
				</Frames>
			</Frame>
			<Frame name="GuildRegistrarPurchaseFrame" setAllPoints="true" hidden="true">
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="GuildRegistrarPurchaseText" inherits="QuestFont" text="GUILD_REGISTRAR_PURCHASE_TEXT" justifyH="LEFT">
							<Size x="270" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="20" y="-70"/>
							</Anchors>
						</FontString>
						<FontString name="GuildRegistrarCostLabel" inherits="GameFontNormal" text="COSTS_LABEL">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="GuildRegistrarPurchaseText" relativePoint="BOTTOMLEFT" x="0" y="-5"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Frame name="GuildRegistrarMoneyFrame" inherits="MoneyFrameTemplate">
						<Anchors>
							<Anchor point="LEFT" relativeTo="GuildRegistrarCostLabel" relativePoint="RIGHT" x="15" y="0"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								MoneyFrame_OnLoad(self);
								MoneyFrame_SetType(self, "STATIC");
							</OnLoad>
						</Scripts>
					</Frame>
					<Button name="GuildRegistrarFrameCancelButton" inherits="UIPanelButtonTemplate" text="CANCEL">
						<Size x="75" y="22"/>
						<Anchors>
							<Anchor point="BOTTOMRIGHT" relativeTo="GuildRegistrarFrame" relativePoint="BOTTOMRIGHT" x="-6" y="4"/>
						</Anchors>
						<Scripts>
							<OnClick>
								HideUIPanel(GuildRegistrarFrame);
							</OnClick>
						</Scripts>
					</Button>
					<Button name="GuildRegistrarFramePurchaseButton" motionScriptsWhileDisabled="true" inherits="UIPanelButtonTemplate" text="PURCHASE">
						<Size x="85" y="22"/>
						<Anchors>
							<Anchor point="BOTTOMLEFT" relativeTo="GuildRegistrarFrame" x="6" y="4"/>
						</Anchors>
						<Scripts>
							<OnClick>
								GuildRegistrar_PurchaseCharter();
							</OnClick>
							<OnShow>
								if (GameLimitedMode_IsActive()) then
									self.tooltip = RED_FONT_COLOR_CODE..TRIAL_RESTRICTED..FONT_COLOR_CODE_CLOSE;
									self:Disable();
								else
									self.tooltip = nil;
									self:Enable();
								end
							</OnShow>
							<OnEnter>
								if (self.tooltip) then
									GameTooltip:SetOwner(self, "ANCHOR_BOTTOMRIGHT");
									GameTooltip:SetText(self.tooltip, nil, nil, nil, nil, true);
								end
							</OnEnter>
							<OnLeave>
								if self == GameTooltip:GetOwner() then
									GameTooltip:Hide();
								end
							</OnLeave>
						</Scripts>
					</Button>
					<EditBox name="GuildRegistrarFrameEditBox" letters="24" historyLines="1">
						<Size x="130" y="32"/>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="GuildRegistrarCostLabel" relativePoint="BOTTOMLEFT" x="0" y="-5"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture file="Interface\ChatFrame\UI-ChatInputBorder-Left">
									<Size x="75" y="32"/>
									<Anchors>
										<Anchor point="LEFT" x="-10" y="0"/>
									</Anchors>
									<TexCoords left="0" right="0.29296875" top="0" bottom="1.0"/>
								</Texture>
								<Texture file="Interface\ChatFrame\UI-ChatInputBorder-Right">
									<Size x="75" y="32"/>
									<Anchors>
										<Anchor point="RIGHT" x="10" y="0"/>
									</Anchors>
									<TexCoords left="0.70703125" right="1.0" top="0" bottom="1.0"/>
								</Texture>
							</Layer>
						</Layers>
						<Scripts>
							<OnEnterPressed>
								GuildRegistrar_PurchaseCharter();
							</OnEnterPressed>
							<OnEscapePressed>
								ChatEdit_FocusActiveWindow();
							</OnEscapePressed>
						</Scripts>
						<FontString inherits="ChatFontNormal"/>
					</EditBox>	
				</Frames>
			</Frame>
 		</Frames>
		<Scripts>
			<OnLoad>
				self:RegisterEvent("GUILD_REGISTRAR_SHOW");
				self:RegisterEvent("GUILD_REGISTRAR_CLOSED");
			</OnLoad>
			<OnEvent>
				if ( event == "GUILD_REGISTRAR_SHOW" ) then
					ShowUIPanel(GuildRegistrarFrame);
					if ( not GuildRegistrarFrame:IsShown() ) then
						CloseGuildRegistrar();
					end
				elseif ( event == "GUILD_REGISTRAR_CLOSED" ) then
					HideUIPanel(GuildRegistrarFrame);
				end
			</OnEvent>
			<OnShow>
				GuildRegistrar_OnShow();
				PlaySound(SOUNDKIT.IG_QUEST_LIST_OPEN);
			</OnShow>
			<OnHide>
				PlaySound(SOUNDKIT.IG_QUEST_LIST_CLOSE);
				StaticPopup_Hide("CONFIRM_GUILD_CHARTER_PURCHASE");
				CloseGuildRegistrar();
			</OnHide>
		</Scripts>
 	</Frame>
 </Ui>
