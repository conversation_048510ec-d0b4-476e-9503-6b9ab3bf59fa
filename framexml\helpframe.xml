<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="HelpFrame.lua"/>

	<!-- Button templates
	size=256,256
	<Texture name="BigPaperButton-highlight.png" >
		<Size x="199" y="54"/>
		<TexCoords left="0.00390625" right="0.78125000" top="0.00390625" bottom="0.21484375"/>
	</Texture>
	<Texture name="BigPaperButton-down.png" >
		<Size x="199" y="54"/>
		<TexCoords left="0.00390625" right="0.78125000" top="0.22265625" bottom="0.43359375"/>
	</Texture>
	<Texture name="BigPaperButton-up.png" >
		<Size x="199" y="54"/>
		<TexCoords left="0.00390625" right="0.78125000" top="0.44140625" bottom="0.65234375"/>
	</Texture>
	<Texture name="BigPaperButton-select.png" >
		<Size x="199" y="54"/>
		<TexCoords left="0.00390625" right="0.78125000" top="0.66015625" bottom="0.87109375"/>
	</Texture-->


	<!-- Knowledgebase Buttons.
	size=512,128
	<Texture name="KnowledgeBaseButttonLeft-Up.png" >
		<Size x="64" y="25"/>
		<TexCoords left="0.00195313" right="0.12695313" top="0.00781250" bottom="0.20312500"/>
	</Texture>
	<Texture name="KnowledgeBaseButttonLeft-Down.png" >
		<Size x="64" y="25"/>
		<TexCoords left="0.00195313" right="0.12695313" top="0.21875000" bottom="0.41406250"/>
	</Texture>
	<Texture name="KnowledgeBaseButttonRight-Up.png" >
		<Size x="64" y="25"/>
		<TexCoords left="0.00195313" right="0.12695313" top="0.42968750" bottom="0.62500000"/>
	</Texture>
	<Texture name="KnowledgeBaseButttonRight-Down.png" >
		<Size x="64" y="25"/>
		<TexCoords left="0.00195313" right="0.12695313" top="0.64062500" bottom="0.83593750"/>
	</Texture>
	<Texture name="KnowledgeBaseButttonHighlight.png" >
		<Size x="256" y="25"/>
		<TexCoords left="0.13085938" right="0.63085938" top="0.00781250" bottom="0.20312500"/>
	</Texture>
	<Texture name="KnowledgeBaseButttonMid-Up.png" >
		<Size x="256" y="25"/>
		<TexCoords left="0.13085938" right="0.63085938" top="0.21875000" bottom="0.41406250"/>
	</Texture>
	<Texture name="KnowledgeBaseButttonMid-Down.png" >
		<Size x="256" y="25"/>
		<TexCoords left="0.13085938" right="0.63085938" top="0.42968750" bottom="0.62500000"/>
	</Texture-->


	<!-- Nav bar tiled textures.
	size=128,512
	<Texture name="_GMChat-TitleBar.png" horizTile="true" >
		<Size x="128" y="3"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.00195313" bottom="0.00781250"/>
	</Texture>
	<Texture name="_GMChat-TitleBG.png" horizTile="true" >
		<Size x="128" y="24"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.01171875" bottom="0.05859375"/>
	</Texture>
	<Texture name="_NavMenu-Button-Up.png" horizTile="true" >
		<Size x="128" y="30"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.06250000" bottom="0.12109375"/>
	</Texture>
	<Texture name="_NavMenu-Button-Down.png" horizTile="true" >
		<Size x="128" y="30"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.12500000" bottom="0.18359375"/>
	</Texture>
	<Texture name="_NavMenu-BarBG.png" horizTile="true" >
		<Size x="128" y="34"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.18750000" bottom="0.25390625"/>
	</Texture>
	<Texture name="_NavMenu-BarOverlay.png" horizTile="true" >
		<Size x="128" y="34"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.25781250" bottom="0.32421875"/>
	</Texture>
	<Texture name="_GMChatRequest-Mid.png" horizTile="true" >
		<Size x="128" y="72"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.32812500" bottom="0.46875000"/>
	</Texture>
	<Texture name="_GMChatRequestGlow-Mid.png" horizTile="true" >
		<Size x="128" y="72"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.47265625" bottom="0.61328125"/>
	</Texture>
	-->


	<!-- Navbar Textures
	size=512,128
	<Texture name="Divider.png" >
		<Size x="229" y="45"/>
		<TexCoords left="0.00195313" right="0.44921875" top="0.00781250" bottom="0.35937500"/>
	</Texture>
	<Texture name="NavMenu-ButtonSelection.png" >
		<Size x="128" y="34"/>
		<TexCoords left="0.00195313" right="0.25195313" top="0.37500000" bottom="0.64062500"/>
	</Texture>
	<Texture name="NavMenu-ButtonHighlight.png" >
		<Size x="128" y="34"/>
		<TexCoords left="0.00195313" right="0.25195313" top="0.65625000" bottom="0.92187500"/>
	</Texture>
	<Texture name="GMChatRequest-Left.png" >
		<Size x="49" y="72"/>
		<TexCoords left="0.25585938" right="0.35156250" top="0.37500000" bottom="0.93750000"/>
	</Texture>
	<Texture name="GMChatRequest-Right.png" >
		<Size x="40" y="72"/>
		<TexCoords left="0.35546875" right="0.43359375" top="0.37500000" bottom="0.93750000"/>
	</Texture>
	<Texture name="GMChat-TitleBarNubRight.png" >
		<Size x="4" y="10"/>
		<TexCoords left="0.43750000" right="0.44531250" top="0.37500000" bottom="0.45312500"/>
	</Texture>
	<Texture name="GMChat-TitleBarNubLeft.png" >
		<Size x="4" y="10"/>
		<TexCoords left="0.43750000" right="0.44531250" top="0.46875000" bottom="0.54687500"/>
	</Texture>
	<Texture name="NavMenu-HomeButton-up.png" >
		<Size x="128" y="30"/>
		<TexCoords left="0.45312500" right="0.70312500" top="0.00781250" bottom="0.24218750"/>
	</Texture>
	<Texture name="NavMenu-HomeButton-down.png" >
		<Size x="128" y="30"/>
		<TexCoords left="0.45312500" right="0.70312500" top="0.25781250" bottom="0.49218750"/>
	</Texture>
	<Texture name="NavMenu-HomeButton-highlight.png" >
		<Size x="128" y="30"/>
		<TexCoords left="0.45312500" right="0.70312500" top="0.50781250" bottom="0.74218750"/>
	</Texture>
	<Texture name="NavMenu-ButtonOverflow-down.png" >
		<Size x="44" y="30"/>
		<TexCoords left="0.45312500" right="0.53906250" top="0.75781250" bottom="0.99218750"/>
	</Texture>
	<Texture name="NavMenu-ButtonOverflow-up.png" >
		<Size x="44" y="30"/>
		<TexCoords left="0.54296875" right="0.62890625" top="0.75781250" bottom="0.99218750"/>
	</Texture>
	<Texture name="NavMenu-Arrow-down.png" >
		<Size x="21" y="30"/>
		<TexCoords left="0.63281250" right="0.67382813" top="0.75781250" bottom="0.99218750"/>
	</Texture>
	<Texture name="NavMenu-SelectedArrowIcon.png" >
		<Size x="8" y="13"/>
		<TexCoords left="0.67773438" right="0.69335938" top="0.75781250" bottom="0.85937500"/>
	</Texture>
	<Texture name="GMChatRequestGlow-Left.png" >
		<Size x="49" y="72"/>
		<TexCoords left="0.70703125" right="0.80273438" top="0.00781250" bottom="0.57031250"/>
	</Texture>
	<Texture name="TicketBadge.png" >
		<Size x="34" y="35"/>
		<TexCoords left="0.70703125" right="0.77343750" top="0.58593750" bottom="0.85937500"/>
	</Texture>
	<Texture name="GMChatRequestGlow-Right.png" >
		<Size x="40" y="72"/>
		<TexCoords left="0.80664063" right="0.88476563" top="0.00781250" bottom="0.57031250"/>
	</Texture>
	<Texture name="TicketBadge-Highlight.png" >
		<Size x="34" y="35"/>
		<TexCoords left="0.80664063" right="0.87304688" top="0.58593750" bottom="0.85937500"/>
	</Texture>
	<Texture name="TicketBadge-Down.png" >
		<Size x="34" y="35"/>
		<TexCoords left="0.88867188" right="0.95507813" top="0.00781250" bottom="0.28125000"/>
	</Texture>
	<Texture name="NavMenu-Arrow-up.png" >
		<Size x="21" y="30"/>
		<TexCoords left="0.88867188" right="0.92968750" top="0.29687500" bottom="0.53125000"/>
	</Texture>

	-->




	<Button name="HelpFrameButtonTemplate" motionScriptsWhileDisabled="true" virtual="true">
		<Size x="174" y="54"/>
		<Layers>
			<Layer level="OVERLAY">
				<Texture name="$parentIcon" file="Interface\Icons\Ability_ThunderClap" parentKey="icon">
					<Size x="50" y="50"/>
					<Anchors>
						<Anchor point="LEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture name="$parentSelected" file="Interface\HelpFrame\HelpButtons" parentKey="selected" hidden="true">
					<Size x="174" y="54"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.00390625" right="0.68359375" top="0.66015625" bottom="0.87109375"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				local data = HelpFrameNavTbl[self:GetID()];
				if data.icon  then
					self.icon:SetTexture(data.icon);
				end

				if data.text then
					self:SetText(data.text);
				end

				self.tooltip = data.tooltipTex;
				self.newbieText = data.newbieText;
				data.button = self;
				HelpFrame_SetButtonEnabled(self, true);
			</OnLoad>
			<OnEnter>
				if (self.tooltip) then
					GameTooltip:SetOwner(self, "ANCHOR_CURSOR_RIGHT");
					GameTooltip:SetText(self.tooltip, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b);
					GameTooltip:Show();
				end
			</OnEnter>
			<OnLeave function="GameTooltip_Hide" />
			<OnMouseDown>
				if self:IsEnabled() then
					self.icon:SetPoint("LEFT", 4, -1);
				end
			</OnMouseDown>
			<OnMouseUp>
				self.icon:SetPoint("LEFT", 3, 0);
			</OnMouseUp>
			<OnClick>
				PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
				local data = HelpFrameNavTbl[self:GetID()];
				if ( not data.noSelection ) then
					HelpFrame_SetSelectedButton(self);
				end
				HelpFrame_SetFrameByKey(self:GetID());
			</OnClick>
		</Scripts>
		<NormalTexture file="Interface\HelpFrame\HelpButtons">
			<TexCoords left="0.00390625" right="0.68359375" top="0.44140625" bottom="0.65234375"/>
		</NormalTexture>
		<PushedTexture file="Interface\HelpFrame\HelpButtons">
			<TexCoords left="0.00390625" right="0.68359375" top="0.22265625" bottom="0.43359375"/>
		</PushedTexture>
		<HighlightTexture file="Interface\HelpFrame\HelpButtons" alphaMode="ADD">
			<TexCoords left="0.00390625" right="0.68359375" top="0.00390625" bottom="0.21484375"/>
		</HighlightTexture>
		<ButtonText name="$parentText" inherits="GameFontNormalMed2" justifyH="LEFT" parentKey="text">
			<Size x="120" y="30"/>
			<Anchors>
				<Anchor point="LEFT" relativeTo="$parentIcon" relativePoint="RIGHT" x="0" y="-1"/>
			</Anchors>
		</ButtonText>
	</Button>

	<Button name="HelpFrameNavButtonTemplate" inherits="NavButtonTemplate" virtual="true">
		<Scripts>
			<OnClick>
				PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
				if self.myclick ~= KnowledgeBase_Search then
					KnowledgeBase_ClearSearch(HelpFrame.kbase.searchBox);
				end
			</OnClick>
		</Scripts>
	</Button>

	<Button name="KnowledgeBaseArticleTemplate" virtual="true">
		<Size>
			<AbsDimension x="445" y="25"/>
		</Size>
		<Layers>
			<Layer level="ARTWORK" textureSubLevel="2">
				<Texture name="$parentLEndDown" file="Interface\HelpFrame\KnowledgeBaseButtton" parentKey="ldown" hidden="true">
					<Size x="64" y="25"/>
					<Anchors>
						<Anchor point="LEFT" x="-5" y="0"/>
					</Anchors>
					<TexCoords left="0.00195313" right="0.12695313" top="0.21875000" bottom="0.41406250"/>
				</Texture>
				<Texture name="$parentLEndUp" file="Interface\HelpFrame\KnowledgeBaseButtton" parentKey="lup">
					<Size x="64" y="25"/>
					<Anchors>
						<Anchor point="LEFT" x="-5" y="0"/>
					</Anchors>
					<TexCoords left="0.00195313" right="0.12695313" top="0.00781250" bottom="0.20312500"/>
				</Texture>
				<Texture name="$parentREndDown" file="Interface\HelpFrame\KnowledgeBaseButtton" parentKey="rdown" hidden="true">
					<Size x="64" y="25"/>
					<Anchors>
						<Anchor point="RIGHT" x="5" y="0"/>
					</Anchors>
					<TexCoords left="0.00195313" right="0.12695313" top="0.64062500" bottom="0.83593750"/>
				</Texture>
				<Texture name="$parentREndUp" file="Interface\HelpFrame\KnowledgeBaseButtton" parentKey="rup">
					<Size x="64" y="25"/>
					<Anchors>
						<Anchor point="RIGHT" x="5" y="0"/>
					</Anchors>
					<TexCoords left="0.00195313" right="0.12695313" top="0.42968750" bottom="0.62500000"/>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<FontString name="$parentNumber" inherits="GameFontNormal" justifyH="RIGHT" parentKey="number">
					<Size x="0" y="12"/>
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT" x="30" y="0"/>
					</Anchors>
				</FontString>
				<FontString name="$parentTitle" inherits="GameFontNormal" justifyH="LEFT" parentKey="title">
					<Size x="0" y="12"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentNumber" relativePoint="RIGHT" x="5" y="0"/>
						<Anchor point="RIGHT" x="-5" y="0"/>
					</Anchors>
					<Color r="0.8" g="0.8" b="0.8" a="1"/>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnMouseDown>
				self.lup:Hide();
				self.ldown:Show();
				self.rup:Hide();
				self.rdown:Show();
				PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
			</OnMouseDown>
			<OnMouseUp>
				self.ldown:Hide();
				self.lup:Show();
				self.rdown:Hide();
				self.rup:Show();
			</OnMouseUp>
		</Scripts>
		<NormalTexture file="Interface\HelpFrame\KnowledgeBaseButtton">
			<TexCoords left="0.13085938" right="0.63085938" top="0.21875000" bottom="0.41406250"/>
		</NormalTexture>
		<PushedTexture file="Interface\HelpFrame\KnowledgeBaseButtton">
			<TexCoords left="0.13085938" right="0.63085938" top="0.42968750" bottom="0.62500000"/>
		</PushedTexture>
		<HighlightTexture file="Interface\HelpFrame\KnowledgeBaseButtton">
			<TexCoords left="0.13085938" right="0.63085938" top="0.00781250" bottom="0.20312500"/>
		</HighlightTexture>
	</Button>

	<Button name="BrowserButtonTemplate" virtual="true">
		<Size x="32" y="32"/>
		<NormalTexture file="Interface\Buttons\UI-SquareButton-Up"/>
		<PushedTexture file="Interface\Buttons\UI-SquareButton-Down"/>
		<DisabledTexture file="Interface\Buttons\UI-SquareButton-Disabled"/>
		<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
		<Scripts>
			<OnMouseDown>
				local point, relativeTo, relativePoint, x, y = self.Icon:GetPoint(1);
				self.origX = x;
				self.origY = y;
				self.Icon:SetPoint(point, relativeTo, relativePoint, x-1, y-1);
			</OnMouseDown>
			<OnMouseUp>
				local point, relativeTo, relativePoint = self.Icon:GetPoint(1);
				self.Icon:SetPoint(point, relativeTo, relativePoint, self.origX, self.origY);
			</OnMouseUp>
			<OnHide>
				local point, relativeTo, relativePoint = self.Icon:GetPoint(1);
				self.Icon:SetPoint(point, relativeTo, relativePoint, self.origX, self.origY);
			</OnHide>
			<OnEnter>
				if (self.tooltip) then
					GameTooltip:SetOwner(self, "ANCHOR_CURSOR_RIGHT");
					GameTooltip:SetText(self.tooltip, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b);
				end
			</OnEnter>
			<OnLeave function="GameTooltip_Hide" />
		</Scripts>
	</Button>

	<Browser name="BrowserTemplate" frameStrata="HIGH" IMEFont="ChatFontNormal" virtual="true">
		<Frames>
			<Frame parentKey="BrowserInset" inherits="InsetFrameTemplate" frameStrata="MEDIUM">
				<Anchors>
					<Anchor point="TOPLEFT" x="-3" y="3"/>
					<Anchor point="BOTTOMRIGHT" x="3" y="-1"/>
				</Anchors>
			</Frame>
			<Button name="$parentBrowserSettings" inherits="BrowserButtonTemplate" parentKey="settings">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="HelpFrameCloseButton" relativePoint="TOPLEFT" x="-3" y="-5"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK" textureSubLevel="5">
						<Texture parentKey="Icon" file="Interface\Buttons\UI-OptionsButton">
							<Size x="16" y="16"/>
							<Anchors>
								<Anchor point="CENTER" />
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						self.tooltip = BROWSER_SETTINGS_BUTTON_TOOLTIP;
					</OnLoad>
					<OnClick>
						HelpBrowser_ToggleTooltip(self, self:GetParent());
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentNavHome" inherits="BrowserButtonTemplate" parentKey="home">
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativePoint="TOPLEFT" x="2" y="-2"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK" textureSubLevel="5">
						<Texture parentKey="Icon" file="Interface\Buttons\UI-HomeButton">
							<Size x="16" y="16"/>
							<Anchors>
								<Anchor point="CENTER" />
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						self.tooltip = BROWSER_HOME_TOOLTIP;
					</OnLoad>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						local parent = self:GetParent()
						parent:NavigateHome(parent.homepage);
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentNavBack" parentKey="back">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentNavHome" relativePoint="RIGHT" x="3"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						self:Disable();
					</OnLoad>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						self:GetParent():NavigateBack();
					</OnClick>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_CURSOR_RIGHT");
						GameTooltip:SetText(BROWSER_BACK_TOOLTIP, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b);
					</OnEnter>
					<OnLeave function="GameTooltip_Hide" />
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Up"/>
				<PushedTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Down"/>
				<DisabledTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Disabled"/>
				<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
			</Button>
			<Button name="$parentNavForward" parentKey="forward">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentNavBack" relativePoint="RIGHT" x="3"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						self:Disable();
					</OnLoad>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						self:GetParent():NavigateForward();
					</OnClick>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_CURSOR_RIGHT");
						GameTooltip:SetText(BROWSER_FORWARD_TOOLTIP, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b);
					</OnEnter>
					<OnLeave function="GameTooltip_Hide" />
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Up"/>
				<PushedTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Down"/>
				<DisabledTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Disabled"/>
				<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
			</Button>
			<Button name="$parentNavReload" inherits="BrowserButtonTemplate" parentKey="reload">
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentNavForward" relativePoint="RIGHT" x="3"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK" textureSubLevel="5">
						<Texture parentKey="Icon" file="Interface\Buttons\UI-RefreshButton">
							<Size x="16" y="16"/>
							<Anchors>
								<Anchor point="CENTER" />
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						self.tooltip = BROWSER_RELOAD_TOOLTIP;
					</OnLoad>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						self:GetParent():NavigateReload();
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentNavStop" inherits="BrowserButtonTemplate" hidden="true" parentKey="stop">
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentNavForward" relativePoint="RIGHT" x="3"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK" textureSubLevel="5">
						<Texture parentKey="Icon" file="Interface\Buttons\UI-StopButton">
							<Size x="16" y="16"/>
							<Anchors>
								<Anchor point="CENTER" />
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						self.tooltip = BROWSER_STOP_TOOLTIP;
					</OnLoad>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						self:GetParent():NavigateStop();
					</OnClick>
				</Scripts>
			</Button>
			<Frame name="LoadingIcon" toplevel="true" hidden="true" parentKey="loading">
				<Animations>
					<AnimationGroup parentKey="Loop" looping="REPEAT" >
						<Rotation target="LoadingIconSpin" order="1" duration="8" degrees="-360" />
					</AnimationGroup>
				</Animations>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentNavStop" relativePoint="TOPRIGHT"  x="0" y="4"/>
				</Anchors>
				<Size x="40" y="40"/>
				<Frames>
					<Frame name="$parentFrame" setAllPoints="true" >
						<Layers>
							<Layer level="BACKGROUND">
								<Texture file="Interface\COMMON\StreamBackground" name="$parentBackground" setAllPoints="true" >
									<Color r="1" g="0.82" b="0" a="1"/>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<Texture file="Interface\COMMON\StreamFrame" name="$parentAlpha" setAllPoints="true" />
							</Layer>
						</Layers>
					</Frame>
					<Frame name="$parentSpin" setAllPoints="true" >
						<Layers>
							<Layer level="BACKGROUND">
								<Texture file="Interface\COMMON\StreamCircle" name="$parentSpinner" setAllPoints="true" >
									<Color r="1" g="0.82" b="0"/>
								</Texture>
							</Layer>
							<Layer level="OVERLAY" >
								<Texture file="Interface\COMMON\StreamSpark" setAllPoints="true" />
							</Layer>
						</Layers>
					</Frame>
				</Frames>
			</Frame>
		</Frames>
		<Scripts>
			<OnExternalLink>
				StaticPopup_Show("EXTERNAL_LINK", url, nil, {url=url, browser=self});
			</OnExternalLink>
			<OnEscapePressed>
				self:ClearFocus();
			</OnEscapePressed>
			<OnButtonUpdate function="Browser_UpdateButtons"/>
			<OnError>
				local info = ChatTypeInfo["SYSTEM"];
				DEFAULT_CHAT_FRAME:AddMessage(msg, info.r, info.g, info.b, info.id);
			</OnError>
		</Scripts>
	</Browser>


	<Frame name="HelpFrame" toplevel="true" parent="UIParent" inherits="TranslucentFrameTemplate" hidden="true" enableMouse="true" >
		<Size x="974" y="524"/>
		<Anchors>
			<Anchor point="CENTER" x="0" y="0"/>
		</Anchors>
		<Layers>
			<Layer level="BORDER" textureSubLevel="-6">
				<Texture name="$parentTopTileStreaks" inherits="_UI-Frame-TopTileStreaks">
					<Anchors>
						<Anchor point="TOPLEFT" x="13" y="-13"/>
						<Anchor point="BOTTOMRIGHT" relativePoint="TOPRIGHT" x="-13" y="-35"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentHeader" inherits="TranslucentFrameTemplate" parentKey="header">
				<Size x="180" y="45"/>
				<Anchors>
					<Anchor point="CENTER" relativePoint="TOP" x="0" y="-8"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString inherits="GameFontNormal" text="HELP_FRAME_TITLE">
							<Anchors>
								<Anchor point="CENTER" x="0" y="0"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
			<Button name="$parentCloseButton" inherits="UIPanelCloseButton">
				<Anchors>
					<Anchor point="TOPRIGHT" relativePoint="TOPRIGHT">
						<Offset x="-7" y="-7"/>
					</Anchor>
				</Anchors>
			</Button>
			<Frame name="$parentLeftInset" useParentLevel="true" inherits="InsetFrameTemplate" parentKey="leftInset">
				<Anchors>
					<Anchor point="TOPLEFT" x="10" y="-10" />
					<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMLEFT" x="207" y="10"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentLeftShadow" file="Interface\HelpFrame\Tileable-ParchmentEdge-Left" vertTile="true">
							<Size x="64" y="256"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
								<Anchor point="BOTTOMLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentRightShadow" file="Interface\HelpFrame\Tileable-ParchmentEdge-Right" vertTile="true">
							<Size x="64" y="256"/>
							<Anchors>
								<Anchor point="TOPRIGHT" x="0" y="0"/>
								<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentTopShadow" file="Interface\HelpFrame\Tileable-ParchmentEdge-Top" horizTile="true">
							<Size x="256" y="64"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
								<Anchor point="TOPRIGHT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentBottomShadow" file="Interface\HelpFrame\Tileable-ParchmentEdge-Bottom" horizTile="true">
							<Size x="256" y="64"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" x="0" y="0"/>
								<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="$parentMainInset" useParentLevel="true" inherits="InsetFrameTemplate" parentKey="mainInset">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentLeftInset" relativePoint="TOPRIGHT" x="3" y="-32" />
					<Anchor point="BOTTOMRIGHT" x="-10" y="10"/>
				</Anchors>
			</Frame>
			<Button name="$parentButton1" inherits="HelpFrameButtonTemplate" parentKey="button1" id="1">
				<Anchors>
					<Anchor point="TOP" relativeTo="$parentLeftInset" x="2" y="-20"/>
				</Anchors>
			</Button>
			<Button name="$parentButton2" inherits="HelpFrameButtonTemplate" parentKey="button2" id="2">
				<Anchors>
					<Anchor point="TOP" relativeTo="$parentButton1" relativePoint="BOTTOM" x="0" y="-12"/>
				</Anchors>
			</Button>
			<Button name="$parentButton5" inherits="HelpFrameButtonTemplate" parentKey="button5" id="5">
				<Anchors>
					<Anchor point="TOP" relativeTo="$parentButton2" relativePoint="BOTTOM" x="0" y="-12"/>
				</Anchors>
			</Button>
			<Button name="$parentButton3" inherits="HelpFrameButtonTemplate" parentKey="button3" id="3">
				<Anchors>
					<Anchor point="TOP" relativeTo="$parentButton5" relativePoint="BOTTOM" x="0" y="-29"/>
				</Anchors>
			</Button>
			<Button name="$parentButton4" inherits="HelpFrameButtonTemplate" parentKey="button4" id="4">
				<Anchors>
					<Anchor point="TOP" relativeTo="$parentButton3" relativePoint="BOTTOM" x="0" y="-12"/>
				</Anchors>
			</Button>
			<Button name="$parentButton16" inherits="HelpFrameButtonTemplate" parentKey="button16" id="16">
				<Anchors>
					<Anchor point="TOP" relativeTo="$parentButton4" relativePoint="BOTTOM" x="0" y="-12"/>
				</Anchors>
			</Button>
			<Button name="$parentButton6" inherits="HelpFrameButtonTemplate" parentKey="button6" id="6">
				<Anchors>
					<Anchor point="BOTTOM" relativeTo="$parentLeftInset" x="0" y="16"/>
				</Anchors>
			</Button>
			<Frame name="$parentAccountSecurity" hidden="true" parentKey="asec">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentMainInset" x="5" y="-5"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="$parentMainInset" x="-5" y="5"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentTitle" inherits="GameFontNormalLarge" text="HELPFRAME_ACCOUNTSECURITY_TITLE" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="15" y="-15"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
						<FontString name="$parentText1Header" inherits="GameFontNormal" text="HELPFRAME_ACCOUNTSECURITY_TEXT1_HEADER" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTitle" relativePoint="BOTTOMLEFT" x="0" y="-25"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText1" inherits="GameFontHighlight" text="HELPFRAME_ACCOUNTSECURITY_TEXT1" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentText1Header" relativePoint="BOTTOMLEFT" x="0" y="-3"/>
							</Anchors>
						</FontString>

						<FontString name="$parentText2Header" inherits="GameFontNormal" text="HELPFRAME_ACCOUNTSECURITY_TEXT2_HEADER" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentText1" relativePoint="BOTTOMLEFT" x="0" y="-25"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText2" inherits="GameFontHighlight" text="HELPFRAME_ACCOUNTSECURITY_TEXT2" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentText2Header" relativePoint="BOTTOMLEFT" x="0" y="-3"/>
							</Anchors>
						</FontString>

						<FontString name="$parentText3Header" inherits="GameFontNormal" text="HELPFRAME_ACCOUNTSECURITY_TEXT3_HEADER" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentText2" relativePoint="BOTTOMLEFT" x="0" y="-25"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText3" inherits="GameFontHighlight" text="HELPFRAME_ACCOUNTSECURITY_TEXT3" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentText3Header" relativePoint="BOTTOMLEFT" x="0" y="-3"/>
							</Anchors>
						</FontString>

						<FontString name="$parentFooter" inherits="GameFontHighlight" text="HELPFRAME_ACCOUNTSECURITY_FOOTER" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentText3" relativePoint="BOTTOMLEFT" x="0" y="-25"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentOpenTicket" parentKey="ticketButton" inherits="HelpFrameButtonTemplate" id="6">
						<Anchors>
							<Anchor point="BOTTOM" x="0" y="20"/>
						</Anchors>
						<Scripts>
							<OnClick function="AccountSecurityOpenTicket_OnClick"/>
						</Scripts>
					</Button>
				</Frames>
				<Scripts>
					<OnLoad>
						tinsert(HelpFrameWindows, self);
					</OnLoad>
				</Scripts>
			</Frame>
			<Frame name="$parentCharacterStuck" hidden="true" parentKey="stuck">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentMainInset" x="5" y="-5"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="$parentMainInset" x="-5" y="5"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentTitle" inherits="GameFontNormalLarge" text="HELPFRAME_STUCK_TITLE" justifyH="LEFT">
							<Size x="0" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="15" y="-15"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText1" inherits="GameFontHighlight" text="HELPFRAME_STUCK_TEXT1" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTitle" relativePoint="BOTTOMLEFT" x="0" y="-3"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText2Header" inherits="GameFontNormal" text="HELPFRAME_STUCK_HEARTHSTONE_HEADER" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentText1" relativePoint="BOTTOMLEFT" x="0" y="-28"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText2" inherits="GameFontHighlight" text="HELPFRAME_STUCK_HEARTHSTONE" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentText2Header" relativePoint="BOTTOMLEFT" x="0" y="-3"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText3Header" inherits="GameFontNormal" text="HELPFRAME_STUCK_GRAVEYARD_HEADER" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentText2" relativePoint="BOTTOMLEFT" x="0" y="-144"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText3" inherits="GameFontHighlight" text="HELPFRAME_STUCK_GRAVEYARD" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentText3Header" relativePoint="BOTTOMLEFT" x="0" y="-3"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentStuck" inherits="UIPanelButtonTemplate" text="STUCK_BUTTON_TEXT">
						<Anchors>
							<Anchor point="TOP" relativeTo="$parentText3" relativePoint="BOTTOM">
								<Offset>
									<AbsDimension x="0" y="-30"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								self:SetWidth(self:GetTextWidth()+40);
							</OnLoad>
							<OnClick>
								Stuck();
								HideUIPanel(HelpFrame);
							</OnClick>
						</Scripts>
					</Button>
					<Button name="$parentHearthstone">
						<Size x="64" y="64"/>
						<Anchors>
							<Anchor point="TOP" relativeTo="$parentText2" relativePoint="BOTTOM" x="0" y="-34"/>
						</Anchors>
						<Layers>
							<Layer level="ARTWORK">
								<Texture name="$parentIconTexture" setAllPoints="true" parentKey="IconTexture"/>
							</Layer>
						</Layers>
						<Frames>
							<Cooldown name="$parentCooldown" inherits="CooldownFrameTemplate" parentKey="Cooldown"/>
						</Frames>
						<Scripts>
							<OnLoad>
								self.UpdateTooltip = HelpFrameStuckHearthstone_UpdateTooltip;
								self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
							</OnLoad>
							<OnEvent>
								if ( self:IsVisible() ) then
									HelpFrameStuckHearthstone_Update(self);
								end
							</OnEvent>
							<OnClick>
								if (UseHearthstone()) then
									HideUIPanel(HelpFrame);
								end
							</OnClick>
							<OnShow>
								HelpFrameStuckHearthstone_Update(self);
							</OnShow>
							<OnEnter>
								local hearthstoneID = PlayerHasHearthstone();
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								if (hearthstoneID) then
									GameTooltip:SetInventoryItemByID(hearthstoneID);
									GameTooltip:Show();
								else
									GameTooltip:SetText(HELPFRAME_STUCK_HEARTHSTONE_MISSING);
									GameTooltip:Show();
								end
							</OnEnter>
							<OnLeave>
								GameTooltip:Hide();
							</OnLeave>
						</Scripts>
						<PushedTexture file="Interface\Buttons\UI-Quickslot-Depress"/>
						<HighlightTexture name="$parentHighlight" file="Interface\Buttons\ButtonHilight-Square" alphaMode="ADD"/>
					</Button>
				</Frames>
				<Scripts>
					<OnLoad>
						tinsert(HelpFrameWindows, self);
					</OnLoad>
				</Scripts>
			</Frame>
			<Frame name="$parentReportBug" hidden="true" parentKey="bug">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentMainInset" x="5" y="-5"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="$parentMainInset" x="-5" y="5"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentTitle" parentKey="title" inherits="GameFontNormalLarge" text="HELPFRAME_REPORT_BUG_TITLE" justifyH="LEFT">
							<Size x="0" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="15" y="-15"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText1" inherits="GameFontHighlight" text="HELPFRAME_REPORT_BUG_TEXT1" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTitle" relativePoint="BOTTOMLEFT" x="0" y="-20"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentSubmit" inherits="GameMenuButtonTemplate" text="SUBMIT" parentKey="submitButton">
						<Size x="160" y="30"/>
						<Anchors>
							<Anchor point="BOTTOM" x="0" y="15"/>
						</Anchors>
						<Scripts>
							<OnClick function="HelpFrameReportBugSubmit_OnClick"/>
						</Scripts>
					</Button>
					<ScrollFrame name="$parentScrollFrame" inherits="UIPanelScrollFrameCodeTemplate">
						<Size x="435" y="150"/>
						<Anchors>
							<Anchor point="BOTTOM" x="0" y="70"/>
						</Anchors>
						<Layers>
							<Layer level="OVERLAY">
								<FontString name="$parentCharacterCount" inherits="GameFontDisableLarge" parentKey="charCount">
									<Anchors>
										<Anchor point="BOTTOMRIGHT" x="-15" y="0"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Slider name="$parentScrollBar" inherits="UIPanelScrollBarTemplate" parentKey="ScrollBar">
								<Anchors>
									<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="-13" y="-16"/>
									<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="-13" y="16"/>
								</Anchors>
							</Slider>
						</Frames>
						<ScrollChild>
							<EditBox name="HelpFrameReportBugEditBox" multiLine="true" letters="500" autoFocus="false">
								<Size x="420" y="220"/>
								<Scripts>
									<OnTextChanged>
										ScrollingEdit_OnTextChanged(self, self:GetParent());
										self:GetParent().charCount:SetText(self:GetMaxLetters() - self:GetNumLetters());
										if self:GetNumLetters() > 0 then
											self.submitButton:Enable();
										end
									</OnTextChanged>
									<OnCursorChanged function="ScrollingEdit_OnCursorChanged"/>
									<OnUpdate>
										ScrollingEdit_OnUpdate(self, elapsed, self:GetParent());
									</OnUpdate>
									<OnEscapePressed function="EditBox_ClearFocus"/>
									<OnShow function="EditBox_SetFocus"/>
								</Scripts>
								<FontString inherits="ChatFontNormal"/>
							</EditBox>
						</ScrollChild>
					</ScrollFrame>
					<Frame>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentScrollFrame" x="-7" y="7"/>
							<Anchor point="BOTTOMRIGHT" relativeTo="$parentScrollFrame" x="7" y="-7"/>
						</Anchors>
						<Backdrop bgFile="Interface\Tooltips\UI-Tooltip-Background" edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
							<EdgeSize>
								<AbsValue val="16"/>
							</EdgeSize>
							<TileSize>
								<AbsValue val="16"/>
							</TileSize>
							<BackgroundInsets>
								<AbsInset left="5" right="5" top="5" bottom="5"/>
							</BackgroundInsets>
						</Backdrop>
						<Scripts>
							<OnLoad>
								self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b);
								self:SetBackdropColor(0, 0, 0);
							</OnLoad>
						</Scripts>
					</Frame>
				</Frames>
				<Scripts>
					<OnLoad>
						tinsert(HelpFrameWindows, self);
						self.editbox = HelpFrameReportBugEditBox;
						self.editbox.submitButton = self.submitButton;
					</OnLoad>
					<OnShow>
						if self.editbox:GetNumLetters() > 0 then
							self.submitButton:Enable();
						else
							self.submitButton:Disable();
						end
					</OnShow>
				</Scripts>
			</Frame>
			<Frame name="$parentSubmitSuggestion" hidden="true" parentKey="suggestion">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentMainInset" x="5" y="-5"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="$parentMainInset" x="-5" y="5"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentTitle" parentKey="title" inherits="GameFontNormalLarge" text="HELPFRAME_SUBMIT_SUGGESTION_TITLE" justifyH="LEFT">
							<Size x="0" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="15" y="-15"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText1" inherits="GameFontHighlight" text="HELPFRAME_SUBMIT_SUGGESTION_TEXT1" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTitle" relativePoint="BOTTOMLEFT" x="0" y="-20"/>
							</Anchors>
						</FontString>
						<FontString name="$parentLegalDisclaimer" inherits="GameFontHighlight" text="HELPFRAME_SUBMIT_SUGGESTION_LEGAL" justifyH="LEFT" justifyV="BOTTOM">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="BOTTOM" x="0" y="60"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentSubmit" inherits="GameMenuButtonTemplate" text="SUBMIT" parentKey="submitButton">
						<Size x="160" y="30"/>
						<Anchors>
							<Anchor point="BOTTOM" x="0" y="15"/>
						</Anchors>
						<Scripts>
							<OnClick function="HelpFrameSubmitSuggestionSubmit_OnClick"/>
						</Scripts>
					</Button>
					<ScrollFrame name="$parentScrollFrame" inherits="UIPanelScrollFrameCodeTemplate">
						<Size x="435" y="120"/>
						<Anchors>
							<Anchor point="BOTTOM" x="0" y="130"/>
						</Anchors>
						<Layers>
							<Layer level="OVERLAY">
								<FontString name="$parentCharacterCount" inherits="GameFontDisableLarge" parentKey="charCount">
									<Anchors>
										<Anchor point="BOTTOMRIGHT" x="-15" y="0"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Slider name="$parentScrollBar" inherits="UIPanelScrollBarTemplate" parentKey="ScrollBar">
								<Anchors>
									<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="-13" y="-16"/>
									<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="-13" y="16"/>
								</Anchors>
							</Slider>
						</Frames>
						<ScrollChild>
							<EditBox name="HelpFrameSubmitSuggestionEditBox" multiLine="true" letters="500" autoFocus="false">
								<Size x="420" y="220"/>
								<Scripts>
									<OnTextChanged>
										ScrollingEdit_OnTextChanged(self, self:GetParent());
										self:GetParent().charCount:SetText(self:GetMaxLetters() - self:GetNumLetters());
										if self:GetNumLetters() > 0 then
											self.submitButton:Enable();
										end
									</OnTextChanged>
									<OnCursorChanged function="ScrollingEdit_OnCursorChanged"/>
									<OnUpdate>
										ScrollingEdit_OnUpdate(self, elapsed, self:GetParent());
									</OnUpdate>
									<OnEscapePressed function="EditBox_ClearFocus"/>
									<OnShow function="EditBox_SetFocus"/>
								</Scripts>
								<FontString inherits="ChatFontNormal"/>
							</EditBox>
						</ScrollChild>
					</ScrollFrame>
					<Frame>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentScrollFrame" x="-7" y="7"/>
							<Anchor point="BOTTOMRIGHT" relativeTo="$parentScrollFrame" x="7" y="-7"/>
						</Anchors>
						<Backdrop bgFile="Interface\Tooltips\UI-Tooltip-Background" edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
							<EdgeSize>
								<AbsValue val="16"/>
							</EdgeSize>
							<TileSize>
								<AbsValue val="16"/>
							</TileSize>
							<BackgroundInsets>
								<AbsInset left="5" right="5" top="5" bottom="5"/>
							</BackgroundInsets>
						</Backdrop>
						<Scripts>
							<OnLoad>
								self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b);
								self:SetBackdropColor(0, 0, 0);
							</OnLoad>
						</Scripts>
					</Frame>
				</Frames>
				<Scripts>
					<OnLoad>
						tinsert(HelpFrameWindows, self);
						self.editbox = HelpFrameSubmitSuggestionEditBox;
						self.editbox.submitButton = self.submitButton;
					</OnLoad>
					<OnShow>
						if self.editbox:GetNumLetters() > 0 then
							self.submitButton:Enable();
						else
							self.submitButton:Disable();
						end
					</OnShow>
				</Scripts>
			</Frame>
			<Frame name="$parentOpenTicketHelp" hidden="true" parentKey="ticketHelp">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentMainInset" x="5" y="-5"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="$parentMainInset" x="-5" y="5"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentTitle" inherits="GameFontNormalLarge" text="HELPFRAME_OPENTICKETINSTRUCTIONS_TITLE" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="15" y="-15"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText1" inherits="GameFontHighlight" text="HELPFRAME_OPENTICKETINSTRUCTIONS_TEXT1" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTitle" relativePoint="BOTTOMLEFT" x="0" y="-25"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText2" inherits="GameFontHighlight" text="HELPFRAME_OPENTICKETINSTRUCTIONS_TEXT2" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentText1" relativePoint="BOTTOMLEFT" x="0" y="-25"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentOpenTicket" parentKey="ticketButton" inherits="HelpFrameButtonTemplate" id="14">
						<Anchors>
							<Anchor point="BOTTOM" relativeTo="$parent" relativePoint="BOTTOM" x="0" y="10"/>
						</Anchors>
						<Scripts>
							<OnClick>
								StaticPopup_Show("CONFIRM_LAUNCH_URL", nil, nil, {index = 6});
							</OnClick>
						</Scripts>
					</Button>
					<Button name="GMChatOpenLog" inherits="GameMenuButtonTemplate" text="GM_CHAT_OPEN">
						<Size x="250" y="22"/>
						<Anchors>
							<Anchor point="TOPRIGHT" x="-12" y="-12"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								self:SetWidth(self:GetTextWidth()+40);
								self:Disable()
							</OnLoad>
							<OnClick>
								GMChatFrame_Show();
								HideUIPanel(HelpFrame);
							</OnClick>
						</Scripts>
					</Button>
				</Frames>
				<Scripts>
					<OnLoad>
						tinsert(HelpFrameWindows, self);
					</OnLoad>
					<OnShow>
						if (HelpBrowser:HasConnection()) then
							HelpBrowser:Show();
							HelpBrowser:NavigateHome("GMTicket");
							HelpBrowser.homepage = "GMTicket";
							self:Hide();
						end
					</OnShow>
				</Scripts>
			</Frame>

			<Browser name="HelpBrowser" inherits="BrowserTemplate" >
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentMainInset" x="4" y="-2"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="$parentMainInset" x="-3" y="1"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						self.homepage = "KnowledgeBase";
					</OnLoad>
					<OnHide>
						GetWebTicket();
						if (TicketStatusFrame.haveWebSurveyClicked) then
							AcknowledgeSurvey(TicketStatusFrame.caseIndex);
							TicketStatusFrame.haveWebSurvey = nil;
							TicketStatusFrame.haveWebSurveyClicked = nil;
						end
					</OnHide>
				</Scripts>
			</Browser>
			<Frame name="$parentReportPlayer" hidden="true" parentKey="report">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentMainInset" x="5" y="-5"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="$parentMainInset" x="-5" y="5"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentTitle" inherits="GameFontNormalLarge" text="HELPFRAME_REPORT_PLAYER_TITLE" justifyH="LEFT">
							<Size x="0" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="15" y="-15"/>
							</Anchors>
						</FontString>
						<FontString name="$parentText1" inherits="GameFontHighlight" text="HELPFRAME_REPORT_PLAYER_TEXT1" justifyH="LEFT">
							<Size x="710" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTitle" relativePoint="BOTTOMLEFT" x="0" y="-30"/>
							</Anchors>
						</FontString>
						<Texture name="$parentExampleTexture" file="Interface\HelpFrame\ReportHarrasment-HelpImage">
							<Size x="512" y="256"/>
							<Anchors>
								<Anchor point="TOP" relativeTo="$parentText1" relativePoint="BOTTOM" x="10" y="-20"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BORDER">
						<FontString name="$parentExampleChat" inherits="ChatFontNormal" text="HELPFRAME_REPORT_PLAYER_EXAMPLE_CHAT">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentExampleTexture" relativePoint="TOPLEFT" x="52" y="-91"/>
							</Anchors>
							<Color r="1" g="0.50196" b="1"/>
						</FontString>
					</Layer>
					<Layer level="OVERLAY">
						<FontString name="$parentExampleTargetName" inherits="GameFontNormalSmall" text="HELPFRAME_REPORT_PLAYER_EXAMPLE_TARGET_NAME">
							<Anchors>
								<Anchor point="TOP" relativeTo="$parentExampleTexture" relativePoint="TOPLEFT" x="336" y="-76"/>
							</Anchors>
						</FontString>
						<FontString name="$parentExampleChatRightClick" inherits="GameFontNormalLarge" text="HELPFRAME_REPORT_PLAYER_RIGHT_CLICK">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentExampleTexture" relativePoint="TOPLEFT" x="69" y="-17"/>
							</Anchors>
						</FontString>
						<FontString name="$parentExampleTargetRightClick" inherits="GameFontNormalLarge" text="HELPFRAME_REPORT_PLAYER_RIGHT_CLICK">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentExampleTexture" relativePoint="TOPLEFT" x="305" y="-17"/>
							</Anchors>
						</FontString>
						<FontString name="$parentExampleChatClickLocation" inherits="GameFontHighlight" text="HELPFRAME_REPORT_PLAYER_EXAMPLE_CHAT_CLICK_LOCATION">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentExampleTexture" relativePoint="TOPLEFT" x="69" y="-36"/>
							</Anchors>
						</FontString>
						<FontString name="$parentExampleTargetClickLocation" inherits="GameFontHighlight" text="HELPFRAME_REPORT_PLAYER_EXAMPLE_TARGET_CLICK_LOCATION">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentExampleTexture" relativePoint="TOPLEFT" x="305" y="-36"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						tinsert(HelpFrameWindows, self);
					</OnLoad>
				</Scripts>
			</Frame>
			<Frame name="$parentKnowledgebase" hidden="true" parentKey="kbase" useParentLevel="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentMainInset" x="0" y="0"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="$parentMainInset" x="0" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentStoneTex" file="Interface\HelpFrame\DarkSandstone-Tile" vertTile="true" horizTile="true" hidden="true">
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
								<Anchor point="BOTTOMRIGHT" relativePoint="TOPRIGHT" x="0" y="-40"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BACKGROUND" textureSubLevel="1">
						<Texture name="$parentTopTileStreaks" inherits="_UI-Frame-TopTileStreaks" parentKey="TopTileStreaks" hidden="true">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentStoneTex" x="0" y="0"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentStoneTex" x="0" y="-35"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<EditBox name="$parentSearchBox" parentKey="searchBox" autoFocus="false" hidden="true">
						<Size x="320" y="20"/>
						<Anchors>
							<Anchor point="TOPLEFT" x="15" y="-12"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border">
									<Size>
										<AbsDimension x="8" y="20"/>
									</Size>
									<Anchors>
										<Anchor point="TOPLEFT">
											<Offset x="-5" y="0"/>
										</Anchor>
									</Anchors>
									<TexCoords left="0" right="0.0625" top="0" bottom="0.625"/>
								</Texture>
								<Texture name="$parentRight" file="Interface\Common\Common-Input-Border">
									<Size>
										<AbsDimension x="8" y="20"/>
									</Size>
									<Anchors>
										<Anchor point="RIGHT">
											<Offset x="0" y="0"/>
										</Anchor>
									</Anchors>
									<TexCoords left="0.9375" right="1.0" top="0" bottom="0.625"/>
								</Texture>
								<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border">
									<Size>
										<AbsDimension x="0" y="20"/>
									</Size>
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
										<Anchor point="RIGHT" relativeTo="$parentRight" relativePoint="LEFT"/>
									</Anchors>
									<TexCoords left="0.0625" right="0.9375" top="0" bottom="0.625"/>
								</Texture>
							</Layer>
							<Layer level="OVERLAY">
								<Texture name="$parentSearchIcon" file="Interface\Common\UI-Searchbox-Icon" parentKey="icon">
									<Size>
										<AbsDimension x="14" y="14"/>
									</Size>
									<Anchors>
										<Anchor point="LEFT" y="-2"/>
									</Anchors>
									<Color r="0.7" g="0.7" b="0.7" />
								</Texture>
							</Layer>
						</Layers>
						<Scripts>
							<OnLoad>
								self:SetMaxBytes(128);
							</OnLoad>
							<OnShow>
								self:SetTextInsets(16, 0, 0, 0);
								KnowledgeBase_ClearSearch(self)
							</OnShow>
							<OnEnterPressed>
								PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
								KnowledgeBase_Search();
								EditBox_ClearFocus(self);
							</OnEnterPressed>
							<OnEscapePressed function="EditBox_ClearFocus"/>
							<OnEditFocusLost>
								self:HighlightText(0, 0);
								if ( self:GetText() == "" ) then
									KnowledgeBase_ClearSearch(self)
								end
							</OnEditFocusLost>
							<OnEditFocusGained>
								self:HighlightText();
								if ( self:GetText() == SEARCH ) then
									self:SetText("")
									self:SetFontObject("ChatFontSmall");
									self.icon:SetVertexColor(1.0, 1.0, 1.0);
									self.inactive = false;
								end

								self.clearButton:Show();
								self:GetParent().searchButton:Enable();
							</OnEditFocusGained>
						</Scripts>
						<Frames>
							<Button name="$parentExitButton" parentKey="clearButton">
								<Size x="17" y="17"/>
								<Anchors>
									<Anchor point="RIGHT" x="-3" y="0"/>
								</Anchors>
								<Layers>
									<Layer level="ARTWORK">
										<Texture file="Interface\FriendsFrame\ClearBroadcastIcon" alpha="0.5" parentKey="texture">
											<Size>
												<AbsDimension x="17" y="17"/>
											</Size>
											<Anchors>
												<Anchor point="TOPLEFT" x="0" y="0"/>
											</Anchors>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnEnter>
										self.texture:SetAlpha(1.0);
									</OnEnter>
									<OnLeave>
										self.texture:SetAlpha(0.5);
									</OnLeave>
									<OnMouseDown>
										if self:IsEnabled() then
											self.texture:SetPoint("TOPLEFT", 1, -1);
										end
									</OnMouseDown>
									<OnMouseUp>
										self.texture:SetPoint("TOPLEFT", 0, 0);
									</OnMouseUp>
									<OnClick>
										PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
										local editBox = self:GetParent();
										if HelpFrame.kbase.hasSearch then
											NavBar_Reset(HelpFrame.kbase.navBar);
											KnowledgeBase_DisplayCategories();
										end
										KnowledgeBase_ClearSearch(editBox)
									</OnClick>
								</Scripts>
							</Button>
						</Frames>
						<FontString inherits="ChatFontSmall"/>
					</EditBox>
					<Button name="$parentSearchButton" parentKey="searchButton" inherits="GameMenuButtonTemplate" text="SEARCH" hidden="true">
						<Size x="85" y="21"/>
						<Anchors>
							<Anchor point="LEFT" relativeTo="$parentSearchBox" relativePoint="RIGHT" x="30" y="0"/>
						</Anchors>
						<Scripts>
							<OnClick>
								PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
								KnowledgeBase_Search();
							</OnClick>
						</Scripts>
					</Button>
					<Frame name="$parentNavBar" inherits="NavBarTemplate" parentKey="navBar" hidden="true">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentStoneTex" relativePoint="BOTTOMLEFT" x="3" y="0"/>
							<Anchor point="TOPRIGHT" relativeTo="$parentStoneTex" relativePoint="TOPRIGHT" x="-4" y="0"/>
						</Anchors>
					</Frame>
					<ScrollFrame name="$parentScrollFrame" inherits="HybridScrollFrameTemplate" parentKey="scrollFrame" hidden="true">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentNavBar" relativePoint="BOTTOMLEFT" x="0" y="0"/>
							<Anchor point="BOTTOMRIGHT" x="-22" y="3"/>
						</Anchors>
						<Frames>
							<Slider name="$parentScrollBar" parentKey="ScrollBar" inherits="HybridScrollBarTrimTemplate">
								<Anchors>
									<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="0" y="-16"/>
									<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="0" y="14"/>
								</Anchors>
							</Slider>
						</Frames>
					</ScrollFrame>
					<ScrollFrame name="$parentScrollFrame2" parentKey="scrollFrame2" inherits="UIPanelScrollFrameCodeTemplate" hidden="true">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentScrollFrame" x="0" y="0"/>
							<Anchor point="BOTTOMRIGHT" relativeTo="$parentScrollFrame" x="0" y="0"/>
						</Anchors>
						<Frames>
							<Slider name="$parentScrollBar" inherits="UIPanelScrollBarTrimTemplate" parentKey="ScrollBar">
								<Anchors>
									<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="0" y="-16"/>
									<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="0" y="14"/>
								</Anchors>
							</Slider>
						</Frames>
						<ScrollChild>
							<Frame name="$parentScrollChild" parentKey="child">
								<Size x="30" y="30"/>
								<Anchors>
									<Anchor point="TOPLEFT" x="0" y="0"/>
								</Anchors>
								<Layers>
									<Layer level="ARTWORK">
										<FontString name="$parentTitle" inherits="GameFontNormalLarge" justifyH="LEFT" parentKey="articleTitle">
											<Anchors>
												<Anchor point="TOPLEFT" x="15" y="-15"/>
											</Anchors>
										</FontString>
										<FontString name="$parentText" inherits="GameFontWhite" justifyH="LEFT" parentKey="articleText">
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="$parentTitle" relativePoint="BOTTOMLEFT" x="0" y="-12"/>
											</Anchors>
										</FontString>
										<FontString name="$parentArticleId" inherits="GameFontWhite" justifyH="LEFT" parentKey="articleId">
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="$parentText" relativePoint="BOTTOMLEFT" x="0" y="-8"/>
											</Anchors>
										</FontString>
										<FontString text="\n\n">
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="$parentArticleId" relativePoint="BOTTOMLEFT" x="0" y="-8"/>
											</Anchors>
										</FontString>
									</Layer>
								</Layers>
							</Frame>
						</ScrollChild>
					</ScrollFrame>
					<Frame>
						<Size x="300" y="300"/>
						<Anchors>
							<Anchor point="CENTER" relativeTo="$parentScrollFrame" x="0" y="20"/>
						</Anchors>
						<Frames>
							<Frame name="$parentErrorFrame" frameStrata="DIALOG" inherits="TranslucentFrameTemplate" enableMouse="true" hidden="true">
								<Size x="350" y="230"/>
								<Anchors>
									<Anchor point="CENTER" relativeTo="$parentScrollFrame" x="0" y="20"/>
								</Anchors>
								<Layers>
									<Layer level="BACKGROUND">
										<Texture>
											<Anchors>
												<Anchor point="TOPLEFT" x="7" y="-10"/>
												<Anchor point="BOTTOMRIGHT" x="-7" y="10"/>
											</Anchors>
											<Color r="0" g="0" b="0" a="1"/>
										</Texture>
									</Layer>
									<Layer level="OVERLAY">
										<FontString name="$parentText" inherits="ErrorFont" text="ERROR" justifyH="CENTER" parentKey="text">
											<Size x="300" y="0"/>
										</FontString>
									</Layer>
								</Layers>
								<Frames>
									<Button name="$parentCloseButton" inherits="UIPanelCloseButton">
										<Anchors>
											<Anchor point="TOPRIGHT" x="-6" y="-6"/>
										</Anchors>
										<Scripts>
											<OnClick>
												PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
												self:GetParent():Hide();
											</OnClick>
										</Scripts>
									</Button>
								</Frames>
								<Scripts>
									<OnLoad>
										self:GetParent():GetParent().errorFrame = self;
									</OnLoad>
								</Scripts>
							</Frame>
						</Frames>
					</Frame>
				</Frames>
				<Scripts>
					<OnLoad>
						tinsert(HelpFrameWindows, self);
						self.articleTitle = self.scrollFrame2.child.articleTitle;
						self.articleText = self.scrollFrame2.child.articleText;
						self.articleId = self.scrollFrame2.child.articleId;
						KnowledgeBase_OnLoad(self);
					</OnLoad>
					<OnShow function="KnowledgeBase_OnShow"/>
					<OnEvent function="KnowledgeBase_OnEvent"/>
				</Scripts>
			</Frame>
			<Frame name="$parentGM_Response" hidden="true" parentKey="GM_response">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentMainInset" x="5" y="-5"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="$parentMainInset" x="-5" y="5"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentTitle1" parentKey="title1" inherits="GameFontNormalLarge" text="HELPFRAME_OPENTICKET_EDITTEXT" justifyH="LEFT">
							<Size x="0" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="15" y="-15"/>
							</Anchors>
						</FontString>
						<FontString name="$parentTitle2" parentKey="title2" inherits="GameFontNormalLarge" text="GM_RESPONSE_FRAME_HEADER" justifyH="LEFT">
							<Size x="0" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="15" y="-214"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentNeedMoreHelp" inherits="GameMenuButtonTemplate" text="GM_RESPONSE_MORE_HELP">
						<Size x="160" y="30"/>
						<Anchors>
							<Anchor point="BOTTOM" x="-100" y="15"/>
						</Anchors>
						<Scripts>
							<OnClick>
								StaticPopup_Show("GM_RESPONSE_NEED_MORE_HELP");
							</OnClick>
						</Scripts>
					</Button>
					<Button name="$parentCancel" inherits="GameMenuButtonTemplate" text="GM_RESPONSE_RESOLVE" parentKey="cancelButton">
						<Size x="160" y="30"/>
						<Anchors>
							<Anchor point="BOTTOM" x="100" y="15"/>
						</Anchors>
						<Scripts>
							<OnClick>
								StaticPopup_Show("GM_RESPONSE_RESOLVE_CONFIRM");
							</OnClick>
						</Scripts>
					</Button>
					<ScrollFrame name="$parentScrollFrame1" inherits="UIPanelScrollFrameCodeTemplate">
						<Size x="435" y="140"/>
						<Anchors>
							<Anchor point="TOP" x="0" y="-45"/>
						</Anchors>
						<Layers>
							<Layer level="OVERLAY">
								<FontString name="$parentCharacterCount" inherits="GameFontDisableLarge" parentKey="charCount">
									<Anchors>
										<Anchor point="BOTTOMRIGHT" x="-15" y="0"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Slider name="$parentScrollBar" inherits="UIPanelScrollBarTemplate" parentKey="ScrollBar">
								<Anchors>
									<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="-13" y="-16"/>
									<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="-13" y="16"/>
								</Anchors>
							</Slider>
						</Frames>
						<ScrollChild>
							<EditBox name="HelpFrameGMResponse_IssueText" multiLine="true" letters="500" autoFocus="false">
								<Size x="420" y="220"/>
								<Scripts>
									<OnTextChanged>
										self:GetParent():SetVerticalScroll(0);
									</OnTextChanged>
									<OnUpdate>
										ScrollingEdit_OnUpdate(self, elapsed, self:GetParent());
									</OnUpdate>
								</Scripts>
								<FontString inherits="ChatFontNormal"/>
							</EditBox>
						</ScrollChild>
					</ScrollFrame>
					<ScrollFrame name="$parentScrollFrame2" inherits="UIPanelScrollFrameCodeTemplate">
						<Size x="435" y="140"/>
						<Anchors>
							<Anchor point="BOTTOM" x="0" y="70"/>
						</Anchors>
						<Layers>
							<Layer level="OVERLAY">
								<FontString name="$parentCharacterCount" inherits="GameFontDisableLarge" parentKey="charCount">
									<Anchors>
										<Anchor point="BOTTOMRIGHT" x="-15" y="0"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Slider name="$parentScrollBar" inherits="UIPanelScrollBarTemplate" parentKey="ScrollBar">
								<Anchors>
									<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="-13" y="-16"/>
									<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="-13" y="16"/>
								</Anchors>
							</Slider>
						</Frames>
						<ScrollChild>
							<EditBox name="HelpFrameGMResponse_GMText" multiLine="true" autoFocus="false">
								<Size x="420" y="220"/>
								<Scripts>
									<OnTextChanged>
										self:GetParent():SetVerticalScroll(0);
									</OnTextChanged>
									<OnUpdate>
										ScrollingEdit_OnUpdate(self, elapsed, self:GetParent());
									</OnUpdate>
								</Scripts>
								<FontString inherits="ChatFontNormal"/>
							</EditBox>
						</ScrollChild>
					</ScrollFrame>
					<Frame>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentScrollFrame1" x="-7" y="7"/>
							<Anchor point="BOTTOMRIGHT" relativeTo="$parentScrollFrame1" x="7" y="-7"/>
						</Anchors>
						<Backdrop bgFile="Interface\Tooltips\UI-Tooltip-Background" edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
							<EdgeSize>
								<AbsValue val="16"/>
							</EdgeSize>
							<TileSize>
								<AbsValue val="16"/>
							</TileSize>
							<BackgroundInsets>
								<AbsInset left="5" right="5" top="5" bottom="5"/>
							</BackgroundInsets>
						</Backdrop>
						<Scripts>
							<OnLoad>
								self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b);
								self:SetBackdropColor(0, 0, 0);
							</OnLoad>
						</Scripts>
					</Frame>
					<Frame>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentScrollFrame2" x="-7" y="7"/>
							<Anchor point="BOTTOMRIGHT" relativeTo="$parentScrollFrame2" x="7" y="-7"/>
						</Anchors>
						<Backdrop bgFile="Interface\Tooltips\UI-Tooltip-Background" edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
							<EdgeSize>
								<AbsValue val="16"/>
							</EdgeSize>
							<TileSize>
								<AbsValue val="16"/>
							</TileSize>
							<BackgroundInsets>
								<AbsInset left="5" right="5" top="5" bottom="5"/>
							</BackgroundInsets>
						</Backdrop>
						<Scripts>
							<OnLoad>
								self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b);
								self:SetBackdropColor(0, 0, 0);
							</OnLoad>
						</Scripts>
					</Frame>
				</Frames>
				<Scripts>
					<OnLoad>
						tinsert(HelpFrameWindows, self);
						HelpFrameGMResponse_IssueText:Disable();
						HelpFrameGMResponse_GMText:Disable();

					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
		<Layers>
			<Layer level="BORDER">
				<Texture file="Interface\HelpFrame\CS_HelpTextures">
					<Size x="229" y="45"/>
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentButton5" relativePoint="BOTTOM" x="0" y="8"/>
					</Anchors>
					<TexCoords left="0.00195313" right="0.44921875" top="0.00781250" bottom="0.35937500"/>
				</Texture>
				<Texture name="$parentVertDivTop" inherits="Dialog-BorderTopLeft">
					<Anchors>
						<Anchor point="TOP" relativePoint="TOPLEFT" x="216"/>
					</Anchors>
				</Texture>
				<Texture name="$parentVertDivBottom" inherits="Dialog-BorderBottomLeft">
					<Anchors>
						<Anchor point="BOTTOM" relativePoint="BOTTOMLEFT" x="216"/>
					</Anchors>
				</Texture>
				<Texture name="$parentVertDivMiddle" inherits="Dialog-BorderLeft">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentVertDivTop" relativePoint="BOTTOMLEFT" x="1" y="0"/>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentVertDivBottom" relativePoint="TOPLEFT" x="1" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad function="HelpFrame_OnLoad"/>
			<OnShow function="HelpFrame_OnShow"/>
			<OnEvent function="HelpFrame_OnEvent"/>
			<OnHide function="HelpFrame_OnHide"/>
		</Scripts>
	</Frame>

	<Frame name="BrowserSettingsTooltip" inherits="TooltipBorderedFrameTemplate" parent="UIParent" frameStrata="FULLSCREEN_DIALOG" hidden="true" >
		<Size x="250" y="100"/>
		<Layers>
			<Layer level="ARTWORK">
				<FontString parentKey="Title" inherits="GameFontHighlight" text="BROWSER_SETTINGS_TOOLTIP">
					<Anchors>
						<Anchor point="TOPLEFT" x="10" y="-12" />
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="CacheButton" inherits="UIPanelButtonTemplate" text="BROWSER_CLEAR_CACHE">
				<Size x="150" y="22"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.Title" relativePoint="BOTTOMLEFT" y="-10"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						local browser = BrowserSettingsTooltip.browser
						if (browser) then
							browser:ClearCache();
						end
					</OnClick>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_CURSOR_RIGHT");
						GameTooltip:SetText(BROWSER_CLEAR_CACHE_TOOLTIP, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b);
						self:GetParent().isCounting = nil;
					</OnEnter>
					<OnLeave>
						self:GetParent().isCounting = true;
						GameTooltip_Hide()
					</OnLeave>
				</Scripts>
			</Button>
			<Button parentKey="CookiesButton" inherits="UIPanelButtonTemplate" text="BROWSER_DELETE_COOKIES">
				<Size x="150" y="22"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.CacheButton" relativePoint="BOTTOMLEFT" y="-10"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						local browser = BrowserSettingsTooltip.browser
						if (browser) then
							browser:DeleteCookies();
						end
					</OnClick>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_CURSOR_RIGHT");
						GameTooltip:SetText(BROWSER_DELETE_COOKIES_TOOLTIP, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b);
						self:GetParent().isCounting = nil;
					</OnEnter>
					<OnLeave>
						self:GetParent().isCounting = true;
						GameTooltip_Hide()
					</OnLeave>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnShow>
				self.showTimer = 2;
				self.isCounting = true;
			</OnShow>
			<OnHide>
				self.showTimer = nil;
				self.isCounting = nil;
			</OnHide>
			<OnUpdate>
				if (self.showTimer and self.isCounting) then
					if (not (self.showTimer >= 0)) then --no less thans!
						self:Hide();
					else
						self.showTimer = self.showTimer - elapsed;
					end
				end
			</OnUpdate>
			<OnEnter>
				self.isCounting = nil;
			</OnEnter>
			<OnLeave>
				self.isCounting = true;
				self.showTimer = 2;
			</OnLeave>
		</Scripts>
	</Frame>

	<Button name="HelpOpenTicketButton" parent="MainMenuMicroButton" hidden="true">
		<Size x="34" y="35"/>
		<Anchors>
			<Anchor point="CENTER" relativeTo="$parent" relativePoint="TOPRIGHT" x="-3" y="-26"/>
		</Anchors>
		<Frames>
			<Frame name="$parentTutorial" inherits="GlowBoxTemplate" parentKey="tutorial" enableMouse="true" hidden="true" frameStrata="DIALOG">
				<Size x="220" y="100"/>
				<Anchors>
					<Anchor point="BOTTOM" relativeTo="$parent" relativePoint="TOP" x="0" y="14"/>
				</Anchors>
				<Layers>
					<Layer level="OVERLAY">
						<FontString name="$parentText" inherits="GameFontHighlightLeft" justifyV="TOP" text="HELPFRAME_TICKET_TUTORIAL_HELP" parentKey="text">
							<Size x="188" y="80"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="16" y="-24"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentCloseButton" inherits="UIPanelCloseButton">
						<Anchors>
							<Anchor point="TOPRIGHT" x="6" y="6"/>
						</Anchors>
						<Scripts>
							<OnClick>
								self:GetParent():Hide();
							</OnClick>
						</Scripts>
					</Button>
					<Frame inherits="GlowBoxArrowTemplate">
						<Anchors>
							<Anchor point="TOP" relativePoint="BOTTOM" y="4"/>
						</Anchors>
					</Frame>
				</Frames>
				<Scripts>
					<OnLoad>
						self.text:SetText(HELPFRAME_TICKET_TUTORIAL_HELP);
						self.text:SetSpacing(4);
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self.refreshTime = 0;
				self:RegisterEvent("UPDATE_TICKET");
			</OnLoad>
			<OnClick>
				HelpOpenTicketButtonTutorial:Hide();
				if ( TicketStatusFrame.hasGMSurvey ) then
					GMSurveyFrame_LoadUI();
					ShowUIPanel(GMSurveyFrame);
					TicketStatusFrame:Hide();
				else
					HelpFrame_ShowFrame(HELPFRAME_SUBMIT_TICKET);
				end
			</OnClick>
			<OnEvent function="HelpOpenTicketButton_OnEvent"/>
			<OnEnter>
				self:SetScript("OnUpdate", HelpOpenTicketButton_OnUpdate);
			</OnEnter>
			<OnLeave>
				self:SetScript("OnUpdate", nil);
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
		<HitRectInsets>
			<AbsInset left="4" right="4" top="4" bottom="4"/>
		</HitRectInsets>
		<NormalTexture file="Interface\HelpFrame\CS_HelpTextures">
			<TexCoords left="0.70703125" right="0.77343750" top="0.58593750" bottom="0.85937500"/>
		</NormalTexture>
		<PushedTexture file="Interface\HelpFrame\CS_HelpTextures">
			<TexCoords left="0.88867188" right="0.95507813" top="0.00781250" bottom="0.28125000"/>
		</PushedTexture>
		<HighlightTexture file="Interface\HelpFrame\CS_HelpTextures">
			<TexCoords left="0.80664063" right="0.87304688" top="0.58593750" bottom="0.85937500"/>
		</HighlightTexture>
	</Button>

	<Button name="HelpOpenWebTicketButton" parent="MainMenuMicroButton" hidden="true">
		<Size x="34" y="35"/>
		<Anchors>
			<Anchor point="CENTER" relativeTo="$parent" relativePoint="TOPRIGHT" x="-3" y="-26"/>
		</Anchors>
		<Scripts>
			<OnLoad>
				self.refreshTime = 0;
				self:RegisterEvent("UPDATE_WEB_TICKET")
			</OnLoad>
			<OnShow>
				HelpOpenTicketButton:Hide();
			</OnShow>
			<OnClick>
				HelpFrame_ShowFrame(HELPFRAME_SUBMIT_TICKET)
				if (self.caseIndex) then
					HelpBrowser:OpenTicket(self.caseIndex)
				else
					HelpBrowser:NavigateHome("GMTicketStatus")
				end
			</OnClick>
			<OnEvent function="HelpOpenWebTicketButton_OnEvent"/>
			<OnEnter function="HelpOpenWebTicketButton_OnEnter"/>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
		<HitRectInsets>
			<AbsInset left="4" right="4" top="4" bottom="4"/>
		</HitRectInsets>
		<NormalTexture file="Interface\HelpFrame\CS_HelpTextures">
			<TexCoords left="0.70703125" right="0.77343750" top="0.58593750" bottom="0.85937500"/>
		</NormalTexture>
		<PushedTexture file="Interface\HelpFrame\CS_HelpTextures">
			<TexCoords left="0.88867188" right="0.95507813" top="0.00781250" bottom="0.28125000"/>
		</PushedTexture>
		<HighlightTexture file="Interface\HelpFrame\CS_HelpTextures">
			<TexCoords left="0.80664063" right="0.87304688" top="0.58593750" bottom="0.85937500"/>
		</HighlightTexture>
	</Button>

	<!-- Ticket Status -->
	<!-- It might be a good idea to put this in a separate file and convert the HelpFrame stuff into an addon -->
	<Frame name="TicketStatusFrame" toplevel="true" parent="UIParent" hidden="true">
		<Size>
			<AbsDimension x="208" y="75"/>
		</Size>
		<!-- This frame gets positioned in UIParent_UpdateTopFramePositions() -->
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="TicketStatusTitleText" inherits="GameFontNormalSmall" justifyH="LEFT">
					<Size x="168" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT">
							<Offset>
								<AbsDimension x="10" y="-8"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				<FontString name="TicketStatusTime" inherits="GameFontHighlightSmall" justifyH="LEFT">
					<Size x="168" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="TicketStatusTitleText" relativePoint="BOTTOMLEFT">
							<Offset>
								<AbsDimension x="0" y="-2"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				<Texture name="TicketStatusFrameIcon" file="Interface\HelpFrame\OpenTicketIcon">
					<Size>
						<AbsDimension x="32" y="32"/>
					</Size>
					<Anchors>
						<Anchor point="TOPRIGHT">
							<Offset>
								<AbsDimension x="-3" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Button name="TicketStatusFrameButton">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TicketStatusTitleText">
						<Offset x="-8" y="8"/>
					</Anchor>
					<Anchor point="BOTTOMRIGHT" relativeTo="TicketStatusTime">
						<Offset x="30" y="-8"/>		<!-- width added to accomodate the status button -->
					</Anchor>
				</Anchors>
				<Backdrop bgFile="Interface\Tooltips\UI-Tooltip-Background" edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
					<EdgeSize>
						<AbsValue val="16"/>
					</EdgeSize>
					<TileSize>
						<AbsValue val="16"/>
					</TileSize>
					<BackgroundInsets>
						<AbsInset left="5" right="5" top="5" bottom="5"/>
					</BackgroundInsets>
				</Backdrop>
				<Scripts>
					<OnLoad function="TicketStatusFrameButton_OnLoad"/>
					<OnClick function="TicketStatusFrameButton_OnClick"/>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad function="TicketStatusFrame_OnLoad"/>
			<OnEvent function="TicketStatusFrame_OnEvent"/>
			<OnShow function="TicketStatusFrame_OnShow"/>
			<OnHide function="TicketStatusFrame_OnHide"/>
		</Scripts>
	</Frame>
	<Frame name="ReportCheatingDialog" parent="UIParent" hidden="true" frameStrata="DIALOG">
		<Size x="344" y="310"/>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentTitle" inherits="GameFontNormal" text="REPORT_CHEATING_TITLE">
					<Anchors>
						<Anchor point="TOPLEFT" x="20" y="-20"/>
					</Anchors>
				</FontString>
				<FontString name="$parentText1" inherits="GameFontHighlight" text="REPORT_CHEATING_TEXT1" justifyH="LEFT">
					<Size x="305" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTitle" relativePoint="BOTTOMLEFT" x="0" y="-10"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentCommentFrame" parentKey="CommentFrame">
				<Size x="300" y="100"/>
				<Anchors>
					<Anchor point="BOTTOM" x="0" y="70"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentTopLeft" file="Interface\Common\Common-Input-Border-TL">
							<Size>
								<AbsDimension x="8" y="8"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset x="0" y="0"/>
								</Anchor>
							</Anchors>
						</Texture>
						<Texture name="$parentTopRight" file="Interface\Common\Common-Input-Border-TR">
							<Size>
								<AbsDimension x="8" y="8"/>
							</Size>
							<Anchors>
								<Anchor point="TOPRIGHT">
									<Offset x="0" y="0"/>
								</Anchor>
							</Anchors>
						</Texture>
						<Texture name="$parentTop" file="Interface\Common\Common-Input-Border-T">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTopLeft" relativePoint="TOPRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentTopRight" relativePoint="BOTTOMLEFT"/>
							</Anchors>
						</Texture>
						<Texture name="$parentBottomLeft" file="Interface\Common\Common-Input-Border-BL">
							<Size>
								<AbsDimension x="8" y="8"/>
							</Size>
							<Anchors>
								<Anchor point="BOTTOMLEFT">
									<Offset x="0" y="0"/>
								</Anchor>
							</Anchors>
						</Texture>
						<Texture name="$parentBottomRight" file="Interface\Common\Common-Input-Border-BR">
							<Size>
								<AbsDimension x="8" y="8"/>
							</Size>
							<Anchors>
								<Anchor point="BOTTOMRIGHT">
									<Offset x="0" y="0"/>
								</Anchor>
							</Anchors>
						</Texture>
						<Texture name="$parentBottom" file="Interface\Common\Common-Input-Border-B">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentBottomLeft" relativePoint="TOPRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRight" relativePoint="BOTTOMLEFT"/>
							</Anchors>
						</Texture>
						<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border-L">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTopLeft" relativePoint="BOTTOMLEFT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomLeft" relativePoint="TOPRIGHT"/>
							</Anchors>
						</Texture>
						<Texture name="$parentRight" file="Interface\Common\Common-Input-Border-R">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTopRight" relativePoint="BOTTOMLEFT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRight" relativePoint="TOPRIGHT"/>
							</Anchors>
						</Texture>
						<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border-M">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentRight" relativePoint="BOTTOMLEFT"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<EditBox name="$parentEditBox" multiLine="true" letters="255" setAllPoints="true" parentKey="EditBox" autoFocus="false">
						<Anchors>
							<Anchor point="TOPLEFT" x="5" y="-5"/>
							<Anchor point="BOTTOMRIGHT" x="-5" y="5"/>
						</Anchors>
						<Layers>
							<Layer level="ARTWORK">
								<FontString parentKey="InformationText" inherits="GameFontDisable" setAllPoints="true" justifyH="LEFT" justifyV="TOP" text="REPORT_CHEATING_EDITBOX_INFO"/>
							</Layer>
						</Layers>
						<Scripts>
							<OnEditFocusGained>
								self.InformationText:Hide();
							</OnEditFocusGained>
							<OnEditFocusLost>
								if ( self:GetText() == "" ) then
									self.InformationText:Show();
								end
							</OnEditFocusLost>
							<OnEnterPressed>
								--Don't allow line breaks
							</OnEnterPressed>
							<OnTextChanged>
								if ( self:GetText() ~= "" ) then
									self:GetParent():GetParent().reportButton:Enable();
								else
									self:GetParent():GetParent().reportButton:Disable();
								end
							</OnTextChanged>
						</Scripts>
						<FontString inherits="GameFontHighlightSmall"/>
					</EditBox>
				</Frames>
				<Scripts>
					<OnMouseDown>
						self.EditBox:SetFocus();
					</OnMouseDown>
				</Scripts>
			</Frame>
			<Button name="$parentReportButton" inherits="UIPanelButtonTemplate" text="REPORT_PLAYER" parentKey="reportButton">
				<Size x="120" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT" x="30" y="30"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						ReportPlayer(PLAYER_REPORT_TYPE_CHEATING, self:GetParent().target, self:GetParent().CommentFrame.EditBox:GetText());
						StaticPopupSpecial_Hide(self:GetParent());
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentCancelButton" inherits="UIPanelButtonTemplate" text="CANCEL">
				<Size x="120" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" x="-30" y="30"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						StaticPopupSpecial_Hide(self:GetParent());
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self.exclusive = true;
				self:SetBackdropColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b);
			</OnLoad>
		</Scripts>
	</Frame>
</Ui>
