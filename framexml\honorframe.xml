<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="HonorFrame.lua"/>
	<Include file="HonorFrameTemplates.xml"/>
	<Frame name="HonorFrame" setAllPoints="true" enableMouse="true" parent="CharacterFrame" hidden="true">
		<HitRectInsets>
			<AbsInset left="0" right="30" top="0" bottom="75"/>
		</HitRectInsets>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture file="Interface\PaperDollInfoFrame\UI-Character-General-TopLeft">
					<Size x="256" y="256"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="2" y="-1"/>
					</Anchors>
				</Texture>
				<Texture file="Interface\PaperDollInfoFrame\UI-Character-General-TopRight">
					<Size x="128" y="256"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="258" y="-1"/>
					</Anchors>
				</Texture>
				<Texture file="Interface\PaperDollInfoFrame\UI-Character-General-BottomLeft">
					<Size x="256" y="256"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="2" y="-257"/>
					</Anchors>
				</Texture>
				<Texture file="Interface\PaperDollInfoFrame\UI-Character-General-BottomRight">
					<Size x="128" y="256"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="258" y="-257"/>
					</Anchors>
				</Texture>
				<FontString name="HonorLevelText" inherits="GameFontNormalSmall" text="Level level race class">
                    <Anchors>
                        <Anchor point="TOP" relativeTo="CharacterFrameTitleText" relativePoint="BOTTOM" x="0" y="-6"/>
                    </Anchors>
                </FontString>
                <FontString name="HonorGuildText" inherits="GameFontNormalSmall">
                    <Anchors>
                        <Anchor point="TOP" relativeTo="HonorLevelText" relativePoint="BOTTOM" x="0" y="-1"/>
                    </Anchors>
                </FontString>
			</Layer>
			<Layer level="ARTWORK">
				<Texture file="Interface\PaperDollInfoFrame\UI-Character-Honor-TopLeft">
					<Size x="256" y="256"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="22" y="-69"/>
					</Anchors>
				</Texture>
				<Texture file="Interface\PaperDollInfoFrame\UI-Character-Honor-TopRight">
					<Size x="128" y="256"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="275" y="-69"/>
					</Anchors>
				</Texture>
				<Texture file="Interface\PaperDollInfoFrame\UI-Character-Honor-BottomLeft">
					<Size x="256" y="128"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="22" y="-325"/>
					</Anchors>
				</Texture>
				<Texture file="Interface\PaperDollInfoFrame\UI-Character-Honor-BottomRight">
					<Size x="128" y="128"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="275" y="-325"/>
					</Anchors>
				</Texture>
				<FontString name="HonorFrameCurrentPVPTitle" inherits="GameFontNormal">
					<Anchors>
						<Anchor point="TOP" x="0" y="-87"/>
					</Anchors>
				</FontString>
				<FontString name="HonorFrameCurrentPVPRank" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="LEFT" relativeTo="HonorFrameCurrentPVPTitle" relativePoint="RIGHT" x="5" y="-1"/>
					</Anchors>
				</FontString>
				<FontString name="HonorFrameCurrentSessionTitle" inherits="GameFontNormal" text="HONOR_THIS_SESSION">
					<Anchors>
						<Anchor point="TOPLEFT" x="36" y="-112"/>
					</Anchors>
				</FontString>
				<FontString name="HonorFrameYesterdayTitle" inherits="GameFontNormal" text="HONOR_YESTERDAY">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="HonorFrameCurrentSessionTitle" relativePoint="BOTTOMLEFT" x="0" y="-41"/>
					</Anchors>
				</FontString>
				<FontString name="HonorFrameThisWeekTitle" inherits="GameFontNormal" text="HONOR_THISWEEK">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="HonorFrameYesterdayTitle" relativePoint="BOTTOMLEFT" x="0" y="-43"/>
					</Anchors>
				</FontString>
				<FontString name="HonorFrameLastWeekTitle" inherits="GameFontNormal" text="HONOR_LASTWEEK">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="HonorFrameThisWeekTitle" relativePoint="BOTTOMLEFT" x="0" y="-42"/>
					</Anchors>
				</FontString>
				<FontString name="HonorFrameLifeTimeTitle" inherits="GameFontNormal" text="HONOR_LIFETIME">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="HonorFrameLastWeekTitle" relativePoint="BOTTOMLEFT" x="0" y="-64"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="HonorFramePvPIcon">
					<Size x="24" y="24"/>
					<Anchors>
						<Anchor point="RIGHT" relativeTo="HonorFrameCurrentPVPTitle" relativePoint="LEFT" x="-5" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="HonorFrameCurrentHK" inherits="HonorFrameHKButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="HonorFrameCurrentSessionTitle" relativePoint="BOTTOMLEFT" x="10" y="-3"/>
				</Anchors>
			</Frame>
			<Frame name="HonorFrameCurrentDK" inherits="HonorFrameDKButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="HonorFrameCurrentHK" relativePoint="BOTTOMLEFT" x="0" y="-2"/>
				</Anchors>
			</Frame>
			<Frame name="HonorFrameYesterdayHK" inherits="HonorFrameHKButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="HonorFrameYesterdayTitle" relativePoint="BOTTOMLEFT" x="10" y="-3"/>
				</Anchors>
			</Frame>
			<Frame name="HonorFrameYesterdayContribution" inherits="HonorFrameContributionButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="HonorFrameYesterdayHK" relativePoint="BOTTOMLEFT" x="0" y="-3"/>
				</Anchors>
			</Frame>
			<Frame name="HonorFrameThisWeekHK" inherits="HonorFrameHKButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="HonorFrameThisWeekTitle" relativePoint="BOTTOMLEFT" x="10" y="-3"/>
				</Anchors>
			</Frame>
			<Frame name="HonorFrameThisWeekContribution" inherits="HonorFrameContributionButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="HonorFrameThisWeekHK" relativePoint="BOTTOMLEFT" x="0" y="-3"/>
				</Anchors>
			</Frame>
			<Frame name="HonorFrameLastWeekHK" inherits="HonorFrameHKButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="HonorFrameLastWeekTitle" relativePoint="BOTTOMLEFT" x="10" y="-3"/>
				</Anchors>
			</Frame>
			<Frame name="HonorFrameLastWeekContribution" inherits="HonorFrameContributionButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="HonorFrameLastWeekHK" relativePoint="BOTTOMLEFT" x="0" y="-2"/>
				</Anchors>
			</Frame>
			<Frame name="HonorFrameLastWeekStanding" inherits="HonorFrameStandingButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="HonorFrameLastWeekContribution" relativePoint="BOTTOMLEFT" x="0" y="-2"/>
				</Anchors>
			</Frame>
			<Frame name="HonorFrameLifeTimeHK" inherits="HonorFrameHKButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="HonorFrameLifeTimeTitle" relativePoint="BOTTOMLEFT" x="10" y="-4"/>
				</Anchors>
			</Frame>
			<Frame name="HonorFrameLifeTimeDK" inherits="HonorFrameDKButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="HonorFrameLifeTimeHK" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
				</Anchors>
			</Frame>
			<Frame name="HonorFrameLifeTimeRank" inherits="HonorFrameRankButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="HonorFrameLifeTimeDK" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
				</Anchors>
			</Frame>
			<Frame name="HonorFrameRankButton" enableMouse="true" hidden="false">
				<Size x="10" y="24"/>
				<Anchors>
					<Anchor point="LEFT" relativeTo="HonorFramePvPIcon"/>
					<Anchor point="RIGHT" relativeTo="HonorFrameCurrentPVPRank"/>
				</Anchors>
				<Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText(RANK, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b);
						GameTooltip:AddLine(NEWBIE_TOOLTIP_RANK, NORMAL_FONT_COLOR.r, NORMAL_FONT_COLOR.g, NORMAL_FONT_COLOR.b, true);
						GameTooltip:Show();
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Frame>
			<StatusBar name="HonorFrameProgressBar" drawLayer="BORDER" minValue="0" maxValue="1" defaultValue="0">
				<Size x="315" y="29"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="22" y="-77"/>
				</Anchors>
				<Frames>
					<Frame name="HonorFrameProgressButton" enableMouse="true" setAllPoints="true">
						<Scripts>
							<OnLoad function="LowerFrameLevel"/>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								GameTooltip:SetText(RANK_POSITION, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b);
								GameTooltip:AddLine(NEWBIE_TOOLTIP_RANK_POSITION, NORMAL_FONT_COLOR.r, NORMAL_FONT_COLOR.g, NORMAL_FONT_COLOR.b, true);
								GameTooltip:Show();
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</Frame>
				</Frames>
				<Scripts>
					<OnLoad function="LowerFrameLevel"/>
				</Scripts>
				<BarTexture name="$parentBar" file="Interface\PaperDollInfoFrame\UI-Character-Skills-Bar" />
			</StatusBar>
		</Frames>
		<Scripts>
			<OnLoad function="HonorFrame_OnLoad"/>
			<OnEvent function="HonorFrame_OnEvent"/>
		</Scripts>
	</Frame>
</Ui>
