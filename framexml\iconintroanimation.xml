<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="IconIntroAnimation.lua"/>



	<Frame name="IconIntroAnimTemplate" virtual="true" alpha="0.5">
		<Animations>
			<AnimationGroup parentKey="flyin" mixin="IconIntroFlyinAnimMixin">
				<Alpha fromAlpha="1" toAlpha="0" startDelay="1.0" duration="0.5" order="1"/>
				<Path curve="SMOOTH" duration="1.5" order="1">
					<ControlPoints>
						<ControlPoint offsetX="-117" offsetY="190"/>
						<ControlPoint offsetX="-300" offsetY="260"/>
					</ControlPoints>
				</Path>
				<Alpha parentKey="wait"  fromAlpha="0" toAlpha="0" duration="0.1" order="2"/>
				<Scripts>
					<OnPlay method="OnAnimPlay"/>
					<OnFinished method="OnAnimFinished"/>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="glow">
				<Alpha 	target="$parentGlow" fromAlpha="0" toAlpha="1"	duration="0.3" order="1"/>
				<Alpha 	fromAlpha="1" toAlpha="0" duration="0.3" order="2"/>
				<Scripts>
					<OnPlay>
						local trail = self:GetParent().trail;
						while trail do
							trail:Hide();
							trail = trail.trail;
						end
					</OnPlay>
					<OnFinished>
						self:GetParent():GetParent():Hide();
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="$parentIcon" parentKey="icon" setAllPoints="true" alphaMode="ADD">
				</Texture>
				<Texture name="$parentBG" parentKey="bg" setAllPoints="true">
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentGlow" file="Interface\SpellActivationOverlay\IconAlert" alpha="0" setAllPoints="true" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPLEFT" x="-10" y="10"/>
						<Anchor point="BOTTOMRIGHT"  x="10" y="-10"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.50781250" top="0.53515625" bottom="0.78515625"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>


	<Frame name="IconIntroTemplate" virtual="true" frameStrata="HIGH">
		<Size>
			<AbsDimension x="40" y="40"/>
		</Size>
		<Frames>
			<Frame name="$parentIconTrail3" inherits="IconIntroAnimTemplate" parentKey="trail3" >
				<Size>
					<AbsDimension x="10" y="10"/>
				</Size>
				<Anchors>
					<Anchor point="CENTER" x="0" y="0"/>
				</Anchors>
			</Frame>
			<Frame name="$parentIconTrail2" inherits="IconIntroAnimTemplate" parentKey="trail2" >
				<Size>
					<AbsDimension x="20" y="20"/>
				</Size>
				<Anchors>
					<Anchor point="CENTER" x="0" y="0"/>
				</Anchors>
			</Frame>
			<Frame name="$parentIconTrail1" inherits="IconIntroAnimTemplate" parentKey="trail1" >
				<Size>
					<AbsDimension x="30" y="30"/>
				</Size>
				<Anchors>
					<Anchor point="CENTER" x="0" y="0"/>
				</Anchors>
			</Frame>
			<Frame name="$parentIcon" inherits="IconIntroAnimTemplate" parentKey="icon" >
				<Size>
					<AbsDimension x="40" y="40"/>
				</Size>
				<Anchors>
					<Anchor point="CENTER" x="0" y="0"/>
				</Anchors>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self.icon.isBase = true;
				self.icon:SetAlpha(1.0);
				self.trail1.flyin.wait:SetDuration(0.16);
				self.trail2.flyin.wait:SetDuration(0.21);
				self.trail3.flyin.wait:SetDuration(0.255);

				self.icon.trail = self.trail1;
				self.trail1.trail = self.trail2;
				self.trail2.trail = self.trail3;
			</OnLoad>
		</Scripts>
	</Frame>



	<Frame name="IconIntroTracker" toplevel="true" parent="UIParent" hidden="true" mixin="IconIntroTrackerMixin">
		<Size x="5" y="5"/>
		<Anchors>
			<Anchor point="BOTTOMLEFT" x="20" y="20"/>
		</Anchors>
		<Scripts>
			<OnLoad method="OnLoad"/>
			<OnEvent method="OnEvent"/>
		</Scripts>
	</Frame>
</Ui>
