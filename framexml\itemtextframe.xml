<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="ItemTextFrame.lua"/>
	<Frame name="ItemTextFrame" toplevel="true" movable="true" enableMouse="true" hidden="true" parent="UIParent" inherits="ButtonFrameTemplate">
		<Layers>
			<Layer level="OVERLAY" textureSubLevel="-1">
				<Texture file="Interface\Spellbook\Spellbook-Icon">
					<Size x="58" y="58"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-5" y="5"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND">
				<Texture name="ItemTextFramePageBg" file="Interface\QuestFrame\QuestBG">
					<Size x="512" y="543"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="7" y="-62"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture name="ItemTextMaterialTopLeft">
					<Size x="256" y="256"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="7" y="-62"/>
					</Anchors>
				</Texture>
				<Texture name="ItemTextMaterialTopRight">
					<Size x="64" y="256"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="ItemTextMaterialTopLeft" relativePoint="TOPRIGHT"/>
					</Anchors>
				</Texture>
				<Texture name="ItemTextMaterialBotLeft">
					<Size x="256" y="128"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="ItemTextMaterialTopLeft" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="ItemTextMaterialBotRight">
					<Size x="64" y="128"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="ItemTextMaterialTopLeft" relativePoint="BOTTOMRIGHT"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<FontString name="ItemTextCurrentPage" inherits="GameFontNormal">
					<Size x="192" y="0"/>
					<Anchors>
						<Anchor point="TOP" x="20" y="-35"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<ScrollFrame name="ItemTextScrollFrame" inherits="UIPanelScrollFrameCodeTemplate">
				<Anchors>
					<Anchor point="TOPRIGHT" x="-33" y="-83"/>
					<Anchor point="BOTTOMLEFT" x="25" y="6"/>
				</Anchors>
				<Frames>
					<Slider name="$parentScrollBar" inherits="UIPanelStretchableArtScrollBarTemplate" parentKey="ScrollBar">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="ItemTextFrame" relativePoint="TOPRIGHT" x="-27" y="-79"/>
							<Anchor point="BOTTOMLEFT" relativeTo="ItemTextFrame" relativePoint="BOTTOMRIGHT" x="-27" y="22"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								self.scrollStep = 30;
							</OnLoad>
						</Scripts>
					</Slider>
				</Frames>
				<ScrollChild>
					<Frame name="ItemTextPageScrollChild">
						<Size x="10" y="10"/>
						<Frames>
							<SimpleHTML name="ItemTextPageText">
								<Size x="270" y="304"/>
								<Anchors>
									<Anchor point="TOPLEFT" x="0" y="-15"/>
								</Anchors>
								<FontString inherits="QuestFont" justifyH="LEFT"/>
							</SimpleHTML>
						</Frames>
					</Frame>
				</ScrollChild>
			</ScrollFrame>
			<StatusBar name="ItemTextStatusBar" hidden="true">
				<Size x="250" y="21"/>
				<Anchors>
					<Anchor point="BOTTOM" relativeTo="ItemTextFrame" x="-5" y="128"/>
				</Anchors>
				<Layers>
					<Layer level="OVERLAY">
						<Texture file="Interface\PaperDollInfoFrame\UI-Character-Skills-BarBorder">
							<Size x="256" y="23"/>
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.0625" bottom="0.75"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad function="RaiseFrameLevel"/>
				</Scripts>
				<BarTexture file="Interface\TargetingFrame\UI-TargetingFrame-BarFill"/>
				<BarColor r="0.25" g="0.75" b="0.25"/>
			</StatusBar>
			<Button name="ItemTextPrevPageButton">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="CENTER" relativePoint="TOPLEFT" x="75" y="-41"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString inherits="GameFontNormal" justifyH="LEFT" text="PREV">
							<Anchors>
								<Anchor point="LEFT" relativePoint="RIGHT"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						ItemTextPrevPage();
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Up"/>
				<PushedTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Down"/>
				<DisabledTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Disabled"/>
				<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
			</Button>
			<Button name="ItemTextNextPageButton">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="CENTER" relativePoint="TOPRIGHT" x="-23" y="-41"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString inherits="GameFontNormal" justifyH="RIGHT" text="NEXT">
							<Anchors>
								<Anchor point="RIGHT" relativePoint="LEFT"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						ItemTextNextPage();
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Up"/>
				<PushedTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Down"/>
				<DisabledTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Disabled"/>
				<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad function="ItemTextFrame_OnLoad"/>
			<OnEvent function="ItemTextFrame_OnEvent"/>
			<OnUpdate function="ItemTextFrame_OnUpdate"/>
			<OnShow>
				PlaySound(SOUNDKIT.IG_MAINMENU_OPEN);
			</OnShow>
			<OnHide>
				CloseItemText();
				PlaySound(SOUNDKIT.IG_MAINMENU_CLOSE);
			</OnHide>
		</Scripts>
	</Frame>
</Ui>
