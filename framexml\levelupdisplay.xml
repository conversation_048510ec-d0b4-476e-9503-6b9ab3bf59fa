<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="LevelUpDisplay.lua"/>
	<Script file="TopBannerManager.lua"/>



	
	<!--size=512,512
<Texture name="LevelUp-WhiteLine-H.png" >
	<Size x="418" y="7"/>	
	<TexCoords left="0.00195313" right="0.81835938" top="0.00195313" bottom="0.01562500"/>	
</Texture>
<Texture name="LevelUp-GoldLine-H.png" >
	<Size x="418" y="7"/>	
	<TexCoords left="0.00195313" right="0.81835938" top="0.01953125" bottom="0.03320313"/>	
</Texture>
<Texture name="LevelUp-BlackBg-H.png" >
	<Size x="326" y="103"/>	
	<TexCoords left="0.00195313" right="0.63867188" top="0.03710938" bottom="0.23828125"/>	
</Texture>
<Texture name="LevelUp-Icon-Book.png" >
	<Size x="40" y="38"/>	
	<TexCoords left="0.64257813" right="0.72070313" top="0.03710938" bottom="0.11132813"/>	
</Texture>
<Texture name="Levelup-Icon-Lock.png" >
	<Size x="30" y="35"/>	
	<TexCoords left="0.64257813" right="0.70117188" top="0.11523438" bottom="0.18359375"/>	
</Texture>
<Texture name="LevelUp-GoldButton-V.png" >
	<Size x="21" y="22"/>	
	<TexCoords left="0.64257813" right="0.68359375" top="0.18750000" bottom="0.23046875"/>	
</Texture>
<Texture name="LevelUp-Icon-Arrow.png" >
	<Size x="30" y="34"/>	
	<TexCoords left="0.72460938" right="0.78320313" top="0.03710938" bottom="0.10351563"/>	
</Texture>
<Texture name="LevelUp-BlackBg-V.png" >
	<Size x="284" y="296"/>	
	<TexCoords left="0.00195313" right="0.55664063" top="0.24218750" bottom="0.82031250"/>	
</Texture>
<Texture name="LevelUp-GoldBg-V.png" >
	<Size x="223" y="115"/>	
	<TexCoords left="0.56054688" right="0.99609375" top="0.24218750" bottom="0.46679688"/>	
</Texture-->

	
	<Frame name="LevelUpSkillTemplate" virtual="true">
		<Animations>
			<AnimationGroup parentKey="sideAnimIn">
				<Alpha fromAlpha="0" toAlpha="1" duration="0.4" order="2"/>
				<Scripts>
					<OnFinished>
						LevelUpDisplaySide_AnimStep(LevelUpDisplaySide);
					</OnFinished>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="showAnim">
				<Alpha fromAlpha="0" toAlpha="1" duration="0.5" order="1" />
				<Alpha fromAlpha="1" toAlpha="0" duration="0.5" startDelay="1.8" order="2" parentKey="anim2"/>
				<Scripts>
					<OnFinished>
						LevelUpDisplay_AnimStep(LevelUpDisplay, false);
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Size>
			<AbsDimension x="230" y="44"/>
		</Size>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="$parentIcon" parentKey="icon">
					<Size x="36" y="36"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="8" y="2"/>
					</Anchors>
				</Texture>
			
				<FontString name="$parentName" inherits="GameFontNormalLarge" justifyH="LEFT" parentKey="name">
					<Anchors>
						<Anchor point="BOTTOMLEFT"  relativeTo="$parentIcon" relativePoint="BOTTOMRIGHT" x="10" y="3"/>
					</Anchors>
				</FontString>
				<FontString name="$parentSubText" inherits="GameFontNormal" justifyH="LEFT" parentKey="flavorText">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentName" relativePoint="TOPLEFT" x="0" y="1"/>
					</Anchors>
					<Color r="0.0" g="1.0" b="0.0"/>
				</FontString>

				<FontString name="$parentUpperWhite" parentKey="upperwhite" inherits="SystemFont_Shadow_Large" justifyH="LEFT" justifyV="TOP">
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" relativeTo="$parentIcon" x="10" y="6"/>
					</Anchors>
				</FontString>
				<FontString name="$parentBottomGiant" parentKey="bottomGiant" inherits="GameFont_Gigantic" justifyH="LEFT" justifyV="BOTTOM">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" relativeTo="$parentIcon" x="10" y="-6"/>
					</Anchors>
				</FontString>

				<!-- Pet Battle Capture with Rarity -->
				<FontString name="$parentRarityUpperWhite" parentKey="rarityUpperwhite" inherits="SystemFont_Shadow_Large" justifyH="LEFT" justifyV="TOP">
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" relativeTo="$parentIcon" x="10" y="12"/>
					</Anchors>
				</FontString>
				<FontString name="$parentRarityBottomHuge" parentKey="rarityMiddleHuge" inherits="GameFontNormalHuge" justifyH="LEFT" justifyV="BOTTOM">
					<Anchors>
						<Anchor point="LEFT" relativePoint="RIGHT" relativeTo="$parentIcon" x="10" y="1"/>
					</Anchors>
				</FontString>
				<Texture name="$parentRarityIcon" parentKey="rarityIcon" file="Interface\PetBattles\PetBattle-StatIcons" hidden="true">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" relativeTo="$parentIcon" x="10" y="-8"/>
					</Anchors>
					<TexCoords left="0.5" right="1.0" top="0.0" bottom="0.5"/>
				</Texture>
				<FontString name="$parentRarityValue" inherits="GameFontHighlight" parentKey="rarityValue" text="" hidden="true">
					<Size x="0" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentRarityIcon" relativePoint="RIGHT" x="2" y="0"/>
					</Anchors>
				</FontString>
				
				<FontString parentKey="instructionalText" inherits="GameFontNormal" justifyH="CENTER" justifyV="MIDDLE">
					<Size x="0" y="0"/>
					<Anchors>
						<Anchor point="TOP" relativePoint="BOTTOM" y="-13"/>
					</Anchors>
					<Color r="1" g="1" b="1"/>
				</FontString>

				<!-- Spell bucket -->
				<FontString parentKey="middleName" inherits="GameFontNormalLarge" justifyH="LEFT">
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.icon" relativePoint="RIGHT" x="10" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentIconBorder" parentKey="iconBorder" file="Interface\Common\WhiteIconFrame" hidden="true">
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" relativeKey="$parent.icon"/>
						<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT" relativeKey="$parent.icon"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="2">
				<Texture name="$parentSubIcon" parentKey="subIcon" file="Interface\LevelUp\LevelUpTex">
					<Size x="22" y="22"/>
					<Anchors>
						<Anchor point="CENTER" relativeTo="$parentIcon" relativePoint="BOTTOMLEFT" x="2" y="2"/>
					</Anchors>
				</Texture>
				<Texture name="$parentSubIconRight" parentKey="subIconRight" file="Interface\LevelUp\LevelUpTex" hidden="true">
					<Anchors>
						<Anchor point="CENTER" relativeTo="$parentIcon" relativePoint="BOTTOMRIGHT" x="-2" y="2"/>
					</Anchors>
					<Size x="22" y="22"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="SpellBucketSpellTemplate" parentArray="BucketIcons" virtual="true">
		<Size x="56" y="56"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="icon">
					<Size x="36" y="36"/>
					<Anchors>
						<Anchor point="TOP"/>
					</Anchors>
				</Texture>
			
				<FontString  parentKey="name" inherits="GameFontNormalSmall" justifyH="CENTER" justifyV="TOP" wordwrap="true">
					<Size x="56" y="20"/>
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.icon" relativePoint="BOTTOM" x="0" y="-4"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="LevelUpDisplay" toplevel="true" parent="UIParent" hidden="true" frameStrata="HIGH">
		<Animations>
			<AnimationGroup parentKey="hideAnim">
				<Alpha fromAlpha="1" toAlpha="0" duration="1.0" order="1" />
				<Scripts>
					<OnFinished function="LevelUpDisplay_AnimOutFinished"/>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="fastHideAnim">
				<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="1" />
				<Scripts>
					<OnFinished function="LevelUpDisplay_AnimOutFinished"/>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Size x="418" y="72"/>
		<Anchors>
			<Anchor point="TOP" x="0" y="-190"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture file="Interface\LevelUp\LevelUpTex" name="$parentBlackBg" parentKey="blackBg">
					<Animations>
						<AnimationGroup parentKey="grow">
							<Scale scaleX="1.0" scaleY="0.001" duration="0.0" startDelay="1.5" order="1" parentKey="anim1">
								<Scripts>
									<OnFinished>
										self:GetParent():GetParent():Show();
									</OnFinished>
								</Scripts>
								<Origin point="BOTTOM">
									<Offset x="0" y="0"/>
								 </Origin>
							</Scale>
							<Scale scaleX="1.0" scaleY="1000.0" startDelay="0.25" duration="0.15" order="2">
								 <Origin point="BOTTOM">
									<Offset x="0" y="0"/>
								 </Origin>
							</Scale>
						</AnimationGroup>
					</Animations>
					<Anchors>
						<Anchor point="BOTTOM" x="0" y="0"/>
					</Anchors>
					<Size x="326" y="103"/>	
					<TexCoords left="0.00195313" right="0.63867188" top="0.03710938" bottom="0.23828125"/>
					<Color r="1.0" g="1.0" b="1.0" a="0.6"/>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="2">
				<Texture file="Interface\LevelUp\LevelUpTex" name="$parentGLine2" parentKey="gLine2">
					<Animations>
						<AnimationGroup parentKey="grow">
							<Scale scaleX="0.001" scaleY="1.0" duration="0.0" startDelay="1.5" order="1" parentKey="anim1">
								<Scripts>
									<OnFinished>
										self:GetParent():GetParent():Show();
									</OnFinished>
								</Scripts>
							</Scale>
							<Scale scaleX="1000.0" scaleY="1.0" duration="0.5" order="2"/>
						</AnimationGroup>
					</Animations>
					<Anchors>
						<Anchor point="TOP" x="0" y="0"/>
					</Anchors>
					<Size x="418" y="7"/>
				</Texture>
				<Texture file="Interface\LevelUp\LevelUpTex" name="$parentGLine" parentKey="gLine">
					<Anchors>
						<Anchor point="BOTTOM" x="0" y="0"/>
					</Anchors>
					<Size x="418" y="7"/>
					<Animations>
						<AnimationGroup parentKey="grow">
							<Scale scaleX="0.001" scaleY="1.0" duration="0.0" startDelay="1.5" order="1" parentKey="anim1">
								<Scripts>
									<OnFinished>
										self:GetParent():GetParent():Show();
									</OnFinished>
								</Scripts>
							</Scale>
							<Scale scaleX="1000.0" scaleY="1.0" duration="0.5" order="2"/>
							<Scripts>
								<OnPlay>
									LevelUpDisplayBlackBg:Hide();
									LevelUpDisplayGLine:Hide();
									LevelUpDisplayGLine2:Hide();
									LevelUpDisplayGLine2.grow:Play();
									LevelUpDisplayBlackBg.grow:Play();
								</OnPlay>
							</Scripts>
						</AnimationGroup>
					</Animations>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame  name="$parentLevelFrame" parentKey="levelFrame" alpha="0">
				<Animations>
					<AnimationGroup parentKey="levelUp">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.7" startDelay="1.5" order="1" />
						<Alpha fromAlpha="1" toAlpha="0" duration="0.5" startDelay="1.5" order="2"/>
						<Scripts>
							<OnPlay>
								LevelUpDisplayGLine.grow:Play();
							</OnPlay>
							<OnFinished>
								LevelUpDisplay_AnimStep(LevelUpDisplay, false);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
					<AnimationGroup parentKey="fastReveal">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.7" startDelay="0.5" order="1" />
						<Alpha fromAlpha="1" toAlpha="0" duration="0.5" startDelay="1.5" order="2"/>
						<Scripts>
							<OnPlay>
								LevelUpDisplayGLine.grow:Play();
							</OnPlay>
							<OnFinished>
								LevelUpDisplay_AnimStep(LevelUpDisplay, true);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
					<AnimationGroup parentKey="immediateReveal">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.7" startDelay="0" order="1">
							<Scripts>
								<OnFinished>
									LevelUpDisplay_AnimStep(LevelUpDisplay, false);
								</OnFinished>
							</Scripts>
						</Alpha>
						<Alpha fromAlpha="1" toAlpha="0" duration="0.5" startDelay="1.5" order="2"/>
						<Scripts>
							<OnPlay>
								LevelUpDisplayGLine.grow:Play();
							</OnPlay>
						</Scripts>
					</AnimationGroup>
				</Animations>
				<Size x="418" y="72"/>
				<Anchors>
					<Anchor point="TOPLEFT"/>
					<Anchor point="BOTTOMRIGHT"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentLevel" inherits="GameFont_Gigantic" justifyH="CENTER" parentKey="levelText">
							<Anchors>
								<Anchor point="BOTTOM" x="0" y="5"/>
							</Anchors>
							<Color r="1.0" g="0.82" b="0"/>
						</FontString>
						<FontString inherits="SystemFont_Shadow_Large" justifyH="CENTER" parentKey="reachedText">
							<Anchors>
								<Anchor point="BOTTOM" relativeTo="$parentLevel" relativePoint="TOP" x="0" y="5"/>
							</Anchors>
						</FontString>
						<FontString name="$parentTextLine" inherits="GameFont_Gigantic" parentKey="singleline" text="">
							<Anchors>
								<Anchor point="CENTER" y="-4"/>
							</Anchors>
						</FontString>
						<FontString name="$parentBlockText" inherits="GameFontNormalHuge" parentKey="blockText">
							<Anchors>
								<Anchor point="CENTER" y="-4"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="$parentScenarioFrame" parentKey="scenarioFrame" alpha="0">
				<Animations>
					<AnimationGroup parentKey="newStage">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.7" endDelay="4.5" order="1"/>
						<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
						<Scripts>
							<OnPlay>
								LevelUpDisplay.scenarioFrame.BG1:SetAlpha(0);
								LevelUpDisplay.scenarioFrame.BG2:SetAlpha(0);
								LevelUpDisplay.scenarioFrame.level:SetTextColor(1, 0.914, 0.682); 
								LevelUpDisplay.scenarioFiligree:Show();
								LevelUpDisplay.extraFrame = LevelUpDisplay.scenarioFiligree;
								LevelUpDisplayGLine.grow:Play();
								LevelUpDisplay.scenarioBits.fadeIn:Play();
							</OnPlay>
							<OnFinished>
								LevelUpDisplay_AnimOut();
							</OnFinished>
						</Scripts>
					</AnimationGroup>
					<AnimationGroup parentKey="LegionInvasionNewStage" setToFinalAlpha="true">
						<Scale childKey="BG1" duration="0.33" order="1" fromScaleX="0.001" fromScaleY="1" toScaleX="1" toScaleY="1"/>
						<Alpha childKey="BG1" duration="0" order="1" fromAlpha="0" toAlpha="1"/>
						<Scale childKey="BG2" duration="0.33" order="1" fromScaleX="0.001" fromScaleY="1" toScaleX="1" toScaleY="1"/>
						<Alpha childKey="BG2" duration="0.33" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="BG2" startDelay="0.33" smoothing="IN" duration="0.33" order="1" fromAlpha="1" toAlpha="0"/>
						
						<Alpha childKey="level" duration="0.33" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="name" duration="0.33" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="levelFlash" duration="0.33" order="1" fromAlpha="0" toAlpha="1"/>
						<Alpha childKey="levelFlash" startDelay="0.33" smoothing="IN" duration="0.33" order="1" fromAlpha="1" toAlpha="0"/>
						
						<!-- The key to fixing the thing where text goes blank is this animation -->
						<Alpha fromAlpha="0" toAlpha="1" duration="0.7" order="1"/>
						<Alpha fromAlpha="1" toAlpha="0" duration="0.5" startDelay="4.5" order="1"/>
						<Scripts>
							<OnPlay>
								LevelUpDisplay.scenarioFrame.level:SetTextColor(0.753, 1, 0);
								LevelUpDisplay.blackBg:Hide();
								LevelUpDisplay.gLine:Hide();
								LevelUpDisplay.gLine2:Hide();
							</OnPlay>
							<OnFinished>
								LevelUpDisplay_AnimOutFinished();
							</OnFinished>
						</Scripts>
					</AnimationGroup>
				</Animations>
				<Size x="418" y="72"/>
				<Anchors>
					<Anchor point="TOPLEFT"/>
					<Anchor point="BOTTOMRIGHT"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture parentKey="BG1" hidden="false" alpha="0" alphaMode="BLEND" atlas="legioninvasion-title-bg" useAtlasSize="true">
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BORDER" textureSubLevel="1">
						<Texture parentKey="BG2" hidden="false" alpha="0" alphaMode="ADD" atlas="legioninvasion-title-bg" useAtlasSize="true">
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="ARTWORK">
						<FontString inherits="DestinyFontHuge" justifyH="CENTER" parentKey="level">
							<Anchors>
								<Anchor point="TOP" x="0" y="-14"/>
							</Anchors>
							<Color r="1" g="0.914" b="0.682"/>
						</FontString>
						<FontString inherits="DestinyFontHuge" justifyH="CENTER" parentKey="levelFlash" alpha="0">
							<Anchors>
								<Anchor point="TOP" x="0" y="-14"/>
							</Anchors>
							<Color r="1" g="1" b="1"/>
						</FontString>
						<FontString inherits="SystemFont_Shadow_Large" justifyH="CENTER" parentKey="name">
							<Anchors>
								<Anchor point="BOTTOM" x="0" y="8"/>
							</Anchors>
							<Color r="1" g="0.831" b="0.381"/>
						</FontString>
						<FontString inherits="SystemFont_Shadow_Large" justifyH="CENTER" parentKey="description">
							<Size x="300" y="0"/>
							<Anchors>
								<Anchor point="TOP" relativePoint="BOTTOM" x="0" y="-12"/>
							</Anchors>
							<Color r="1" g="0.831" b="0.381"/>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
			<Frame parentKey="scenarioBits" alpha="0" setAllPoints="true">
				<Animations>
					<AnimationGroup parentKey="fadeIn">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.7" startDelay="0.7" endDelay="3.8" order="1"/>
						<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
					</AnimationGroup>
				</Animations>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture atlas="Banner-BgFiligree" useAtlasSize="true" alphaMode="ADD" alpha="0.3">
							<Anchors>
								<Anchor point="BOTTOM" relativePoint="TOP" x="0" y="-6"/>
							</Anchors>
						</Texture>
						<Texture atlas="Banner-BgFiligree" useAtlasSize="true" alphaMode="ADD" alpha="0.3">
							<Anchors>
								<Anchor point="TOP" relativePoint="BOTTOM" x="0" y="0"/>
							</Anchors>
							<TexCoords left="0" right="1" bottom="0" top="1"/>
						</Texture>
					</Layer>
					<Layer level="BORDER">
						<Texture atlas="Banner-FiligreeShadow">
							<Size x="480" y="35"/>
							<Anchors>
								<Anchor point="BOTTOM" relativePoint="TOP" x="0" y="-6"/>
							</Anchors>
						</Texture>
						<Texture atlas="Banner-FiligreeShadow">
							<Size x="480" y="35"/>
							<Anchors>
								<Anchor point="TOP" relativePoint="BOTTOM" x="0" y="0"/>
							</Anchors>
							<TexCoords left="0" right="1" bottom="0" top="1"/>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame parentKey="scenarioFiligree" setAllPoints="true" hidden="true">
				<Layers>
					<Layer level="OVERLAY">
						<Texture atlas="Banner-SmallFiligree" useAtlasSize="true">
							<Anchors>
								<Anchor point="TOP" x="0" y="4"/>
							</Anchors>
						</Texture>
						<Texture atlas="Banner-SmallFiligree" useAtlasSize="true">
							<Anchors>
								<Anchor point="BOTTOM" x="0" y="-10"/>
							</Anchors>
							<TexCoords left="0" right="1" bottom="0" top="1"/>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="$parentChallengeModeFrame" parentKey="challengeModeFrame" alpha="0">
				<Animations>
					<AnimationGroup parentKey="challengeComplete">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.7" endDelay="2.5" order="1"/>
						<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
						<Scripts>
							<OnPlay>
								LevelUpDisplayGLine.grow:Play();
								if ( not self.showMedal ) then
									LevelUpDisplay.challengeModeBits.fadeIn:Play();
								end
							</OnPlay>
							<OnFinished>
								LevelUpDisplay_AnimOut();
							</OnFinished>
						</Scripts>
					</AnimationGroup>
				</Animations>
				<Size x="418" y="72"/>
				<Anchors>
					<Anchor point="TOPLEFT"/>
					<Anchor point="BOTTOMRIGHT"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString inherits="DestinyFontHuge" justifyH="CENTER" parentKey="LevelCompleted">
							<Anchors>
								<Anchor point="TOP" x="0" y="-14"/>
							</Anchors>
							<Color r="0.87" g="0.80" b="0.61"/>
						</FontString>
						<FontString inherits="SystemFont_Shadow_Large" justifyH="CENTER" parentKey="RecordTime">
							<Anchors>
								<Anchor point="BOTTOM" x="0" y="8"/>
							</Anchors>
							<Color r="1" g="0.82" b="0"/>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
			<Frame parentKey="challengeModeBits" alpha="0" setAllPoints="true">
				<Animations>
					<AnimationGroup parentKey="fadeIn">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.7" startDelay="0.7" endDelay="2.3" order="1"/>
						<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
					</AnimationGroup>
				</Animations>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture parentKey="MedalFlare" atlas="challenges-bannershine" useAtlasSize="true">
							<Anchors>
								<Anchor point="BOTTOM" relativePoint="TOP" x="0" y="-8"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BORDER">
						<Texture atlas="Banner-FiligreeShadow">
							<Size x="480" y="35"/>
							<Anchors>
								<Anchor point="BOTTOM" relativePoint="TOP" x="0" y="-6"/>
							</Anchors>
						</Texture>
						<Texture atlas="Banner-FiligreeShadow">
							<Size x="480" y="35"/>
							<Anchors>
								<Anchor point="TOP" relativePoint="BOTTOM" x="0" y="0"/>
							</Anchors>
							<TexCoords left="0" right="1" bottom="0" top="1"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture parentKey="BottomFiligree" atlas="Banner-SmallFiligree" useAtlasSize="true">
							<Anchors>
								<Anchor point="BOTTOM" x="0" y="-10"/>
							</Anchors>
							<TexCoords left="0" right="1" bottom="0" top="1"/>
						</Texture>
						<Texture parentKey="MedalIcon">
							<Size x="102" y="102"/>
							<Anchors>
								<Anchor point="BOTTOM" relativePoint="TOP" x="0" y="-22"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame parentKey="SpellBucketFrame" setAllPoints="true" alpha="0">
				<Animations>
					<AnimationGroup parentKey="bucketUnlocked">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.7" startDelay="1.5" endDelay="2.5" order="1"/>
						<Alpha fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
						<Scripts>
							<OnPlay>
								LevelUpDisplayGLine.grow:Play();
							</OnPlay>
							<OnFinished>
								LevelUpDisplay_AnimStep(LevelUpDisplay, false);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
				</Animations>
				<Size x="230" y="44"/>
				<Frames>
					<Frame parentKey="SpellBucketDisplay" setAllPoints="true">
						<Layers>
							<Layer level="ARTWORK">
								<FontString parentKey="Name" inherits="GameFontNormal" justifyH="CENTER">
									<Size x="0" y="20"/>
									<Anchors>
										<Anchor point="TOP" x="0" y="-14"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Frame inherits="SpellBucketSpellTemplate"/>
						</Frames>
					</Frame>
					<Frame parentKey="AllAbilitiesUnlocked" setAllPoints="true">
						<Layers>
							<Layer level="ARTWORK">
								<Texture name="$parentIcon" parentKey="icon">
									<Size x="36" y="36"/>
									<Anchors>
										<Anchor point="CENTER" x="-96" y="0"/>
									</Anchors>
								</Texture>
							
								<FontString name="$parentName" inherits="GameFontNormalLarge" justifyH="LEFT" parentKey="name" text="SPELL_BUCKET_ALL_ABILITIES_UNLOCKED">
									<Anchors>
										<Anchor point="LEFT"  relativeTo="$parentIcon" relativePoint="RIGHT" x="10" y="0"/>
									</Anchors>
									<Color r="0" g="1" b="0"/>
								</FontString>
							</Layer>
							<Layer level="OVERLAY" textureSubLevel="2">
								<Texture name="$parentSubIcon" parentKey="subIcon" file="Interface\LevelUp\LevelUpTex">
									<Size x="22" y="22"/>
									<Anchors>
										<Anchor point="CENTER" relativeTo="$parentIcon" relativePoint="BOTTOMLEFT" x="2" y="2"/>
									</Anchors>
								</Texture>
							</Layer>
						</Layers>
					</Frame>
				</Frames>
			</Frame>
			<Frame name="$parentSpellFrame" inherits="LevelUpSkillTemplate" parentKey="spellFrame" alpha="0">
				<Anchors>
					<Anchor point="BOTTOM" x="0" y="5"/>
				</Anchors>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="LevelUpDisplay_OnLoad"/>
			<OnEvent function="LevelUpDisplay_OnEvent"/>
		</Scripts>
	</Frame>
	

	
	<Button name="LevelUpDisplaySide" enableMouse="true" toplevel="true" parent="UIParent" hidden="true">
		<Animations>
			<AnimationGroup parentKey="fadeIn">
				<Alpha fromAlpha="1" toAlpha="0" duration="0.0" order="1" />
				<Alpha fromAlpha="0" toAlpha="1" duration="0.5" order="2" />
				<Scripts>
					<OnFinished>
						LevelUpDisplaySide_AnimStep(LevelUpDisplaySide);
					</OnFinished>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="fadeOut">
				<Alpha fromAlpha="1" toAlpha="0" duration="1.0" order="1" />
				<Scripts>
					<OnFinished>
						LevelUpDisplaySide:Hide();
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Size x="270" y="65"/>
		<Anchors>
			<Anchor point="TOPRIGHT"  relativeTo="LevelUpDisplay" relativePoint="TOPLEFT" x="20" y="20"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture file="Interface\LevelUp\LevelUpTex" name="$parentGoldBg" parentKey="goldBG">
					<Anchors>
						<Anchor point="TOP" x="0" y="53"/>
					</Anchors>
					<Size x="223" y="115"/>
				</Texture>
				<Texture file="Interface\LevelUp\LevelUpTex" name="$parentBlackBg">
					<Size x="284" y="296"/>
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentGoldBg" relativePoint="BOTTOM"/>
					</Anchors>
					<Color r="1.0" g="1.0" b="1.0" a="0.6"/>
					<TexCoords left="0.00195313" right="0.55664063" top="0.24218750" bottom="0.82031250"/>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="2">
				<Texture file="Interface\LevelUp\LevelUpTex" name="$parentDot" parentKey="dot">
					<Size x="21" y="22"/>
					<Anchors>
						<Anchor point="CENTER" relativeTo="$parentGoldBg" relativePoint="BOTTOM" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString name="$parentLevel" inherits="GameFont_Gigantic" justifyH="CENTER" parentKey="levelText">
					<Anchors>
						<Anchor point="BOTTOM" relativeTo="$parentGoldBg" relativePoint="BOTTOM" x="0" y="5"/>
					</Anchors>
					<Color r="1.0" g="0.82" b="0"/>
				</FontString>
				<FontString inherits="SystemFont_Shadow_Large" justifyH="CENTER" parentKey="reachedText">
					<Anchors>
						<Anchor point="BOTTOM" relativeTo="$parentLevel" relativePoint="TOP" x="0" y="5"/>
					</Anchors>
				</FontString>
				<FontString parentKey="spellBucketName" inherits="GameFontNormalLarge" justifyH="CENTER" hidden="true">
					<Anchors>
						<Anchor point="BOTTOM" relativeTo="$parentGoldBg" relativePoint="BOTTOM" x="0" y="5"/>
					</Anchors>
				</FontString>
				<FontString parentKey="abilitiesUnlocked" inherits="GameFontHighlight" justifyH="CENTER" text="SPELL_BUCKET_ABILITIES_UNLOCKED" hidden="true">
					<Anchors>
						<Anchor point="BOTTOM" relativeKey="$parent.spellBucketName" relativePoint="TOP" x="0" y="5"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentUnlockFrame1" inherits="LevelUpSkillTemplate" alpha="0">
				<Anchors>
					<Anchor point="TOP"  relativeTo="$parentGoldBg" relativePoint="BOTTOM" x="-20" y="-3"/>
				</Anchors>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self:RegisterForClicks("LeftButtonDown", "LeftButtonUp");
			</OnLoad>
			<OnHide function="LevelUpDisplaySide_OnHide"/>
			<OnShow function="LevelUpDisplaySide_OnShow"/>
			<OnClick function="LevelUpDisplaySide_Remove"/>
		</Scripts>
	</Button>

	<Frame name="BossBannerLootFrameTemplate" parentArray="LootFrames" virtual="true">
		<Size x="269" y="44"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Background" alphaMode="BLEND" atlas="LootBanner-ItemBg" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="Icon">
					<Size x="37" y="37"/>
					<Anchors>
						<Anchor point="LEFT" x="124" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Count" inherits="NumberFontNormal" justifyH="RIGHT" hidden="true">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.Icon" x="-5" y="2"/>
					</Anchors>
				</FontString>
				<FontString parentKey="ItemName" inherits="GameFontNormalMed2" maxLines="1" justifyH="LEFT">
					<Size x="204" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="56" y="-7"/>
					</Anchors>
				</FontString>
				<FontString parentKey="SetName" inherits="GameFontHighlight" maxLines="1" justifyH="LEFT" hidden="true">
					<Size x="204" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.ItemName" relativePoint="BOTTOMLEFT" x="0" y="0"/>
					</Anchors>
					<Color r="0" g="1.0" b="0"/>
				</FontString>
				<FontString parentKey="PlayerName" inherits="GameFontNormal" maxLines="1" justifyH="LEFT">
					<Size x="204" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.ItemName" relativePoint="BOTTOMLEFT" x="0" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="IconHitBox">
				<Size x="37" y="37"/>
				<Anchors>
					<Anchor point="CENTER" relativeKey="$parent.Icon"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture parentKey="IconBorder" file="Interface\Common\WhiteIconFrame" hidden="true">
							<Size x="37" y="37"/>
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="ARTWORK">
						<Texture parentKey="Glow" alphaMode="ADD" atlas="LootBanner-IconGlow" useAtlasSize="true">
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
							<Color r="0.63921568627451" g="0.2078431372549" b="0.93333333333333"/>
						</Texture>
						<Texture parentKey="GlowWhite" alphaMode="ADD" atlas="LootBanner-IconGlow" useAtlasSize="true">
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						self.UpdateTooltip = function(owner) BossBanner_OnLootItemEnter(owner); end;
					</OnLoad>
					<OnEnter function="BossBanner_OnLootItemEnter"/>
					<OnLeave function="BossBanner_OnLootItemLeave"/>
				</Scripts>
			</Frame>
		</Frames>
		<Animations>
			<AnimationGroup parentKey="Anim" setToFinalAlpha="true">
				<Alpha childKey="Background" duration="0.45" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Icon" startDelay="0.1" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="IconHitBox.IconBorder" startDelay="0.1" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Translation childKey="Icon" startDelay="0.25" smoothing="OUT" duration="0.4" order="1" offsetX="-110" offsetY="0"/>
				<Alpha childKey="IconHitBox.Glow" smoothing="IN" duration="0.15" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="IconHitBox.Glow" startDelay="0.25" duration="1" order="1" fromAlpha="1" toAlpha="0"/>
				<Translation childKey="IconHitBox" startDelay="0.25" smoothing="OUT" duration="0.4" order="1" offsetX="-110" offsetY="0"/>
				<Alpha childKey="IconHitBox.GlowWhite" smoothing="IN" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="IconHitBox.GlowWhite" startDelay="0.25" duration="0.25" order="1" fromAlpha="1" toAlpha="0"/>
				<Scale childKey="IconHitBox.GlowWhite" smoothing="IN_OUT" duration="0.25" order="1" fromScaleX="1" fromScaleY="1" toScaleX="1.25" toScaleY="1.25"/>
				<Alpha childKey="ItemName" startDelay="0.4" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="PlayerName" startDelay="0.4" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="SetName" startDelay="0.4" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Scripts>
					<OnPlay>
						local lootFrame = self:GetParent();
						lootFrame.ItemName:SetAlpha(0);
						lootFrame.PlayerName:SetAlpha(0);
						lootFrame.SetName:SetAlpha(0);
						lootFrame.Icon:SetAlpha(0);
						lootFrame.IconHitBox.IconBorder:SetAlpha(0);
						lootFrame.Icon:SetPoint("LEFT", 124, 0);
					</OnPlay>
					<OnFinished>
						self:GetParent().Icon:SetPoint("LEFT", 14, 0);
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
	</Frame>

	<Frame name="BossBanner" parent="UIParent" hidden="true" alpha="1">
		<Size x="128" y="156"/>
		<Anchors>
			<Anchor point="TOP" x="0" y="-120"/>
		</Anchors>
		<Layers>
			<Layer level="BORDER">
				<Texture parentKey="BannerTop" hidden="false" alpha="0" alphaMode="BLEND" atlas="BossBanner-BgBanner-Top" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOP" x="0" y="-44"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BannerTopGlow" hidden="false" alpha="0" alphaMode="ADD" atlas="BossBanner-BgBanner-Top" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOP" x="0" y="-44"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BannerBottom" hidden="false" alpha="0" alphaMode="BLEND" atlas="BossBanner-BgBanner-Bottom" useAtlasSize="true">
					<Anchors>
						<Anchor point="BOTTOM" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BannerBottomGlow" hidden="false" alpha="0" alphaMode="ADD" atlas="BossBanner-BgBanner-Bottom" useAtlasSize="true">
					<Anchors>
						<Anchor point="BOTTOM" x="0" y="0"/>
					</Anchors>
				</Texture>	
			</Layer>
			<Layer level="BACKGROUND">
				<Texture parentKey="BannerMiddle" alpha="0" atlas="BossBanner-BgBanner-Mid" alphaMode="BLEND" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BannerTop" x="0" y="-34"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BannerBottom" x="0" y="25"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BannerMiddleGlow" alpha="0" atlas="BossBanner-BgBanner-Mid" alphaMode="ADD" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BannerTop" x="0" y="-34"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BannerBottom" x="0" y="25"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="SkullCircle" hidden="false" alpha="0" alphaMode="BLEND" atlas="BossBanner-SkullCircle" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.BannerTop" x="0" y="36"/>
					</Anchors>
				</Texture>
				<Texture parentKey="LootCircle" hidden="false" alpha="0" alphaMode="BLEND" atlas="LootBanner-LootBagCircle" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.BannerTop" x="0" y="36"/>
					</Anchors>
				</Texture>				
				<Texture parentKey="RedFlash" hidden="false" alpha="0" alphaMode="ADD" atlas="BossBanner-RedFlash" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.SkullCircle" x="0" y="3"/>
					</Anchors>
				</Texture>
			</Layer>			
			<Layer level="ARTWORK">
				<Texture parentKey="BottomFillagree" hidden="false" alpha="0" alphaMode="BLEND" atlas="BossBanner-BottomFillagree" useAtlasSize="true">
					<Anchors>
						<Anchor point="BOTTOM" x="0" y="8"/>
					</Anchors>
				</Texture>
				<Texture parentKey="SkullSpikes" hidden="false" alpha="0" alphaMode="BLEND" atlas="BossBanner-SkullSpikes" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.SkullCircle" x="-1" y="6"/>
					</Anchors>
				</Texture>
				<Texture parentKey="RightFillagree" hidden="false" alpha="0" alphaMode="BLEND" atlas="BossBanner-RightFillagree" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.SkullCircle" x="10" y="6"/>
					</Anchors>
				</Texture>
				<Texture parentKey="LeftFillagree" hidden="false" alpha="0" alphaMode="BLEND" atlas="BossBanner-LeftFillagree" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.SkullCircle" x="-10" y="6"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Title" inherits="QuestFont_Enormous" text="BOSS_YOU_DEFEATED">
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.BannerTop" x="0" y="-47"/>
					</Anchors>
					<Color r="1" g="0" b="0" a="0"/>
				</FontString>
				<FontString parentKey="SubTitle" inherits="GameFontNormalLarge" text="BOSS_KILL_SUBTITLE">
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.BottomFillagree" relativePoint="BOTTOM" x="0" y="0"/>
					</Anchors>
					<Color r="1" g="0" b="0" a="0"/>
				</FontString>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="2">
				<Texture parentKey="FlashBurst" hidden="false" alpha="0.01" alphaMode="ADD" atlas="BossBanner-RedLightning" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.SkullSpikes" x="15" y="-4"/>
					</Anchors>
				</Texture>
				<Texture parentKey="FlashBurstLeft" hidden="false" alpha="0.01" alphaMode="ADD" atlas="BossBanner-RedLightning" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.SkullSpikes" x="-15" y="-4"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="3">
				<Texture parentKey="FlashBurstCenter" hidden="false" alpha="0.01" alphaMode="ADD" atlas="BossBanner-RedLightning" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.SkullSpikes"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="4">
				<Texture parentKey="RedFlash" hidden="false" alpha="0.01" alphaMode="ADD" atlas="BossBanner-RedFlash" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.SkullSpikes" x="1" y="-4"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame inherits="BossBannerLootFrameTemplate" hidden="true">
				<Anchors>
					<Anchor point="TOP" x="0" y="-84"/>
				</Anchors>
			</Frame>
		</Frames>
		<Animations>
			<AnimationGroup parentKey="AnimIn" setToFinalAlpha="true">
				<Scale childKey="SkullCircle" duration="0.15" order="1" fromScaleX="5" fromScaleY="5" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="SkullCircle" duration="0.1" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="BannerTop" startDelay="0.2" duration="0.25" order="2" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="BannerTop" startDelay="0.1" duration="0.3" order="2" fromScaleX="0.1" fromScaleY="1" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="BannerBottom" startDelay="0.2" duration="0.25" order="2" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="BannerBottom" startDelay="0.1" duration="0.3" order="2" fromScaleX="0.1" fromScaleY="1" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="BannerMiddle" startDelay="0.2" duration="0.25" order="2" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="BannerMiddle" startDelay="0.1" duration="0.3" order="2" fromScaleX="0.1" fromScaleY="1" toScaleX="1" toScaleY="1"/>				
				<Alpha childKey="BottomFillagree" duration="0.15" order="2" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="SkullSpikes" startDelay="0.1" duration="0.1" order="2" fromScaleX="0.5" fromScaleY="0.5" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="SkullSpikes" duration="0.1" order="2" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="RightFillagree" duration="0.1" order="2" fromAlpha="0" toAlpha="1"/>
				<Translation childKey="RightFillagree" startDelay="0.15" duration="0.15" order="2" offsetX="37" offsetY="0"/>
				<Scale childKey="RightFillagree" startDelay="0.15" duration="0.15" order="2" fromScaleX="0.5" fromScaleY="0.5" toScaleX="1" toScaleY="1">
					<Origin point="BOTTOMLEFT">
						<Offset x="0" y="0"/>
					</Origin>
				</Scale>
				<Alpha childKey="LeftFillagree" duration="0.1" order="2" fromAlpha="0" toAlpha="1"/>
				<Translation childKey="LeftFillagree" startDelay="0.15" duration="0.15" order="2" offsetX="-37" offsetY="0"/>
				<Scale childKey="LeftFillagree" startDelay="0.15" duration="0.15" order="2" fromScaleX="0.5" fromScaleY="0.5" toScaleX="1" toScaleY="1">
					<Origin point="BOTTOMRIGHT">
						<Offset x="0" y="0"/>
					</Origin>
				</Scale>
				<Alpha childKey="BannerTopGlow" startDelay="0.9" duration="0.25" order="2" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="BannerTopGlow" startDelay="0.9" duration="0.5" order="2" fromScaleX="0.5" fromScaleY="1" toScaleX="1.6" toScaleY="1"/>
				<Alpha childKey="BannerTopGlow" startDelay="1.1" duration="0.6" order="2" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="BannerBottomGlow" startDelay="0.9" duration="0.25" order="2" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="BannerBottomGlow" startDelay="0.9" duration="0.5" order="2" fromScaleX="0.5" fromScaleY="1" toScaleX="1.6" toScaleY="1"/>
				<Alpha childKey="BannerBottomGlow" startDelay="1.1" duration="0.6" order="2" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="BannerMiddleGlow" startDelay="0.9" duration="0.25" order="2" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="BannerMiddleGlow" startDelay="0.9" duration="0.5" order="2" fromScaleX="0.5" fromScaleY="1" toScaleX="1.6" toScaleY="1"/>
				<Alpha childKey="BannerMiddleGlow" startDelay="1.1" duration="0.6" order="2" fromAlpha="1" toAlpha="0"/>							
				<Alpha childKey="Title" startDelay="0.2" duration="0.25" order="2" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="SubTitle" startDelay="0.2" duration="0.25" order="2" fromAlpha="0" toAlpha="1"/>

				<Alpha childKey="RedFlash" duration="0.1" order="2" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="RedFlash" smoothing="IN" duration="0.25" order="2" fromScaleX="2.5" fromScaleY="2.5" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="RedFlash" startDelay="0.25" duration="0.5" order="2" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="FlashBurst" startDelay="0.25" duration="0.25" order="2" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="FlashBurst" startDelay="0.25" duration="0.4" order="2" fromScaleX="1" fromScaleY="0.75" toScaleX="1.25" toScaleY="0.75">
					<Origin point="LEFT">
						<Offset x="0" y="0"/>
					</Origin>
				</Scale>
				<Translation childKey="FlashBurst" startDelay="0.25" duration="0.5" order="2" offsetX="10" offsetY="0"/>
				<Alpha childKey="FlashBurst" startDelay="0.25" duration="0.4" order="2" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="FlashBurstLeft" startDelay="0.25" duration="0.25" order="2" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="FlashBurstLeft" startDelay="0.25" duration="0.5" order="2" fromScaleX="1" fromScaleY="0.75" toScaleX="1.25" toScaleY="0.75">
					<Origin point="RIGHT">
						<Offset x="0" y="0"/>
					</Origin>
				</Scale>
				<Translation childKey="FlashBurstLeft" startDelay="0.25" duration="0.5" order="2" offsetX="-10" offsetY="0"/>
				<Alpha childKey="FlashBurstLeft" startDelay="0.25" duration="0.5" order="2" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="FlashBurstCenter" startDelay="0.25" duration="0.25" order="2" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="FlashBurstCenter" startDelay="0.25" duration="0.5" order="2" fromScaleX="1" fromScaleY="1" toScaleX="1.25" toScaleY="1.25"/>
				<Alpha childKey="FlashBurstCenter" startDelay="0.25" duration="0.5" order="2" fromAlpha="1" toAlpha="0"/>
				
				<Scripts>
					<OnPlay>
						local banner = self:GetParent();
						banner.LeftFillagree:SetPoint("CENTER", banner.SkullCircle, "CENTER", -10, 6);
						banner.RightFillagree:SetPoint("CENTER", banner.SkullCircle, "CENTER", 10, 6);
						banner.LootCircle:SetAlpha(0);
					</OnPlay>
					<OnFinished>
						local banner = self:GetParent();
						banner.LeftFillagree:SetPoint("CENTER", banner.SkullCircle, "CENTER", -47, 6);
						banner.RightFillagree:SetPoint("CENTER", banner.SkullCircle, "CENTER", 47, 6);
					</OnFinished>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="AnimSwitch" setToFinalAlpha="true">
				<Alpha childKey="SkullCircle" duration="0.5" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Title" duration="0.25" order="1" fromAlpha="1" toAlpha="0"/>	
				<Alpha childKey="SubTitle" duration="0.25" order="1" fromAlpha="1" toAlpha="0"/>				
				<Alpha childKey="LootCircle" duration="0.5" order="1" fromAlpha="0" toAlpha="1"/>
			</AnimationGroup>
			<AnimationGroup parentKey="AnimOut">
				<Alpha duration="0.5" order="1" fromAlpha="1" toAlpha="0"/>
				<Scripts>
					<OnFinished function="BossBanner_OnAnimOutFinished"/>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Scripts>
			<OnLoad function="BossBanner_OnLoad"/>
			<OnEvent function="BossBanner_OnEvent"/>
			<OnUpdate function="BossBanner_OnUpdate"/>
		</Scripts>
	</Frame>
</Ui>
