<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="LFDFrame.lua"/>
	<Button name="LFDRoleButtonTemplate" inherits="LFGRoleButtonWithBackgroundAndRewardTemplate" virtual="true">
		<Scripts>
			<OnLoad>
				self.background:SetTexCoord(GetBackgroundTexCoordsForRole(self.role));
				self.background:SetAlpha(0.6);
				self.checkButton.onClick = LFDFrameRoleCheckButton_OnClick;
				LFGRoleButtonTemplate_OnLoad(self);
			</OnLoad>
			<OnEnter function="LFDRoleButton_OnEnter"/>
		</Scripts>
	</Button>
	<Button name="LFDRoleCheckPopupButtonTemplate" inherits="LFGRoleButtonTemplate" virtual="true">
		<Scripts>
			<OnLoad>
				self.checkButton.onClick = LFDFramePopupRoleCheckButton_OnClick;
				LFGRoleButtonTemplate_OnLoad(self);
			</OnLoad>
		</Scripts>
	</Button>
	<Frame name="LFDFrameDungeonChoiceTemplate" enableMouse="true" inherits="LFGSpecificChoiceTemplate" virtual="true">
		<Scripts>
			<OnLoad>
				self.enableButton:SetScript("OnClick", LFDQueueFrameDungeonChoiceEnableButton_OnClick);
				self.expandOrCollapseButton:SetScript("OnClick", LFDQueueFrameExpandOrCollapseButton_OnClick);
				self:SetScript("OnEnter", LFDQueueFrameDungeonListButton_OnEnter);
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="LFDRoleCheckPopup" parent="UIParent" frameStrata="DIALOG" hidden="true">
		<Size>
			<AbsDimension x="306" y="180"/>
		</Size>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString parentKey="Text" inherits="GameFontHighlight" justifyH="CENTER" text="CONFIRM_YOUR_ROLE">
					<Size x="266" y="0"/>
					<Anchors>
						<Anchor point="TOP" x="0" y="-15"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="$parentRoleButtonTank" inherits="LFDRoleCheckPopupButtonTemplate" id="1">
				<Size x="70" y="70"/>
				<KeyValues>
					<KeyValue key="role" value="TANK" type="string"/>
				</KeyValues>
				<Anchors>
					<Anchor point="TOP" relativeKey="$parent.Text" relativePoint="BOTTOM" x="0" y="-8"/>
					<Anchor point="LEFT" x="35" y="0"/>
				</Anchors>
				<Scripts>
					<OnEnter function="LFDPopupRoleCheckButton_OnEnter"/>
				</Scripts>
			</Button>
			<Button name="$parentRoleButtonHealer" inherits="LFDRoleCheckPopupButtonTemplate" id="2">
				<Size x="70" y="70"/>
				<KeyValues>
					<KeyValue key="role" value="HEALER" type="string"/>
				</KeyValues>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentRoleButtonTank" relativePoint="RIGHT" x="15" y="0"/>
				</Anchors>
				<Scripts>
					<OnEnter function="LFDPopupRoleCheckButton_OnEnter"/>
				</Scripts>
			</Button>
			<Button name="$parentRoleButtonDPS" inherits="LFDRoleCheckPopupButtonTemplate" id="3">
				<Size x="70" y="70"/>
				<KeyValues>
					<KeyValue key="role" value="DAMAGER" type="string"/>
				</KeyValues>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentRoleButtonHealer" relativePoint="RIGHT" x="15" y="0"/>
				</Anchors>
				<Scripts>
					<OnEnter function="LFDPopupRoleCheckButton_OnEnter"/>
				</Scripts>
			</Button>
			<Button name="$parentAcceptButton" inherits="UIPanelButtonTemplate" text="ACCEPT" motionScriptsWhileDisabled="true">
				<Size>
					<AbsDimension x="115" y="22"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" relativeTo="$parent" relativePoint="BOTTOM">
						<Offset>
							<AbsDimension x="-3" y="15"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="LFDRoleCheckPopupAccept_OnClick"/>
					<OnEnter>
						if ( self.tooltipText ) then
							GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
							GameTooltip:SetText(self.tooltipText, nil, nil, nil, nil, true);
							GameTooltip:Show();
						end
					</OnEnter>
				</Scripts>
			</Button>
			<Button name="$parentDeclineButton" inherits="UIPanelButtonTemplate" text="DECLINE">
				<Size>
					<AbsDimension x="115" y="22"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentAcceptButton" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="6" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="LFDRoleCheckPopupDecline_OnClick"/>
				</Scripts>
			</Button>
			<Frame name="$parentDescription">
				<Size>
					<AbsDimension x="1" y="16"/>
				</Size>
				<Anchors>
					<Anchor point="CENTER" relativeTo="$parent" relativePoint="BOTTOM">
						<Offset>
							<AbsDimension x="0" y="57"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentText" justifyH="CENTER" inherits="GameFontHighlight">
							<Size>
								<AbsDimension x="280" y="25"/>
							</Size>
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
		<Scripts>
			<OnShow function="LFDRoleCheckPopup_OnShow"/>
			<OnHide function="LFDRoleCheckPopup_OnHide"/>
			<OnEvent function="LFDRoleCheckPopup_OnEvent"/>
		</Scripts>
	</Frame>
	<Frame name="LFDReadyCheckPopup" parent="UIParent" frameStrata="DIALOG" hidden="true">
		<Size x="306" y="100"/>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString parentKey="Text" inherits="GameFontHighlight" justifyV="MIDDLE">
					<Size x="240" y="0"/>
					<Anchors>
						<Anchor point="CENTER" relativePoint="TOP" x="0" y="-35"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="YesButton" inherits="UIPanelButtonTemplate" text="READY">
				<Size x="119" y="24"/>
				<Anchors>
					<Anchor point="TOPRIGHT" relativePoint="TOP" x="-7" y="-55"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						CompleteLFGReadyCheck(true);
						LFDReadyCheckPopup:Hide();
					</OnClick>
				</Scripts>
			</Button>
			<Button parentKey="NoButton" inherits="UIPanelButtonTemplate" text="NOT_READY">
				<Size x="119" y="24"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativePoint="TOP" x="7" y="-55"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						CompleteLFGReadyCheck(false);
						LFDReadyCheckPopup:Hide();
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnShow>
				PlaySound(SOUNDKIT.READY_CHECK);
				FlashClientIcon();
			</OnShow>
		</Scripts>
	</Frame>
	<Frame name="LFDParentFrame" hidden="true" parent="GroupFinderFrame" useParentLevel="true">
		<Size x="338" y="428"/>
		<Anchors>
			<Anchor point="TOPLEFT" x="224" y="0"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentRoleBackground" file="Interface\LFGFrame\UI-LFG-BlueBG">
					<Size x="512" y="128"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="2" y="275"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture name="$parentTopTileStreaks" inherits="_UI-Frame-TopTileStreaks" parentKey="TopTileStreaks">
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="-21"/>
						<Anchor point="TOPRIGHT" x="-2" y="-21"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBtnCornerRight" inherits="UI-Frame-BtnCornerRight" hidden="false">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="1" y="-1"/>
					</Anchors>
				</Texture>
				<Texture name="$parentButtonBottomBorder" inherits="_UI-Frame-BtnBotTile">
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-23" y="2"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentBtnCornerRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentInset" useParentLevel="true" inherits="InsetFrameTemplate" parentKey="Inset">
				<Anchors>
					<Anchor point="TOPLEFT" x="4" y="-60" />
					<Anchor point="BOTTOMRIGHT" x="-6" y="26" />
				</Anchors>
			</Frame>
			<Frame name="LFDQueueFrame" setAllPoints="true">
				<Layers>
					<Layer level="BORDER">
						<Texture name="$parentBackground" file="Interface\LFGFrame\UI-LFG-BACKGROUND-QUESTPAPER">
							<Size>
								<AbsDimension x="512" y="256"/>
							</Size>
							<Anchors>
								<Anchor point="BOTTOMLEFT">
									<Offset>
										<AbsDimension x="6" y="26"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentRoleButtonTank" inherits="LFDRoleButtonTemplate" id="1">
						<KeyValues>
							<KeyValue key="role" value="TANK" type="string"/>
						</KeyValues>
						<Anchors>
							<Anchor point="BOTTOMLEFT">
								<Offset>
									<AbsDimension x="50" y="334"/>
								</Offset>
							</Anchor>
						</Anchors>
					</Button>
					<Button name="$parentRoleButtonHealer" inherits="LFDRoleButtonTemplate" id="2">
						<KeyValues>
							<KeyValue key="role" value="HEALER" type="string"/>
						</KeyValues>
						<Anchors>
							<Anchor point="LEFT" relativeTo="$parentRoleButtonTank" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="25" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</Button>
					<Button name="$parentRoleButtonDPS" inherits="LFDRoleButtonTemplate" id="3">
						<KeyValues>
							<KeyValue key="role" value="DAMAGER" type="string"/>
						</KeyValues>
						<Anchors>
							<Anchor point="LEFT" relativeTo="$parentRoleButtonHealer" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="23" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</Button>
					<Button name="$parentRoleButtonLeader" inherits="LFGRoleButtonTemplate" id="4">
						<Anchors>
							<Anchor point="LEFT" relativeTo="$parentRoleButtonDPS" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="32" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								self:GetNormalTexture():SetTexCoord(GetTexCoordsForRole("GUIDE"));
								self.checkButton.onClick = LFDFrameRoleCheckButton_OnClick;
							</OnLoad>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								GameTooltip:SetText(GUIDE_TOOLTIP, nil, nil, nil, nil, true);
								LFGFrameRoleCheckButton_OnEnter(self);
							</OnEnter>
						</Scripts>
					</Button>
					<Frame name="$parentTypeDropDown" inherits="UIDropDownMenuTemplate" id="1">
						<Anchors>
							<Anchor point="BOTTOMLEFT">
								<Offset>
									<AbsDimension x="117" y="285"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Layers>
							<Layer level="OVERLAY">
								<FontString name="$parentName" inherits="GameFontNormal" justifyH="RIGHT" text="CHOOSE_YOUR_DUNGEON">
									<Size>
										<AbsDimension x="115" y="24"/>
									</Size>
									<Anchors>
										<Anchor point="RIGHT" relativePoint="LEFT">
											<Offset>
												<AbsDimension x="0" y="2"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnShow function="LFDQueueFrameTypeDropDown_SetUp"/>
						</Scripts>
					</Frame>
					<Frame name="$parentRandom" setAllPoints="true">
						<Frames>
							<ScrollFrame name="$parentScrollFrame" inherits="UIPanelScrollFrameTemplate">
								<Size>
									<AbsDimension x="303" y="239"/>
								</Size>
								<Anchors>
									<Anchor point="BOTTOMRIGHT">
										<Offset>
											<AbsDimension x="-29" y="35"/>
										</Offset>
									</Anchor>
								</Anchors>
								<Layers>
									<Layer level="BACKGROUND">
										<Texture name="$parentScrollBackground">
											<Anchors>
												<Anchor point="TOPLEFT" relativePoint="TOPRIGHT">
													<Offset>
														<AbsDimension x="-4" y="7"/>
													</Offset>
												</Anchor>
												<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT">
													<Offset>
														<AbsDimension x="24" y="-9"/>
													</Offset>
												</Anchor>
											</Anchors>
											<Color r="0" b="0" g="0" a=".65"/>
										</Texture>
									</Layer>
									<Layer level="ARTWORK">
										<Texture name="$parentScrollBackgroundTopLeft" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
											<Size>
												<AbsDimension x="31" y="256"/>
											</Size>
											<Anchors>
												<Anchor point="TOPLEFT" relativePoint="TOPRIGHT">
													<Offset>
														<AbsDimension x="-5" y="12"/>
													</Offset>
												</Anchor>
											</Anchors>
											<TexCoords left="0" right="0.484375" top="0" bottom="1.0"/>
										</Texture>
										<Texture name="$parentScrollBackgroundBottomRight" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
											<Size>
												<AbsDimension x="31" y="106"/>
											</Size>
											<Anchors>
												<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT">
													<Offset>
														<AbsDimension x="-5" y="-11"/>
													</Offset>
												</Anchor>
											</Anchors>
											<TexCoords left="0.515625" right="1.0" top="0" bottom="0.4140625"/>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnLoad>
										local myName = self:GetName();
										_G[myName.."ScrollBackground"]:SetParent(_G[myName.."ScrollBar"]);
										_G[myName.."ScrollBackgroundTopLeft"]:SetParent(_G[myName.."ScrollBar"]);
										_G[myName.."ScrollBackgroundBottomRight"]:SetParent(_G[myName.."ScrollBar"]);

										local scrollBar = _G[myName.."ScrollBar"];
										scrollBar:ClearAllPoints();
										scrollBar:SetPoint("BOTTOMLEFT", self, "BOTTOMRIGHT", 3, 7);
										scrollBar:SetPoint("TOPLEFT", self, "TOPRIGHT", 3, -9);

										self.scrollBarHideable = true;
										ScrollFrame_OnLoad(self);
									</OnLoad>
								</Scripts>
								<ScrollChild>
									<Frame name="$parentChildFrame" inherits="LFGRewardFrameTemplate">
										<Scripts>
											<OnLoad>
												--Move the title to deal with an offset so scrolling doesn't look as bad with the LFD border.
												self.title:SetPoint("TOPLEFT", 10, -8);
												LFGRewardsFrame_OnLoad(self);
											</OnLoad>
											<OnShow>
												LFDQueueFrameRandom_UpdateFrame();
											</OnShow>
										</Scripts>
									</Frame>
								</ScrollChild>
							</ScrollFrame>
						</Frames>
					</Frame>
					<Frame name="$parentSpecific" setAllPoints="true" hidden="true">
						<Frames>
							<Frame name="$parentListButton1" inherits="LFDFrameDungeonChoiceTemplate" id="1">
								<Anchors>
									<Anchor point="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="10" y="256"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton2" inherits="LFDFrameDungeonChoiceTemplate" id="2">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton1" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton3" inherits="LFDFrameDungeonChoiceTemplate" id="3">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton2" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton4" inherits="LFDFrameDungeonChoiceTemplate" id="4">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton3" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton5" inherits="LFDFrameDungeonChoiceTemplate" id="5">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton4" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton6" inherits="LFDFrameDungeonChoiceTemplate" id="6">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton5" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton7" inherits="LFDFrameDungeonChoiceTemplate" id="7">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton6" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton8" inherits="LFDFrameDungeonChoiceTemplate" id="8">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton7" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton9" inherits="LFDFrameDungeonChoiceTemplate" id="9">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton8" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton10" inherits="LFDFrameDungeonChoiceTemplate" id="10">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton9" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton11" inherits="LFDFrameDungeonChoiceTemplate" id="11">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton10" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton12" inherits="LFDFrameDungeonChoiceTemplate" id="12">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton11" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton13" inherits="LFDFrameDungeonChoiceTemplate" id="13">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton12" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton14" inherits="LFDFrameDungeonChoiceTemplate" id="14">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton13" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<Frame name="$parentListButton15" inherits="LFDFrameDungeonChoiceTemplate" id="15">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton14" relativePoint="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="0" y="0"/>
										</Offset>
									</Anchor>
								</Anchors>
							</Frame>
							<ScrollFrame name="$parentListScrollFrame" inherits="FauxScrollFrameTemplate">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parentListButton1" relativePoint="TOPLEFT">
										<Offset>
											<AbsDimension x="0" y="7"/>
										</Offset>
									</Anchor>
									<Anchor point="BOTTOMRIGHT" relativeTo="$parentListButton15" relativePoint="BOTTOMRIGHT">
										<Offset>
											<AbsDimension x="1" y="-7"/>
										</Offset>
									</Anchor>
								</Anchors>
								<Layers>
									<Layer level="BACKGROUND">
										<Texture name="$parentScrollBackgroundTopLeft" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
											<Size>
												<AbsDimension x="31" y="256"/>
											</Size>
											<Anchors>
												<Anchor point="TOPLEFT" relativePoint="TOPRIGHT">
													<Offset>
														<AbsDimension x="-2" y="5"/>
													</Offset>
												</Anchor>
											</Anchors>
											<TexCoords left="0" right="0.484375" top="0" bottom="1.0"/>
										</Texture>
										<Texture name="$parentScrollBackgroundBottomRight" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
											<Size>
												<AbsDimension x="31" y="106"/>
											</Size>
											<Anchors>
												<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT">
													<Offset>
														<AbsDimension x="-2" y="-2"/>
													</Offset>
												</Anchor>
											</Anchors>
											<TexCoords left="0.515625" right="1.0" top="0" bottom="0.4140625"/>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnVerticalScroll>
										FauxScrollFrame_OnVerticalScroll(self, offset, 16, LFDQueueFrameSpecificList_Update);
									</OnVerticalScroll>
								</Scripts>
							</ScrollFrame>
						</Frames>
						<Scripts>
							<OnShow function="LFDQueueFrame_Update"/>
						</Scripts>
					</Frame>
					<Button name="$parentFindGroupButton" inherits="MagicButtonTemplate" text="FIND_A_GROUP" motionScriptsWhileDisabled="true">
						<Size>
							<AbsDimension x="135" y="22"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOM">
								<Offset>
									<AbsDimension x="0" y="4"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnClick>
								local mode, subMode = GetLFGMode(LE_LFG_CATEGORY_LFD);
								if ( mode == "queued" or mode == "listed" or mode == "rolecheck" or mode == "suspended" ) then
									LeaveLFG(LE_LFG_CATEGORY_LFD);
								else
									LFDQueueFrame_Join();
								end
							</OnClick>
							<OnEnter>
								if ( self.tooltip ) then
									GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
									GameTooltip:SetText(self.tooltip, NORMAL_FONT_COLOR.r, NORMAL_FONT_COLOR.g, NORMAL_FONT_COLOR.b, 1, true);
									GameTooltip:Show();
								end
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</Button>
					<Frame name="$parentPartyBackfill" parentKey="PartyBackfill" inherits="LFGBackfillCoverTemplate">
						<Size>
							<AbsDimension x="330" y="257"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMRIGHT">
								<Offset>
									<AbsDimension x="-6" y="27"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								LFGBackfillCover_SetUp(self, {LFG_SUBTYPEID_DUNGEON, LFG_SUBTYPEID_HEROIC}, LE_LFG_CATEGORY_LFD, LFDQueueFrameFindGroupButton_Update);
							</OnLoad>
						</Scripts>
					</Frame>
					<Frame name="$parentCooldownFrame" parentKey="CooldownFrame" inherits="LFGCooldownCoverTemplate">
						<Size>
							<AbsDimension x="330" y="257"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMRIGHT">
								<Offset>
									<AbsDimension x="-6" y="27"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								LFGCooldownCover_SetUp(self, self:GetParent().PartyBackfill);
							</OnLoad>
							<OnShow>
								LFDQueueFrameFindGroupButton_Update();
							</OnShow>
							<OnHide>
								LFDQueueFrameFindGroupButton_Update();
							</OnHide>
						</Scripts>
					</Frame>
					<Frame name="$parentNoLFDWhileLFR" hidden="true" enableMouse="true">
						<Size>
							<AbsDimension x="330" y="257"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMRIGHT">
								<Offset>
									<AbsDimension x="-6" y="27"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentBlackFilter" setAllPoints="true">
									<Color r="0" b="0" g="0" a="0.93"/>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<FontString name="$parentDescription" inherits="GameFontNormal" text="NO_LFD_WHILE_LFR" justifyH="CENTER">
									<Size>
										<AbsDimension x="300" y="0"/>
									</Size>
									<Anchors>
										<Anchor point="TOP">
											<Offset>
												<AbsDimension x="-0" y="-70"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Button name="$parentLeaveQueueButton" inherits="UIPanelButtonTemplate" text="LIST_ME">
								<Size>
									<AbsDimension x="153" y="22"/>
								</Size>
								<Anchors>
									<Anchor point="TOP" relativeTo="$parentDescription" relativePoint="BOTTOM">
										<Offset>
											<AbsDimension x="0" y="-10"/>
										</Offset>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnClick>
											LeaveLFG(LE_LFG_CATEGORY_LFR);
									</OnClick>
								</Scripts>
							</Button>
						</Frames>
						<Scripts>
							<OnLoad>
								self:SetFrameLevel(16);
							</OnLoad>
						</Scripts>
					</Frame>
				</Frames>
				<Scripts>
					<OnShow>
						PlaySound(SOUNDKIT.IG_CHARACTER_INFO_OPEN);
						RequestLFDPlayerLockInfo();
						RequestLFDPartyLockInfo();
					</OnShow>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="LFDFrame_OnLoad"/>
			<OnEvent function="LFDFrame_OnEvent"/>
			<OnShow function="LFDFrame_OnShow"/>
			<OnHide function="LFDFrame_OnHide"/>
		</Scripts>
	</Frame>
</Ui>
