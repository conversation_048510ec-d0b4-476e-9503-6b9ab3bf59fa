<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="LootHistory.lua"/>
	<Button name="LootHistoryItemTemplate" virtual="true">
		<Size x="177" y="36"/>
		<HitRectInsets>
			<AbsInset left="20" right="0" top="0" bottom="0"/>
		</HitRectInsets>
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<!--RollWindow-ItemDivider-->
				<Texture parentKey="Divider" file="Interface\LootFrame\LootToast">
					<Size x="181" y="38"/>
					<Anchors>
						<Anchor point="TOP" x="0" y="1"/>
					</Anchors>
					<TexCoords left="0.00097656" right="0.17773438" top="0.74218750" bottom="0.89062500"/>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="3">
				<Texture parentKey="Icon">
					<Size x="28" y="28"/>
					<Anchors>
						<Anchor point="LEFT" x="23" y="-2"/>
					</Anchors>
				</Texture>
				<!--SmallItemNameBorder-Left-->
				<Texture parentKey="NameBorderLeft" file="Interface\LootFrame\LootToast">
					<Size x="16" y="32"/>	
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Icon" relativePoint="RIGHT" x="3" y="0"/>
					</Anchors>
					<TexCoords left="0.79101563" right="0.80664063" top="0.57421875" bottom="0.69921875"/>	
				</Texture>
				<Texture parentKey="NameBorderRight" file="Interface\LootFrame\LootToast">
					<Size x="16" y="32"/>	
					<Anchors>
						<Anchor point="RIGHT" x="0" y="-2"/>
					</Anchors>
					<TexCoords left="0.73242188" right="0.74804688" top="0.80859375" bottom="0.93359375"/>	
				</Texture>
				<Texture parentKey="NameBorderMid" file="Interface\LootFrame\LootToast">
					<Size x="16" y="32"/>	
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.NameBorderLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.NameBorderRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0.75" right="0.764648438" top="0.80859375" bottom="0.93359375"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<!--SmallItemIcon-Border-->
				<Texture parentKey="IconBorder" file="Interface\LootFrame\LootToast">
					<Size x="30" y="30"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Icon" relativePoint="CENTER" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.62792969" right="0.65722656" top="0.87500000" bottom="0.99218750"/>	
				</Texture>
				<FontString parentKey="ItemName" inherits="GameFontNormalSmall" justifyH="LEFT" justifyV="MIDDLE">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.NameBorderLeft" relativePoint="TOPLEFT" x="2" y="-2"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.NameBorderRight" relativePoint="BOTTOMRIGHT" x="-2" y="2"/>
					</Anchors>
				</FontString>
				<FontString parentKey="WinnerRoll" inherits="GameFontNormalSmall" justifyH="RIGHT">
					<Size x="24" y="16"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.NameBorderRight" relativePoint="BOTTOMRIGHT" x="-2" y="2"/>
					</Anchors>
				</FontString>
				<Texture parentKey="WinnerRollType">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="RIGHT" relativeKey="$parent.WinnerRoll" relativePoint="LEFT" x="2" y="-1"/>
					</Anchors>
				</Texture>
				<FontString parentKey="WinnerName" inherits="GameFontNormalSmall" justifyH="LEFT">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.NameBorderLeft" relativePoint="LEFT" x="2" y="0"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.WinnerRollType" relativePoint="BOTTOMLEFT" x="-2" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="2">
				<Texture parentKey="ActiveHighlight" file="Interface\LootFrame\LootHistory-NewItemGlow" alpha="0.7">
					<Size x="128" y="32"/>
					<Anchors>
						<Anchor point="BOTTOM" relativeKey="$parent.NameBorderMid" relativePoint="BOTTOM" x="2" y="-12"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="ToggleButton">
				<Size>
					<AbsDimension x="14" y="14"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT">
						<Offset>
							<AbsDimension x="5" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						LootHistoryFrame_ToggleRollExpanded(LootHistoryFrame, self:GetParent().rollID);
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-MinusButton-UP"/>
				<PushedTexture file="Interface\Buttons\UI-MinusButton-Down"/>
				<DisabledTexture file="Interface\Buttons\UI-MinusButton-Disabled"/>
				<HighlightTexture name="$parentHighlight" file="Interface\Buttons\UI-PlusButton-Hilight" alphaMode="ADD"/>
			</Button>
		</Frames>
		<Scripts>
			<OnEnter>
				if ( self.itemLink ) then
					GameTooltip:SetOwner(self, "ANCHOR_LEFT");
					GameTooltip:SetHyperlink(self.itemLink);
					GameTooltip:Show();
				end
			</OnEnter>
			<OnLeave function="GameTooltip_Hide"/>
			<OnClick>
				if ( self.itemLink and IsModifiedClick("CHATLINK") ) then
					HandleModifiedItemClick(self.itemLink);
				end
			</OnClick>
		</Scripts>
	</Button>
	<Button name="LootHistoryPlayerTemplate" virtual="true">
		<Size x="177" y="16"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="AlternatingBG">
					<Anchors>
						<Anchor point="TOPLEFT"/>
						<Anchor point="BOTTOMRIGHT" x="10" y="0"/>
					</Anchors>
					<Color r="1" g="1" b="1" a="0.08"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="RollText" inherits="GameFontNormalSmall" justifyH="RIGHT" text="94">
					<Size x="24" y="16"/>
					<Anchors>
						<Anchor point="RIGHT" x="-2" y="0"/>
					</Anchors>
				</FontString>
				<FontString parentKey="PlayerName" inherits="GameFontNormalSmall" justifyH="LEFT" text="PLAYERNAME">
					<Anchors>
						<Anchor point="LEFT" x="25" y="0"/>
						<!--Anchored in OnLoad because the RollIcon frame isn't loaded yet.
						<Anchor point="RIGHT" relativeKey="$parent.RollIcon" relativePoint="LEFT" x="-2" y="0"/>-->
					</Anchors>
				</FontString>
				<Texture parentKey="WinMark" file="Interface\Buttons\UI-CheckBox-Check">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="RIGHT" relativeKey="$parent.PlayerName" relativePoint="LEFT" x="-1" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="RollIcon">
				<Size x="16" y="16"/>
				<Anchors>
					<Anchor point="RIGHT" relativeKey="$parent.RollText" relativePoint="LEFT" x="2" y="-1"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture parentKey="Texture" setAllPoints="true"/>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						if ( self.tooltip ) then
							GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
							GameTooltip:SetText(self.tooltip, nil, nil, nil, nil, true);
							GameTooltip:Show();
						end
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self.PlayerName:SetPoint("RIGHT", self.RollIcon, "LEFT", -2, 0);
				self:RegisterForClicks("RightButtonUp");
			</OnLoad>
			<OnClick function="LootHistoryPlayerFrame_OnClick"/>
		</Scripts>
	</Button>
	<Frame name="LootHistoryFrame" movable="true" resizable="true" clampedToScreen="true" parent="UIParent" inherits="TooltipBorderedFrameTemplate" hidden="true">
		<Size x="210" y="175"/>
		<Anchors>
			<Anchor point="BOTTOM" x="280" y="180"/>
		</Anchors>
		<ResizeBounds>
			<minResize>
				<AbsDimension x="210" y="103"/>
			</minResize>
		</ResizeBounds>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="LootIcon" file="Interface\Minimap\Tracking\Banker">
					<Size x="18" y="18"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="5" y="-5"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Label" inherits="GameFontNormal" text="LOOT_ROLLS">
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.LootIcon" relativePoint="RIGHT" x="3" y="1"/>
					</Anchors>
				</FontString>
				<Texture parentKey="Divider" file="Interface\Common\UI-TooltipDivider">
					<Size x="0" y="8"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="4" y="-20"/>
						<Anchor point="TOPRIGHT" x="-4" y="-20"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="CloseButton">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="2" y="2"/>
				</Anchors>
				<Scripts>
					<OnClick>
						self:GetParent():Hide();
					</OnClick>
				</Scripts>
				<DisabledTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Disabled"/>
				<NormalTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Up"/>
				<PushedTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Down"/>
				<HighlightTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Highlight" alphaMode="ADD"/>
			</Button>
			<Button parentKey="DragButton">
				<Anchors>
					<Anchor point="TOPLEFT"/>
					<Anchor point="BOTTOMRIGHT" relativeKey="$parent.CloseButton" relativePoint="BOTTOMLEFT"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						self:RegisterForDrag("LeftButton");
					</OnLoad>
					<OnDragStart>
						self:GetParent():StartMoving();
					</OnDragStart>
					<OnDragStop>
						self:GetParent():StopMovingOrSizing();
					</OnDragStop>
				</Scripts>
			</Button>
			<Button parentKey="ResizeButton">
				<Size x="32" y="16"/>
				<Anchors>
					<Anchor point="TOP" relativePoint="BOTTOM" x="0" y="7"/>
				</Anchors>
				<Scripts>
					<OnMouseDown>
						self:GetParent():StartSizing("BOTTOM");
					</OnMouseDown>
					<OnMouseUp>
						self:GetParent():StopMovingOrSizing();
					</OnMouseUp>
				</Scripts>
				<NormalTexture file="Interface\RaidFrame\Raid-Move-Down"/>
			</Button>
			<ScrollFrame name="$parentScrollFrame" parentKey="ScrollFrame" inherits="UIPanelScrollFrameTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.Divider" relativePoint="BOTTOMLEFT" x="0" y="1"/>
					<Anchor point="BOTTOMRIGHT" relativeKey="$parent" relativePoint="BOTTOMRIGHT" x="-28" y="7"/>
				</Anchors>
				<ScrollChild>
					<Frame parentKey="ScrollChild">
						<Size x="1" y="1"/>
					</Frame>
				</ScrollChild>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture parentKey="ScrollBarBackground">
							<Anchors>
								<Anchor point="TOPLEFT" relativeKey="$parent.ScrollBar.ScrollUpButton" relativePoint="TOPLEFT" x="-2" y="2"/>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.ScrollBar.ScrollDownButton" relativePoint="BOTTOMRIGHT" x="0" y="-2"/>
							</Anchors>
							<Color r="0" g="0" b="0"/>
						</Texture>
					</Layer>
				</Layers>
			</ScrollFrame>
		</Frames>
		<Scripts>
			<OnLoad function="LootHistoryFrame_OnLoad"/>
			<OnEvent function="LootHistoryFrame_OnEvent"/>
			<OnHide function="LootHistoryFrame_OnHide"/>
		</Scripts>
	</Frame>
	<Frame name="LootHistoryDropDown" inherits="UIDropDownMenuTemplate" hidden="true">
		<Anchors>
			<Anchor point="TOPLEFT"/>
		</Anchors>
		<Scripts>
			<OnLoad function="LootHistoryDropDown_OnLoad"/>
		</Scripts>
	</Frame>	
</Ui>
