<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="LossOfControlFrame.lua"/>

	<Frame name="LossOfControlFrame" frameStrata="MEDIUM" toplevel="true" parent="UIParent" hidden="true">
		<Size>
			<AbsDimension x="256" y="58"/>
		</Size>
		<Anchors>
			<Anchor point="CENTER" x="0" y="0"/>
		</Anchors>
		<Animations>
			<AnimationGroup parentKey="Anim">
				<Scale childKey="RedLineTop" duration="0.01" scaleX="0.1" scaleY="1" order="1"/>
				<Scale childKey="RedLineBottom" duration="0.01" scaleX="0.1" scaleY="1" order="1"/>
				
				<Scale childKey="RedLineTop" duration="0.1" scaleX="14" scaleY="1" order="2"/>
				<Scale childKey="RedLineBottom" duration="0.1" scaleX="14" scaleY="1" order="2"/>
				<Translation childKey="Icon" offsetX="-20" offsetY="0" duration="0.1" order="2"/>
				<Scale childKey="Icon" scaleX="1.33" scaleY="1.33" duration="0.1" order="2"/>

				<Scale childKey="RedLineTop" duration="0.1" scaleX="0.71" scaleY="1" order="3"/>
				<Scale childKey="RedLineBottom" duration="0.1" scaleX="0.71" scaleY="1" order="3"/>
				<Translation childKey="Icon" offsetX="20" offsetY="0" duration="0.1" order="3"/>
				<Scale childKey="Icon" scaleX="0.75" scaleY="0.75" duration="0.1" order="3"/>
				<Scripts>
					<OnFinished>
						self:GetParent().Cooldown:Show();
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture file="Interface\Cooldown\LoC-ShadowBG" parentKey="blackBg">
					<Anchors>
						<Anchor point="BOTTOM" x="0" y="0"/>
					</Anchors>
					<Size x="256" y="58"/>
					<Color r="1.0" g="1.0" b="1.0" a="0.6"/>
				</Texture>
				<Texture file="Interface\Cooldown\Loc-RedLine" parentKey="RedLineTop">
					<Size x="236" y="27"/>
					<Anchors>
						<Anchor point="BOTTOM" relativePoint="TOP" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture file="Interface\Cooldown\Loc-RedLine" parentKey="RedLineBottom">
					<Size x="236" y="27"/>
					<Anchors>
						<Anchor point="TOP" relativePoint="BOTTOM" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0" right="1" top="1" bottom="0"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Icon">
					<Size x="48" y="48"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
				</Texture>
				<FontString inherits="MovieSubtitleFont" parentKey="AbilityName">
					<Size x="0" y="20"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Cooldown parentKey="Cooldown" hideCountdownNumbers="true" drawEdge="true" inherits="CooldownFrameTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.Icon"/>
					<Anchor point="BOTTOMRIGHT" relativeKey="$parent.Icon"/>
				</Anchors>
				<SwipeTexture>
					<Color r="0.17" g="0" b="0" a="0.8"/>
				</SwipeTexture>
				<EdgeTexture file="Interface\Cooldown\edge-LoC.blp"/>
			</Cooldown>
			<Frame parentKey="TimeLeft">
				<Size x="200" y="20"/>
				<Layers>
					<Layer level="ARTWORK">
						<FontString inherits="GameFontNormalHuge" parentKey="NumberText" justifyH="LEFT" text="8.8">
							<Size x="0" y="20"/>
							<Anchors>
								<Anchor point="LEFT"/>
							</Anchors>
							<Shadow>
								<Offset>
									<AbsDimension x="2" y="-2"/>
								</Offset>
								<Color r="0" g="0" b="0"/>
							</Shadow>
							<Color r="1" g="1" b="1"/>
						</FontString>
						<FontString inherits="GameFontNormalHuge" parentKey="SecondsText" text="LOSS_OF_CONTROL_SECONDS">
							<Size x="0" y="20"/>
							<Anchors>
								<Anchor point="LEFT" relativePoint="CENTER" x="-64" y="0"/>
							</Anchors>
							<Shadow>
								<Offset>
									<AbsDimension x="2" y="-2"/>
								</Offset>
								<Color r="0" g="0" b="0"/>
							</Shadow>
							<Color r="1" g="1" b="1"/>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="LossOfControlFrame_OnLoad"/>
			<OnEvent function="LossOfControlFrame_OnEvent"/>
			<OnUpdate function="LossOfControlFrame_OnUpdate"/>
			<OnHide function="LossOfControlFrame_OnHide"/>
		</Scripts>
	</Frame>
</Ui>
