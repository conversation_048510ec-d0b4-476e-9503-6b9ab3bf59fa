<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="MainMenuBar.lua"/>
	<Frame name="MainMenuBarWatchBarTemplate" enableMouse="true" parent="MainMenuBar" hidden="true" virtual="true">
		<Size x="1024" y="11"/>
		<Anchors>
			<Anchor point="BOTTOM" relativeTo="MainMenuBar" relativePoint="TOP" x="0" y="-3"/>
		</Anchors>
		<Frames>
			<StatusBar parentKey="StatusBar" drawLayer="ARTWORK" inherits="AnimatedStatusBarTemplate">
				<Size x="1024" y="8"/>
				<Anchors>
					<Anchor point="TOP"/>
				</Anchors>
				<BarTexture parentKey="BarTexture" file="Interface\TargetingFrame\UI-StatusBar"/>
				<BarColor r="0.58" g="0.0" b="0.55"/>
				<Layers>
					<Layer level="ARTWORK">
						<Texture parentKey="WatchBarTexture0" file="Interface\PaperDollInfoFrame\UI-ReputationWatchBar">
							<Size x="256" y="11"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="2"/>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0" bottom="0.171875"/>
						</Texture>
						<Texture parentKey="WatchBarTexture1" file="Interface\PaperDollInfoFrame\UI-ReputationWatchBar">
							<Size x="256" y="11"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.WatchBarTexture0" relativePoint="RIGHT"/>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.1875" bottom="0.359375"/>
						</Texture>
						<Texture parentKey="WatchBarTexture2" file="Interface\PaperDollInfoFrame\UI-ReputationWatchBar">
							<Size x="256" y="11"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.WatchBarTexture1" relativePoint="RIGHT"/>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.375" bottom="0.546875"/>
						</Texture>
						<Texture parentKey="WatchBarTexture3" file="Interface\PaperDollInfoFrame\UI-ReputationWatchBar">
							<Size x="256" y="11"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.WatchBarTexture2" relativePoint="RIGHT"/>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.5625" bottom="0.734375"/>
						</Texture>
						<Texture parentKey="XPBarTexture0" file="Interface\MainMenuBar\UI-MainMenuBar-Dwarf">
							<Size x="256" y="10"/>
							<Anchors>
								<Anchor point="TOPLEFT"/>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.79296875" bottom="0.83203125"/>
						</Texture>
						<Texture parentKey="XPBarTexture1" file="Interface\MainMenuBar\UI-MainMenuBar-Dwarf">
							<Size x="256" y="10"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.XPBarTexture0" relativePoint="RIGHT"/>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.54296875" bottom="0.58203125"/>
						</Texture>
						<Texture parentKey="XPBarTexture2" file="Interface\MainMenuBar\UI-MainMenuBar-Dwarf">
							<Size x="256" y="10"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.XPBarTexture1" relativePoint="RIGHT"/>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.29296875" bottom="0.33203125"/>
						</Texture>
						<Texture parentKey="XPBarTexture3" file="Interface\MainMenuBar\UI-MainMenuBar-Dwarf">
							<Size x="256" y="10"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.XPBarTexture2" relativePoint="RIGHT"/>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.04296875" bottom="0.08203125"/>
						</Texture>
					</Layer>
					<Layer level="BACKGROUND">
						<Texture parentKey="Background">
							<Color r="0.0" g="0.0" b="0.0" a="0.5"/>
						</Texture>
					</Layer>
					<Layer level="BACKGROUND" textureSubLevel="1">
						<Texture parentKey="Underlay" hidden="true" file="Interface\TargetingFrame\UI-StatusBar">
							<Color r=".502" g=".443" b=".278" />
						</Texture>
					</Layer>
					<Layer level="ARTWORK" textureSubLevel="-1">
						<Texture parentKey="Overlay" hidden="true" atlas="XPBarAnim-OrangeGain" alphaMode="ADD">
							<Color r=".901" g=".8" b=".601" />
						</Texture>
					</Layer>
				</Layers>
			</StatusBar>
			<Frame parentKey="OverlayFrame" frameStrata="DIALOG" setAllPoints="true">
				<Layers>
					<Layer level="ARTWORK">
						<FontString parentKey="Text" inherits="TextStatusBarText" hidden="true">
							<Anchors>
								<Anchor point="CENTER" x="0" y="3"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
	</Frame>
	<Frame name="MainMenuBar" enableMouse="true" parent="UIParent">
		<Animations>
			<AnimationGroup parentKey="slideOut">
				<Translation offsetX="0" offsetY="-180" duration="0.4" order="1"/>
				<Scripts>
					<OnFinished>
						if MainMenuBar.hideOnFinish then
							MainMenuBar:Hide();
						end
						ValidateActionBarTransition();
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Size>
			<AbsDimension x="1024" y="53"/>
		</Size>
		<Anchors>
			<Anchor point="BOTTOM"/>
		</Anchors>
		<Frames>
			<StatusBar name="MainMenuExpBar" inherits="TextStatusBar, AnimatedStatusBarTemplate">
				<Size>
					<AbsDimension x="1024" y="11"/>
				</Size>
				<Anchors>
					<Anchor point="TOP"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture name="ExhaustionLevelFillBar">
							<Size>
								<AbsDimension x="0" y="10"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="MainMenuExpBar">
									<Offset>
										<AbsDimension x="0" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<Color r="1.0" g="1.0" b="1.0" a="1.0"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY" textureSubLevel="-1">
						<Texture name="MainMenuXPBarTextureLeftCap" file="Interface\MainMenuBar\UI-XP-Bar">
							<Size>
								<AbsDimension x="14" y="14"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT"  relativePoint="LEFT" x="-3" y="0"/>
							</Anchors>
							<TexCoords left="0.18750000" right="0.43750000" top="0.01562500" bottom="0.26562500"/>
						</Texture>
						<Texture name="MainMenuXPBarTextureRightCap" file="Interface\MainMenuBar\UI-XP-Bar">
							<Size>
								<AbsDimension x="14" y="14"/>
							</Size>
							<Anchors>
								<Anchor point="RIGHT" relativePoint="RIGHT" x="3" y="0"/>
							</Anchors>
							<TexCoords left="0.18750000" right="0.43750000" top="0.29687500" bottom="0.54687500"/>
						</Texture>
						<Texture name="MainMenuXPBarTextureMid" file="Interface\MainMenuBar\UI-XP-Mid" horizTile="true">
							<Size>
								<AbsDimension x="14" y="14"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT" relativeTo="MainMenuXPBarTextureLeftCap" relativePoint="RIGHT"/>
								<Anchor point="RIGHT" relativeTo="MainMenuXPBarTextureRightCap" relativePoint="LEFT"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BACKGROUND">
						<Texture>
							<Color r="0.0" g="0.0" b="0.0" a="0.5"/>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Frame name="MainMenuBarOverlayFrame" frameStrata="DIALOG">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="MainMenuBar"/>
							<Anchor point="BOTTOMRIGHT" relativeTo="MainMenuBar" y="2"/>
						</Anchors>
						<Layers>
							<Layer level="ARTWORK">
								<FontString name="MainMenuBarExpText" inherits="TextStatusBarText">
									<Anchors>
										<Anchor point="CENTER" relativeTo="MainMenuExpBar">
											<Offset>
												<AbsDimension x="0" y="1"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnLoad>
								MainMenuExpBar.lockShow = 0;
							</OnLoad>
						</Scripts>
					</Frame>
				</Frames>
				<Scripts>
					<OnLoad inherit="prepend">
						TextStatusBar_Initialize(self);

						SetTextStatusBarText(MainMenuExpBar, MainMenuBarExpText);
						ExpBar_Update();

						self:RegisterEvent("PLAYER_ENTERING_WORLD");
						self:RegisterEvent("PLAYER_XP_UPDATE");
						self.textLockable = 1;
						self.cvar = "xpBarText";
						self.cvarLabel = "XP_BAR_TEXT";
						self.alwaysPrefix = true;
						self.alwaysShow = true;
						MainMenuExpBar_SetWidth(1024);
					</OnLoad>
					<OnEvent>
						if ( event == "CVAR_UPDATE" ) then
							TextStatusBar_OnEvent(self, event, ...);
						else
							ExpBar_Update();
						end
					</OnEvent>
					<OnShow>
						if ( GetCVar("xpBarText") == "1" ) then
							ExpBar_UpdateTextString();
						end
					</OnShow>
					<OnEnter function="ExpBar_OnEnter"/>
					<OnLeave>
						HideTextStatusBarText(self);
						if (GameLimitedMode_IsActive()) then
							MicroButtonPulseStop(StoreMicroButton);
						end
						GameTooltip:Hide();
						ExhaustionTick.timer = nil;
					</OnLeave>
					<OnUpdate inherit="prepend">
						ExhaustionTick_OnUpdate(ExhaustionTick, elapsed);
					</OnUpdate>
					<OnValueChanged>
						if (not self:IsShown()) then
							return;
						end
						TextStatusBar_OnValueChanged(self);
					</OnValueChanged>
				</Scripts>
				<BarTexture file="Interface\TargetingFrame\UI-StatusBar"/>
				<BarColor r="0.58" g="0.0" b="0.55"/>
			</StatusBar>
			<Frame name="MainMenuBarMaxLevelBar" enableMouse="true" hidden="true">
				<Size>
					<AbsDimension x="1024" y="7"/>
				</Size>
				<Anchors>
					<Anchor point="TOP" relativeTo="MainMenuBar" relativePoint="TOP">
						<Offset>
							<AbsDimension x="0" y="-11"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="MainMenuMaxLevelBar0" file="Interface\MainMenuBar\UI-MainMenuBar-MaxLevel">
							<Size>
								<AbsDimension x="256" y="7"/>
							</Size>
							<Anchors>
								<Anchor point="BOTTOM" relativePoint="TOP">
									<Offset>
										<AbsDimension x="-384" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0" bottom="0.21875"/>
						</Texture>
						<Texture name="MainMenuMaxLevelBar1" file="Interface\MainMenuBar\UI-MainMenuBar-MaxLevel">
							<Size>
								<AbsDimension x="256" y="7"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT" relativeTo="MainMenuMaxLevelBar0" relativePoint="RIGHT"/>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.25" bottom="0.46875"/>
						</Texture>
						<Texture name="MainMenuMaxLevelBar2" file="Interface\MainMenuBar\UI-MainMenuBar-MaxLevel">
							<Size>
								<AbsDimension x="256" y="7"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT" relativeTo="MainMenuMaxLevelBar1" relativePoint="RIGHT"/>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.5" bottom="0.71875"/>
						</Texture>
						<Texture name="MainMenuMaxLevelBar3" file="Interface\MainMenuBar\UI-MainMenuBar-MaxLevel">
							<Size>
								<AbsDimension x="256" y="7"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT" relativeTo="MainMenuMaxLevelBar2" relativePoint="RIGHT"/>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.75" bottom="0.96875"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnShow function="UIParent_ManageFramePositions"/>
					<OnHide function="UIParent_ManageFramePositions"/>
				</Scripts>
			</Frame>
			<Frame name="MainMenuBarArtFrame">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MainMenuBar"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="MainMenuBar"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="MainMenuBarTexture0" file="Interface\MainMenuBar\UI-MainMenuBar-Dwarf">
							<Size>
								<AbsDimension x="256" y="43"/>
							</Size>
							<Anchors>
								<Anchor point="BOTTOM">
									<Offset>
										<AbsDimension x="-384" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.83203125" bottom="1.0"/>
						</Texture>
						<Texture name="MainMenuBarTexture1" file="Interface\MainMenuBar\UI-MainMenuBar-Dwarf">
							<Size>
								<AbsDimension x="256" y="43"/>
							</Size>
							<Anchors>
								<Anchor point="BOTTOM">
									<Offset>
										<AbsDimension x="-128" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.58203125" bottom="0.75"/>
						</Texture>
						<Texture name="MainMenuBarTexture2" file="Interface\MainMenuBar\UI-MainMenuBar-Dwarf">
							<Size>
								<AbsDimension x="256" y="43"/>
							</Size>
							<Anchors>
								<Anchor point="BOTTOM">
									<Offset>
										<AbsDimension x="128" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.33203125" bottom="0.5"/>
						</Texture>
						<Texture name="MainMenuBarTexture3" file="Interface\MainMenuBar\UI-MainMenuBar-Dwarf">
							<Size>
								<AbsDimension x="256" y="43"/>
							</Size>
							<Anchors>
								<Anchor point="BOTTOM">
									<Offset>
										<AbsDimension x="384" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0" right="1.0" top="0.08203125" bottom="0.25"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY" textureSubLevel="5">
						<Texture name="MainMenuBarLeftEndCap" file="Interface\MainMenuBar\UI-MainMenuBar-EndCap-Dwarf">
							<Size>
								<AbsDimension x="128" y="128"/>
							</Size>
							<Anchors>
								<Anchor point="BOTTOM">
									<Offset>
										<AbsDimension x="-544" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
						<Texture name="MainMenuBarRightEndCap" file="Interface\MainMenuBar\UI-MainMenuBar-EndCap-Dwarf">
							<Size>
								<AbsDimension x="128" y="128"/>
							</Size>
							<Anchors>
								<Anchor point="BOTTOM">
									<Offset>
										<AbsDimension x="544" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="1.0" right="0.0" top="0.0" bottom="1.0"/>
						</Texture>
						<FontString name="MainMenuBarPageNumber" inherits="GameFontNormalSmall">
							<Anchors>
								<Anchor point="CENTER">
									<Offset>
										<AbsDimension x="30" y="-5"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad function="MainMenuBar_OnLoad"/>
					<OnEvent function="MainMenuBar_OnEvent"/>
				</Scripts>
			</Frame>
			<Button name="MainMenuBarVehicleLeaveButton" hidden="true" motionScriptsWhileDisabled="true">
				<Size>
					<AbsDimension x="32" y="32"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="0" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad function="MainMenuBarVehicleLeaveButton_OnLoad"/>
					<OnEvent function="MainMenuBarVehicleLeaveButton_OnEvent"/>
					<OnClick function="MainMenuBarVehicleLeaveButton_OnClicked"/>
					<OnEnter function="MainMenuBarVehicleLeaveButton_OnEnter"/>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
				<NormalTexture file="Interface\Vehicles\UI-Vehicles-Button-Exit-Up">
					<TexCoords left="0.140625" right="0.859375" top="0.140625" bottom="0.859375"/>
				</NormalTexture>
				<PushedTexture file="Interface\Vehicles\UI-Vehicles-Button-Exit-Down">
					<TexCoords left="0.140625" right="0.859375" top="0.140625" bottom="0.859375"/>
				</PushedTexture>
				<HighlightTexture parentKey="Highlight" alphaMode="ADD" file="Interface\Buttons\ButtonHilight-Square"/>
			</Button>
		</Frames>
		<Scripts>
			<OnShow>
				UpdateMicroButtonsParent(MainMenuBarArtFrame);
				MoveMicroButtons("BOTTOMLEFT", MainMenuBarArtFrame, "BOTTOMLEFT", 556, 2, false);
			</OnShow>
		</Scripts>
	</Frame>
	<Frame name="ArtifactWatchBar" inherits="MainMenuBarWatchBarTemplate">
		<Frames>
			<Button parentKey="Tick" hidden="true" frameStrata="MEDIUM">
				<Size x="32" y="32"/>
				<Scripts>
					<OnEnter function="MainMenuBar_ArtifactTick_OnEnter"/>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
				<NormalTexture parentKey="Normal" file="Interface\MainMenuBar\UI-ExhaustionTickNormal"/>
				<HighlightTexture parentKey="Highlight" file="Interface\MainMenuBar\UI-ExhaustionTickHighlight" alphaMode="ADD"/>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self:RegisterEvent("PLAYER_ENTERING_WORLD");
				self:RegisterEvent("UNIT_INVENTORY_CHANGED");
				self:RegisterEvent("ARTIFACT_XP_UPDATE");
				self:RegisterEvent("CVAR_UPDATE");
				self.StatusBar:SetStatusBarColor(.901, .8, .601);
				self.StatusBar:SetAnimatedTextureColors(.901, .8, .601);

				self.StatusBar.Overlay:SetAllPoints(self.StatusBar:GetStatusBarTexture());

				self.StatusBar:SetOnAnimatedValueChangedCallback(MainMenuBar_ArtifactOnAnimatedValueChangedCallback);
			</OnLoad>
			<OnEvent>
				if ( event == "UNIT_INVENTORY_CHANGED" ) then
					local unitTag = ...;
					if ( unitTag == "player" ) then
						MainMenuBar_UpdateExperienceBars();
					end
				elseif ( event == "ARTIFACT_XP_UPDATE") then
					MainMenuBar_UpdateExperienceBars();
				elseif ( event == "CVAR_UPDATE" ) then
					local name, value = ...;
					if ( name == "XP_BAR_TEXT" ) then
						if( value == "1" ) then
							ShowWatchBarText(ArtifactWatchBar, "lock");
						else
							HideWatchBarText(ArtifactWatchBar, "unlock");
						end
						MainMenuBar_UpdateExperienceBars();
					end
				end
			</OnEvent>
			<OnShow>
				if ( GetCVarBool("xpBarText") ) then
					ShowWatchBarText(ArtifactWatchBar, "lock");
				end
				UIParent_ManageFramePositions();
			</OnShow>
			<OnHide function="UIParent_ManageFramePositions"/>
			<OnEnter>
				self.OverlayFrame.Text:Show();
				MainMenuBar_ArtifactUpdateOverlayFrameText();
				MainMenuBar_ArtifactTick_OnEnter(self);
			</OnEnter>
			<OnLeave>
				if (not self.textLocked) then
					self.OverlayFrame.Text:Hide();
				end
				GameTooltip_Hide();
			</OnLeave>
		</Scripts>
	</Frame>
	<Frame name="HonorWatchBar" enableMouse="true" parent="MainMenuBar" inherits="MainMenuBarWatchBarTemplate" hidden="true">
		<Size x="1024" y="11"/>
		<Anchors>
			<Anchor point="BOTTOM" relativeTo="MainMenuBar" relativePoint="TOP" x="0" y="-3"/>
		</Anchors>
        <Layers>
            <Layer level="BORDER">
                <Texture parentKey="ExhaustionLevelFillBar">
                    <Size x="0" y="10"/>
                    <Anchors>
                        <Anchor point="TOPLEFT" relativeTo="HonorWatchBar" x="0" y="0"/>
                    </Anchors>
                    <Color r="1.0" g="1.0" b="1.0" a="1.0"/>
                </Texture>
            </Layer>
        </Layers>
        <Frames>
            <Button parentKey="ExhaustionTick" hidden="true" frameStrata="MEDIUM">
                <Size x="32" y="32"/>
                <Anchors>
                    <Anchor point="CENTER" relativeKey="$parent.ExhaustionLevelFillBar" relativePoint="RIGHT" x="0" y="0"/>
                </Anchors>
                <Scripts>
                    <OnLoad function="HonorExhaustionTick_OnLoad"/>
                    <OnEvent>
						MainMenuBar_UpdateExperienceBars();
					</OnEvent>
                    <OnEnter function="HonorExhaustionToolTipText"/>
                    <OnLeave function="GameTooltip_Hide"/>
                </Scripts>
                <NormalTexture parentKey="Normal" file="Interface\MainMenuBar\UI-ExhaustionTickNormal"/>
                <HighlightTexture parentKey="Highlight" file="Interface\MainMenuBar\UI-ExhaustionTickHighlight" alphaMode="ADD"/>
            </Button>
			<Frame parentKey="OverlayFrame" frameStrata="DIALOG" setAllPoints="true">
				<Layers>
					<Layer level="ARTWORK">
						<FontString parentKey="Text" inherits="TextStatusBarText" hidden="true">
							<Anchors>
								<Anchor point="CENTER" x="0" y="3"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="HonorWatchBar_OnLoad"/>
			<OnEvent>
				local arg1, arg2 = ...;
				if( event == "CVAR_UPDATE" and arg1 == "XP_BAR_TEXT" ) then
					if( arg2 == "1" ) then
						ShowWatchBarText(HonorWatchBar, "lock");
					else
						HideWatchBarText(HonorWatchBar, "unlock");
					end
				else
					MainMenuBar_UpdateExperienceBars();
				end
			</OnEvent>
			<OnShow>
				if ( GetCVarBool("xpBarText") ) then
					ShowWatchBarText(HonorWatchBar, "lock");
				end
				UIParent_ManageFramePositions();
			</OnShow>
			<OnHide function="UIParent_ManageFramePositions"/>
			<OnEnter>
				self.OverlayFrame.Text:Show();
				MainMenuBar_HonorUpdateOverlayFrameText();
			</OnEnter>
			<OnLeave>
				if (not self.textLocked) then
					self.OverlayFrame.Text:Hide();
				end
			</OnLeave>
		</Scripts>
	</Frame>
	<Button name="ExhaustionTick" parent="MainMenuExpBar" hidden="false" frameStrata="MEDIUM">
		<Size>
			<AbsDimension x="32" y="32"/>
		</Size>
		<Anchors>
			<Anchor point="CENTER" relativeTo="MainMenuExpBar">
				<Offset>
					<AbsDimension x="0" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Scripts>
			<OnLoad function="ExhaustionTick_OnLoad"/>
			<OnEvent function="ExhaustionTick_OnEvent"/>
			<OnEnter function="ExhaustionToolTipText"/>
			<OnLeave function="GameTooltip_Hide"/>
		</Scripts>
		<NormalTexture name="ExhaustionTickNormal" file="Interface\MainMenuBar\UI-ExhaustionTickNormal"/>
		<HighlightTexture name="ExhaustionTickHighlight" file="Interface\MainMenuBar\UI-ExhaustionTickHighlight" alphaMode="ADD"/>
	</Button>
</Ui>
