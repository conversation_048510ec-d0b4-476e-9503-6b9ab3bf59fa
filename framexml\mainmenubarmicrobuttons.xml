<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="MainMenuBarMicroButtons.lua"/>
	<Button name="MainMenuBarMicroButton" motionScriptsWhileDisabled="true" virtual="true">
		<Size>
			<AbsDimension x="28" y="58"/>
		</Size>
		<HitRectInsets>
			<AbsInset left="0" right="0" top="18" bottom="0"/>
		</HitRectInsets>
		<Layers>
			<Layer level="OVERLAY">
				<Texture name="$parentFlash" file="Interface\Buttons\Micro-Highlight" alphaMode="ADD" parentKey="Flash" hidden="true">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-2" y="-18"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnEnter>
				MicroButton_OnEnter(self);
			</OnEnter>
			<OnEnable>
				self:SetAlpha(1);
			</OnEnable>
			<OnDisable>
				self:SetAlpha(0.5);
			</OnDisable>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
	</Button>
	<Frame name="MicroButtonAlertTemplate" inherits="GlowBoxTemplate" parent="UIParent" enableMouse="true" hidden="true" frameStrata="DIALOG" frameLevel="2" toplevel="true" virtual="true">
		<Size x="220" y="100"/>
		<!--KeyValues>
			Optional
			<KeyValue key="label" value="DISPLAY_STRING" type="global"/>
			<KeyValue key="tutorialIndex" value="LE_FRAME_TUTORIAL_X" type="global"/>
		</KeyValues-->
		<Layers>
			<Layer level="OVERLAY">
				<FontString parentKey="Text" inherits="GameFontHighlightLeft" justifyV="TOP">
					<Size x="188" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="16" y="-24"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="CloseButton" inherits="UIPanelCloseButton">
				<Anchors>
					<Anchor point="TOPRIGHT" x="6" y="6"/>
				</Anchors>
				<Scripts>
					<OnClick>
						self:GetParent():Hide();
						local tutIdx = self:GetParent().tutorialIndex;
						if ( tutIdx ) then
							SetCVarBitfield("closedInfoFrames", tutIdx, true);
						end
					</OnClick>
				</Scripts>
			</Button>
			<Frame parentKey="Arrow" inherits="GlowBoxArrowTemplate">
				<Anchors>
					<Anchor point="TOP" relativePoint="BOTTOM" y="4"/>
				</Anchors>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="MicroButtonAlert_OnLoad"/>
			<OnShow function="MicroButtonAlert_OnShow"/>
			<OnHide function="MicroButtonAlert_OnHide"/>
		</Scripts>
	</Frame>
	<Button name="CharacterMicroButton" inherits="MainMenuBarMicroButton" parent="UIParent">
		<Anchors>
			<Anchor point="BOTTOMLEFT">
				<Offset>
					<AbsDimension x="548" y="2"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Layers>
			<Layer level="OVERLAY">
				<Texture name="MicroButtonPortrait">
					<Size>
						<AbsDimension x="18" y="25"/>
					</Size>
					<Anchors>
						<Anchor point="TOP">
							<Offset>
								<AbsDimension x="0" y="-28"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.2" right="0.8" top="0.0666" bottom="0.9"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad function="CharacterMicroButton_OnLoad"/>
			<OnMouseDown>
				if ( self.down ) then
					self.down = nil;
					ToggleCharacter("PaperDollFrame");
					return;
				end
				CharacterMicroButton_SetPushed();
				self.down = 1;
			</OnMouseDown>
			<OnMouseUp>
				if ( self.down ) then
					self.down = nil;
					if ( self:IsMouseOver() ) then
						ToggleCharacter("PaperDollFrame");
					end
					UpdateMicroButtons();
					return;
				end
				if ( self:GetButtonState() == "NORMAL" ) then
					CharacterMicroButton_SetPushed();
					self.down = 1;
				else
					CharacterMicroButton_SetNormal();
					self.down = 1;
				end
			</OnMouseUp>
			<OnEvent function="CharacterMicroButton_OnEvent"/>
		</Scripts>
	</Button>
	<Button name="SpellbookMicroButton" inherits="MainMenuBarMicroButton" parent="UIParent">
		<Anchors>
			<Anchor point="BOTTOMLEFT" relativeTo="CharacterMicroButton" relativePoint="BOTTOMRIGHT">
				<Offset>
					<AbsDimension x="-2" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Scripts>
			<OnLoad>
				LoadMicroButtonTextures(self, "Spellbook");
			</OnLoad>
			<OnClick>
				ToggleSpellBook(BOOKTYPE_SPELL);
			</OnClick>
			<OnEnter>
				self.tooltipText = MicroButtonTooltipText(SPELLBOOK_ABILITIES_BUTTON, "TOGGLESPELLBOOK");
				GameTooltip_AddNewbieTip(self, self.tooltipText, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_SPELLBOOK);
			</OnEnter>
		</Scripts>
	</Button>
	<Button name="TalentMicroButton" inherits="MainMenuBarMicroButton" mixin="TalentMicroButtonMixin" parent="UIParent">
		<Anchors>
			<Anchor point="BOTTOMLEFT" relativeTo="SpellbookMicroButton" relativePoint="BOTTOMRIGHT">
				<Offset>
					<AbsDimension x="-2" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Scripts>
			<OnLoad>
				LoadMicroButtonTextures(self, "Talents");
				self.tooltipText = MicroButtonTooltipText(TALENTS_BUTTON, "TOGGLETALENTS");
				self.newbieText = NEWBIE_TOOLTIP_TALENTS;

				self.minLevel = SHOW_SPEC_LEVEL;
				self:RegisterEvent("PLAYER_LEVEL_UP");
				self:RegisterEvent("UPDATE_BINDINGS");
				self:RegisterEvent("PLAYER_TALENT_UPDATE");
				self:RegisterEvent("PLAYER_SPECIALIZATION_CHANGED");
                self:RegisterEvent("HONOR_LEVEL_UPDATE");
	            self:RegisterEvent("HONOR_PRESTIGE_UPDATE");
	            self:RegisterEvent("PLAYER_PVP_TALENT_UPDATE");
				self:RegisterEvent("PLAYER_CHARACTER_UPGRADE_TALENT_COUNT_CHANGED");
			</OnLoad>
			<OnClick function="TalentMicroButton_OnClick"/>
			<OnEvent function="TalentMicroButton_OnEvent"/>
		</Scripts>
	</Button>
	<Button name="AchievementMicroButton" inherits="MainMenuBarMicroButton" parent="UIParent">
		<Anchors>
			<Anchor point="BOTTOMLEFT" relativeTo="TalentMicroButton" relativePoint="BOTTOMRIGHT">
				<Offset>
					<AbsDimension x="-2" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Scripts>
			<OnLoad>
				LoadMicroButtonTextures(self, "Achievement");
				self:RegisterEvent("RECEIVED_ACHIEVEMENT_LIST");
				self:RegisterEvent("ACHIEVEMENT_EARNED");
				self:RegisterEvent("UPDATE_BINDINGS");
				self.tooltipText = MicroButtonTooltipText(ACHIEVEMENT_BUTTON, "TOGGLEACHIEVEMENT");
				self.newbieText = NEWBIE_TOOLTIP_ACHIEVEMENT;
				self.minLevel = 10;	--Just used for display. But we know that it will become available by level 10 due to the level 10 achievement.
				if (IsKioskModeEnabled()) then
					self:Disable();
				end
			</OnLoad>
			<OnShow function="MicroButton_KioskModeDisable"/>
			<OnEvent function="AchievementMicroButton_OnEvent"/>
			<OnClick>
				ToggleAchievementFrame();
			</OnClick>
		</Scripts>
	</Button>
	<Button name="QuestLogMicroButton" inherits="MainMenuBarMicroButton" parent="UIParent">
		<Anchors>
			<Anchor point="BOTTOMLEFT" relativeTo="AchievementMicroButton" relativePoint="BOTTOMRIGHT">
				<Offset>
					<AbsDimension x="-2" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Scripts>
			<OnLoad>
				LoadMicroButtonTextures(self, "Quest");
				self.tooltipText = MicroButtonTooltipText(QUESTLOG_BUTTON, "TOGGLEQUESTLOG");
				self.newbieText = NEWBIE_TOOLTIP_QUESTLOG;
			</OnLoad>
			<OnEvent>
				self.tooltipText = MicroButtonTooltipText(QUESTLOG_BUTTON, "TOGGLEQUESTLOG");
			</OnEvent>
			<OnClick>
				ToggleQuestLog();
			</OnClick>
		</Scripts>
	</Button>
	<Button name="GuildMicroButton" inherits="MainMenuBarMicroButton" parent="UIParent">
		<Anchors>
			<Anchor point="BOTTOMLEFT" relativeTo="QuestLogMicroButton" relativePoint="BOTTOMRIGHT">
				<Offset>
					<AbsDimension x="-2" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Frames>
			<Frame name="$parentTabard" hidden="true">
				<Size>
					<AbsDimension x="28" y="58"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture name="$parentBackground" file="Interface\Buttons\UI-MicroButton-Guild-Banner" parentKey="background">
							<Size>
								<AbsDimension x="30" y="60"/>
							</Size>
							<Anchors>
								<Anchor point="CENTER" x="0" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="OVERLAY" textureSubLevel="1">
						<Texture name="$parentEmblem" parentKey="emblem" file="Interface\GuildFrame\GuildEmblems_01">
							<Size x="16" y="16"/>
							<Anchors>
								<Anchor point="CENTER" x="0" y="-9"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				LoadMicroButtonTextures(self, "Socials");
				self.tooltipText = MicroButtonTooltipText(LOOKINGFORGUILD, "TOGGLEGUILDTAB");
				self.newbieText = NEWBIE_TOOLTIP_LOOKINGFORGUILDTAB;
				self:RegisterEvent("UPDATE_BINDINGS");
				self:RegisterEvent("PLAYER_GUILD_UPDATE");
				self:RegisterEvent("NEUTRAL_FACTION_SELECT_RESULT");
				GuildMicroButton_UpdateTabard(true);
				if ( IsTrialAccount() or (IsVeteranTrialAccount() and not IsInGuild()) ) then
					self:Disable();
					self.disabledTooltip = ERR_RESTRICTED_ACCOUNT_TRIAL;
				end
				if (IsKioskModeEnabled()) then
					self:Disable();
				end
			</OnLoad>
			<OnShow function="MicroButton_KioskModeDisable"/>
			<OnEvent function="GuildMicroButton_OnEvent"/>
			<OnClick>
				ToggleGuildFrame();
			</OnClick>
		</Scripts>
	</Button>
	<Button name="LFDMicroButton" inherits="MainMenuBarMicroButton" parent="UIParent">
		<Anchors>
			<Anchor point="BOTTOMLEFT" relativeTo="GuildMicroButton" relativePoint="BOTTOMRIGHT">
				<Offset>
					<AbsDimension x="-2" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Scripts>
			<OnLoad>
				LoadMicroButtonTextures(self, "LFG");
				SetDesaturation(self:GetDisabledTexture(), true);
				self.tooltipText = MicroButtonTooltipText(DUNGEONS_BUTTON, "TOGGLEGROUPFINDER");
				self.newbieText = NEWBIE_TOOLTIP_LFGPARENT;
				self.minLevel = math.min(SHOW_LFD_LEVEL, SHOW_PVP_LEVEL);
				if (IsKioskModeEnabled()) then
					self:Disable();
				end
			</OnLoad>
			<OnShow function="MicroButton_KioskModeDisable"/>
			<OnEvent>
				if (IsKioskModeEnabled()) then
					return;
				end
				self.tooltipText = MicroButtonTooltipText(DUNGEONS_BUTTON, "TOGGLEGROUPFINDER");
				self.newbieText = NEWBIE_TOOLTIP_LFGPARENT;
				UpdateMicroButtons();
			</OnEvent>
			<OnClick>
				PVEFrame_ToggleFrame();
			</OnClick>
		</Scripts>
	</Button>
	<Button name="CollectionsMicroButton" inherits="MainMenuBarMicroButton" mixin="CollectionMicroButtonMixin" parent="UIParent">
		<KeyValues>
			<KeyValue key="lastNumMountsNeedingFanfare" value="0" type="number"/>
			<KeyValue key="lastNumPetsNeedingFanfare" value="0" type="number"/>
		</KeyValues>
		<Anchors>
			<Anchor point="BOTTOMLEFT" relativeTo="LFDMicroButton" relativePoint="BOTTOMRIGHT">
				<Offset>
					<AbsDimension x="-2" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Scripts>
			<OnLoad>
				LoadMicroButtonTextures(self, "Mounts");
				SetDesaturation(self:GetDisabledTexture(), true);
				self.tooltipText = MicroButtonTooltipText(COLLECTIONS, "TOGGLECOLLECTIONS");
				self.newbieText = NEWBIE_TOOLTIP_MOUNTS_AND_PETS;

				self:RegisterEvent("HEIRLOOMS_UPDATED");
				self:RegisterEvent("PET_JOURNAL_NEW_BATTLE_SLOT");
				self:RegisterEvent("TOYS_UPDATED");
				self:RegisterEvent("COMPANION_LEARNED");
				self:RegisterEvent("PET_JOURNAL_LIST_UPDATE");
				self:RegisterEvent("PLAYER_ENTERING_WORLD");
			</OnLoad>
			<OnClick>
				ToggleCollectionsJournal();
			</OnClick>
			<OnEvent function="CollectionsMicroButton_OnEvent" />
		</Scripts>
	</Button>
	<Button name="EJMicroButton" inherits="MainMenuBarMicroButton" mixin="EJMicroButtonMixin" parent="UIParent">
		<Anchors>
			<Anchor point="BOTTOMLEFT" relativeTo="CollectionsMicroButton" relativePoint="BOTTOMRIGHT">
				<Offset>
					<AbsDimension x="-2" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Frames>
			<Frame parentKey="NewAdventureNotice" hidden="true">
				<Size x="28" y="28"/>
				<Anchors>
					<Anchor point="RIGHT" x="8" y="2"/>
				</Anchors>
				<Layers>
					<Layer level="OVERLAY">
						<Texture atlas="adventureguide-microbutton-alert"/>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="EJMicroButton_OnLoad"/>
			<OnShow function="MicroButton_KioskModeDisable"/>
			<OnEvent function="EJMicroButton_OnEvent"/>
			<OnClick function="ToggleEncounterJournal"/>
		</Scripts>
	</Button>
	<Button name="StoreMicroButton" inherits="MainMenuBarMicroButton" mixin="StoreMicroButtonMixin" parent="UIParent">
		<Anchors>
			<Anchor point="BOTTOMLEFT" relativeTo="EJMicroButton" relativePoint="BOTTOMRIGHT">
				<Offset>
					<AbsDimension x="-2" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Scripts>
			<OnLoad function="StoreMicroButton_OnLoad"/>
			<OnShow function="MicroButton_KioskModeDisable"/>
			<OnEvent function="StoreMicroButton_OnEvent"/>
			<OnClick function="ToggleStoreUI"/>
		</Scripts>
	</Button>
	<Button name="MainMenuMicroButton" inherits="MainMenuBarMicroButton" parent="UIParent">
		<Anchors>
			<Anchor point="BOTTOMLEFT" relativeTo="StoreMicroButton" relativePoint="BOTTOMRIGHT" x="-3"/>
		</Anchors>
		<Layers>
			<Layer level="OVERLAY">
				<Texture name="MainMenuBarPerformanceBar" file="Interface\MainMenuBar\UI-MainMenuBar-PerformanceBar">
					<Size x="28" y="58"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture name="MainMenuBarDownload" file="Interface\BUTTONS\UI-MicroStream-Yellow" hidden="true">
					<Size>
						<AbsDimension x="28" y="28"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOM" relativeTo="MainMenuMicroButton" relativePoint="BOTTOM">
							<Offset x="0" y="7"/>
						</Anchor>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				LoadMicroButtonTextures(self, "MainMenu");
				self.tooltipText = MicroButtonTooltipText(MAINMENU_BUTTON, "TOGGLEGAMEMENU");
				self.newbieText = NEWBIE_TOOLTIP_MAINMENU;

				PERFORMANCEBAR_LOW_LATENCY = 300;
				PERFORMANCEBAR_MEDIUM_LATENCY = 600;
				self.hover = nil;
				self.updateInterval = 0;
				self:RegisterForClicks("LeftButtonDown", "RightButtonDown", "LeftButtonUp", "RightButtonUp");
			</OnLoad>
			<OnUpdate>
				if (self.updateInterval > 0) then
					self.updateInterval = self.updateInterval - elapsed;
				else
					self.updateInterval = PERFORMANCEBAR_UPDATE_INTERVAL;
					local status = GetFileStreamingStatus();
					if(status==0) then
						status = (GetBackgroundLoadingStatus()~=0) and 1 or 0;
					end
					if(status == 0) then
						MainMenuBarDownload:Hide();
						self:SetNormalTexture("Interface\\Buttons\\UI-MicroButton-MainMenu-Up");
						self:SetPushedTexture("Interface\\Buttons\\UI-MicroButton-MainMenu-Down");
						self:SetDisabledTexture("Interface\\Buttons\\UI-MicroButton-MainMenu-Disabled");
					else
						self:SetNormalTexture("Interface\\Buttons\\UI-MicroButtonStreamDL-Up");
						self:SetPushedTexture("Interface\\Buttons\\UI-MicroButtonStreamDL-Down");
						self:SetDisabledTexture("Interface\\Buttons\\UI-MicroButtonStreamDL-Up");
						if (status == 1) then
							MainMenuBarDownload:SetTexture("Interface\\BUTTONS\\UI-MicroStream-Green");
						elseif (status == 2) then
							MainMenuBarDownload:SetTexture("Interface\\BUTTONS\\UI-MicroStream-Yellow");
						elseif (status == 3) then
							MainMenuBarDownload:SetTexture("Interface\\BUTTONS\\UI-MicroStream-Red");
						end
						MainMenuBarDownload:Show();
					end
					local bandwidthIn, bandwidthOut, latencyHome, latencyWorld = GetNetStats();
					local latency = latencyHome > latencyWorld and latencyHome or latencyWorld;
					if (latency > PERFORMANCEBAR_MEDIUM_LATENCY) then
						MainMenuBarPerformanceBar:SetVertexColor(1, 0, 0);
					elseif (latency > PERFORMANCEBAR_LOW_LATENCY) then
						MainMenuBarPerformanceBar:SetVertexColor(1, 1, 0);
					else
						MainMenuBarPerformanceBar:SetVertexColor(0, 1, 0);
					end
					if (self.hover) then
						MainMenuBarPerformanceBarFrame_OnEnter(self);
					end
				end
			</OnUpdate>
			<OnEvent>
				self.tooltipText = MicroButtonTooltipText(MAINMENU_BUTTON, "TOGGLEGAMEMENU");
			</OnEvent>
			<OnMouseDown>
				if ( self.down ) then
					self.down = nil; -- I'm pretty sure none of this should ever get run.
					if ( not GameMenuFrame:IsShown() ) then
						if ( VideoOptionsFrame:IsShown() ) then
							VideoOptionsFrameCancel:Click();
						elseif ( AudioOptionsFrame:IsShown() ) then
							AudioOptionsFrameCancel:Click();
						elseif ( InterfaceOptionsFrame:IsShown() ) then
							InterfaceOptionsFrameCancel:Click();
						end
						CloseMenus();
						CloseAllWindows()
						PlaySound(SOUNDKIT.IG_MAINMENU_OPEN);
						ShowUIPanel(GameMenuFrame);
					else
						PlaySound(SOUNDKIT.IG_MAINMENU_QUIT);
						HideUIPanel(GameMenuFrame);
						MainMenuMicroButton_SetNormal();
					end
					return;
				end
				MainMenuMicroButton_SetPushed();
				self.down = 1;
			</OnMouseDown>
			<OnMouseUp>
				if ( self.down ) then
					self.down = nil;
					if ( self:IsMouseOver() ) then
						if ( not GameMenuFrame:IsShown() ) then
							if ( VideoOptionsFrame:IsShown() ) then
								VideoOptionsFrameCancel:Click();
							elseif ( AudioOptionsFrame:IsShown() ) then
								AudioOptionsFrameCancel:Click();
							elseif ( InterfaceOptionsFrame:IsShown() ) then
								InterfaceOptionsFrameCancel:Click();
							end
							CloseMenus();
							CloseAllWindows()
							PlaySound(SOUNDKIT.IG_MAINMENU_OPEN);
							ShowUIPanel(GameMenuFrame);
						else
							PlaySound(SOUNDKIT.IG_MAINMENU_QUIT);
							HideUIPanel(GameMenuFrame);
							MainMenuMicroButton_SetNormal();
						end
					end
					UpdateMicroButtons();
					return;
				end
				if ( self:GetButtonState() == "NORMAL" ) then
					MainMenuMicroButton_SetPushed();
					self.down = 1;
				else
					MainMenuMicroButton_SetNormal();
					self.down = 1;
				end
			</OnMouseUp>
			<OnEnter>
				self.hover = 1;
				self.updateInterval = 0;
			</OnEnter>
			<OnLeave>
				self.hover = nil;
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
	</Button>

	<Frame name="TalentMicroButtonAlert" inherits="MicroButtonAlertTemplate">
		<KeyValues>
			<KeyValue key="MicroButton" value="TalentMicroButton" type="global"/>
		</KeyValues>
		<Anchors>
			<Anchor point="BOTTOM" relativeTo="TalentMicroButton" relativePoint="TOP" x="0" y="-8"/>
		</Anchors>
		<Scripts>
			<OnLoad>
				MicroButtonAlert_OnLoad(self);
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="CollectionsMicroButtonAlert" inherits="MicroButtonAlertTemplate">
		<KeyValues>
			<KeyValue key="MicroButton" value="CollectionsMicroButton" type="global"/>
		</KeyValues>
		<Anchors>
			<Anchor point="BOTTOM" relativeTo="CollectionsMicroButton" relativePoint="TOP" x="0" y="-8"/>
		</Anchors>
		<Scripts>
			<OnLoad>
				MicroButtonAlert_OnLoad(self);
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="LFDMicroButtonAlert" inherits="MicroButtonAlertTemplate">
		<Anchors>
			<Anchor point="BOTTOM" relativeTo="LFDMicroButton" relativePoint="TOP" x="0" y="-8"/>
		</Anchors>
		<Scripts>
			<OnLoad>
				MicroButtonAlert_OnLoad(self);
				self.Text:SetText(LFG_MICRO_BUTTON_SPEC_TUTORIAL);
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="EJMicroButtonAlert" inherits="MicroButtonAlertTemplate">
		<KeyValues>
			<KeyValue key="MicroButton" value="EJMicroButton" type="global"/>
		</KeyValues>
		<Anchors>
			<Anchor point="BOTTOM" relativeTo="EJMicroButton" relativePoint="TOP" x="0" y="-8"/>
		</Anchors>
		<Scripts>
			<OnLoad>
				MicroButtonAlert_OnLoad(self);
			</OnLoad>
			<OnHide>
				SetCVar("advJournalLastOpened", GetServerTime() );
			</OnHide>
		</Scripts>
	</Frame>
	<Frame name="StoreMicroButtonAlert" inherits="MicroButtonAlertTemplate">
		<KeyValues>
			<KeyValue key="MicroButton" value="StoreMicroButton" type="global"/>
		</KeyValues>
		<Anchors>
			<Anchor point="BOTTOM" relativeTo="StoreMicroButton" relativePoint="TOP" x="0" y="-8"/>
		</Anchors>
	</Frame>
</Ui>
