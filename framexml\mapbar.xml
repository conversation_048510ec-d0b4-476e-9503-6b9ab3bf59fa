<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="MapBar.lua"/>

	<!--Patchwerks-->
	<Texture name="mapbar-start-mogu" file="Interface\WorldMap\MapProgress\map-progress-main" virtual="true" >
		<Size x="84" y="80"/>	
		<TexCoords left="0.00781250" right="0.66406250" top="0.00781250" bottom="0.63281250"/>	
	</Texture>
	<Texture name="mapbar-end-mogu" file="Interface\WorldMap\MapProgress\map-progress-main" virtual="true" >
		<Size x="31" y="35"/>	
		<TexCoords left="0.67968750" right="0.92187500" top="0.00781250" bottom="0.28125000"/>	
	</Texture>
	<Texture name="mapbar-middle-mogu" file="Interface\WorldMap\MapProgress\map-progress-main" virtual="true" >
		<Size x="32" y="33"/>	
		<TexCoords left="0.67968750" right="0.92968750" top="0.29687500" bottom="0.55468750"/>	
	</Texture>
	<Texture name="mapbar-fillspark-mogu" file="Interface\WorldMap\MapProgress\map-progress-main" virtual="true" >
		<Size x="10" y="28"/>	
		<TexCoords left="0.00781250" right="0.08593750" top="0.64843750" bottom="0.86718750"/>	
	</Texture>
	<Texture name="mapbar-fillbg-mogu" file="Interface\WorldMap\MapProgress\map-progress-main" virtual="true" >
		<Size x="32" y="17"/>	
		<TexCoords left="0.10156250" right="0.35156250" top="0.64843750" bottom="0.78125000"/>	
	</Texture>
	<!--End Patchwerks-->

	<StatusBar name="MapBarFrame" parent="WorldMapDetailFrame">
		<Size x="225" y="17"/>
		<Anchors>
			<Anchor point="TOPLEFT" relativeTo="WorldMapDetailFrame" relativePoint="TOPLEFT" x="150" y="-70"/>
		</Anchors>
		<HitRectInsets>
			<AbsInset left="-80" right="-10" top="-60" bottom="0"/>
		</HitRectInsets>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="Start" inherits="mapbar-start-mogu">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent" relativePoint="BOTTOMLEFT" x="8" y="-18"/>
					</Anchors>
				</Texture>
				<Texture parentKey="End" inherits="mapbar-end-mogu">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent" relativePoint="BOTTOMRIGHT" x="-9" y="-11"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Middle" inherits="mapbar-middle-mogu">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.Start" relativePoint="BOTTOMRIGHT" x="0" y="9"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.End" relativePoint="BOTTOMLEFT" x="0" y="2"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Description" inherits="GameFontHighlightOutline" justifyH="LEFT">
					<Size x="220" y="0"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent" relativePoint="TOPLEFT" x="9" y="11"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Title" inherits="GameFontNormalLargeOutline" justifyH="LEFT">
					<Size x="200" y="0"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.Description" relativePoint="TOPLEFT" x="0" y="2"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="Spark" alphaMode="ADD" inherits="mapbar-fillspark-mogu"/>
			</Layer>
			<Layer level="BACKGROUND">
				<Texture parentKey="FillBG" inherits="mapbar-fillbg-mogu" setAllPoints="true"/>
			</Layer>
		</Layers>
		<BarTexture parentKey="BarTexture" file="Interface\WorldMap\MapProgress\mapbar-fill-mogu"/>
		<Scripts>
			<OnLoad function="MapBarFrame_OnLoad"/>
			<OnEvent function="MapBarFrame_OnEvent"/>
			<OnEnter function="MapBarFrame_OnEnter"/>
			<OnLeave function="MapBarFrame_OnLeave"/>
		</Scripts>
	</StatusBar>
</Ui>
