<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="MerchantFrame.lua"/>
	<Frame name="MerchantItemTemplate" virtual="true">	
		<Size x="153" y="44"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentSlotTexture" file="Interface\Buttons\UI-EmptySlot">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-13" y="13"/>
					</Anchors>
				</Texture>
				<Texture name="$parentNameFrame" file="Interface\MerchantFrame\UI-Merchant-LabelSlots">
					<Size x="128" y="78"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentSlotTexture" relativePoint="RIGHT" x="-9" y="-18"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Name" name="$parentName" inherits="GameFontNormalSmall" text="Item Name" justifyH="LEFT">
					<Size x="100" y="30"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentSlotTexture" relativePoint="RIGHT" x="-5" y="7"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="ItemButton" name="$parentItemButton" inherits="ItemButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parent"/>
				</Anchors>
				<Scripts>
					<OnClick>
						if ( IsModifiedClick() ) then
							MerchantItemButton_OnModifiedClick(self, button);
						else
							MerchantItemButton_OnClick(self, button);
						end
					</OnClick>
					<OnLoad>
						MerchantItemButton_OnLoad(self);
					</OnLoad>
					<OnDragStart>
						MerchantItemButton_OnClick(self, "LeftButton");
					</OnDragStart>
					<OnEnter>
						MerchantItemButton_OnEnter(self, motion);
					</OnEnter>
					<OnLeave>
						GameTooltip:Hide();
						ResetCursor();
						MerchantFrame.itemHover = nil;
					</OnLeave>
					<OnHide>
						if ( self.hasStackSplit == 1 ) then
							StackSplitFrame:Hide();
						end
					</OnHide>
				</Scripts>
			</Button>
			<Frame name="$parentMoneyFrame" inherits="SmallMoneyFrameTemplate">
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativeTo="$parentNameFrame" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="2" y="31"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						SmallMoneyFrame_OnLoad(self);
						MoneyFrame_SetType(self, "STATIC");
						MoneyFrame_SetMaxDisplayWidth(self, 100);
					</OnLoad>
				</Scripts>
			</Frame>
			<Frame name="$parentAltCurrencyFrame" inherits="SmallAlternateCurrencyFrameTemplate" hidden="true">
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativeTo="$parentNameFrame" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="2" y="31"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
		</Frames>
	</Frame>
	<Frame name="MerchantFrame" toplevel="true" parent="UIParent" movable="true" enableMouse="true" hidden="true" inherits="ButtonFrameTemplate">
		<Size x="336" y="444"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="MerchantFramePortrait">
                    <Size x="60" y="60"/>
                    <Anchors>
                        <Anchor point="TOPLEFT" x="7" y="-6"/>
                    </Anchors>
                </Texture>
				<Texture name="BuybackBG" >
					<Color r="1" g="1" b="1" a=".2"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="MerchantFrame" x="7" y="-60"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="MerchantFrame" x="-7" y="26"/>
					</Anchors>
				</Texture>
            </Layer>
			<Layer level="BORDER">
				 <FontString name="MerchantNameText" inherits="GameFontNormal" text="Merchant Name">
					<Anchors>
						<Anchor point="TOP" relativeTo="MerchantFrame" relativePoint="TOP" x="0" y="-5"/>
					</Anchors>
				</FontString>
				<FontString name="MerchantPageText" inherits="GameFontNormal" text="Page">
					<Size x="104" y="0"/>
					<Anchors>
						<Anchor point="BOTTOM" x="0" y="86"/>
					</Anchors>
				</FontString>
				<FontString name="MerchantRepairText" inherits="GameFontHighlightSmall" text="REPAIR_ITEMS">
 					<Anchors>
 						<Anchor point="BOTTOMLEFT" x="16" y="40"/>
 					</Anchors>
 				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="MerchantFrameBottomLeftBorder" file="Interface\MerchantFrame\UI-Merchant-BottomBorder">
					<Size x="256" y="61"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="1" y="26"/>
					</Anchors>
					<TexCoords left="0" right="1" top="0" bottom="0.4765625"/>
				</Texture>
				<Texture name="MerchantFrameBottomRightBorder" file="Interface\MerchantFrame\UI-Merchant-BottomBorder">
					<Size x="76" y="61"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="MerchantFrameBottomLeftBorder" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0" right="0.296875" top="0.4765625" bottom="0.953125"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="MerchantItem1" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" x="11" y="-69"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem2" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem1" relativePoint="TOPRIGHT" x="12" y="0"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem3" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem1" relativePoint="BOTTOMLEFT" x="0" y="-8"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem4" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem3" relativePoint="TOPRIGHT" x="12" y="0"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem5" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem3" relativePoint="BOTTOMLEFT" x="0" y="-8"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem6" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem5" relativePoint="TOPRIGHT" x="12" y="0"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem7" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem5" relativePoint="BOTTOMLEFT" x="0" y="-8"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem8" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem7" relativePoint="TOPRIGHT" x="12" y="0"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem9" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem7" relativePoint="BOTTOMLEFT" x="0" y="-8"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem10" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem9" relativePoint="TOPRIGHT" x="12" y="0"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem11" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem9" relativePoint="BOTTOMLEFT" x="0" y="-15"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem12" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem11" relativePoint="TOPRIGHT" x="12" y="0"/>
				</Anchors>
			</Frame>
			<Button name="MerchantRepairAllButton">
				<Size x="36" y="36"/>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMLEFT" x="172" y="11"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture name="MerchantRepairAllIcon" file="Interface\MerchantFrame\UI-Merchant-RepairIcons">
							<TexCoords left="0.28125" right="0.5625" top="0" bottom="0.5625"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						local repairAllCost, canRepair = GetRepairAllCost();
						if ( canRepair and (repairAllCost > 0) ) then
							GameTooltip:SetText(REPAIR_ALL_ITEMS);
							SetTooltipMoney(GameTooltip, repairAllCost);
						end
						GameTooltip:Show();
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick>
						RepairAllItems();
						PlaySound(SOUNDKIT.ITEM_REPAIR);
						GameTooltip:Hide();
					</OnClick>
					<OnEvent>
						local _, canRepair = GetRepairAllCost();
						if ( not canRepair ) then
							SetDesaturation(MerchantRepairAllIcon, true);
							SetDesaturation(MerchantGuildBankRepairButtonIcon, true);
							MerchantGuildBankRepairButton:Disable();
							self:Disable();
						else
							SetDesaturation(MerchantRepairAllIcon, false);
							SetDesaturation(MerchantGuildBankRepairButtonIcon, false);
							self:Enable();
							MerchantGuildBankRepairButton:Enable();
						end
					</OnEvent>
					<OnLoad>
						self:RegisterEvent("UPDATE_INVENTORY_DURABILITY");
					</OnLoad>
				</Scripts>
				<PushedTexture file="Interface\Buttons\UI-Quickslot-Depress"/>
				<HighlightTexture file="Interface\Buttons\ButtonHilight-Square" alphaMode="ADD"/>
			</Button>
			<Button name="MerchantRepairItemButton">
				<Size x="36" y="36"/>
				<Anchors>
					<Anchor point="RIGHT" relativeTo="MerchantRepairAllButton" relativePoint="LEFT" x="-2" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture file="Interface\MerchantFrame\UI-Merchant-RepairIcons">
							<TexCoords left="0" right="0.28125" top="0" bottom="0.5625"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText(REPAIR_AN_ITEM);
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick>
						if ( InRepairMode() ) then
							MerchantFrame:UnregisterEvent("PLAYER_MONEY");
							HideRepairCursor();
						else
							MerchantFrame:RegisterEvent("PLAYER_MONEY");
							ShowRepairCursor();
						end
					</OnClick>
				</Scripts>
				<PushedTexture file="Interface\Buttons\UI-Quickslot-Depress"/>
				<HighlightTexture file="Interface\Buttons\ButtonHilight-Square" alphaMode="ADD"/>
			</Button>
			<Button name="MerchantGuildBankRepairButton" hidden="true">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="LEFT" relativeTo="MerchantRepairAllButton" relativePoint="RIGHT" x="4" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture name="MerchantGuildBankRepairButtonIcon" file="Interface\MerchantFrame\UI-Merchant-RepairIcons">
							<TexCoords left="0.5625" right="0.84375" top="0" bottom="0.5625"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						local repairAllCost, canRepair = GetRepairAllCost();
						if ( canRepair and (repairAllCost > 0) ) then
							GameTooltip:SetText(REPAIR_ALL_ITEMS);
							SetTooltipMoney(GameTooltip, repairAllCost);
							local amount = GetGuildBankWithdrawMoney();
							local guildBankMoney = GetGuildBankMoney();
							if ( amount == -1 ) then
								-- Guild leader shows full guild bank amount
								amount = guildBankMoney;
							else
								amount = min(amount, guildBankMoney);
							end
							GameTooltip:AddLine(GUILDBANK_REPAIR, nil, nil, nil, true);
							SetTooltipMoney(GameTooltip, amount, "GUILD_REPAIR");
							GameTooltip:Show();
						end
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick>
						--FIXME!!! Need actual amount of guild money left to withdraw
						if(CanGuildBankRepair()) then
							RepairAllItems(true);
							PlaySound(SOUNDKIT.ITEM_REPAIR);
						end
						GameTooltip:Hide();
					</OnClick>
					<OnEvent>
						local _, canRepair = GetRepairAllCost();
						if ( not canRepair ) then
							SetDesaturation(MerchantRepairAllIcon, true);
							SetDesaturation(MerchantGuildBankRepairButtonIcon, true);
							MerchantGuildBankRepairButton:Disable();
							MerchantRepairAllButton:Disable();
						else
							SetDesaturation(MerchantRepairAllIcon, false);
							SetDesaturation(MerchantGuildBankRepairButtonIcon, false);
							self:Enable();
							MerchantRepairAllButton:Enable();
						end
					</OnEvent>
					<OnLoad>
						self:RegisterEvent("UPDATE_INVENTORY_DURABILITY");
					</OnLoad>
				</Scripts>
				<PushedTexture file="Interface\Buttons\UI-Quickslot-Depress"/>
				<HighlightTexture file="Interface\Buttons\ButtonHilight-Square" alphaMode="ADD"/>
			</Button>
			<Frame name="MerchantBuyBackItem">	
				<Size x="153" y="37"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem10" relativePoint="BOTTOMLEFT" x="0" y="-53"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentSlotTexture" file="Interface\Buttons\UI-EmptySlot">
							<Size x="64" y="64"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="-13" y="13"/>
							</Anchors>
						</Texture>
						<Texture name="$parentNameFrame" file="Interface\MerchantFrame\UI-Merchant-LabelSlots">
							<Size x="128" y="64"/>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentSlotTexture" relativePoint="RIGHT" x="-9" y="-10"/>
							</Anchors>
						</Texture>
						<FontString parentKey="Name" name="$parentName" inherits="GameFontNormalSmall" text="Item Name" justifyH="LEFT">
							<Size x="90" y="30"/>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentSlotTexture" relativePoint="RIGHT" x="-5" y="10"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button parentKey="ItemButton" name="$parentItemButton" inherits="ItemButtonTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parent"/>
						</Anchors>
						<Scripts>
							<OnLoad function="MerchantItemBuybackButton_OnLoad"/>
							<OnEvent>
								if ( event == "MERCHANT_UPDATE" ) then
									if ( GameTooltip:IsOwned(self) ) then
										GameTooltip:SetBuybackItem(GetNumBuybackItems());
										GameTooltip:Show();
									end
								end
							</OnEvent>
							<OnClick>
								BuybackItem(GetNumBuybackItems());
							</OnClick>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								GameTooltip:SetBuybackItem(GetNumBuybackItems());
								if (CanAffordMerchantItem(self:GetID()) == false) then
									SetCursor("BUY_ERROR_CURSOR");
								else
									SetCursor("BUY_CURSOR");
								end
							</OnEnter>
							<OnLeave function="GameTooltip_HideResetCursor"/>
						</Scripts>
					</Button>
					<Frame name="$parentMoneyFrame" inherits="SmallMoneyFrameTemplate">
						<Anchors>
							<Anchor point="BOTTOMLEFT" relativeTo="$parentNameFrame" relativePoint="BOTTOMLEFT" x="0" y="25"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								SmallMoneyFrame_OnLoad(self);
								MoneyFrame_SetType(self, "STATIC");
							</OnLoad>
						</Scripts>
					</Frame>
				</Frames>
			</Frame>
			<Frame name="MerchantExtraCurrencyInset" inherits="InsetFrameTemplate" useParentLevel="true">
				<Anchors>
					<Anchor point="BOTTOMLEFT" x="4" y="4"/>
					<Anchor point="TOPRIGHT" relativePoint="BOTTOMLEFT" x="169" y="27"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantExtraCurrencyBg" inherits="ThinGoldEdgeTemplate" >
				<Size x="120" y="30"/>
				<Anchors>
					<Anchor point="TOPRIGHT" relativePoint="BOTTOMLEFT" x="166" y="25"/>
					<Anchor point="BOTTOMLEFT" x="7" y="6"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantMoneyInset" inherits="InsetFrameTemplate" useParentLevel="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativePoint="BOTTOMRIGHT" x="-171" y="27"/>
					<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT" x="-5" y="4"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantMoneyBg" inherits="ThinGoldEdgeTemplate" >
				<Size x="120" y="30"/>
				<Anchors>
					<Anchor point="TOPRIGHT" relativePoint="BOTTOMRIGHT" x="-7" y="25"/>
					<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="-166" y="6"/>
				</Anchors>
			</Frame>
			<Frame name="MerchantMoneyFrame" inherits="SmallMoneyFrameTemplate">
				<Anchors>
					<Anchor point="BOTTOMRIGHT" relativeTo="MerchantFrame" relativePoint="BOTTOMRIGHT" x="-6" y="10"/>
				</Anchors>
			</Frame>
			<Button name="MerchantPrevPageButton">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="CENTER" relativeTo="MerchantFrame" relativePoint="BOTTOMLEFT" x="25" y="96"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString inherits="GameFontNormal" justifyH="LEFT" text="PREV">
							<Anchors>
								<Anchor point="LEFT" relativeTo="MerchantPrevPageButton" relativePoint="RIGHT"/>
							</Anchors>
						</FontString>
						<Texture file="Interface\Buttons\UI-PageButton-Background">
							<Size x="32" y="32"/>
							<Anchors>
								<Anchor point="CENTER" x="0" y="1"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnClick function="MerchantPrevPageButton_OnClick"/>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Up"/>
				<PushedTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Down"/>
				<DisabledTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Disabled"/>
				<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
			</Button>
			<Button name="MerchantNextPageButton">
				<Size x="32" y="32"/>
				<Anchors>
					<Anchor point="CENTER" relativeTo="MerchantFrame" relativePoint="BOTTOMLEFT" x="310" y="96"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString inherits="GameFontNormal" justifyH="RIGHT" text="NEXT">
							<Anchors>
								<Anchor point="RIGHT" relativeTo="MerchantNextPageButton" relativePoint="LEFT" x="-3" y="0"/>
							</Anchors>
						</FontString>
						<Texture file="Interface\Buttons\UI-PageButton-Background">
							<Size x="32" y="32"/>
							<Anchors>
								<Anchor point="CENTER" x="0" y="1"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnClick function="MerchantNextPageButton_OnClick"/>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Up"/>
				<PushedTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Down"/>
				<DisabledTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Disabled"/>
				<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
			</Button>
			<Button name="MerchantFrameTab1" inherits="CharacterFrameTabButtonTemplate" id="1" text="MERCHANT">
                <Anchors>
                    <Anchor point="CENTER" relativePoint="BOTTOMLEFT" x="50" y="-15"/>

                </Anchors>
                <Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText("", 1.0,1.0,1.0 );
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick>
						PanelTemplates_SetTab(MerchantFrame, self:GetID());
						MerchantFrame_Update();
					</OnClick>
                </Scripts>
            </Button>
            <Button name="MerchantFrameTab2" inherits="CharacterFrameTabButtonTemplate" id="2" text="BUYBACK">
                <Anchors>
                   <Anchor point="LEFT" relativeTo="MerchantFrameTab1" relativePoint="RIGHT" x="-16" y="0"/>
                </Anchors>
                <Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText("", 1.0,1.0,1.0 );
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick>
						PanelTemplates_SetTab(MerchantFrame, self:GetID());
						MerchantFrame_Update();
					</OnClick>
                </Scripts>
            </Button>
			<Frame name="$parentLootFilter" inherits="UIDropDownMenuTemplate" enableMouse="true" parentKey="lootFilter">
				<Anchors>
					<Anchor point="TOPRIGHT" x="0" y="-28"/>
				</Anchors>
			</Frame>
		</Frames>
		<Scripts>
			<OnHide function="MerchantFrame_OnHide"/>
			<OnLoad function="MerchantFrame_OnLoad"/>
			<OnShow function="MerchantFrame_OnShow"/>
			<OnEvent function="MerchantFrame_OnEvent"/>
			<OnUpdate function="MerchantFrame_OnUpdate"/>
			<OnMouseUp>
				if ( MerchantFrame.refundItem ) then
					if ( ContainerFrame_GetExtendedPriceString(MerchantFrame.refundItem, MerchantFrame.refundItemEquipped)) then
						-- a confirmation dialog has been shown
						return;
					end
				end
				PickupMerchantItem(0);
			</OnMouseUp>
			<OnReceiveDrag>
				MerchantItemButton_OnClick(self, "LeftButton");
			</OnReceiveDrag>
			<OnMouseWheel function="MerchantFrame_OnMouseWheel"/>
		</Scripts>
	</Frame>
</Ui>
