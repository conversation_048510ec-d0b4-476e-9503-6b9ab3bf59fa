<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Frame name="MoneyInputFrameTemplate" virtual="true">
		<Size>
			<AbsDimension x="176" y="18"/>
		</Size>
		<Frames>
			<EditBox parentKey="gold" name="$parentGold" letters="7" historyLines="0" numeric="true" autoFocus="false">
				<Size>
					<AbsDimension x="70" y="20"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border">
							<Size>
								<AbsDimension x="8" y="20"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="-5" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0" right="0.0625" top="0" bottom="0.625"/>
						</Texture>
						<Texture name="$parentRight" file="Interface\Common\Common-Input-Border">
							<Size>
								<AbsDimension x="8" y="20"/>
							</Size>
							<Anchors>
								<Anchor point="RIGHT">
									<Offset>
										<AbsDimension x="0" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0.9375" right="1.0" top="0" bottom="0.625"/>
						</Texture>
						<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border">
							<Size>
								<AbsDimension x="10" y="20"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
								<Anchor point="RIGHT" relativeTo="$parentRight" relativePoint="LEFT"/>
							</Anchors>
							<TexCoords left="0.0625" right="0.9375" top="0" bottom="0.625"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture parentKey="texture" file="Interface\MoneyFrame\UI-MoneyIcons">
							<Size>
								<AbsDimension x="13" y="13"/>
							</Size>
							<Anchors>
								<Anchor point="RIGHT">
									<Offset>
										<AbsDimension x="-4" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0" right="0.25" top="0" bottom="1"/>
						</Texture>
						<FontString parentKey="label" font="GameFontHighlightLeft" text="GOLD_AMOUNT_SYMBOL" hidden="true" justifyH="RIGHT">
							<Size x="18" y="18"/>
							<Anchors>
								<Anchor point="RIGHT">
									<Offset x="-4" y="0"/>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						self.darkenOnDigits = 9;
					</OnLoad>
					<OnTabPressed>
						if ( IsShiftKeyDown() and self:GetParent().previousFocus ) then
							self:GetParent().previousFocus:SetFocus();
						else
							_G[self:GetParent():GetName().."Silver"]:SetFocus();
						end
					</OnTabPressed>
					<OnEnterPressed>
						_G[self:GetParent():GetName().."Silver"]:SetFocus();
					</OnEnterPressed>
					<OnEscapePressed>
						EditBox_ClearFocus(self);
					</OnEscapePressed>
					<OnTextChanged>
						MoneyInputFrame_OnTextChanged(self);
					</OnTextChanged>
					<OnEditFocusLost>
						EditBox_ClearHighlight(self);
					</OnEditFocusLost>
					<OnEditFocusGained>
						EditBox_HighlightText(self);
					</OnEditFocusGained>
				</Scripts>
				<FontString inherits="ChatFontNormal"/>
			</EditBox>
			<EditBox parentKey="silver" name="$parentSilver" letters="2" historyLines="0" numeric="true" autoFocus="false">
				<Size>
					<AbsDimension x="48" y="20"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentGold" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="10" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border">
							<Size>
								<AbsDimension x="8" y="20"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="-5" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0" right="0.0625" top="0" bottom="0.625"/>
						</Texture>
						<Texture name="$parentRight" file="Interface\Common\Common-Input-Border">
							<Size>
								<AbsDimension x="8" y="20"/>
							</Size>
							<Anchors>
								<Anchor point="RIGHT">
									<Offset>
										<AbsDimension x="-10" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0.9375" right="1.0" top="0" bottom="0.625"/>
						</Texture>
						<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border">
							<Size>
								<AbsDimension x="10" y="20"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
								<Anchor point="RIGHT" relativeTo="$parentRight" relativePoint="LEFT"/>
							</Anchors>
							<TexCoords left="0.0625" right="0.9375" top="0" bottom="0.625"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture parentKey="texture" file="Interface\MoneyFrame\UI-MoneyIcons">
							<Size>
								<AbsDimension x="13" y="13"/>
							</Size>
							<Anchors>
								<Anchor point="RIGHT">
									<Offset>
										<AbsDimension x="-14" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0.25" right="0.5" top="0" bottom="1"/>
						</Texture>
						<FontString parentKey="label" font="GameFontHighlightLeft" text="SILVER_AMOUNT_SYMBOL" hidden="true" justifyH="RIGHT">
							<Size x="18" y="18"/>
							<Anchors>
								<Anchor point="RIGHT">
									<Offset x="-14" y="0"/>
								</Anchor>
							</Anchors>
						</FontString>						
					</Layer>
				</Layers>
				<Scripts>
					<OnTabPressed>
						if ( IsShiftKeyDown() ) then
							_G[self:GetParent():GetName().."Gold"]:SetFocus();
						else
							_G[self:GetParent():GetName().."Copper"]:SetFocus();
						end
					</OnTabPressed>
					<OnEnterPressed>
						_G[self:GetParent():GetName().."Copper"]:SetFocus();
					</OnEnterPressed>
					<OnEscapePressed>
						EditBox_ClearFocus(self);
					</OnEscapePressed>
					<OnTextChanged>
						MoneyInputFrame_OnTextChanged(self);
					</OnTextChanged>
					<OnEditFocusLost>
						EditBox_ClearHighlight(self);
					</OnEditFocusLost>
					<OnEditFocusGained>
						EditBox_HighlightText(self);
					</OnEditFocusGained>
				</Scripts>
				<FontString inherits="ChatFontNormal"/>
			</EditBox>
			<EditBox parentKey="copper" name="$parentCopper" letters="2" historyLines="0" numeric="true" autoFocus="false">
				<Size>
					<AbsDimension x="48" y="20"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentSilver" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="0" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border">
							<Size>
								<AbsDimension x="8" y="20"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="-5" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0" right="0.0625" top="0" bottom="0.625"/>
						</Texture>
						<Texture name="$parentRight" file="Interface\Common\Common-Input-Border">
							<Size>
								<AbsDimension x="8" y="20"/>
							</Size>
							<Anchors>
								<Anchor point="RIGHT">
									<Offset>
										<AbsDimension x="-10" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0.9375" right="1.0" top="0" bottom="0.625"/>
						</Texture>
						<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border">
							<Size>
								<AbsDimension x="10" y="20"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
								<Anchor point="RIGHT" relativeTo="$parentRight" relativePoint="LEFT"/>
							</Anchors>
							<TexCoords left="0.0625" right="0.9375" top="0" bottom="0.625"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture parentKey="texture" file="Interface\MoneyFrame\UI-MoneyIcons">
							<Size>
								<AbsDimension x="13" y="13"/>
							</Size>
							<Anchors>
								<Anchor point="RIGHT">
									<Offset>
										<AbsDimension x="-14" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0.5" right="0.75" top="0" bottom="1"/>
						</Texture>
						<FontString parentKey="label" font="GameFontHighlightLeft" text="COPPER_AMOUNT_SYMBOL" hidden="true" justifyH="RIGHT">
							<Size x="18" y="18"/>
							<Anchors>
								<Anchor point="RIGHT">
									<Offset x="-14" y="0"/>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnTabPressed>
						if ( IsShiftKeyDown() ) then
							_G[self:GetParent():GetName().."Silver"]:SetFocus();
						else
							if ( self:GetParent().nextFocus ) then
								self:GetParent().nextFocus:SetFocus();
							else
								self:ClearFocus();
							end
						end
					</OnTabPressed>
					<OnEnterPressed>
						if ( self:GetParent().nextFocus ) then
							self:GetParent().nextFocus:SetFocus();
						else
							self:ClearFocus();
						end
					</OnEnterPressed>
					<OnEscapePressed>
						EditBox_ClearFocus(self);
					</OnEscapePressed>
					<OnTextChanged>
						MoneyInputFrame_OnTextChanged(self);
					</OnTextChanged>
					<OnEditFocusLost>
						EditBox_ClearHighlight(self);
					</OnEditFocusLost>
					<OnEditFocusGained>
						EditBox_HighlightText(self);
					</OnEditFocusGained>
				</Scripts>
				<FontString inherits="ChatFontNormal"/>
			</EditBox>
		</Frames>
		<Scripts>
			<OnShow>
				MoneyInputFrame_OnShow(self);
			</OnShow>
		</Scripts>
	</Frame>
	<Frame name="FixedCoinFrameTemplate" virtual="true" hidden="true">
		<Size>
			<AbsDimension x="32" y="13"/>
		</Size>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="$parentTexture" parentKey="texture" file="Interface\MoneyFrame\UI-MoneyIcons">
					<Size>
						<AbsDimension x="13" y="13"/>
					</Size>
					<Anchors>
						<Anchor point="RIGHT"/>
					</Anchors>
				</Texture>
				<FontString name="$parentAmount" parentKey="amount" inherits="NumberFontNormalRight">
					<Size>
						<AbsDimension x="0" y="0"/>
					</Size>
					<Anchors>
						<Anchor point="RIGHT" x="-13" y="0"/>
					</Anchors>
				</FontString>
				<FontString name="$parentLabel" parentKey="label" inherits="GameFontHighlightLeft">
					<Size>
						<AbsDimension x="0" y="0"/>
					</Size>
					<Anchors>
						<Anchor point="RIGHT" x="0" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Frame>
</Ui>
