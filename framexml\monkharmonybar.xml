<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
  <Script file="MonkHarmonyBar.lua"/>		
 
	<Frame name="MonkLightEnergyTemplate" parentArray="LightEnergy" virtual="true">
		<Size x="18" y="17"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="OrbOff" atlas="MonkUI-OrbOff" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Glow" atlas="MonkUI-LightOrb" useAtlasSize="true" alpha="0">
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="activate" setToFinalAlpha="true">
				<Alpha childKey="Glow" fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
			</AnimationGroup>
			<AnimationGroup parentKey="deactivate" setToFinalAlpha="true">
				<Alpha childKey="Glow" fromAlpha="1" toAlpha="0" duration="0.3" order="1"/>
			</AnimationGroup>
			<AnimationGroup parentKey="spin">
				<Rotation radians="6.28318531" duration="0.7" order="1">
					<Origin point="BOTTOMRIGHT">
						<Offset x="-6" y="6"/>
					</Origin>
				</Rotation>
				<Scripts>
					<OnFinished>
						if self:GetParent():GetParent().hasHarmony then
							self:Play();
						end
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Scripts>
			<OnEnter>
				GameTooltip_SetDefaultAnchor(GameTooltip, self);
				GameTooltip:SetText(CHI_POWER, 1, 1, 1);
				GameTooltip:AddLine(CHI_TOOLTIP, nil, nil, nil, true);
				GameTooltip:Show();
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
	</Frame>
  
	<Frame name="MonkHarmonyBarFrame" inherits="ClassPowerBarFrame" mixin="ClassPowerBar, MonkPowerBar">
		<Size x="1" y="60"/>
		<Anchors>
			<Anchor point="TOP" x="49" y="-46"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture atlas="MonkUI-background-shadow" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture atlas="MonkUI-background" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame inherits="MonkLightEnergyTemplate">
				<Anchors>
					<Anchor point="LEFT" x="-43" y="1"/>
				</Anchors>
			</Frame>
		</Frames>
	</Frame>
</Ui>


