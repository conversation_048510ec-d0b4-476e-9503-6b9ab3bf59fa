<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="MonkStaggerBar.lua"/>
	<StatusBar name="MonkStaggerBar" inherits="AlternatePowerBarTemplate" parent="PlayerFrame">	<!--For Brewmaster Monks-->
		<Size x="94" y="12"/>
		<Anchors>
			<Anchor point="BOTTOMLEFT" x="118" y="23"/>
		</Anchors>
		<Scripts>
			<OnLoad function="MonkStaggerBar_OnLoad"/>
			<OnEvent>
				MonkStaggerBar_OnEvent(self, event, ...);
				TextStatusBar_OnEvent(self, event, ...);
			</OnEvent>
			<OnUpdate>
				MonkStaggerBar_OnUpdate(self, elapsed);
			</OnUpdate>
			<OnMouseUp>
				self:GetParent():Click(button);
			</OnMouseUp>
		</Scripts>
	</StatusBar>
</Ui>