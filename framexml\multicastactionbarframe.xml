<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="MultiCastActionBarFrame.lua"/>
	<CheckButton name="MultiCastSlotButtonTemplate" virtual="true">
		<Size x="30" y="30"/>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture file="Interface\Buttons\UI-TotemBar" parentKey="background"/>
			</Layer>
			<Layer level="OVERLAY">
				<Texture file="Interface\Buttons\UI-TotemBar" parentKey="overlayTex">
					<Size x="34" y="34"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnEvent>
				MultiCastSlotButton_OnEvent(self, event, ...);
			</OnEvent>
			<OnEnter>
				MultiCastSlotButton_OnEnter(self);
			</OnEnter>
			<OnLeave>
				MultiCastSlotButton_OnLeave(self);
			</OnLeave>
		</Scripts>
		<HighlightTexture alphaMode="ADD" file="Interface\Buttons\ButtonHilight-Square"/>
	</CheckButton>
	<CheckButton name="MultiCastActionButtonTemplate" inherits="ActionBarButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="30" y="30"/>
		</Size>
		<Layers>
			<Layer level="OVERLAY">
				<Texture file="Interface\Buttons\UI-TotemBar" parentKey="overlayTex">
					<Size x="34" y="34"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				MultiCastActionButton_OnLoad(self);
			</OnLoad>
			<OnEvent>
				MultiCastActionButton_OnEvent(self, event, ...);
			</OnEvent>
			<OnShow>
				MultiCastActionButton_OnShow(self);
			</OnShow>
			<OnEnter>
				MultiCastActionButton_OnEnter(self);
			</OnEnter>
			<OnLeave>
				MultiCastActionButton_OnLeave(self);
			</OnLeave>
			<PostClick>
				MultiCastActionButton_OnPostClick(self, button, down);
			</PostClick>
		</Scripts>
	</CheckButton>
	<CheckButton name="MultiCastSpellButton" inherits="SecureFrameTemplate, ActionButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="30" y="30"/>
		</Size>
		<Anchors>
			<Anchor point="BOTTOMLEFT">
				<Offset>
					<AbsDimension x="36" y="3"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentHotKey" inherits="NumberFontNormalSmallGray" justifyH="RIGHT">
					<Size>
						<AbsDimension x="36" y="10"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT">
							<Offset>
								<AbsDimension x="-2" y="-2"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentHighlight" file="Interface\Buttons\UI-TotemBar">
					<Size x="34" y="34"/>
					<Anchors>
						<Anchor point="CENTER">
						</Anchor>
					</Anchors>
					<TexCoords left="0.0078125" right="0.2734375" top="0.53515625" bottom="0.66796875"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad function="MultiCastSpellButton_OnLoad"/>
			<OnEvent function="MultiCastSpellButton_OnEvent"/>
			<OnEnter function="MultiCastSpellButton_OnEnter"/>
			<OnLeave function="MultiCastSpellButton_OnLeave"/>
		</Scripts>
	</CheckButton>
	<CheckButton name="MultiCastFlyoutButtonTemplate" virtual="true">
		<Size x="24" y="24"/>
		<Anchors>
			<Anchor point="BOTTOM">
				<Offset x="0" y="0"/>
			</Anchor>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentIcon" parentKey="icon"/>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				MultiCastFlyoutButton_OnLoad(self);
			</OnLoad>
			<OnClick>
				MultiCastFlyoutButton_OnClick(self);
			</OnClick>
			<OnEnter>
				MultiCastFlyoutButton_OnEnter(self);
			</OnEnter>
			<OnLeave>
				MultiCastFlyoutButton_OnLeave(self);
			</OnLeave>
		</Scripts>
		<HighlightTexture alphaMode="ADD" file="Interface\Buttons\ButtonHilight-Square"/>
	</CheckButton>

	<Frame name="MultiCastActionBarFrame" parent="MainMenuBar" enableMouse="true" hidden="true">
		<Size>
			<AbsDimension x="230" y="38"/>
		</Size>
		<Anchors>
			<Anchor point="BOTTOMLEFT" relativeTo="MainMenuBar" relativePoint="TOPLEFT">
				<Offset>
					<AbsDimension x="30" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Frames>
			<Frame name="MultiCastFlyoutFrame" hidden="true">
				<Size x="32" y="30"/>
				<Anchors>
					<Anchor point="BOTTOM">
						<Offset x="0" y="0"/>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentTop" file="Interface\Buttons\UI-TotemBar" parentKey="top">
							<Size x="32" y="20"/>
							<Anchors>
								<Anchor point="TOP">
									<Offset x="0" y="-10"/>
								</Anchor>
							</Anchors>
						</Texture>
						<Texture name="$parentMiddle" file="Interface\Buttons\UI-TotemBar" parentKey="middle">
							<Size x="32" y="20"/>
							<Anchors>
								<Anchor point="TOP" relativeTo="$parentTop" relativePoint="BOTTOM">
									<Offset x="0" y="0"/>
								</Anchor>
								<Anchor point="BOTTOM">
									<Offset x="0" y="0"/>
								</Anchor>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Button name="MultiCastFlyoutFrameCloseButton">
						<Size x="28" y="18"/>
						<Anchors>
							<Anchor point="TOP">
								<Offset x="0" y="0"/>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnClick function="MultiCastFlyoutFrameCloseButton_OnClick"/>
						</Scripts>
						<NormalTexture file="Interface\Buttons\UI-TotemBar" parentKey="normalTexture"/>
						<HighlightTexture file="Interface\Buttons\UI-TotemBar" alphaMode="ADD">
							<Size x="20" y="11"/>
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
							<TexCoords left="0.5625" right="0.71875" top="0.26953125" bottom="0.30859375"/>
						</HighlightTexture>
					</Button>
				</Frames>
				<Scripts>
					<OnShow function="MultiCastFlyoutFrame_OnShow"/>
					<OnHide function="MultiCastFlyoutFrame_OnHide"/>
					<OnEnter function="MultiCastFlyoutFrame_OnEnter"/>
					<OnLeave function="MultiCastFlyoutFrame_OnLeave"/>
					<OnUpdate function="MultiCastFlyoutFrame_OnUpdate"/>
				</Scripts>
			</Frame>
			<Button name="MultiCastFlyoutFrameOpenButton" hidden="true">
				<Size x="28" y="18"/>
				<Anchors>
					<Anchor point="BOTTOM" relativePoint="TOP">
						<Offset x="0" y="0"/>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="MultiCastFlyoutFrameOpenButton_OnClick"/>
					<OnLeave function="MultiCastFlyoutFrameOpenButton_OnLeave"/>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-TotemBar" parentKey="normalTexture"/>
				<HighlightTexture file="Interface\Buttons\UI-TotemBar" alphaMode="ADD">
					<Size x="20" y="11"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
					<TexCoords left="0.5625" right="0.71875" top="0.34375" bottom="0.3828125"/>
				</HighlightTexture>
			</Button>
			<CheckButton name="MultiCastSummonSpellButton" inherits="MultiCastSpellButton" id="1">
				<Size>
					<AbsDimension x="30" y="30"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="3" y="3"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Frames>
					<Button name="$parentFlyoutButton" parentKey="flyoutButton" hidden="true">
						<Size x="28" y="18"/>
						<Anchors>
							<Anchor point="BOTTOM" relativePoint="TOP">
								<Offset x="0" y="0"/>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								self:SetFrameLevel(MultiCastFlyoutFrame:GetFrameLevel() + 6);
							</OnLoad>
							<OnClick>
								MultiCastSummonSpellButtonFlyoutButton_OnClick(self);
							</OnClick>
							<OnLeave>
								MultiCastSummonSpellButtonFlyoutButton_OnLeave(self);
							</OnLeave>
						</Scripts>
						<NormalTexture file="Interface\Buttons\UI-TotemBar">
							<TexCoords left="0.7734375" right="0.9921875" top="0.328125" bottom="0.3984375"/>
						</NormalTexture>
						<HighlightTexture file="Interface\Buttons\UI-TotemBar" alphaMode="ADD">
							<Size x="20" y="11"/>
							<Anchors>
								<Anchor point="CENTER">
									<Offset x="0" y="0"/>
								</Anchor>
							</Anchors>
							<TexCoords left="0.5625" right="0.71875" top="0.34375" bottom="0.3828125"/>
						</HighlightTexture>
					</Button>
				</Frames>
				<Scripts>
					<OnLoad function="MultiCastSummonSpellButton_OnLoad"/>
					<OnEvent function="MultiCastSummonSpellButton_OnEvent"/>
					<OnClick function="MultiCastSummonSpellButton_OnClick"/>
					<OnEnter function="MultiCastSummonSpellButton_OnEnter"/>
					<OnLeave function="MultiCastSummonSpellButton_OnLeave"/>
				</Scripts>
			</CheckButton>
			<CheckButton name="MultiCastSlotButton1" inherits="MultiCastSlotButtonTemplate" id="1">
				<Anchors>
					<Anchor point="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="36" y="3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</CheckButton>
			<CheckButton name="MultiCastSlotButton2" inherits="MultiCastSlotButtonTemplate" id="2">
				<Anchors>
					<Anchor point="LEFT" relativeTo="MultiCastSlotButton1" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="8" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
			</CheckButton>
			<CheckButton name="MultiCastSlotButton3" inherits="MultiCastSlotButtonTemplate" id="3">
				<Anchors>
					<Anchor point="LEFT" relativeTo="MultiCastSlotButton2" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="8" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
			</CheckButton>
			<CheckButton name="MultiCastSlotButton4" inherits="MultiCastSlotButtonTemplate" id="4">
				<Anchors>
					<Anchor point="LEFT" relativeTo="MultiCastSlotButton3" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="8" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
			</CheckButton>
			<Frame name="MultiCastActionPage1" id="1">
				<Size x="144" y="30"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="36" y="3"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Frames>
					<CheckButton name="MultiCastActionButton1" inherits="MultiCastActionButtonTemplate" id="1">
						<Anchors>
							<Anchor point="LEFT">
								<Offset>
									<AbsDimension x="0" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</CheckButton>
					<CheckButton name="MultiCastActionButton2" inherits="MultiCastActionButtonTemplate" id="2">
						<Anchors>
							<Anchor point="LEFT" relativeTo="MultiCastActionButton1" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="8" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</CheckButton>
					<CheckButton name="MultiCastActionButton3" inherits="MultiCastActionButtonTemplate" id="3">
						<Anchors>
							<Anchor point="LEFT" relativeTo="MultiCastActionButton2" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="8" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</CheckButton>
					<CheckButton name="MultiCastActionButton4" inherits="MultiCastActionButtonTemplate" id="4">
						<Anchors>
							<Anchor point="LEFT" relativeTo="MultiCastActionButton3" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="8" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</CheckButton>
				</Frames>
			</Frame>
			<Frame name="MultiCastActionPage2" id="2" hidden="true">
				<Size x="144" y="30"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="36" y="3"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Frames>
					<CheckButton name="MultiCastActionButton5" inherits="MultiCastActionButtonTemplate" id="5">
						<Anchors>
							<Anchor point="LEFT">
								<Offset>
									<AbsDimension x="0" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</CheckButton>
					<CheckButton name="MultiCastActionButton6" inherits="MultiCastActionButtonTemplate" id="6">
						<Anchors>
							<Anchor point="LEFT" relativeTo="MultiCastActionButton5" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="8" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</CheckButton>
					<CheckButton name="MultiCastActionButton7" inherits="MultiCastActionButtonTemplate" id="7">
						<Anchors>
							<Anchor point="LEFT" relativeTo="MultiCastActionButton6" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="8" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</CheckButton>
					<CheckButton name="MultiCastActionButton8" inherits="MultiCastActionButtonTemplate" id="8">
						<Anchors>
							<Anchor point="LEFT" relativeTo="MultiCastActionButton7" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="8" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</CheckButton>
				</Frames>
			</Frame>
			<Frame name="MultiCastActionPage3" id="3" hidden="true">
				<Size x="144" y="30"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="36" y="3"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Frames>
					<CheckButton name="MultiCastActionButton9" inherits="MultiCastActionButtonTemplate" id="9">
						<Anchors>
							<Anchor point="LEFT">
								<Offset>
									<AbsDimension x="0" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</CheckButton>
					<CheckButton name="MultiCastActionButton10" inherits="MultiCastActionButtonTemplate" id="10">
						<Anchors>
							<Anchor point="LEFT" relativeTo="MultiCastActionButton9" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="8" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</CheckButton>
					<CheckButton name="MultiCastActionButton11" inherits="MultiCastActionButtonTemplate" id="11">
						<Anchors>
							<Anchor point="LEFT" relativeTo="MultiCastActionButton10" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="8" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</CheckButton>
					<CheckButton name="MultiCastActionButton12" inherits="MultiCastActionButtonTemplate" id="12">
						<Anchors>
							<Anchor point="LEFT" relativeTo="MultiCastActionButton11" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="8" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</CheckButton>
				</Frames>
			</Frame>
			<CheckButton name="MultiCastRecallSpellButton" inherits="MultiCastSpellButton" id="1">
				<Size>
					<AbsDimension x="30" y="30"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativeTo="MultiCastActionButton4" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="8" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad function="MultiCastRecallSpellButton_OnLoad"/>
					<OnClick function="MultiCastRecallSpellButton_OnClick"/>
				</Scripts>
			</CheckButton>
		</Frames>
		<Scripts>
			<OnLoad function="MultiCastActionBarFrame_OnLoad"/>
			<OnEvent function="MultiCastActionBarFrame_OnEvent"/>
			<OnUpdate function="MultiCastActionBarFrame_OnUpdate"/>
			<OnShow function="UIParent_ManageFramePositions"/>
			<OnHide function="UIParent_ManageFramePositions"/>
		</Scripts>
	</Frame>
</Ui>
