<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="NavigationBar.lua"/>

	<!-- Nav bar tiled textures.
	size=128,512
	<Texture name="_GMChat-TitleBar.png" horizTile="true" >
		<Size x="128" y="3"/>	
		<TexCoords left="0.00000000" right="1.00000000" top="0.00195313" bottom="0.00781250"/>	
	</Texture>
	<Texture name="_GMChat-TitleBG.png" horizTile="true" >
		<Size x="128" y="24"/>	
		<TexCoords left="0.00000000" right="1.00000000" top="0.01171875" bottom="0.05859375"/>	
	</Texture>
	<Texture name="_NavMenu-Button-Up.png" horizTile="true" >
		<Size x="128" y="30"/>	
		<TexCoords left="0.00000000" right="1.00000000" top="0.06250000" bottom="0.12109375"/>	
	</Texture>
	<Texture name="_NavMenu-Button-Down.png" horizTile="true" >
		<Size x="128" y="30"/>	
		<TexCoords left="0.00000000" right="1.00000000" top="0.12500000" bottom="0.18359375"/>	
	</Texture>
	<Texture name="_NavMenu-BarBG.png" horizTile="true" >
		<Size x="128" y="34"/>	
		<TexCoords left="0.00000000" right="1.00000000" top="0.18750000" bottom="0.25390625"/>	
	</Texture>
	<Texture name="_NavMenu-BarOverlay.png" horizTile="true" >
		<Size x="128" y="34"/>	
		<TexCoords left="0.00000000" right="1.00000000" top="0.25781250" bottom="0.32421875"/>	
	</Texture>
	<Texture name="_GMChatRequest-Mid.png" horizTile="true" >
		<Size x="128" y="72"/>	
		<TexCoords left="0.00000000" right="1.00000000" top="0.32812500" bottom="0.46875000"/>	
	</Texture>
	<Texture name="_GMChatRequestGlow-Mid.png" horizTile="true" >
		<Size x="128" y="72"/>	
		<TexCoords left="0.00000000" right="1.00000000" top="0.47265625" bottom="0.61328125"/>	
	</Texture>
	-->

	<!-- Navbar Textures
	size=512,128
	<Texture name="Divider.png" >
		<Size x="229" y="45"/>	
		<TexCoords left="0.00195313" right="0.44921875" top="0.00781250" bottom="0.35937500"/>	
	</Texture>
	<Texture name="NavMenu-ButtonSelection.png" >
		<Size x="128" y="34"/>	
		<TexCoords left="0.00195313" right="0.25195313" top="0.37500000" bottom="0.64062500"/>	
	</Texture>
	<Texture name="NavMenu-ButtonHighlight.png" >
		<Size x="128" y="34"/>	
		<TexCoords left="0.00195313" right="0.25195313" top="0.65625000" bottom="0.92187500"/>	
	</Texture>
	<Texture name="GMChatRequest-Left.png" >
		<Size x="49" y="72"/>	
		<TexCoords left="0.25585938" right="0.35156250" top="0.37500000" bottom="0.93750000"/>	
	</Texture>
	<Texture name="GMChatRequest-Right.png" >
		<Size x="40" y="72"/>	
		<TexCoords left="0.35546875" right="0.43359375" top="0.37500000" bottom="0.93750000"/>	
	</Texture>
	<Texture name="GMChat-TitleBarNubRight.png" >
		<Size x="4" y="10"/>	
		<TexCoords left="0.43750000" right="0.44531250" top="0.37500000" bottom="0.45312500"/>	
	</Texture>
	<Texture name="GMChat-TitleBarNubLeft.png" >
		<Size x="4" y="10"/>	
		<TexCoords left="0.43750000" right="0.44531250" top="0.46875000" bottom="0.54687500"/>	
	</Texture>
	<Texture name="NavMenu-HomeButton-up.png" >
		<Size x="128" y="30"/>	
		<TexCoords left="0.45312500" right="0.70312500" top="0.00781250" bottom="0.24218750"/>	
	</Texture>
	<Texture name="NavMenu-HomeButton-down.png" >
		<Size x="128" y="30"/>	
		<TexCoords left="0.45312500" right="0.70312500" top="0.25781250" bottom="0.49218750"/>	
	</Texture>
	<Texture name="NavMenu-HomeButton-highlight.png" >
		<Size x="128" y="30"/>	
		<TexCoords left="0.45312500" right="0.70312500" top="0.50781250" bottom="0.74218750"/>	
	</Texture>
	<Texture name="NavMenu-ButtonOverflow-down.png" >
		<Size x="44" y="30"/>	
		<TexCoords left="0.45312500" right="0.53906250" top="0.75781250" bottom="0.99218750"/>	
	</Texture>
	<Texture name="NavMenu-ButtonOverflow-up.png" >
		<Size x="44" y="30"/>	
		<TexCoords left="0.54296875" right="0.62890625" top="0.75781250" bottom="0.99218750"/>	
	</Texture>
	<Texture name="NavMenu-Arrow-down.png" >
		<Size x="21" y="30"/>	
		<TexCoords left="0.63281250" right="0.67382813" top="0.75781250" bottom="0.99218750"/>	
	</Texture>
	<Texture name="NavMenu-SelectedArrowIcon.png" >
		<Size x="8" y="13"/>	
		<TexCoords left="0.67773438" right="0.69335938" top="0.75781250" bottom="0.85937500"/>	
	</Texture>
	<Texture name="GMChatRequestGlow-Left.png" >
		<Size x="49" y="72"/>	
		<TexCoords left="0.70703125" right="0.80273438" top="0.00781250" bottom="0.57031250"/>	
	</Texture>
	<Texture name="TicketBadge.png" >
		<Size x="34" y="35"/>	
		<TexCoords left="0.70703125" right="0.77343750" top="0.58593750" bottom="0.85937500"/>	
	</Texture>
	<Texture name="GMChatRequestGlow-Right.png" >
		<Size x="40" y="72"/>	
		<TexCoords left="0.80664063" right="0.88476563" top="0.00781250" bottom="0.57031250"/>	
	</Texture>
	<Texture name="TicketBadge-Highlight.png" >
		<Size x="34" y="35"/>	
		<TexCoords left="0.80664063" right="0.87304688" top="0.58593750" bottom="0.85937500"/>	
	</Texture>
	<Texture name="TicketBadge-Down.png" >
		<Size x="34" y="35"/>	
		<TexCoords left="0.88867188" right="0.95507813" top="0.00781250" bottom="0.28125000"/>	
	</Texture>
	<Texture name="NavMenu-Arrow-up.png" >
		<Size x="21" y="30"/>	
		<TexCoords left="0.88867188" right="0.92968750" top="0.29687500" bottom="0.53125000"/>	
	</Texture-->

	
	<Button name="NavButtonTemplate" motionScriptsWhileDisabled="true" virtual="true">
		<Size x="80" y="30"/>
		<Layers>
			<Layer level="OVERLAY" textureSubLevel="2">
				<Texture file="Interface\HelpFrame\CS_HelpTextures" parentKey="arrowUp">
					<Size x="21" y="30"/>
					<Anchors>
						<Anchor point="LEFT" relativePoint="RIGHT" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.88867188" right="0.92968750" top="0.29687500" bottom="0.53125000"/>
				</Texture>
				<Texture file="Interface\HelpFrame\CS_HelpTextures" parentKey="arrowDown">
					<Size x="21" y="30"/>
					<Anchors>
						<Anchor point="LEFT" relativePoint="RIGHT" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.63281250" right="0.67382813" top="0.75781250" bottom="0.99218750"/>
				</Texture>
				<Texture file="Interface\HelpFrame\CS_HelpTextures" parentKey="selected" hidden="true">
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="0"/>
						<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.00195313" right="0.25195313" top="0.37500000" bottom="0.64062500"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="MenuArrowButton" hidden="true">
				<Size x="27" y="31"/>
				<Anchors>
					<Anchor point="RIGHT" relativePoint="TOPRIGHT" x="-2" y="-15"/>
				</Anchors>
				<Layers>
					<Layer level="OVERLAY">
						<Texture parentKey="Art" file="Interface\Buttons\SquareButtonTextures">
							<Size x="12" y="12"/>
							<Anchors>
								<Anchor point="CENTER" x="0" y="-1"/>
							</Anchors>
							<TexCoords left="0.45312500" right="0.64062500" bottom="0.01562500" top="0.20312500"/>
						</Texture>
					</Layer>
				</Layers>
				<NormalTexture file="Interface\Buttons\UI-SquareButton-Up" parentKey="NormalTexture" alpha="0">
					<Size x="32" y="32"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</NormalTexture>
				<PushedTexture file="Interface\Buttons\UI-SquareButton-Down" parentKey="PushedTexture" alpha="0">
					<Size x="32" y="32"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</PushedTexture>
				<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD">
					<Size x="32" y="32"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</HighlightTexture>
				<Scripts>
					<OnMouseDown>
						self.Art:SetPoint("CENTER", -1, -2);
					</OnMouseDown>
					<OnMouseUp>
						self.Art:SetPoint("CENTER", 0, -1);
					</OnMouseUp>
					<OnEnter>
						self.NormalTexture:SetAlpha(1);
						self.PushedTexture:SetAlpha(1);
					</OnEnter>
					<OnLeave>
						self.NormalTexture:SetAlpha(0);
						self.PushedTexture:SetAlpha(0);
					</OnLeave>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						NavBar_ToggleMenu(self:GetParent());
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnMouseDown>
				if self:IsEnabled() then
					self.arrowUp:Hide();
					self.arrowDown:Show();
				end
			</OnMouseDown>
			<OnMouseUp>
				self.arrowDown:Hide();
				self.arrowUp:Show();
			</OnMouseUp>
			<OnClick>
				PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
			</OnClick>
			<OnEnable>
				self.arrowDown:Hide();
				self.arrowUp:Show();
			</OnEnable>
			<OnDisable>
				self.arrowDown:Hide();
				self.arrowUp:Show();
			</OnDisable>
		</Scripts>
		<NormalTexture file="Interface\HelpFrame\CS_HelpTextures_Tile" horizTile="true">
			<TexCoords left="0.00000000" right="1.00000000" top="0.06250000" bottom="0.12109375"/>
		</NormalTexture>		
		<PushedTexture file="Interface\HelpFrame\CS_HelpTextures_Tile" horizTile="true">
			<TexCoords left="0.00000000" right="1.00000000" top="0.12500000" bottom="0.18359375"/>
		</PushedTexture>
		<HighlightTexture file="Interface\HelpFrame\CS_HelpTextures" alphaMode="ADD">
			<TexCoords left="0.00195313" right="0.25195313" top="0.65625000" bottom="0.92187500"/>
		</HighlightTexture>
		<ButtonText name="$parentText" inherits="GameFontNormal" justifyH="LEFT" parentKey="text">
			<Size x="0" y="12"/>
			<Anchors>
				<Anchor point="LEFT" x="20" y="0"/>
			</Anchors>
		</ButtonText>
	</Button>
	
	<Frame name="NavBarTemplate" parentKey="navBar" virtual="true">
		<Size x="300" y="34"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture file="Interface\HelpFrame\CS_HelpTextures_Tile" horizTile="true">
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="0" y="0"/>
						<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.00000000" right="1.00000000" top="0.18750000" bottom="0.25390625"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentOverlay" parentKey="overlay">
				<Anchors>
					<Anchor point="TOPLEFT" x="0" y="0"/>
					<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="OVERLAY" textureSubLevel="5">
						<Texture file="Interface\HelpFrame\CS_HelpTextures_Tile" horizTile="true">
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
								<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
							</Anchors>
							<TexCoords left="0.00000000" right="1.00000000" top="0.25781250" bottom="0.32421875"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						self:SetFrameLevel(50);
					</OnLoad>
				</Scripts>
			</Frame>
			<Button name="$parentOverflowButton" parentKey="overflow">
				<Size x="44" y="30"/>
				<Scripts>
					<OnLoad>
						self.xoffset = -18
					</OnLoad>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\HelpFrame\CS_HelpTextures">
					<TexCoords left="0.54296875" right="0.62890625" top="0.75781250" bottom="0.99218750"/>
				</NormalTexture>		
				<PushedTexture file="Interface\HelpFrame\CS_HelpTextures">
					<TexCoords left="0.45312500" right="0.53906250" top="0.75781250" bottom="0.99218750"/>
				</PushedTexture>
				<HighlightTexture file="Interface\HelpFrame\CS_HelpTextures" alphaMode="ADD" alpha="0.4">
					<TexCoords left="0.54296875" right="0.62890625" top="0.75781250" bottom="0.99218750"/>
				</HighlightTexture>
			</Button>
			<Button name="$parentHomeButton" parentKey="home" text="HOME">
				<Size x="128" y="30"/>
				<Layers>
					<Layer level="OVERLAY">
						<Texture name="$parentLeft" file="Interface\Common\ShadowOverlay-Left">
							<Size x="30" y="30"/>
							<Anchors>
								<Anchor point="LEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						self.xoffset = -15;
						local newWidth = min(128, self.text:GetStringWidth()+50)
						local texCoordoffsetX = (newWidth/128)*0.25;
							
						self:GetNormalTexture():SetTexCoord(0.70312500-texCoordoffsetX, 0.70312500, 0.00781250, 0.24218750);
						self:GetPushedTexture():SetTexCoord(0.70312500-texCoordoffsetX, 0.70312500, 0.25781250, 0.49218750);
						self:GetHighlightTexture():SetTexCoord(0.70312500-texCoordoffsetX, 0.71312500, 0.50781250, 0.74218750);
						
						self:SetWidth(newWidth);
					</OnLoad>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\HelpFrame\CS_HelpTextures">
				</NormalTexture>		
				<PushedTexture file="Interface\HelpFrame\CS_HelpTextures">
				</PushedTexture>
				<HighlightTexture file="Interface\HelpFrame\CS_HelpTextures" alphaMode="ADD">
				</HighlightTexture>
				<ButtonText name="$parentText" inherits="GameFontNormal" justifyH="LEFT" parentKey="text">
					<Size x="0" y="12"/>
					<Anchors>
						<Anchor point="LEFT" x="10" y="0"/>
						<Anchor point="RIGHT" x="-30" y="0"/>
					</Anchors>
				</ButtonText>
			</Button>
		</Frames>
	</Frame>
</Ui>
