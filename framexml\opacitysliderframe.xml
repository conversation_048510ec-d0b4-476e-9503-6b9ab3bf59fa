<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Slider name="OpacitySliderFrame" orientation="VERTICAL">
		<Size>
			<AbsDimension x="24" y="128"/>
		</Size>
		<Anchors>
			<Anchor point="TOPLEFT">
				<Offset>
					<AbsDimension x="0" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Backdrop bgFile="Interface\Buttons\UI-SliderBar-Background" edgeFile="Interface\Buttons\UI-SliderBar-Border" tile="true">
			<EdgeSize>
				<AbsValue val="8"/>
			</EdgeSize>
			<TileSize>
				<AbsValue val="8"/>
			</TileSize>
			<BackgroundInsets>
				<AbsInset left="3" right="3" top="6" bottom="6"/>
			</BackgroundInsets>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentText" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="BOTTOM" relativePoint="TOP"/>
					</Anchors>
				</FontString>
				<FontString inherits="GameFontHighlightSmall" text="LOW">
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="BOTTOMLEFT">
							<Offset>
								<AbsDimension x="2" y="3"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				<FontString inherits="GameFontHighlightSmall" text="HIGH">
					<Anchors>
						<Anchor point="TOPRIGHT" relativePoint="BOTTOMRIGHT">
							<Offset>
								<AbsDimension x="-2" y="3"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<ThumbTexture file="Interface\Buttons\UI-SliderBar-Button-Horizontal">
			<Size>
				<AbsDimension x="32" y="32"/>
			</Size>
		</ThumbTexture>
	</Slider>
</Ui>
