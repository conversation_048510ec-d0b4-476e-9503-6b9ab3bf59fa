<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<!-- if you change something here you probably want to change the glue version too -->

	<Script file="OptionsFrameTemplates.lua"/>

	<!-- Options Frame Templates -->

	<Button name="OptionsFrameTabButtonTemplate" virtual="true">
        <Size>
            <AbsDimension x="115" y="24"/>
        </Size>
        <Layers>
        	<Layer level="BORDER">
        		<Texture name="$parentLeftDisabled" file="Interface\OptionsFrame\UI-OptionsFrame-ActiveTab">
        			<Size>
        				<AbsDimension x="20" y="24"/>
        			</Size>
        			<Anchors>
        				<Anchor point="BOTTOMLEFT">
        					<Offset>
        						<AbsDimension x="0" y="-3"/>
        					</Offset>
        				</Anchor>
        			</Anchors>
					<TexCoords left="0" right="0.15625" top="0" bottom="1.0"/>
        		</Texture>
				<Texture name="$parentMiddleDisabled" file="Interface\OptionsFrame\UI-OptionsFrame-ActiveTab">
        			<Size>
        				<AbsDimension x="88" y="24"/>
        			</Size>
        			<Anchors>
        				<Anchor point="LEFT" relativeTo="$parentLeftDisabled" relativePoint="RIGHT"/>
        			</Anchors>
					<TexCoords left="0.15625" right="0.84375" top="0" bottom="1.0"/>
        		</Texture>
				<Texture name="$parentRightDisabled" file="Interface\OptionsFrame\UI-OptionsFrame-ActiveTab">
        			<Size>
        				<AbsDimension x="20" y="24"/>
        			</Size>
        			<Anchors>
        				<Anchor point="LEFT" relativeTo="$parentMiddleDisabled" relativePoint="RIGHT"/>
        			</Anchors>
					<TexCoords left="0.84375" right="1.0" top="0" bottom="1.0"/>
        		</Texture>
				<Texture name="$parentLeft" file="Interface\OptionsFrame\UI-OptionsFrame-InActiveTab">
        			<Size>
        				<AbsDimension x="20" y="24"/>
        			</Size>
        			<Anchors>
        				<Anchor point="TOPLEFT"/>
        			</Anchors>
					<TexCoords left="0" right="0.15625" top="0" bottom="1.0"/>
        		</Texture>
				<Texture name="$parentMiddle" file="Interface\OptionsFrame\UI-OptionsFrame-InActiveTab">
        			<Size>
        				<AbsDimension x="88" y="24"/>
        			</Size>
        			<Anchors>
        				<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.15625" right="0.84375" top="0" bottom="1.0"/>
        		</Texture>
				<Texture name="$parentRight" file="Interface\OptionsFrame\UI-OptionsFrame-InActiveTab">
        			<Size>
        				<AbsDimension x="20" y="24"/>
        			</Size>
        			<Anchors>
        				<Anchor point="LEFT" relativeTo="$parentMiddle" relativePoint="RIGHT"/>
        			</Anchors>
					<TexCoords left="0.84375" right="1.0" top="0" bottom="1.0"/>
        		</Texture>
        	</Layer>
        </Layers>
		<Scripts>
			<OnLoad>
				self:SetFrameLevel(self:GetFrameLevel() + 4);
				self.deselectedTextY = -3;
				self.selectedTextY = -2;
			</OnLoad>
			<OnShow>
				PanelTemplates_TabResize(self, 0);
				_G[self:GetName().."HighlightTexture"]:SetWidth(self:GetTextWidth() + 30);
			</OnShow>
		</Scripts>
		<ButtonText name="$parentText">
			<Anchors>
				<Anchor point="CENTER">
					<Offset>
						<AbsDimension x="0" y="-3"/>
					</Offset>
				</Anchor>
			</Anchors>
		</ButtonText>
		<NormalFont style="GameFontNormalSmall"/>
		<HighlightFont style="GameFontHighlightSmall"/>
		<DisabledFont style="GameFontHighlightSmall"/>
		<HighlightTexture name="$parentHighlightTexture" file="Interface\PaperDollInfoFrame\UI-Character-Tab-Highlight" alphaMode="ADD">
			<Anchors>
				<Anchor point="LEFT">
					<Offset>
						<AbsDimension x="10" y="-4"/>
					</Offset>
				</Anchor>
				<Anchor point="RIGHT">
					<Offset>
						<AbsDimension x="-10" y="-4"/>
					</Offset>
				</Anchor>
			</Anchors>
		</HighlightTexture>
    </Button>
	<Frame name="OptionsFrameListTemplate" virtual="true">
		<Layers>
		<!--
			<Layer level="ARTWORK">
				<FontString name="$parentLabel" inherits="GameFontHighlight" text="CATEGORY">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parent" relativePoint="TOPLEFT">
							<Offset>
								<AbsDimension x="6" y="4"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		-->
			<Layer level="BACKGROUND">
				<!-- Yay. It's a backdrop. -->
				<Texture name="$parentTopLeft" file="Interface\Tooltips\UI-Tooltip-Border">
					<Size>
						<AbsDimension x="16" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0.5" right="0.625" top="0" bottom="1"/>
				</Texture>
				<Texture name="$parentBottomLeft" file="Interface\Tooltips\UI-Tooltip-Border">
					<Size>
						<AbsDimension x="16" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0.75" right="0.875" top="0" bottom="1"/>
				</Texture>
				<Texture name="$parentBottomRight" file="Interface\Tooltips\UI-Tooltip-Border">
					<Size>
						<AbsDimension x="16" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
					<TexCoords left="0.875" right="1" top="0" bottom="1"/>
				</Texture>
				<Texture name="$parentTopRight" file="Interface\Tooltips\UI-Tooltip-Border">
					<Size>
						<AbsDimension x="16" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="TOPRIGHT"/>
					</Anchors>
					<TexCoords left="0.625" right="0.75" top="0" bottom="1"/>
				</Texture>
				<Texture name="$parentLeft" file="Interface\Tooltips\UI-Tooltip-Border">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTopLeft" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomLeft" relativePoint="TOPRIGHT"/>
					</Anchors>
					<TexCoords left="0" right="0.125" top="0" bottom="1"/>
				</Texture>
				<Texture name="$parentRight" file="Interface\Tooltips\UI-Tooltip-Border">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTopRight" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRight" relativePoint="TOPRIGHT"/>
					</Anchors>
					<TexCoords left="0.125" right="0.25" top="0" bottom="1"/>
				</Texture>
				<Texture name="$parentBottom" file="Interface\OptionsFrame\UI-OptionsFrame-Spacer">
					<Size>
						<AbsDimension x="0" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentBottomLeft" relativePoint="BOTTOMRIGHT">
							<Offset>
								<AbsDimension x="0" y="-2"/>
							</Offset>
						</Anchor>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentTop" file="Interface\OptionsFrame\UI-OptionsFrame-Spacer">
					<Size>
						<AbsDimension x="0" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTopLeft" relativePoint="TOPRIGHT">
							<Offset>
								<AbsDimension x="0" y="7"/>
							</Offset>
						</Anchor>
						<Anchor point="TOPRIGHT" relativeTo="$parentTopRight" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<ScrollFrame name="$parentList" hidden="true">
				<Size>
					<AbsDimension x="24" y="0"/>
				</Size>
				<Anchors>
					<Anchor point="TOPRIGHT">
						<Offset>
							<AbsDimension x="-3" y="-3"/>
						</Offset>
					</Anchor>
					<Anchor point="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="-3" y="3"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Backdrop edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
					<EdgeSize>
						<AbsValue val="12"/>
					</EdgeSize>
					<TileSize>
						<AbsValue val="16"/>
					</TileSize>
					<BackgroundInsets>
						<AbsInset left="0" right="0" top="5" bottom="5"/>
					</BackgroundInsets>
				</Backdrop>				
				<Scripts>
					<OnLoad>
						self:SetBackdropBorderColor(.6, .6, .6, .6);
						ScrollFrame_OnLoad(self);
					</OnLoad>
					<OnMouseWheel function="ScrollFrameTemplate_OnMouseWheel"/>
					<OnVerticalScroll>
						FauxScrollFrame_OnVerticalScroll(self, offset, self:GetParent().buttonHeight, OptionsListScroll_Update);
					</OnVerticalScroll>
				</Scripts>
				<Frames>
					<Slider name="$parentScrollBar" inherits="UIPanelScrollBarTemplate">
						<Anchors>
							<Anchor point="TOPRIGHT">
								<Offset>
									<AbsDimension x="0" y="-20"/>
								</Offset>
							</Anchor>
							<Anchor point="BOTTOMLEFT">
								<Offset>
									<AbsDimension x="0" y="19"/>
								</Offset>
							</Anchor>	
						</Anchors>
						<Scripts>
							<OnValueChanged>
								self:GetParent():SetVerticalScroll(value);
							</OnValueChanged>
						</Scripts>
					</Slider>
					<Frame name="$parentScrollChildFrame" hidden="true"/>
				</Frames>
			</ScrollFrame>
		</Frames>
		<Scripts>
			<OnLoad>
				OptionsList_OnLoad(self);
			</OnLoad>
		</Scripts>
	</Frame>
	<Button name="OptionsListButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="175" y="18"/>
		</Size>
		<Frames>
			<Button name="$parentToggle" hidden="true">
				<Size>
					<AbsDimension x="14" y="14"/>
				</Size>
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="$parent" relativePoint="TOPRIGHT">
						<Offset>
							<AbsDimension x="-6" y="-1"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						self:GetParent().toggle = self;
						self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
					</OnLoad>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						OptionsListButtonToggle_OnClick(self);
					</OnClick>
				</Scripts>
				<NormalTexture name="$parentNormalTexture" file="Interface\Buttons\UI-MinusButton-UP"/>
				<PushedTexture name="$parentPushedTexture" file="Interface\Buttons\UI-MinusButton-DOWN"/>
				<HighlightTexture name="$parentHighlightTexture" file="Interface\Buttons\UI-PlusButton-Hilight" alphaMode="ADD"/>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				OptionsListButton_OnLoad(self);
			</OnLoad>
			<OnClick>
				PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
				OptionsListButton_OnClick(self, button);
			</OnClick>
			<OnEnter function="OptionsListButton_OnEnter"/>
			<OnLeave function="OptionsListButton_OnLeave"/>
		</Scripts>
		<ButtonText name="$parentText" justifyH="LEFT" wordwrap="false"/>
		<NormalFont style="GameFontNormal"/>
		<HighlightFont style="GameFontHighlight"/>
		<HighlightTexture file="Interface\QuestFrame\UI-QuestLogTitleHighlight" alphaMode="ADD">
			<Anchors>
				<Anchor point="TOPLEFT">	
					<Offset>
						<AbsDimension x="0" y="1"/>
					</Offset>
				</Anchor>
				<Anchor point="BOTTOMRIGHT">	
					<Offset>
						<AbsDimension x="0" y="1"/>
					</Offset>
				</Anchor>
			</Anchors>
		</HighlightTexture>
	</Button>

	<Frame name="OptionsFrameTemplate" toplevel="true" parent="UIParent" hidden="true" enableMouse="true" frameStrata="HIGH" virtual="true">
		<Size>
			<AbsDimension x="648" y="520"/><!-- this may be overwritten for the VideoOptions frame, see VideoOptionsPanels.lua -->
		</Size>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="11" top="12" bottom="10"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="$parentHeader" file="Interface\DialogFrame\UI-DialogBox-Header">
					<Size>
						<AbsDimension x="300" y="68"/>
					</Size>
					<Anchors>
						<Anchor point="TOP">
							<Offset>
								<AbsDimension x="0" y="12"/>
							</Offset>
						</Anchor>
					</Anchors>
				</Texture>
				<FontString name="$parentHeaderText" inherits="GameFontNormal" text="OPTIONS">
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentHeader">
							<Offset>
								<AbsDimension x="0" y="-14"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentCategoryFrame" inherits="OptionsFrameListTemplate">
				<Size>
					<AbsDimension x="175" y="429"/><!-- this may be overwritten for the VideoOptions frame, see VideoOptionsPanels.lua -->
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="22" y="-40"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						--self.labelText = CATEGORY;
						OptionsList_OnLoad(self);
						self.update = OptionsCategoryFrame_Update;
						self.toggleSubCategories = OptionsFrame_ToggleSubCategories;
					</OnLoad>
				</Scripts>
			</Frame>
			<Frame name="$parentPanelContainer">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentCategoryFrame" relativePoint="TOPRIGHT">
						<Offset>
							<AbsDimension x="16" y="0"/>
						</Offset>
					</Anchor>
					<Anchor point="BOTTOMLEFT" relativeTo="$parentCategoryFrame" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="16" y="1"/>
						</Offset>
					</Anchor>
					<Anchor point="RIGHT">
						<Offset>
							<AbsDimension x="-22" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Backdrop edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
					<EdgeSize>
						<AbsValue val="16"/>
					</EdgeSize>
					<TileSize>
						<AbsValue val="16"/>
					</TileSize>
					<BackgroundInsets>
						<AbsInset left="5" right="5" top="5" bottom="5"/>
					</BackgroundInsets>
				</Backdrop>
				<Scripts>
					<OnLoad>
						self:SetBackdropBorderColor(.6, .6, .6, 1);
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				OptionsFrame_OnLoad(self);
			</OnLoad>
			<OnShow>
				OptionsFrame_OnShow(self);
			</OnShow>
			<OnHide>
				OptionsFrame_OnHide(self);
			</OnHide>
		</Scripts>
	</Frame>
</Ui>
