<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<!-- if you change something here you probably want to change the glue version too -->

	<Button name="OptionsButtonTemplate" inherits="UIPanelButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="90" y="21"/>
		</Size>
	</Button>
	<CheckButton name="OptionsBaseCheckButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="26" y="26"/>
		</Size>
		<HitRectInsets>
			<AbsInset left="0" right="-100" top="0" bottom="0"/>
		</HitRectInsets>
		<Scripts>
			<OnClick>
				if ( self:GetChecked() ) then
					PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
				else
					PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_OFF);
				end
				BlizzardOptionsPanel_CheckButton_OnClick(self);
			</OnClick>
			<OnEnter>
				if ( self.tooltipText ) then
					GameTooltip:SetOwner(self, self.tooltipOwnerPoint or "ANCHOR_RIGHT");
					GameTooltip:SetText(self.tooltipText, nil, nil, nil, nil, true);
				end
				if ( self.tooltipRequirement ) then
					GameTooltip:AddLine(self.tooltipRequirement, 1.0, 1.0, 1.0, true);
					GameTooltip:Show();
				end
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
		<NormalTexture file="Interface\Buttons\UI-CheckBox-Up"/>
		<PushedTexture file="Interface\Buttons\UI-CheckBox-Down"/>
	 	<HighlightTexture file="Interface\Buttons\UI-CheckBox-Highlight" alphaMode="ADD"/>
		<CheckedTexture file="Interface\Buttons\UI-CheckBox-Check"/>
		<DisabledCheckedTexture file="Interface\Buttons\UI-CheckBox-Check-Disabled"/>		
	</CheckButton>
	<CheckButton name="OptionsCheckButtonTemplate" virtual="true" inherits="OptionsBaseCheckButtonTemplate">
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentText" inherits="GameFontNormal">
					<Anchors>
						<Anchor point="LEFT" relativePoint="RIGHT">
							<Offset>
								<AbsDimension x="0" y="1"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</CheckButton>
	<CheckButton name="OptionsSmallCheckButtonTemplate" virtual="true" inherits="OptionsBaseCheckButtonTemplate">
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentText" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="LEFT" relativePoint="RIGHT">
							<Offset>
								<AbsDimension x="0" y="1"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</CheckButton>
	<Slider name="OptionsSliderTemplate" orientation="HORIZONTAL" virtual="true" enableMouse="true">
		<Size>
			<AbsDimension x="144" y="17"/>
		</Size>
		<HitRectInsets>
			<AbsInset left="0" right="0" top="-10" bottom="-10"/>
		</HitRectInsets>
		<Backdrop bgFile="Interface\Buttons\UI-SliderBar-Background" edgeFile="Interface\Buttons\UI-SliderBar-Border" tile="true">
			<EdgeSize>
				<AbsValue val="8"/>
			</EdgeSize>
			<TileSize>
				<AbsValue val="8"/>
			</TileSize>
			<BackgroundInsets>
				<AbsInset left="3" right="3" top="6" bottom="6"/>
			</BackgroundInsets>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentText" inherits="GameFontHighlight">
					<Anchors>
						<Anchor point="BOTTOM" relativePoint="TOP"/>
					</Anchors>
				</FontString>
				<FontString name="$parentLow" parentKey="Low" inherits="GameFontHighlightSmall" text="LOW">
					<Anchors>
						<Anchor point="TOPLEFT" relativePoint="BOTTOMLEFT">
							<Offset>
								<AbsDimension x="-4" y="3"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				<FontString name="$parentHigh" parentKey="High" inherits="GameFontHighlightSmall" text="HIGH">
					<Anchors>
						<Anchor point="TOPRIGHT" relativePoint="BOTTOMRIGHT">
							<Offset>
								<AbsDimension x="4" y="3"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnEnter>
				if ( self:IsEnabled() ) then
					if ( self.tooltipText ) then
						GameTooltip:SetOwner(self, self.tooltipOwnerPoint or "ANCHOR_RIGHT");
						GameTooltip:SetText(self.tooltipText, nil, nil, nil, nil, true);
					end
					if ( self.tooltipRequirement ) then
						GameTooltip:AddLine(self.tooltipRequirement, 1.0, 1.0, 1.0, true);
						GameTooltip:Show();
					end
				end
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
		<ThumbTexture name="$parentThumb" file="Interface\Buttons\UI-SliderBar-Button-Horizontal">
			<Size>
				<AbsDimension x="32" y="32"/>
			</Size>
		</ThumbTexture>
	</Slider>
	<Frame name="OptionsBoxTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND">
				<FontString name="$parentTitle" inherits="GameFontHighlightSmall">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativePoint="TOPLEFT">
							<Offset>
								<AbsDimension x="5" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Backdrop edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
			<EdgeSize>
				<AbsValue val="16"/>
			</EdgeSize>
			<TileSize>
				<AbsValue val="16"/>
			</TileSize>
			<BackgroundInsets>
				<AbsInset left="5" right="5" top="5" bottom="5"/>
			</BackgroundInsets>
		</Backdrop>
		<Scripts>
			<OnLoad>
				self:SetBackdropBorderColor(0.4, 0.4, 0.4);
				self:SetBackdropColor(0.5, 0.5, 0.5);
			</OnLoad>
		</Scripts>
	</Frame>
</Ui>
