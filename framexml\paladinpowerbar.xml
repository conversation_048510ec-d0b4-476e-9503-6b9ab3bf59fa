<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
  <Script file="PaladinPowerBar.lua"/>

	<Frame name="PaladinPowerBarFrame" inherits="ClassPowerBarFrame" mixin="ClassPowerBar, PaladinPowerBar">
		<Animations>
			<AnimationGroup parentKey="showAnim">
				<Alpha fromAlpha="0" toAlpha="1" duration="0.5" order="1"/>
				<Scripts>
					<OnFinished>
						self:GetParent():SetAlpha(1.0);
					</OnFinished>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="showBankAnim">
				<Alpha target="$parentBankBG" fromAlpha="0" toAlpha="1" duration="0.5" order="1"/>
				<Scripts>
					<OnFinished>
						self:GetParent().bankBG:SetAlpha(1.0);
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Size x="136" y="39"/>
		<Anchors>
			<Anchor point="TOP" relativeTo="PlayerFrame" relativePoint="BOTTOM" x="43" y="39"/>
		</Anchors>
		<HitRectInsets>
			<AbsInset left="17" right="17" top="7" bottom="4"/>
		</HitRectInsets>
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="-7">
				<Texture name="$parentBankBG" file="Interface\PlayerFrame\PaladinPowerTextures" parentKey="bankBG" alpha="0">
					<Size x="69" y="16"/>
					<Anchors>
						<Anchor point="TOP" x="0" y="-29"/>
					</Anchors>
					<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>	
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="-5">
				<Texture name="$parentBG" file="Interface\PlayerFrame\PaladinPowerTextures">
					<Anchors>
						<Anchor point="TOP" />
					</Anchors>
					<Size x="136" y="39"/>
					<TexCoords left="0.********" right="0.********" top="0.********" bottom="0.********"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentGlowBG" setAllPoints="true"  useParentLevel="true" parentKey="glow">
				<Animations>
					<AnimationGroup parentKey="pulse">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.5" order="1"/>
						<Alpha fromAlpha="1" toAlpha="0" startDelay="0.3" duration="0.6" order="2"/>
						<Scripts>
							<OnFinished>
								if not self.stopPulse then
									self:Play();
								end
							</OnFinished>
						</Scripts>
					</AnimationGroup>
				</Animations>
				<Layers>
					<Layer level="BACKGROUND" textureSubLevel="-1">
						<Texture name="$parentTexture" file="Interface\PlayerFrame\PaladinPowerTextures">
							<Anchors>
								<Anchor point="TOP" />
							</Anchors>
							<Size x="136" y="39"/>
							<TexCoords left="0.********" right="0.********" top="0.32812500" bottom="0.63281250"/>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="$parentRune1" parentKey="rune1">
				<Animations>
					<AnimationGroup parentKey="activate">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
						<Scripts>
							<OnFinished>
								self:GetParent():SetAlpha(1);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
					<AnimationGroup parentKey="deactivate">
						<Alpha fromAlpha="1" toAlpha="0" duration="0.3" order="1"/>
						<Scripts>
							<OnFinished>
								self:GetParent():SetAlpha(0);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
				</Animations>
				<Anchors>
					<Anchor point="TOPLEFT" x="21" y="-11"/>
				</Anchors>
				<Size x="36" y="22"/>
				<Layers>
					<Layer level="OVERLAY">
						<Texture name="$parentTexture" file="Interface\PlayerFrame\PaladinPowerTextures">
							<TexCoords left="0.********" right="0.14453125" top="0.78906250" bottom="0.96093750"/>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="$parentRune2" parentKey="rune2">
				<Animations>
					<AnimationGroup parentKey="activate">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
						<Scripts>
							<OnFinished>
								self:GetParent():SetAlpha(1);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
					<AnimationGroup parentKey="deactivate">
						<Alpha fromAlpha="1" toAlpha="0" duration="0.3" order="1"/>
						<Scripts>
							<OnFinished>
								self:GetParent():SetAlpha(0);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
				</Animations>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentRune1" relativePoint="RIGHT"/>
				</Anchors>
				<Size x="31" y="17"/>
				<Layers>
					<Layer level="OVERLAY">
						<Texture name="$parentTexture" file="Interface\PlayerFrame\PaladinPowerTextures">
							<TexCoords left="0.15234375" right="0.********" top="0.78906250" bottom="0.92187500"/>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="$parentRune3" parentKey="rune3">
				<Animations>
					<AnimationGroup parentKey="activate">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
						<Scripts>
							<OnFinished>
								self:GetParent():SetAlpha(1);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
					<AnimationGroup parentKey="deactivate">
						<Alpha fromAlpha="1" toAlpha="0" duration="0.3" order="1"/>
						<Scripts>
							<OnFinished>
								self:GetParent():SetAlpha(0);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
				</Animations>
				<Anchors>
						<Anchor point="LEFT" relativeTo="$parentRune2" relativePoint="RIGHT" x="2" y="-1"/>
					</Anchors>
					<Size x="27" y="21"/>
				<Layers>
					<Layer level="OVERLAY">
						<Texture name="$parentTexture" file="Interface\PlayerFrame\PaladinPowerTextures">
							<TexCoords left="0.28125000" right="0.38671875" top="0.********" bottom="0.81250000"/>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="$parentRune4" parentKey="rune4" alpha="0">
				<Animations>
					<AnimationGroup parentKey="activate">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
						<Scripts>
							<OnFinished>
								self:GetParent():SetAlpha(1);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
					<AnimationGroup parentKey="deactivate">
						<Alpha fromAlpha="1" toAlpha="0" duration="0.3" order="1"/>
						<Scripts>
							<OnFinished>
								self:GetParent():SetAlpha(0);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
				</Animations>
				<Anchors>
					<Anchor point="TOPLEFT" x="43" y="-28"/>
				</Anchors>
				<Size x="27" y="12"/>
				<Layers>
					<Layer level="BACKGROUND" textureSubLevel="-6">
						<Texture name="$parentTexture" file="Interface\PlayerFrame\PaladinPowerTextures">
							<TexCoords left="0.28125000" right="0.38671875" top="0.82812500" bottom="0.92187500"/>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="$parentRune5" parentKey="rune5" alpha="0">
				<Animations>
					<AnimationGroup parentKey="activate">
						<Alpha fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
						<Scripts>
							<OnFinished>
								self:GetParent():SetAlpha(1);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
					<AnimationGroup parentKey="deactivate">
						<Alpha fromAlpha="1" toAlpha="0" duration="0.3" order="1"/>
						<Scripts>
							<OnFinished>
								self:GetParent():SetAlpha(0);
							</OnFinished>
						</Scripts>
					</AnimationGroup>
				</Animations>
				<Anchors>
					<Anchor point="TOPLEFT" x="67" y="-28"/>
				</Anchors>
				<Size x="26" y="12"/>
				<Layers>
					<Layer level="BACKGROUND" textureSubLevel="-6">
						<Texture name="$parentTexture" file="Interface\PlayerFrame\PaladinPowerTextures">
							<TexCoords left="0.39453125" right="0.49609375" top="0.********" bottom="0.74218750"/>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
	</Frame>
</Ui>


