<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
    <Script file="PaperDollFrame.lua"/>

    <Button name="PaperDollItemSlotButtonTemplate" inherits="ItemButtonTemplate" virtual="true">
		<Layers>
			<Layer level="OVERLAY">
				<Texture parentKey="ignoreTexture" file="Interface\PaperDollInfoFrame\UI-GearManager-LeaveItem-Transparent">
					<Size x="40" y="40"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Cooldown name="$parentCooldown" inherits="CooldownFrameTemplate"/>
			<Button name="$parentPopoutButton" parentKey="popoutButton" inherits="EquipmentFlyoutPopoutButtonTemplate"/>
		</Frames>
        <Scripts>
            <OnLoad>
				PaperDollItemSlotButton_OnLoad(self);
			</OnLoad>
            <OnEvent>
				PaperDollItemSlotButton_OnEvent(self, event, ...);
			</OnEvent>
			<OnShow>
				PaperDollItemSlotButton_OnShow(self);
			</OnShow>
			<OnHide>
				PaperDollItemSlotButton_OnHide(self);
			</OnHide>
			<OnClick>
				if ( IsModifiedClick() ) then
					PaperDollItemSlotButton_OnModifiedClick(self, button);
				else
					PaperDollItemSlotButton_OnClick(self, button);
				end
			</OnClick>
            <OnDragStart>
				PaperDollItemSlotButton_OnClick(self, "LeftButton");
			</OnDragStart>
            <OnReceiveDrag>
				PaperDollItemSlotButton_OnClick(self, "LeftButton");
			</OnReceiveDrag>
            <OnEnter>
				PaperDollItemSlotButton_OnEnter(self, motion);
			</OnEnter>
            <OnLeave>
				PaperDollItemSlotButton_OnLeave(self, motion);
			</OnLeave>
        </Scripts>
    </Button>
	<Button name="PaperDollItemSlotButtonLeftTemplate" inherits="PaperDollItemSlotButtonTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="-1">
				<Texture name="$parentFrame" inherits="Char-LeftSlot">	
					<Anchors>
						<Anchor point="TOPLEFT" x="-4"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Button>
	<Button name="PaperDollItemSlotButtonRightTemplate" inherits="PaperDollItemSlotButtonTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="-1">
				<Texture name="$parentFrame" inherits="Char-RightSlot">	
					<Anchors>
						<Anchor point="TOPRIGHT" x="4"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Button>
	<Button name="PaperDollItemSlotButtonBottomTemplate" inherits="PaperDollItemSlotButtonTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="-1">
				<Texture name="$parentFrame" inherits="Char-BottomSlot">	
					<Anchors>
						<Anchor point="TOPLEFT" x="-4" y="8"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Button>
	<Button name="PlayerTitleButtonTemplate" virtual="true">
		<Size x="169" y="22"/>
		<Anchors>
			<Anchor point="LEFT">
				<Offset x="2" y="0"/>
			</Anchor>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentBgTop" inherits="Char-Stat-Top" parentKey="BgTop">
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="1"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBgBottom" inherits="Char-Stat-Top" parentKey="BgBottom">
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="-4"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBgMiddle" file="Interface\CharacterFrame\Char-Stat-Middle" parentKey="BgMiddle" vertTile="true">
					<Size x="169" y="8"/>
					<Anchors>
						<Anchor point="LEFT" x="1"/>
					</Anchors>
					<TexCoords left="0.00390625" right="0.66406250" top="0.00000000" bottom="1.00000000"/>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture name="$parentStripe" parentKey="Stripe">
					<Anchors>
						<Anchor point="TOPLEFT" x="1" y="0"/>
						<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture name="$parentCheck" file="Interface\Buttons\UI-CheckBox-Check" parentKey="Check" hidden="true">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="LEFT">
							<Offset x="8" y="0"/>
						</Anchor>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentSelectedBar" parentKey="SelectedBar" file="Interface\FriendsFrame\UI-FriendsFrame-HighlightBar" alpha="0.4" alphaMode="ADD" hidden="true"/>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				SetClampedTextureRotation(self.BgBottom, 180);
			</OnLoad>
			<OnClick>
				PlayerTitleButton_OnClick(self);
			</OnClick>
		</Scripts>
		<ButtonText name="$parentTitleText" inherits="GameFontNormalSmallLeft" justifyH="LEFT" parentKey="text">
			<Anchors>
				<Anchor point="LEFT" relativeTo="$parentCheck" relativePoint="RIGHT" x="3" y="0"/>
				<Anchor point="RIGHT" x="-3" y="0"/>
			</Anchors>
		</ButtonText>
		<HighlightTexture file="Interface\FriendsFrame\UI-FriendsFrame-HighlightBar-Blue" alphaMode="ADD"/>	
	</Button>
	
	<Button name="GearSetButtonTemplate" virtual="true">
		<Size x="169" y="44"/>
		<Anchors>
			<Anchor point="LEFT" x="2" y="0"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentBgTop" inherits="Char-Stat-Top" parentKey="BgTop">
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="1"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBgBottom" inherits="Char-Stat-Top" parentKey="BgBottom">
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="-4"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBgMiddle" file="Interface\CharacterFrame\Char-Stat-Middle" parentKey="BgMiddle" vertTile="true">
					<Size x="169" y="8"/>
					<Anchors>
						<Anchor point="LEFT" x="1"/>
					</Anchors>
					<TexCoords left="0.00390625" right="0.66406250" top="0.00000000" bottom="1.00000000"/>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture name="$parentStripe" parentKey="Stripe">
					<Anchors>
						<Anchor point="TOPLEFT" x="1" y="0"/>
						<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture name="$parentCheck" file="Interface\Buttons\UI-CheckBox-Check" parentKey="Check" hidden="true">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="RIGHT">
							<Offset x="-8" y="0"/>
						</Anchor>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString name="$parentText" inherits="GameFontNormalLeft" parentKey="text">
					<Size x="98" y="38"/>
					<Anchors>
						<Anchor point="LEFT" x="44"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="-2">
				<Texture parentKey="SpecRing" atlas="equipmentmanager-spec-border" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPLEFT" x="18" y="-18"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="-3">
				<Texture parentKey="SpecIcon">
					<Size x="18" y="18"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.SpecRing" relativePoint="CENTER" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="-1">
				<Texture name="$parentHighlightBar" parentKey="HighlightBar" file="Interface\FriendsFrame\UI-FriendsFrame-HighlightBar-Blue" alpha="0.4" alphaMode="ADD" hidden="true">
					<TexCoords left="0.2" right="0.8" top="0" bottom="1"/>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentSelectedBar" parentKey="SelectedBar" file="Interface\FriendsFrame\UI-FriendsFrame-HighlightBar" alpha="0.4" alphaMode="ADD" hidden="true">
					<TexCoords left="0.2" right="0.8" top="0" bottom="1"/>
				</Texture>
			</Layer>
		</Layers>
		
		<Frames>
			<Button name="$parentDeleteButton" parentKey="DeleteButton" hidden="true">
				<Size>
					<AbsDimension x="14" y="14"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" x="-2" y="2"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture file="Interface\Buttons\UI-GroupLoot-Pass-Up" alpha="0.5" parentKey="texture"/>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						self.texture:SetAlpha(1.0);
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText(DELETE);
					</OnEnter>
					<OnLeave>
						self.texture:SetAlpha(0.5);
						GameTooltip_Hide();
					</OnLeave>
					<OnMouseDown>
						self.texture:SetPoint("TOPLEFT", 1, -1);
					</OnMouseDown>
					<OnMouseUp>
						self.texture:SetPoint("TOPLEFT", 0, 0);
					</OnMouseUp>
					<OnClick>
						local dialog = StaticPopup_Show("CONFIRM_DELETE_EQUIPMENT_SET", self:GetParent().text:GetText());
						if ( dialog ) then
							dialog.data = self:GetParent().setID;
						else
							UIErrorsFrame:AddMessage(ERR_CLIENT_LOCKED_OUT, 1.0, 0.1, 0.1, 1.0);
						end
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentEditButton" parentKey="EditButton" hidden="true">
				<Size x="16" y="16"/>
				<Anchors>
					<Anchor point="RIGHT" relativeTo="$parentDeleteButton" relativePoint="LEFT" x="-1"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture file="Interface\WorldMap\GEAR_64GREY" alpha="0.5" parentKey="texture"/>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad function="GearSetEditButton_OnLoad"/>
					<OnEnter>
						self.texture:SetAlpha(1.0);
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText(EQUIPMENT_SET_SETTINGS);
					</OnEnter>
					<OnLeave>
						self.texture:SetAlpha(0.5);
						GameTooltip_Hide();
					</OnLeave>
					<OnMouseDown>
						self.texture:SetPoint("TOPLEFT", 1, -1);
					</OnMouseDown>
					<OnMouseUp>
						self.texture:SetPoint("TOPLEFT", 0, 0);
					</OnMouseUp>
					<OnClick function="GearSetEditButton_OnClick"/>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self:RegisterForDrag("LeftButton");
				SetClampedTextureRotation(self.BgBottom, 180);
			</OnLoad>
			<OnClick>
				GearSetButton_OnClick(self, button, down);
			</OnClick>
			<OnDoubleClick>
				if ( self.setID ) then
					PlaySound(SOUNDKIT.IG_CHARACTER_INFO_TAB);			-- inappropriately named, but a good sound.
					EquipmentManager_EquipSet(self.setID);
				end
			</OnDoubleClick>
			<OnEnter>
				GearSetButton_OnEnter(self);
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
			<OnDragStart>
				if ( self.setID ) then
					C_EquipmentSet.PickupEquipmentSet(self.setID);
				end
			</OnDragStart>
		</Scripts>
		<NormalTexture name="$parentIcon" parentKey="icon">
			<Size x="36" y="36"/>
			<Anchors>
				<Anchor point="LEFT" x="4" y="0"/>
			</Anchors>
		</NormalTexture>
	</Button>
	
	<CheckButton name="GearSetPopupButtonTemplate" inherits="SimplePopupButtonTemplate" virtual="true">
		<Scripts>
			<OnLoad>
				local name = self:GetName();
				self.icon = _G[name .. "Icon"];
				self.name = _G[name .. "Name"];
			</OnLoad>
			<OnClick>
				GearSetPopupButton_OnClick(self, button, down);
			</OnClick>
		</Scripts>
		<NormalTexture name="$parentIcon" nonBlocking="true">
			<Size>
				<AbsDimension x="36" y="36"/>
			</Size>
			<Anchors>
				<Anchor point="CENTER">
					<Offset>
						<AbsDimension x="0" y="-1"/>
					</Offset>
				</Anchor>
			</Anchors>
		</NormalTexture>
		<HighlightTexture alphaMode="ADD" file="Interface\Buttons\ButtonHilight-Square"/>
		<CheckedTexture alphaMode="ADD" file="Interface\Buttons\CheckButtonHilight"/>
	</CheckButton>	
		
	<Button name="PaperDollSidebarTabTemplate" motionScriptsWhileDisabled="true" virtual="true">
		<Size x="33" y="35"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture file="Interface\PaperDollInfoFrame\PaperDollSidebarTabs" parentKey="TabBg">
					<Size x="50" y="43"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-9" y="-2"/>
					</Anchors>
					<TexCoords left="0.01562500" right="0.79687500" top="0.61328125" bottom="0.78125000"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Icon">
					<Size x="33" y="35"/>
					<Anchors>
						<Anchor point="BOTTOM" x="1" y="-2"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="Hider" file="Interface\PaperDollInfoFrame\PaperDollSidebarTabs">
					<Size x="34" y="19"/>
					<Anchors>
						<Anchor point="BOTTOM"/>
					</Anchors>
					<TexCoords left="0.01562500" right="0.54687500" top="0.11328125" bottom="0.18750000"/>
				</Texture>
			</Layer>
			<Layer level="HIGHLIGHT">
				<Texture parentKey="Highlight" file="Interface\PaperDollInfoFrame\PaperDollSidebarTabs">
					<Size x="31" y="31"/>	
					<Anchors>
						<Anchor point="TOPLEFT" x="2" y="-3"/>
					</Anchors>
					<TexCoords left="0.01562500" right="0.50000000" top="0.19531250" bottom="0.31640625"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				self.Icon:SetTexture(PAPERDOLL_SIDEBARS[self:GetID()].icon);
				local tcoords = PAPERDOLL_SIDEBARS[self:GetID()].texCoords;
				self.disabledTooltip = PAPERDOLL_SIDEBARS[self:GetID()].disabledTooltip;
				self.Icon:SetTexCoord(tcoords[1], tcoords[2], tcoords[3], tcoords[4]);
			</OnLoad>
			<OnClick>
				PaperDollFrame_SetSidebar(self, self:GetID());
			</OnClick>
			<OnEnable>
				self:SetAlpha(1);
				self.Icon:SetDesaturation(0);
			</OnEnable>
			<OnDisable>
				self:SetAlpha(0.5);
				self.Icon:SetDesaturation(1);
			</OnDisable>
			<OnEnter>
				GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
				GameTooltip:SetText(PAPERDOLL_SIDEBARS[self:GetID()].name, 1, 1, 1);
				if ( not self:IsEnabled() and self.disabledTooltip ) then
					GameTooltip:AddLine(self.disabledTooltip, RED_FONT_COLOR.r, RED_FONT_COLOR.g, RED_FONT_COLOR.b, true);
				end
				
				GameTooltip:Show();
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
	</Button>
	
    <Frame name="PaperDollFrame" setAllPoints="true" enableMouse="true" parent="CharacterFrame" useParentLevel="true" id="1">
		<Layers>
            <Layer level="ARTWORK">
                <FontString name="CharacterLevelText" inherits="GameFontNormalSmall2" justifyV="MIDDLE" justifyH="CENTER" text="Level level race class">
					<Size x="220" y="24"/>
                </FontString>
			</Layer>
			<Layer level="OVERLAY">
                <FontString name="CharacterTrialLevelErrorText" inherits="GameFontNormalSmall" text="TRIAL_LEVEL_CAPPED" hidden="true">
                    <Size x="0" y="0"/>
					<Anchors>
                        <Anchor point="TOP" relativeTo="CharacterLevelText" relativePoint="BOTTOM">
                            <Offset>
                                <AbsDimension x="0" y="0"/>
                            </Offset>
                        </Anchor>
                    </Anchors>
					<Color r="1" g="0.1" b="0.1" a="1"/>
                </FontString>
            </Layer>
        </Layers>
        <Frames>
			<Frame parentKey="EditButtonDropdown" name="GearSetEditButtonDropDown" inherits="UIDropDownMenuTemplate"/>
			<Frame name="PaperDollSidebarTabs" parent="CharacterFrameInsetRight" hidden="true">
				<Size x="168" y="35"/>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" relativePoint="TOPRIGHT" x="-6" y="-1"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture parentKey="DecorLeft" file="Interface\PaperDollInfoFrame\PaperDollSidebarTabs">
							<Size x="28" y="11"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT"/>
							</Anchors>
							<TexCoords left="0.01562500" right="0.45312500" top="0.00390625" bottom="0.04687500"/>
						</Texture>
						<Texture parentKey="DecorRight" file="Interface\PaperDollInfoFrame\PaperDollSidebarTabs">
							<Size x="28" y="13"/>
							<Anchors>
								<Anchor point="BOTTOMRIGHT"/>
							</Anchors>
							<TexCoords left="0.01562500" right="0.45312500" top="0.05468750" bottom="0.10546875"/>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Button name="PaperDollSidebarTab3" inherits="PaperDollSidebarTabTemplate" id="3">
						<Anchors>
							<Anchor point="BOTTOMRIGHT" x="-30" y="0"/>
						</Anchors>
					</Button>
					<Button name="PaperDollSidebarTab2" inherits="PaperDollSidebarTabTemplate" id="2">
						<Anchors>
							<Anchor point="RIGHT" relativeTo="PaperDollSidebarTab3" relativePoint="LEFT" x="-4"/>
						</Anchors>
					</Button>
					<Button name="PaperDollSidebarTab1" inherits="PaperDollSidebarTabTemplate" id="1">
						<Anchors>
							<Anchor point="RIGHT" relativeTo="PaperDollSidebarTab2" relativePoint="LEFT" x="-4"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								self:RegisterEvent("UNIT_PORTRAIT_UPDATE");
								self:RegisterEvent("PLAYER_ENTERING_WORLD");
								
								local tcoords = PAPERDOLL_SIDEBARS[self:GetID()].texCoords;
								self.Icon:SetTexCoord(tcoords[1], tcoords[2], tcoords[3], tcoords[4]);
								self.Icon:SetSize(29, 31);
								self.Icon:SetPoint("BOTTOM", 1, 0);
							</OnLoad>
							<OnEvent>
								if ( event == "UNIT_PORTRAIT_UPDATE" ) then
									local unit = ...;
									if ( not unit or unit == "player" ) then
										SetPortraitTexture(self.Icon, "player");
									end
								elseif ( event == "PLAYER_ENTERING_WORLD" ) then
									SetPortraitTexture(self.Icon, "player");
								end
							</OnEvent>
						</Scripts>
					</Button>
				</Frames>
			</Frame>
		
			<ScrollFrame name="PaperDollTitlesPane" inherits="HybridScrollFrameTemplate" hidden="true">
				<Size x="172" y="354"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="CharacterFrameInsetRight" x="4" y="-4"/>
				</Anchors>
				<Frames>
					<Slider name="$parentScrollBar" inherits="HybridScrollBarTemplate" parentKey="scrollBar">
						<Anchors>
							<Anchor point="TOPLEFT" relativePoint="TOPRIGHT">
								<Offset x="4" y="-13"/>
							</Anchor>
							<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT">
								<Offset x="4" y="14"/>
							</Anchor>
						</Anchors>
					</Slider>
				</Frames>
				<Scripts>
					<OnLoad>
						self.scrollBar.doNotHide  = 1;
						self:SetFrameLevel(CharacterFrameInsetRight:GetFrameLevel()+1);
						PaperDollTitlesPane_OnLoad(self);
					</OnLoad>
					<OnShow>
						PaperDollTitlesPane_Update();
					</OnShow>
				</Scripts>
			</ScrollFrame>
			
			<ScrollFrame name="PaperDollEquipmentManagerPane" inherits="HybridScrollFrameTemplate" hidden="true">
				<Size x="172" y="354"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="CharacterFrameInsetRight" x="4" y="-4"/>
				</Anchors>
				<Frames>
					<Button name="$parentEquipSet" text="EQUIPSET_EQUIP" parentKey="EquipSet" inherits="UIPanelButtonTemplate">
						<Size x="87" y="22"/>
						<Anchors>
							<Anchor point="TOPLEFT" x="0" y="0"/>
						</Anchors>
						<Scripts>
							<OnClick function="PaperDollEquipmentManagerPaneEquipSet_OnClick"/>
						</Scripts>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture parentKey="ButtonBackground">
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="PaperDollEquipmentManagerPane"/>
										<Anchor point="BOTTOMRIGHT" relativeTo="PaperDollEquipmentManagerPane" relativePoint="TOPRIGHT" x="0" y="-23"/>
									</Anchors>
									<Color r="0" g="0" b="0"/>
								</Texture>
							</Layer>
						</Layers>
					</Button>
					<Button name="$parentSaveSet" text="SAVE" parentKey="SaveSet" inherits="UIPanelButtonTemplate">
						<Size x="87" y="22"/>
						<Anchors>
							<Anchor point="LEFT" relativeTo="$parentEquipSet" relativePoint="RIGHT"/>
						</Anchors>
						<Scripts>
							<OnClick function="PaperDollEquipmentManagerPaneSaveSet_OnClick"/>
						</Scripts>
					</Button>
					<Slider name="$parentScrollBar" inherits="HybridScrollBarTemplate" parentKey="scrollBar">
						<Anchors>
							<Anchor point="TOPLEFT" relativePoint="TOPRIGHT">
								<Offset x="4" y="-13"/>
							</Anchor>
							<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT">
								<Offset x="4" y="14"/>
							</Anchor>
						</Anchors>
					</Slider>
				</Frames>
				<Scripts>
					<OnLoad>
						self.scrollBar.doNotHide  = 1;
						self:SetFrameLevel(CharacterFrameInsetRight:GetFrameLevel()+1);
						self.EquipSet:SetFrameLevel(self:GetFrameLevel()+3);
						self.SaveSet:SetFrameLevel(self:GetFrameLevel()+3);
						PaperDollEquipmentManagerPane_OnLoad(self);
					</OnLoad>
					<OnShow function="PaperDollEquipmentManagerPane_OnShow"/>
					<OnHide function="PaperDollEquipmentManagerPane_OnHide"/>
					<OnEvent function="PaperDollEquipmentManagerPane_OnEvent"/>
					<OnUpdate function="PaperDollEquipmentManagerPane_OnUpdate"/>
				</Scripts>
			</ScrollFrame>

			<PlayerModel name="CharacterModelFrame" inherits="ModelWithControlsTemplate">
                <Size x="231" y="320"/>
                <Anchors>
                    <Anchor point="TOPLEFT" x="52" y="-66"/>
                </Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="CharacterModelFrameBackgroundTopLeft" parentKey="BackgroundTopLeft">
							<Size  x="212" y="245"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
							</Anchors>
							<TexCoords left="0.171875" right="1" top="0.0392156862745098" bottom="1"/>
						</Texture>
						<Texture name="CharacterModelFrameBackgroundTopRight" parentKey="BackgroundTopRight">
							<Size x="19" y="245"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="CharacterModelFrameBackgroundTopLeft" relativePoint="TOPRIGHT"/>
							</Anchors>
							<TexCoords left="0" right="0.296875" top="0.0392156862745098" bottom="1"/>
						</Texture>
						<Texture name="CharacterModelFrameBackgroundBotLeft" parentKey="BackgroundBotLeft">
							<Size x="212" y="128"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="CharacterModelFrameBackgroundTopLeft" relativePoint="BOTTOMLEFT"/>
							</Anchors>
							<TexCoords left="0.171875" right="1" top="0" bottom="1"/>
						</Texture>
						<Texture name="CharacterModelFrameBackgroundBotRight" parentKey="BackgroundBotRight">
							<Size x="19" y="128"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="CharacterModelFrameBackgroundTopLeft" relativePoint="BOTTOMRIGHT"/>
							</Anchors>
							<TexCoords left="0" right="0.296875" top="0" bottom="1"/>
						</Texture>
					</Layer>
					<Layer level="BORDER">
						<Texture name="CharacterModelFrameBackgroundOverlay" parentKey="BackgroundOverlay">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="CharacterModelFrameBackgroundTopLeft"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="CharacterModelFrameBackgroundBotRight" x="0" y="52"/>
							</Anchors>
							<Color r="0" g="0" b="0"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture name="PaperDollInnerBorderTopLeft" inherits="Char-Corner-UpperLeft">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="CharacterFrameInset" x="46" y="-4"/>
							</Anchors>
						</Texture>
						<Texture name="PaperDollInnerBorderTopRight" inherits="Char-Corner-UpperRight">
							<Anchors>
								<Anchor point="TOPRIGHT" relativeTo="CharacterFrameInset" x="-47" y="-4"/>
							</Anchors>
						</Texture>
						<Texture name="PaperDollInnerBorderBottomLeft" inherits="Char-Corner-LowerLeft">
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativeTo="CharacterFrameInset" x="46" y="31"/>
							</Anchors>
						</Texture>
						<Texture name="PaperDollInnerBorderBottomRight" inherits="Char-Corner-LowerRight">
							<Anchors>
								<Anchor point="BOTTOMRIGHT" relativeTo="CharacterFrameInset" x="-47" y="31"/>
							</Anchors>
						</Texture>
						<Texture name="PaperDollInnerBorderLeft" inherits="Char-Inner-Left">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="PaperDollInnerBorderTopLeft" relativePoint="BOTTOMLEFT" x="-1"/>
								<Anchor point="BOTTOMLEFT" relativeTo="PaperDollInnerBorderBottomLeft" relativePoint="TOPLEFT" x="-1"/>
							</Anchors>
						</Texture>
						<Texture name="PaperDollInnerBorderRight" inherits="Char-Inner-Right">
							<Anchors>
								<Anchor point="TOPRIGHT" relativeTo="PaperDollInnerBorderTopRight" relativePoint="BOTTOMRIGHT" x="1"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="PaperDollInnerBorderBottomRight" relativePoint="TOPRIGHT" x="1"/>
							</Anchors>
						</Texture>
						<Texture name="PaperDollInnerBorderTop" inherits="Char-Inner-Top">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="PaperDollInnerBorderTopLeft" relativePoint="TOPRIGHT" y="1"/>
								<Anchor point="TOPRIGHT" relativeTo="PaperDollInnerBorderTopRight" relativePoint="TOPLEFT" y="1"/>
							</Anchors>
						</Texture>
						<Texture name="PaperDollInnerBorderBottom" inherits="Char-Inner-Bottom">
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativeTo="PaperDollInnerBorderBottomLeft" relativePoint="BOTTOMRIGHT" y="-1"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="PaperDollInnerBorderBottomRight" relativePoint="BOTTOMLEFT" y="-1"/>
							</Anchors>
						</Texture>
						<Texture name="PaperDollInnerBorderBottom2" inherits="Char-Inner-Bottom">
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativeTo="CharacterFrameInset" y="27"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="CharacterFrameInset" y="27"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
                <Scripts>
					<OnLoad>
						Model_OnLoad(self, MODELFRAME_MAX_PLAYER_ZOOM, nil, nil, CharacterModelFrame_OnMouseUp);
					</OnLoad>
					<OnReceiveDrag>
						AutoEquipCursorItem();
					</OnReceiveDrag>
                </Scripts>
            </PlayerModel>
			<Frame name="PaperDollItemsFrame" setAllPoints="true">
				<Frames>
					<Button name="CharacterHeadSlot" inherits="PaperDollItemSlotButtonLeftTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterFrameInset" x="4" y="-2"/>
						</Anchors>
					</Button>
					<Button name="CharacterNeckSlot" inherits="PaperDollItemSlotButtonLeftTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterHeadSlot" relativePoint="BOTTOMLEFT"  x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterShoulderSlot" inherits="PaperDollItemSlotButtonLeftTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterNeckSlot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterBackSlot" inherits="PaperDollItemSlotButtonLeftTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterShoulderSlot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterChestSlot" inherits="PaperDollItemSlotButtonLeftTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterBackSlot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterShirtSlot" inherits="PaperDollItemSlotButtonLeftTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterChestSlot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterTabardSlot" inherits="PaperDollItemSlotButtonLeftTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterShirtSlot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterWristSlot" inherits="PaperDollItemSlotButtonLeftTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterTabardSlot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterHandsSlot" inherits="PaperDollItemSlotButtonRightTemplate">
						<Anchors>
							<Anchor point="TOPRIGHT" relativeTo="CharacterFrameInset" x="-4" y="-2"/>
						</Anchors>
					</Button>
					<Button name="CharacterWaistSlot" inherits="PaperDollItemSlotButtonRightTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterHandsSlot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterLegsSlot" inherits="PaperDollItemSlotButtonRightTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterWaistSlot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterFeetSlot" inherits="PaperDollItemSlotButtonRightTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterLegsSlot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterFinger0Slot" inherits="PaperDollItemSlotButtonRightTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterFeetSlot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterFinger1Slot" inherits="PaperDollItemSlotButtonRightTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterFinger0Slot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterTrinket0Slot" inherits="PaperDollItemSlotButtonRightTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterFinger1Slot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterTrinket1Slot" inherits="PaperDollItemSlotButtonRightTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterTrinket0Slot" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
						</Anchors>
					</Button>
					<Button name="CharacterMainHandSlot" inherits="PaperDollItemSlotButtonBottomTemplate">
						<Anchors>
							<Anchor point="BOTTOMLEFT" x="130" y="16"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture inherits="Char-Slot-Bottom-Left">
									<Anchors>
										<Anchor point="TOPRIGHT" relativeTo="$parentFrame" relativePoint="TOPLEFT"/>
									</Anchors>
								</Texture>
							</Layer>
						</Layers>
					</Button>
					<Button name="CharacterSecondaryHandSlot" inherits="PaperDollItemSlotButtonBottomTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="CharacterMainHandSlot" relativePoint="TOPRIGHT" x="5" y="0"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture inherits="Char-Slot-Bottom-Right">
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentFrame" relativePoint="TOPRIGHT"/>
									</Anchors>
								</Texture>
							</Layer>
						</Layers>
					</Button>
				</Frames>
			</Frame>
		</Frames>
        <Scripts>
            <OnLoad function="PaperDollFrame_OnLoad"/>
            <OnEvent function="PaperDollFrame_OnEvent"/>
            <OnShow function="PaperDollFrame_OnShow"/>
			<OnHide function="PaperDollFrame_OnHide"/>
        </Scripts>
    </Frame>

	<Frame name="GearManagerDialogPopup" hidden="true" frameLevel="50" parent="PaperDollFrame">
		<Size x="525" y="495"/>
		<Anchors>
			<Anchor point="LEFT" relativePoint="RIGHT"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="BG">
					<Anchors>
						<Anchor point="TOPLEFT" x="7" y="-7"/>
						<Anchor point="BOTTOMRIGHT" x="-7" y="7"/>
					</Anchors>
					<Color r="0" g="0" b="0" a=".80"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="BorderBox" frameLevel="60" hidden="false">
				<Anchors>
					<Anchor point="TOPLEFT"/>
					<Anchor point="BOTTOMRIGHT"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture parentKey="topLeft" atlas="macropopup-topleft" useAtlasSize="true">
							<Anchors>
								<Anchor point="TOPLEFT"/>
							</Anchors>
						</Texture>
						<Texture parentKey="topRight" atlas="macropopup-topright" useAtlasSize="true">
							<Anchors>
								<Anchor point="TOPRIGHT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture atlas="_macropopup-top" horizTile="true" useAtlasSize="true">
							<Anchors>
								<Anchor point="TOPLEFT" relativeKey="$parent.topLeft" relativePoint="TOPRIGHT" x="0" y="0"/>
								<Anchor point="TOPRIGHT" relativeKey="$parent.topRight" relativePoint="TOPLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture parentKey="bottomLeft" atlas="macropopup-bottomleft" useAtlasSize="true">
							<Anchors>
								<Anchor point="BOTTOMLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture parentKey="bottomRight" atlas="macropopup-bottomright" useAtlasSize="true">
							<Anchors>
								<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture atlas="_macropopup-bottom" horizTile="true" useAtlasSize="true">
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativeKey="$parent.bottomLeft" relativePoint="BOTTOMRIGHT" x="0" y="0"/>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.bottomRight" relativePoint="BOTTOMLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture atlas="!macropopup-left" vertTile="true" useAtlasSize="true">
							<Anchors>
								<Anchor point="TOPLEFT" relativeKey="$parent.topLeft" relativePoint="BOTTOMLEFT" x="0" y="0"/>
								<Anchor point="BOTTOMLEFT" relativeKey="$parent.bottomLeft" relativePoint="TOPLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture atlas="!macropopup-right" vertTile="true" useAtlasSize="true">
							<Anchors>
								<Anchor point="TOPRIGHT" relativeKey="$parent.topRight" relativePoint="BOTTOMRIGHT" x="0" y="0"/>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.bottomRight" relativePoint="TOPRIGHT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<FontString inherits="GameFontHighlightSmall" text="GEARSETS_POPUP_TEXT" parentKey="NameText">
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset x="24" y="-21"/>
								</Anchor>
							</Anchors>
						</FontString>
						<FontString inherits="GameFontHighlightSmall" text="MACRO_POPUP_CHOOSE_ICON" parentKey="ChooseIconText">
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset x="24" y="-69"/>
								</Anchor>
							</Anchors>
						</FontString>					
					</Layer>
				</Layers>
				<Frames>
					<EditBox name="GearManagerDialogPopupEditBox" letters="16" historyLines="0" autoFocus="true">
						<Size x="182" y="20"/>
						<Anchors>
							<Anchor point="TOPLEFT" x="29" y="-35"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentLeft" file="Interface\ClassTrainerFrame\UI-ClassTrainer-FilterBorder">
									<Size x="12" y="29"/>
									<Anchors>
										<Anchor point="TOPLEFT">
											<Offset x="-11" y="0"/>
										</Anchor>
									</Anchors>
									<TexCoords left="0" right="0.09375" top="0" bottom="1.0"/>
								</Texture>
								<Texture name="$parentMiddle" file="Interface\ClassTrainerFrame\UI-ClassTrainer-FilterBorder">
									<Size x="175" y="29"/>
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
									</Anchors>
									<TexCoords left="0.09375" right="0.90625" top="0" bottom="1.0"/>
								</Texture>
								<Texture name="$parentRight" file="Interface\ClassTrainerFrame\UI-ClassTrainer-FilterBorder">
									<Size x="12" y="29"/>
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentMiddle" relativePoint="RIGHT"/>
									</Anchors>
									<TexCoords left="0.90625" right="1.0" top="0" bottom="1.0"/>
								</Texture>
							</Layer>
						</Layers>
						<Scripts>
							<OnTextChanged>
								local text = self:GetText();
								if ( text ~= "" ) then
									GearManagerDialogPopup.name = text;
								else
									GearManagerDialogPopup.name = nil;
								end
								
								GearManagerDialogPopupOkay_Update();
							</OnTextChanged>
							<OnEscapePressed function="GearManagerDialogPopupCancel_OnClick"/>
							<OnEnterPressed>
								if ( GearManagerDialogPopupOkay:IsEnabled() ) then
									GearManagerDialogPopupOkay:Click();
								end
							</OnEnterPressed>
						</Scripts>
						<FontString inherits="ChatFontNormal"/>
					</EditBox>	
					<Button name="GearManagerDialogPopupCancel" inherits="UIPanelButtonTemplate" text="CANCEL">
						<Size x="78" y="22"/>
						<Anchors>
							<Anchor point="BOTTOMRIGHT" x="-11" y="13"/>
						</Anchors>
						<Scripts>
							<OnClick>
								GearManagerDialogPopupCancel_OnClick(self, button, pushed);
								PlaySound(SOUNDKIT.GS_TITLE_OPTION_OK);
							</OnClick>
						</Scripts>
					</Button>					
					<Button name="GearManagerDialogPopupOkay" inherits="UIPanelButtonTemplate" text="OKAY">
						<Size x="78" y="22"/>
						<Anchors>
							<Anchor point="RIGHT" relativeTo="GearManagerDialogPopupCancel" relativePoint="LEFT" x="-2" y="0"/>
						</Anchors>
						<Scripts>
							<OnClick>
								GearManagerDialogPopupOkay_OnClick(self, button, pushed);
								PlaySound(SOUNDKIT.GS_TITLE_OPTION_OK);
							</OnClick>
						</Scripts>
					</Button>
				</Frames>
			</Frame>
			<ScrollFrame name="$parentScrollFrame" inherits="ListScrollFrameTemplate">
				<Size x="489" y="386"/>
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="$parent" relativePoint="TOPRIGHT" x="-36" y="-71"/>
				</Anchors>
				<Scripts>
					<OnVerticalScroll>
						FauxScrollFrame_OnVerticalScroll(self, offset, GEARSET_ICON_ROW_HEIGHT, GearManagerDialogPopup_Update);
					</OnVerticalScroll>
				</Scripts>
			</ScrollFrame>
		</Frames>
		<Scripts>
			<OnLoad function="GearManagerDialogPopup_OnLoad"/>
			<OnShow function="GearManagerDialogPopup_OnShow"/>
			<OnUpdate function="GearManagerDialogPopup_OnUpdate"/>
			<OnHide function="GearManagerDialogPopup_OnHide"/>
		</Scripts>				
	</Frame>
</Ui>
