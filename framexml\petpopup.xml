<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="PetPopup.lua"/>
	<Button name="PetPopupButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="64" y="20"/>
		</Size>
		<NormalTexture file="Interface\Buttons\UI-DialogBox-Button-Up">
			<TexCoords left="0.0" right="1.0" top="0.0" bottom="0.625"/>
		</NormalTexture>
		<PushedTexture file="Interface\Buttons\UI-DialogBox-Button-Down">
			<TexCoords left="0.0" right="1.0" top="0.0" bottom="0.625"/>
		</PushedTexture>
		<HighlightTexture file="Interface\Buttons\UI-DialogBox-Button-Highlight" alphaMode="ADD">
			<TexCoords left="0.0" right="1.0" top="0.0" bottom="0.625"/>
		</HighlightTexture>
		<NormalFont style="GameFontNormal"/>
		<HighlightFont style="GameFontHighlight"/>
	</Button>
	<Frame name="PetRenamePopup" toplevel="true" frameStrata="DIALOG" enableMouse="true" hidden="true" parent="UIParent">
		<Size>
			<AbsDimension x="196" y="112"/>
		</Size>
		<Anchors>
			<Anchor point="TOPLEFT" relativePoint="TOPLEFT">
				<Offset>
					<AbsDimension x="10" y="-128"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString text="PET_RENAME_LABEL" inherits="GameFontHighlight">
					<Size>
						<AbsDimension x="300" y="0"/>
					</Size>
					<Anchors>
						<Anchor point="TOP">
							<Offset>
								<AbsDimension x="0" y="-25"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<EditBox name="PetRenamePopupEditBox" letters="12" historyLines="1">
				<Size>
					<AbsDimension x="150" y="32"/>
				</Size>
				<Anchors>
					<Anchor point="CENTER"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture file="Interface\ChatFrame\UI-ChatInputBorder-Left">
							<Size>
								<AbsDimension x="150" y="32"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT"/>
							</Anchors>
						</Texture>
						<Texture file="Interface\ChatFrame\UI-ChatInputBorder-Right">
							<Size>
								<AbsDimension x="150" y="32"/>
							</Size>
							<Anchors>
								<Anchor point="RIGHT"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnterPressed function="PetPopup_Confirm"/>
					<OnEscapePressed function="PetPopup_Cancel"/>
				</Scripts>
				<FontString inherits="ChatFontNormal"/>
			</EditBox>			
			<Button name="PetRenamePopupAcceptButton" inherits="PetPopupButtonTemplate">
				<Anchors>
					<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOM">
						<Offset>
							<AbsDimension x="-5" y="15"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="PetPopup_Confirm"/>
				</Scripts>
				<ButtonText text="ACCEPT"/>
				<NormalFont style="GameFontNormalSmall"/>
				<HighlightFont style="GameFontNormalSmall"/>
			</Button>
			<Button name="PetRenamePopupCancelButton" inherits="PetPopupButtonTemplate">
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativePoint="BOTTOM">
						<Offset>
							<AbsDimension x="5" y="15"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="PetPopup_Cancel"/>
				</Scripts>
				<ButtonText text="CANCEL"/>
				<NormalFont style="GameFontNormalSmall"/>
				<HighlightFont style="GameFontNormalSmall"/>
			</Button>
		</Frames>
	</Frame>
</Ui>
