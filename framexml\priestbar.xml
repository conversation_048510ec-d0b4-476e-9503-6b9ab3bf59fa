<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
  <Script file="PriestBar.lua"/>
  
	<Frame name="ShadowOrbLargeTemplate" parentArray="LargeOrbs" virtual="true">
		<Size x="38" y="37"/>
		<Animations>
			<AnimationGroup parentKey="AnimIn" setToFinalAlpha="true">
				<Alpha childKey="Bg" fromAlpha="0.5" toAlpha="1" duration="0.2" order="1"/>
				<Alpha childKey="Highlight" fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
				<Alpha childKey="Orb" fromAlpha="0.5" toAlpha="1" duration="0.2" order="1"/>
				<Alpha childKey="Glow" fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
				<Alpha childKey="Glow" fromAlpha="1" toAlpha="0" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="AnimOut" setToFinalAlpha="true">
				<Alpha childKey="Glow" fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
				<Alpha childKey="Glow" fromAlpha="1" toAlpha="0" duration="0.2" order="2"/>
				<Alpha childKey="Bg" fromAlpha="1" toAlpha="0.5" duration="0.2" order="2"/>
				<Alpha childKey="Highlight" fromAlpha="1" toAlpha="0" duration="0.2" order="2"/>
				<Alpha childKey="Orb" fromAlpha="0.5" toAlpha="0" duration="0.2" order="2"/>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentBg" file="Interface\PlayerFrame\Priest-ShadowUI" parentKey="Bg" alpha="0.5">
					<Size x="38" y="37"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
					<TexCoords left="0.30078125" right="0.44921875" top="0.44531250" bottom="0.73437500"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Orb" atlas="shadoworbs-large-Orb" useAtlasSize="true" alpha="0">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Highlight" atlas="shadoworbs-large-Frame-OrbHighlight" useAtlasSize="true" alpha="0">
					<Anchors>
						<Anchor point="TOP" x="0" y="-1"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="Glow" atlas="shadoworbs-large-Orb" useAtlasSize="true" alpha="0" alphaMode="ADD">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="ShadowOrbSmallTemplate" parentArray="SmallOrbs" virtual="true">
		<Size x="38" y="37"/>
		<Animations>
			<AnimationGroup parentKey="AnimIn" setToFinalAlpha="true">
				<Alpha childKey="Bg" fromAlpha="0.5" toAlpha="1" duration="0.2" order="1"/>
				<Alpha childKey="Highlight" fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
				<Alpha childKey="Orb" fromAlpha="0.5" toAlpha="1" duration="0.2" order="1"/>
				<Alpha childKey="Glow" fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
				<Alpha childKey="Glow" fromAlpha="1" toAlpha="0" duration="0.2" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="AnimOut" setToFinalAlpha="true">
				<Alpha childKey="Glow" fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
				<Alpha childKey="Glow" fromAlpha="1" toAlpha="0" duration="0.2" order="2"/>
				<Alpha childKey="Bg" fromAlpha="1" toAlpha="0.5" duration="0.2" order="2"/>
				<Alpha childKey="Highlight" fromAlpha="1" toAlpha="0" duration="0.2" order="2"/>
				<Alpha childKey="Orb" fromAlpha="0.5" toAlpha="0" duration="0.2" order="2"/>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Bg" atlas="shadoworbs-small-Orb-Bg" useAtlasSize="true" alpha="0.5">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Orb" atlas="shadoworbs-small-Orb" useAtlasSize="true" alpha="0">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Highlight" atlas="shadoworbs-small-Frame-OrbHighlight" useAtlasSize="true" alpha="0">
					<Anchors>
						<Anchor point="TOP" x="0" y="-1"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="Glow" atlas="shadoworbs-small-Orb" useAtlasSize="true" alpha="0" alphaMode="ADD">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="PriestBarFrame" parent="PlayerFrame" toplevel="true" hidden="true" enableMouse="true">
		<Size x="159" y="54"/>
		<Anchors>
			<Anchor point="TOP" relativeTo="PlayerFrame" relativePoint="BOTTOM" x="53" y="37"/>
		</Anchors>
		<HitRectInsets>
			<AbsInset left="28" right="33" top="2" bottom="22"/>
		</HitRectInsets>
		<Animations>
			<AnimationGroup parentKey="ShowAnim">
				<Alpha fromAlpha="0" toAlpha="1" duration="0.5" order="1"/>
				<Scripts>
					<OnFinished>
						self:GetParent():SetAlpha(1.0);
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Holder" atlas="shadoworbs-Large-Frame" setAllPoints="true"/>
			</Layer>
		</Layers>
		<Frames>
			<Frame inherits="ShadowOrbLargeTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" x="26" y="-1"/>
				</Anchors>
			</Frame>
			<Frame inherits="ShadowOrbSmallTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" x="12" y="-1"/>
				</Anchors>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="PriestBarFrame_OnLoad"/>
			<OnEvent function="PriestBarFrame_OnEvent"/>
			<OnEnter>
				GameTooltip_SetDefaultAnchor(GameTooltip, self);
				GameTooltip:SetText(SHADOW_ORBS, 1, 1, 1);
				GameTooltip:AddLine(SHADOW_ORBS_TOOLTIP, nil, nil, nil, true);
				GameTooltip:Show();
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
	</Frame>
</Ui>
