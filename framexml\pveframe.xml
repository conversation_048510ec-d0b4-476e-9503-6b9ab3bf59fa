<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="PVEFrame.lua"/>

	<Button name="GroupFinderGroupButtonTemplate" motionScriptsWhileDisabled="true" virtual="true">
		<Size x="203" y="60"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture file="Interface\Common\bluemenu-main" parentKey="bg">
					<Size x="224" y="80"/>
					<TexCoords left="0.00390625" right="0.87890625" top="0.75195313" bottom="0.83007813"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="2">
				<Texture name="$parentRing" atlas="bluemenu-Ring" parentKey="ring">
					<Size x="95" y="96"/>
					<Anchors>
						<Anchor point="LEFT" x="-12" y="-1" />
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture name="$parentIcon" parentKey="icon">
					<Size x="66" y="66"/>
					<Anchors>
						<Anchor point="CENTER" relativeTo="$parentRing" x="0" y="0" />
					</Anchors>
				</Texture>
				<FontString name="$parentName" inherits="GameFontNormalLarge" parentKey="name" spacing="2" justifyH="LEFT">
					<Size x="106" y="0"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentRing" relativePoint="RIGHT" x="0" y="0" />
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<HighlightTexture file="Interface\Common\bluemenu-main" alphaMode="ADD" alpha="0.8">
			<Size x="224" y="80"/>
			<TexCoords left="0.00390625" right="0.87890625" top="0.75195313" bottom="0.83007813"/>
			<Anchors>
				<Anchor point="CENTER"/>
			</Anchors>
		</HighlightTexture>
		<Scripts>
			<OnClick function="GroupFinderFrameGroupButton_OnClick"/>
			<OnEnter>
				if ( self.tooltip ) then
					GameTooltip:SetOwner(self, "ANCHOR_TOP");
					GameTooltip:SetText(self.tooltip, nil, nil, nil, nil, true);
				end
			</OnEnter>
			<OnLeave>
				if ( GameTooltip:GetOwner() == self ) then
					GameTooltip:Hide();
				end
			</OnLeave>
		</Scripts>
	</Button>

	<Frame name="PVEFrame" inherits="PortraitFrameTemplate" enableMouse="true" parent="UIParent" hidden="true" toplevel="true">
		<Size x="563" y="428"/>
		<Anchors>
			<Anchor point="TOPLEFT" x="100" y="-84"/>
		</Anchors>
		<Layers>
			<Layer level="BORDER" textureSubLevel="-1">
				<Texture name="$parentBlueBg" file="Interface\Common\bluemenu-main">
					<Size x="209" y="399"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="7" y="-23" />
					</Anchors>
					<TexCoords left="0.00390625" right="0.82421875" top="0.18554688" bottom="0.58984375"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture name="$parentTLCorner" file="Interface\Common\bluemenu-main">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentBlueBg" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.00390625" right="0.25390625" top="0.00097656" bottom="0.06347656"/>
				</Texture>
				<Texture name="$parentTRCorner" file="Interface\Common\bluemenu-main">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="151" y="-23"/>
					</Anchors>
					<TexCoords left="0.51953125" right="0.76953125" top="0.00097656" bottom="0.06347656"/>
				</Texture>
				<Texture name="$parentBRCorner" file="Interface\Common\bluemenu-main">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="151" y="7"/>
					</Anchors>
					<TexCoords left="0.00390625" right="0.25390625" top="0.06542969" bottom="0.12792969"/>
				</Texture>
				<Texture name="$parentBLCorner" file="Interface\Common\bluemenu-main">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="7" y="7"/>
					</Anchors>
					<TexCoords left="0.26171875" right="0.51171875" top="0.00097656" bottom="0.06347656"/>
				</Texture>
				<Texture name="$parentLLVert" file="Interface\Common\bluemenu-vert" vertTile="true"> 	<!-- left line -->
					<Size x="43" y="270"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="7" y="-87" />
					</Anchors>
					<TexCoords left="0.06250000" right="0.39843750" top="0.00000000" bottom="1.00000000"/>
				</Texture>
				<Texture name="$parentRLVert" file="Interface\Common\bluemenu-vert" vertTile="true">	<!-- right line -->
					<Size x="43" y="270"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="172" y="-87" />
					</Anchors>
					<TexCoords left="0.41406250" right="0.75000000" top="0.00000000" bottom="1.00000000"/>
				</Texture>
				<Texture name="$parentBottomLine" file="Interface\Common\bluemenu-goldborder-horiz" horizTile="true">	<!-- bottom line -->
					<Size x="80" y="43"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentBLCorner" relativePoint="BOTTOMRIGHT" x="0" y="0" />
					</Anchors>
					<TexCoords left="0.00000000" right="1.00000000" top="0.35937500" bottom="0.69531250"/>
				</Texture>
				<Texture name="$parentTopLine" file="Interface\Common\bluemenu-goldborder-horiz" horizTile="true">
					<Size x="80" y="43"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTLCorner" relativePoint="TOPRIGHT" x="0" y="0" />
					</Anchors>
					<TexCoords left="0.00000000" right="1.00000000" top="0.00781250" bottom="0.34375000"/>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture name="$parentTopFiligree" file="Interface\Common\bluemenu-main">		<!-- top filigree -->
					<Size x="185" y="55"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentBlueBg" x="12" y="-6" />
					</Anchors>
					<TexCoords left="0.00390625" right="0.72656250" top="0.12988281" bottom="0.18359375"/>
				</Texture>
				<Texture name="$parentBottomFiligree" file="Interface\Common\bluemenu-main">		<!-- bottom filigree -->
					<Size x="185" y="55"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentBlueBg" x="12" y="4" />
					</Anchors>
					<TexCoords left="0.26171875" right="0.98437500" top="0.06542969" bottom="0.11914063"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentLeftInset" useParentLevel="true" inherits="InsetFrameTemplate" parentKey="Inset">
				<Size x="217" y="496"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="4" y="-24" />
					<Anchor point="BOTTOMLEFT" x="4" y="4" />
				</Anchors>
			</Frame>
			<Button name="$parentTab1" inherits="CharacterFrameTabButtonTemplate" text="GROUP_FINDER" id="1" parentKey="tab1">
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativeTo="$parent" relativePoint="BOTTOMLEFT">
						<Offset x="19" y="-30"/>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="PVEFrame_TabOnClick"/>
				</Scripts>
			</Button>
			<Button name="$parentTab2" inherits="CharacterFrameTabButtonTemplate" text="PLAYER_V_PLAYER" id="2" parentKey="tab2">
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentTab1" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="-16" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="PVEFrame_TabOnClick"/>
				</Scripts>
			</Button>
			<Button name="$parentTab3" inherits="CharacterFrameTabButtonTemplate" text="CHALLENGES" id="3" parentKey="tab3">
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentTab2" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="-16" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick function="PVEFrame_TabOnClick"/>
				</Scripts>
			</Button>
			<Frame name="GroupFinderFrame" useParentLevel="true" setAllPoints="true">
				<Frames>
					<Button name="$parentGroupButton1" inherits="GroupFinderGroupButtonTemplate" parentKey="groupButton1" id="1">
						<Anchors>
							<Anchor point="TOPLEFT" x="10" y="-70" />
						</Anchors>
					</Button>
					<Button name="$parentGroupButton2" inherits="GroupFinderGroupButtonTemplate" parentKey="groupButton2" id="2">
						<Anchors>
							<Anchor point="TOP" relativeTo="$parentGroupButton1" relativePoint="BOTTOM" x="0" y="-23" />
						</Anchors>
					</Button>
					<Button name="$parentGroupButton3" inherits="GroupFinderGroupButtonTemplate" parentKey="groupButton3" id="3">
						<!-- Anchors set in Lua -->
					</Button>
					<Button name="$parentGroupButton4" inherits="GroupFinderGroupButtonTemplate" parentKey="groupButton4" id="4">
						<Anchors>
							<Anchor point="TOP" relativeTo="$parentGroupButton3" relativePoint="BOTTOM" x="0" y="-23" />
						</Anchors>
						<Frames>
							<Frame name="PremadeGroupsPvETutorialAlert" inherits="MicroButtonAlertTemplate" parent="GroupFinderFrameGroupButton4">
								<KeyValues>
									<KeyValue key="label" value="LFG_LIST_TUTORIAL_ALERT" type="global"/>
									<KeyValue key="tutorialIndex" value="LE_FRAME_TUTORIAL_LFG_LIST" type="global"/>
								</KeyValues>
								<Anchors>
									<Anchor point="BOTTOM" relativeKey="$parent" relativePoint="TOP" x="0" y="8"/>
								</Anchors>
							</Frame>
						</Frames>
					</Button>
					<Frame name="LFGListPVEStub" hidden="true">
						<Size x="338" y="428"/>
						<Anchors>
							<Anchor point="TOPLEFT" x="224" y="0"/>
						</Anchors>
						<Scripts>
							<OnShow>
								LFGListFrame:SetParent(self);
								LFGListFrame:ClearAllPoints();
								LFGListFrame:SetAllPoints(self);
								LFGListFrame:SetFrameLevel(self:GetFrameLevel());
								LFGListFrame_SetBaseFilters(LFGListFrame, LE_LFG_LIST_FILTER_PVE);
							</OnShow>
						</Scripts>
					</Frame>
				</Frames>
				<Scripts>
					<OnLoad function="GroupFinderFrame_OnLoad"/>
					<OnShow function="GroupFinderFrame_OnShow"/>
					<OnEvent function="GroupFinderFrame_OnEvent"/>
				</Scripts>
			</Frame>
			<!-- raised frame for shadows to be on top of the buttons -->
			<Frame parentKey="shadows">
				<Size x="222" y="399"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="4" y="-25" />
				</Anchors>
				<Layers>
					<Layer level="OVERLAY">
						<Texture file="Interface\Common\bluemenu-shadowcovers">		<!-- left line -->
							<Size x="84" y="324"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="4" y="-36" />
							</Anchors>
							<TexCoords left="0.00781250" right="0.66406250" top="0.00097656" bottom="0.34960938"/>
						</Texture>
						<Texture file="Interface\Common\bluemenu-shadowcovers">		<!-- right line -->
							<Size x="84" y="324"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="127" y="-36" />
							</Anchors>
							<TexCoords left="0.00781250" right="0.66406250" top="0.35156250" bottom="0.70019531"/>
						</Texture>
						<Texture file="Interface\Common\bluemenu-vert" vertTile="true">
							<Size x="5" y="403"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="211" y="3" />
							</Anchors>
							<TexCoords left="0.00781250" right="0.04687500" top="0" bottom="1"/>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="PVEFrame_OnLoad"/>
			<OnShow>
				PVEFrame_OnShow(self);
				UpdateMicroButtons();
				PlaySound(SOUNDKIT.IG_CHARACTER_INFO_OPEN);
			</OnShow>
			<OnHide>
				UpdateMicroButtons();
				PlaySound(SOUNDKIT.IG_CHARACTER_INFO_CLOSE);
			</OnHide>
			<OnEvent function="PVEFrame_OnEvent"/>
		</Scripts>
	</Frame>
</Ui>
