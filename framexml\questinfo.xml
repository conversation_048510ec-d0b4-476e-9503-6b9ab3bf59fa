 <Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="QuestInfo.lua"/>

	<Font name="QuestMapRewardsFont" inherits="GameFontBlackSmall" virtual="true">
		<Color r="0.902" g="0.788" b="0.671"/>
	</Font>

	<Button name="QuestInfoRewardSpellCodeTemplate" virtual="true">
		<Scripts>
			<OnEnter>
				GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
				if ( QuestInfoFrame.questLog ) then
					GameTooltip:SetQuestLogRewardSpell(self.rewardSpellIndex);
				else
					GameTooltip:SetQuestRewardSpell(self.rewardSpellIndex);
				end
			</OnEnter>
			<OnClick>
				if ( IsModifiedClick("CHATLINK") ) then
					if ( QuestInfoFrame.questLog ) then
						ChatEdit_InsertLink(GetQuestLogSpellLink(self.rewardSpellIndex));
					else
						ChatEdit_InsertLink(GetQuestSpellLink(self.rewardSpellIndex));
					end
				end
			</OnClick>
			<OnLeave>
				GameTooltip:Hide();
				ResetCursor();
			</OnLeave>
		</Scripts>
	</Button>

	<Button name="QuestInfoRewardItemCodeTemplate" virtual="true">
		<Scripts>
			<OnEnter>
				GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
				if ( QuestInfoFrame.questLog ) then
					if (self.objectType == "item") then
						GameTooltip:SetQuestLogItem(self.type, self:GetID());
						GameTooltip_ShowCompareItem(GameTooltip);
					elseif (self.objectType == "currency") then
						GameTooltip:SetQuestLogCurrency(self.type, self:GetID());
					end
				else
					if (self.objectType == "item") then
						GameTooltip:SetQuestItem(self.type, self:GetID());
						GameTooltip_ShowCompareItem(GameTooltip);
					elseif (self.objectType == "currency") then
						GameTooltip:SetQuestCurrency(self.type, self:GetID());
					end
				end
				CursorUpdate(self);
			</OnEnter>
			<OnClick>
				if ( IsModifiedClick() and self.objectType == "item") then
					if ( QuestInfoFrame.questLog ) then
						HandleModifiedItemClick(GetQuestLogItemLink(self.type, self:GetID()));
					else
						HandleModifiedItemClick(GetQuestItemLink(self.type, self:GetID()));
					end
				else
					if ( QuestInfoFrame.chooseItems ) then
						QuestInfoItem_OnClick(self);
					end
				end
			</OnClick>
			<OnLeave>
				GameTooltip:Hide();
				ResetCursor();
			</OnLeave>
			<OnUpdate>
				CursorOnUpdate(self, elapsed);
			</OnUpdate>
		</Scripts>
	</Button>
	
	<Button name="SmallQuestRewardItemButtonTemplate" inherits="SmallItemButtonTemplate, QuestInfoRewardItemCodeTemplate" virtual="true">
		<Layers>
			<Layer level="OVERLAY" textureSubLevel="1">
				<Texture parentKey="IconBorder" file="Interface\Common\WhiteIconFrame" hidden="true">
					<Size x="37" y="37"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Icon"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.Icon"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Button>
	
	<Button name="LargeQuestRewardItemButtonTemplate" inherits="LargeItemButtonTemplate, QuestInfoRewardItemCodeTemplate" virtual="true">
		<Layers>
			<Layer level="OVERLAY" textureSubLevel="1">
				<Texture parentKey="IconBorder" file="Interface\Common\WhiteIconFrame" hidden="true">
					<Size x="37" y="37"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Icon"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.Icon"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Button>
	
	<Button name="QuestInfoRewardFollowerCodeTemplate" virtual="true">
		<Scripts>
			<OnEnter>
				GarrisonFollowerTooltip:ClearAllPoints();
				GarrisonFollowerTooltip:SetPoint("BOTTOMLEFT", self, "TOPRIGHT");
				local link = C_Garrison.GetFollowerLinkByID(self.ID);
				local _, garrisonFollowerID, quality, level, itemLevel, ability1, ability2, ability3, ability4, trait1, trait2, trait3, trait4, spec1 = strsplit(":", link);
				GarrisonFollowerTooltip_Show(tonumber(garrisonFollowerID), false, tonumber(quality), tonumber(level), 0, 0, tonumber(itemLevel), tonumber(spec1), tonumber(ability1), tonumber(ability2), tonumber(ability3), tonumber(ability4), tonumber(trait1), tonumber(trait2), tonumber(trait3), tonumber(trait4));
			</OnEnter>
			<OnClick>
				if ( IsModifiedClick("CHATLINK") ) then
					local followerLink = C_Garrison.GetFollowerLinkByID(self.ID);
					if ( followerLink ) then
						ChatEdit_InsertLink(followerLink);
					end
				end
			</OnClick>
			<OnLeave>
				GarrisonFollowerTooltip:Hide();
			</OnLeave>
		</Scripts>
	</Button>

	<Button name="LargeQuestInfoRewardFollowerTemplate" inherits="QuestInfoRewardFollowerCodeTemplate" virtual="true">
		<Size x="144" y="55" />
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="BG" atlas="GarrMission_FollowerListButton">
					<Anchors>
						<Anchor point="TOPLEFT" x="30" y="-7"/>
						<Anchor point="BOTTOMRIGHT" x="0" y="7"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="2">
				<Texture parentKey="Class" alpha=".2">
					<Size x="41" />
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.BG"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BG"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Name" inherits="GameFontNormal" justifyH="LEFT" justifyV="MIDDLE">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BG" x="18" y="0"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BG" x="-5" y="2"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="PortraitFrame" inherits="GarrisonFollowerPortraitTemplate">
				<Anchors>
					<Anchor point="LEFT" x="4" y="1"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						self:SetFrameLevel(self:GetParent():GetFrameLevel() + 2);
						self:SetScale(.8);
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
	</Button>

	 <Button name="SmallQuestInfoRewardFollowerTemplate" inherits="QuestInfoRewardFollowerCodeTemplate" virtual="true">
		 <Size x="134" y="30"/>
		 <Layers>
			 <Layer level="BACKGROUND">
				 <Texture parentKey="BG" atlas="GarrMission_FollowerListButton">
					 <Anchors>
						 <Anchor point="TOPLEFT" x="30" y="2"/>
						 <Anchor point="BOTTOMRIGHT" x="0" y="-2"/>
					 </Anchors>
				 </Texture>
			 </Layer>
			 <Layer level="BACKGROUND" textureSubLevel="2">
				 <Texture parentKey="Class" alpha=".2">
					 <Size x="33" />
					 <Anchors>
						 <Anchor point="TOPRIGHT" relativeKey="$parent.BG"/>
						 <Anchor point="BOTTOMRIGHT" relativeKey="$parent.BG"/>
					 </Anchors>
				 </Texture>
			 </Layer>
			 <Layer level="ARTWORK">
				 <FontString parentKey="Name" inherits="GameFontNormalSmall" justifyH="LEFT" justifyV="MIDDLE">
					 <Anchors>
						 <Anchor point="TOPLEFT" relativeKey="$parent.BG" x="10" y="0"/>
						 <Anchor point="BOTTOMRIGHT" relativeKey="$parent.BG" x="-5" y="2"/>
					 </Anchors>
				 </FontString>
			 </Layer>
		 </Layers>
		 <Frames>
			 <Frame parentKey="PortraitFrame" inherits="GarrisonFollowerPortraitTemplate">
				 <Anchors>
					 <Anchor point="LEFT" x="4" y="1"/>
				 </Anchors>
				 <Scripts>
					 <OnLoad>
						 self:SetFrameLevel(self:GetParent():GetFrameLevel() + 2);
						 self:SetScale(.65);
					 </OnLoad>
				 </Scripts>
			 </Frame>
		 </Frames>
	 </Button>

	<Frame name="QuestInfoObjectivesFrame" hidden="true">
		<Size x="285" y="10"/>
		<Anchors>
			<Anchor point="CENTER" />
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<FontString name="QuestInfoObjective1" inherits="QuestFontNormalSmall" justifyH="LEFT" hidden="true" parentArray="Objectives">
					<Size x="285" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" />
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="QuestInfoSpecialObjectivesFrame" hidden="true">
		<Size x="285" y="10"/>
		<Anchors>
			<Anchor point="CENTER" />
		</Anchors>
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="QuestInfoSpellObjectiveLearnLabel" inherits="QuestFontNormalSmall" justifyH="LEFT" text="LEARN_SPELL_OBJECTIVE"/>
			</Layer>
		</Layers>
		<Frames>
			<Button name="QuestInfoSpellObjectiveFrame" inherits="QuestSpellTemplate" hidden="true"/>
		</Frames>
	</Frame>

	<Frame name="QuestInfoTimerFrame" hidden="true">
		<Size x="1" y="1"/>
		<Anchors>
			<Anchor point="CENTER" />
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<FontString name="QuestInfoTimerText" inherits="QuestFontNormalSmall" justifyH="LEFT" hidden="false">
					<Size x="285" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" />
					</Anchors>
					<Color r="0" g="0" b="0" />
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnUpdate function="QuestInfoTimerFrame_OnUpdate"/>
		</Scripts>
	</Frame>

	<Frame name="QuestInfoRequiredMoneyFrame">
		<Size x="285" y="28"/>
		<Layers>
			<Layer level="BACKGROUND">
				<FontString name="QuestInfoRequiredMoneyText" inherits="QuestFontNormalSmall" text="REQUIRED_MONEY">
					<Anchors>
						<Anchor point="LEFT"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="QuestInfoRequiredMoneyDisplay" inherits="MoneyFrameTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeTo="QuestInfoRequiredMoneyText" relativePoint="RIGHT" x="10" y="0"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						SmallMoneyFrame_OnLoad(self);
						MoneyFrame_SetType(self, "STATIC");
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
	</Frame>

	<FontString name="QuestInfoSpellHeaderTemplate" inherits="QuestFont" justifyH="LEFT" virtual="true" />

	<Frame name="QuestInfoRewardsFrame" hidden="true">
		<Size x="285" y="10" />
		<Anchors>
			<Anchor point="CENTER" />
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<FontString parentKey="Header" inherits="QuestTitleFont" text="QUEST_REWARDS" justifyH="LEFT">
					<Size x="285" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</FontString>
				<FontString parentKey="ItemChooseText" inherits="QuestFont" text="REWARD_CHOICES" hidden="true" justifyH="LEFT">
					<Size x="285" y="0"/>
				</FontString>
				<FontString parentKey="ItemReceiveText" inherits="QuestFont" hidden="true" justifyH="LEFT" />
				<FontString parentKey="PlayerTitleText" inherits="QuestFont" text="REWARD_TITLE" hidden="true" justifyH="LEFT" />
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="HonorFrame" inherits="LargeItemButtonTemplate" hidden="true"/>
			<Button parentArray="RewardButtons" name="$parentQuestInfoItem1" inherits="LargeQuestRewardItemButtonTemplate" hidden="true"/>
			<Frame parentKey="MoneyFrame" name="QuestInfoMoneyFrame" inherits="MoneyFrameTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.ItemReceiveText" relativePoint="RIGHT" x="15" y="0"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						MoneyFrame_OnLoad(self);
						MoneyFrame_SetType(self, "STATIC");
					</OnLoad>
				</Scripts>
			</Frame>
			<Button parentKey="SkillPointFrame" name="QuestInfoSkillPointFrame" inherits="LargeItemButtonTemplate">
				<Layers>
					<Layer level="OVERLAY">
						<Texture parentKey="CircleBackground" file="Interface\QuestFrame\SkillUp-BG">
							<Size x="32" y="32"/>
							<Anchors>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.Icon" x="12" y="-6"/>
							</Anchors>
						</Texture>
						<Texture parentKey="CircleBackgroundGlow" file="Interface\QuestFrame\SkillUp-Glow" alphaMode="ADD" alpha="0.3">
							<Size x="64" y="64"/>
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.CircleBackground"/>
							</Anchors>
						</Texture>
						<FontString parentKey="ValueText" inherits="GameFontNormalLarge" justifyH="LEFT">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.CircleBackground" relativePoint="CENTER" x="3"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						-- Hacks to get this to display a little more nicely
						self.Name:SetPoint("LEFT", self.NameFrame, 24, 0);
						self.Name:SetWidth(81);
					</OnLoad>
					<OnEnter>
						if (self.tooltip) then
							GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
							GameTooltip:SetText(self.tooltip, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b);
						end
					</OnEnter>
					<OnLeave>
						GameTooltip:Hide();
					</OnLeave>
				</Scripts>
			</Button>
			<Frame parentKey="XPFrame" name="QuestInfoXPFrame">
				<Size x="280" y="20"/>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString parentKey="ReceiveText" inherits="QuestFont" text="EXPERIENCE_COLON">
							<Anchors>
								<Anchor point="LEFT" x="0" y="0"/>
							</Anchors>
						</FontString>
						<FontString parentKey="ValueText" inherits="NumberFontNormalLarge" justifyH="LEFT">
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.ReceiveText" relativePoint="RIGHT" x="15" y="0"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
			<Button parentKey="ArtifactXPFrame" inherits="LargeItemButtonTemplate" hidden="true">
				<Layers>
					<Layer level="BORDER">
						<Texture parentKey="Overlay" file="Interface\Artifacts\ArtifactPower-QuestBorder">
							<Size x="64" y="64" />
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.Icon" />
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText(ARTIFACT_XP_REWARD, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b, nil, true);
					</OnEnter>

					<OnLeave function="GameTooltip_Hide" />
				</Scripts>
			</Button>
			<Frame parentKey="TitleFrame" name="QuestInfoPlayerTitleFrame">
				<Size x="500" y="39"/>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture parentKey="Icon" file="Interface\Icons\INV_Misc_Note_02">
							<Size x="39" y="39"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture parentKey="FrameLeft" file="Interface\QuestFrame\UI-QuestItemNameFrame">
							<Size x="4" y="40"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.Icon" relativePoint="RIGHT" x="2" y="0"/>
							</Anchors>
							<TexCoords left="0.078125" right="0.1171875" top="0.15625" bottom="0.828125"/>
						</Texture>
						<Texture parentKey="FrameCenter" file="Interface\QuestFrame\UI-QuestItemNameFrame">
							<Size x="200" y="40"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.FrameLeft" relativePoint="RIGHT" x="0" y="0"/>
							</Anchors>
							<TexCoords left="0.1171875" right="0.828125" top="0.15625" bottom="0.828125"/>
						</Texture>
						<Texture parentKey="FrameRight" file="Interface\QuestFrame\UI-QuestItemNameFrame">
							<Size x="11" y="40"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.FrameCenter" relativePoint="RIGHT" x="0" y="0"/>
							</Anchors>
							<TexCoords left="0.828125" right="0.9140625" top="0.15625" bottom="0.828125"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<FontString parentKey="Name" inherits="GameFontHighlight" justifyH="LEFT">
							<Size x="200" y="0"/>
							<Anchors>
								<Anchor point="LEFT" relativeKey="$parent.FrameLeft" x="8" y="-2"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
			<Frame parentKey="ItemHighlight" name="QuestInfoItemHighlight" hidden="true">
				<Size x="256" y="64"/>
				<Anchors>
					<Anchor point="TOPLEFT"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture file="Interface\QuestFrame\UI-QuestItemHighlight" alphaMode="ADD">
							<Size x="256" y="64"/>
							<Anchors>
								<Anchor point="TOPLEFT"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad function="RaiseFrameLevel"/>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self.buttonTemplate = "LargeQuestRewardItemButtonTemplate";
				self.spellRewardPool = CreateFramePool("BUTTON", self, "QuestSpellTemplate, QuestInfoRewardSpellCodeTemplate");
				self.followerRewardPool = CreateFramePool("BUTTON", self, "LargeQuestInfoRewardFollowerTemplate");
				self.spellHeaderPool = CreateFontStringPool(self, "BACKGROUND", 0, "QuestInfoSpellHeaderTemplate");
			</OnLoad>
		</Scripts>
	</Frame>

	<FontString name="MapQuestInfoSpellHeaderTemplate" inherits="QuestMapRewardsFont" justifyH="LEFT" virtual="true"/>

	<Frame name="MapQuestInfoRewardsFrame" hidden="true">
		<Size x="285" y="10" />
		<Anchors>
			<Anchor point="CENTER" />
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<FontString parentKey="ItemChooseText" inherits="QuestMapRewardsFont" text="REWARD_CHOICES" hidden="true" justifyH="LEFT">
					<Size x="285" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="-6"/>
					</Anchors>
					<Color r="0.902" g="0.788" b="0.671"/>
				</FontString>
				<FontString parentKey="ItemReceiveText" inherits="QuestMapRewardsFont" hidden="true" justifyH="LEFT"/>
				<FontString parentKey="PlayerTitleText" inherits="QuestMapRewardsFont" text="REWARD_TITLE" hidden="true" justifyH="LEFT"/>
			</Layer>
		</Layers>
		<Frames>
			<!-- need this for anchoring -->
			<Frame parentKey="Header">
				<Size x="1" y="1"/>
				<Anchors>
					<Anchor point="TOPLEFT"/>
				</Anchors>
			</Frame>
			<Button parentArray="RewardButtons" name="$parentQuestInfoItem1" inherits="SmallQuestRewardItemButtonTemplate" hidden="true"/>
			<Button parentKey="XPFrame" inherits="SmallItemButtonTemplate" hidden="true"/>
            <Button parentKey="HonorFrame" inherits="SmallItemButtonTemplate" hidden="true"/>
			<Button parentKey="ArtifactXPFrame" inherits="SmallItemButtonTemplate" hidden="true">
				<Layers>
					<Layer level="BORDER">
						<Texture parentKey="Overlay" file="Interface\Artifacts\ArtifactPower-QuestBorder">
							<Size x="49" y="49" />
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.Icon" />
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText(ARTIFACT_XP_REWARD, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b, nil, true);
					</OnEnter>

					<OnLeave function="GameTooltip_Hide" />
				</Scripts>
			</Button>
			<Button parentKey="MoneyFrame" inherits="SmallItemButtonTemplate" hidden="true"/>
			<Button parentKey="SkillPointFrame" inherits="SmallItemButtonTemplate">
				<Layers>
					<Layer level="OVERLAY">
						<Texture parentKey="CircleBackground" name="$parentSkillPointBg" file="Interface\QuestFrame\SkillUp-BG">
							<Size x="24" y="24"/>
							<Anchors>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.Icon" x="9" y="-4"/>
							</Anchors>
						</Texture>
						<Texture parentKey="CircleBackgroundGlow" name="$parentSkillPointBgGlow" file="Interface\QuestFrame\SkillUp-Glow" alphaMode="ADD" alpha="0.3">
							<Size x="48" y="48"/>
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.CircleBackground"/>
							</Anchors>
						</Texture>
						<FontString parentKey="ValueText" name="$parentPoints" inherits="GameFontNormal" justifyH="LEFT">
							<Anchors>
								<Anchor point="CENTER" relativeKey="$parent.CircleBackground" relativePoint="CENTER" x="3"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						-- Hacks to get this to display a little more nicely
						self.Name:SetPoint("LEFT", self.NameFrame, 10, 0);
						self.Name:SetWidth(81);
					</OnLoad>
					<OnEnter>
						if (self.tooltip) then
							GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
							GameTooltip:SetText(self.tooltip, HIGHLIGHT_FONT_COLOR.r, HIGHLIGHT_FONT_COLOR.g, HIGHLIGHT_FONT_COLOR.b);
						end
					</OnEnter>
					<OnLeave>
						GameTooltip:Hide();
					</OnLeave>
				</Scripts>
			</Button>
			<Button parentKey="TitleFrame" inherits="SmallItemButtonTemplate"/>
		</Frames>
		<Scripts>
			<OnLoad>
				self.buttonTemplate = "SmallQuestRewardItemButtonTemplate";
				self.spellRewardPool = CreateFramePool("BUTTON", self, "SmallItemButtonTemplate, QuestInfoRewardSpellCodeTemplate");
				self.followerRewardPool = CreateFramePool("BUTTON", self, "SmallQuestInfoRewardFollowerTemplate");
				self.spellHeaderPool = CreateFontStringPool(self, "BACKGROUND", 0, "MapQuestInfoSpellHeaderTemplate");

				self.MoneyFrame.Icon:SetTexture("Interface\\Icons\\inv_misc_coin_01");
				self.MoneyFrame.Name:SetFontObject("GameFontHighlight");
				self.XPFrame.Icon:SetTexture("Interface\\Icons\\XP_Icon");
				self.XPFrame.Name:SetFontObject("NumberFontNormal");
				self.ArtifactXPFrame.Name:SetFontObject("NumberFontNormal");
				self.TitleFrame.Icon:SetTexture("Interface\\Icons\\INV_Misc_Note_02");
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="QuestInfoFrame" hidden="true">
		<Size x="300" y="100"/>
		<Layers>
			<Layer level="BACKGROUND">
				<FontString name="QuestInfoTitleHeader" inherits="QuestTitleFont" justifyH="LEFT">
					<Size x="285" y="0"/>
				</FontString>
				<FontString name="QuestInfoQuestType" inherits="QuestFont" justifyH="LEFT">
					<Size x="285" y="0"/>
				</FontString>
				<FontString name="QuestInfoObjectivesText" inherits="QuestFont" justifyH="LEFT">
					<Size x="285" y="0"/>
				</FontString>
				<FontString name="QuestInfoRewardText" inherits="QuestFont" justifyH="LEFT">
					<Size x="285" y="0"/>
				</FontString>
				<FontString name="QuestInfoRequiredMoneyText" inherits="QuestFontNormalSmall" text="REQUIRED_MONEY" />
				<FontString name="QuestInfoGroupSize" inherits="QuestFont" />
				<FontString name="QuestInfoAnchor" inherits="QuestFont" />
				<FontString name="QuestInfoDescriptionHeader" inherits="QuestTitleFont" justifyH="LEFT" text="QUEST_DESCRIPTION">
					<Size x="285" y="0"/>
				</FontString>
				<FontString name="QuestInfoObjectivesHeader" inherits="QuestTitleFont" text="QUEST_OBJECTIVES"  justifyH="LEFT">
					<Size x="285" y="0"/>
				</FontString>
				<FontString name="QuestInfoDescriptionText" inherits="QuestFont" justifyH="LEFT">
					<Size x="285" y="0"/>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="QuestInfoSpacerFrame">
				<Size x="5" y="5"/>
			</Frame>
			<Frame name="QuestInfoSealFrame">
				<Size x="257" y="78"/>
				<Layers>
					<Layer level="ARTWORK">
						<FontString parentKey="Text" inherits="QuestFont_Huge" mixin="ShrinkUntilTruncateFontStringMixin" justifyH="LEFT">
							<Size x="160" y="44"/>
							<Anchors>
								<Anchor point="LEFT" x="5" y="-12"/>
							</Anchors>
						</FontString>
						<Texture parentKey="Texture">
							<Anchors>
								<Anchor point="TOPLEFT" x="160" y="-6"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						self.Text:SetFontObjectsToTry("QuestFont_Huge", "QuestFont_Large", "Fancy14Font", "Fancy12Font");
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self.material = "Parchment";
				self.rewardsFrame = QuestInfoRewardsFrame;
			</OnLoad>
		</Scripts>
	</Frame>
</Ui>
