<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="QuickJoinToast.lua"/>
	<Frame name="QuickJoinToastTemplate" virtual="true">
		<Size x="301" y="32"/>
		<Anchors>
			<Anchor point="LEFT" relativeKey="$parent" relativePoint="RIGHT" x="-13" y="-1"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Background" alpha="0" alphaMode="BLEND" atlas="quickjoin-toast-background" setAllPoints="true"/>
			</Layer>
			<Layer level="ARTWORK">
				<FontString parentKey="Text" alpha="0" inherits="GameFontNormalSmall2" justifyH="LEFT">
					<Size x="272" y="11"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent" relativePoint="LEFT" x="18" y="2"/>
					</Anchors>
					<Color r="0.8" g="0.8" b="0.8"/>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="Line" hidden="false" alpha="0" alphaMode="BLEND" atlas="quickjoin-toast-lines" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Background" x="0" y="2"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>
	<Button name="QuickJoinToastButton" frameStrata="LOW" frameLevel="4" parent="UIParent" mixin="QuickJoinToastMixin">
		<Size x="32" y="32"/>
		<Anchors>
			<Anchor point="BOTTOM" relativeTo="ChatFrameMenuButton" relativePoint="TOP"/>
		</Anchors>
		<Frames>
			<Frame parentKey="Toast" inherits="QuickJoinToastTemplate" frameLevel="3">
			</Frame>
			<Frame parentKey="Toast2" inherits="QuickJoinToastTemplate" frameLevel="3">
			</Frame>
		</Frames>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="FriendsButton" hidden="false" alpha="1" alphaMode="BLEND" atlas="quickjoin-button-friendslist-up" setAllPoints="true"/>
			</Layer>
			<Layer level="BORDER">
				<FontString parentKey="FriendCount" inherits="GameFontHighlightSmall" text="0">
					<Anchors>
						<Anchor point="BOTTOM" relativeKey="$parent" relativePoint="BOTTOM" x="0" y="4"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="QueueButton" hidden="false" alpha="0" alphaMode="BLEND" atlas="quickjoin-button-quickjoin-up" setAllPoints="true"/>
			</Layer>
			<Layer level="OVERLAY">
				<FontString parentKey="QueueCount" alpha="0" inherits="GameFontHighlightSmall" text="0">
					<Anchors>
						<Anchor point="BOTTOM" relativeKey="$parent" relativePoint="BOTTOM" x="0" y="4"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="2">
				<Texture parentKey="FlashingLayer" hidden="false" alpha="0" alphaMode="ADD" atlas="quickjoin-button-quickjoin-up" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.QueueButton"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad method="OnLoad"/>
			<OnClick method="OnClick"/>
			<OnMouseDown method="OnMouseDown"/>
			<OnMouseUp method="OnMouseUp"/>
			<OnEnter method="OnEnter"/>
			<OnEvent method="OnEvent"/>
			<OnLeave method="OnLeave"/>
			<OnShow method="OnShow"/>
			<OnHide method="OnHide"/>
		</Scripts>
		<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
		<Animations>
			<AnimationGroup parentKey="FriendToToastAnim" setToFinalAlpha="true">
				<Scale childKey="Toast.Background" startDelay="0" duration="0.3" order="1" fromScaleX="1.1" fromScaleY="1.1" toScaleX="1" toScaleY="1"/>
				<Scale childKey="Toast.Line" startDelay="0" duration="0.6" order="1" fromScaleX="0" fromScaleY="1" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="FlashingLayer" startDelay="0" duration="0.4" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="QueueButton" startDelay="0" smoothing="OUT" duration="0.4" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="QueueCount" startDelay="0" smoothing="OUT" duration="0.4" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Toast.Text" startDelay="0" smoothing="OUT" duration="0.3" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Toast.Background" startDelay="0" smoothing="OUT" duration="0.3" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Toast.Line" startDelay="0" smoothing="NONE" duration="0.4" order="1" fromAlpha="0" toAlpha="0.5"/>
				<Alpha childKey="Toast.Line" startDelay="0.4" smoothing="NONE" duration="0.4" order="1" fromAlpha="0.5" toAlpha="0"/>
				<Alpha childKey="FlashingLayer" startDelay="0.4" duration="0.7" order="1" fromAlpha="1" toAlpha="0"/>
				<Scripts>
					<OnFinished>
						self:GetParent():FriendToToastFinished();
					</OnFinished>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="ToastActiveAnim" setToFinalAlpha="false">
				<Alpha childKey="FlashingLayer" duration="0.7" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="FlashingLayer" duration="0.7" order="2" fromAlpha="1" toAlpha="0"/>
				<Scripts>
					<OnFinished>
						self:GetParent():ToastPulse();
					</OnFinished>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="ToastToToastAnim" setToFinalAlpha="false">
				<Alpha childKey="Toast.Text" startDelay="0" smoothing="OUT" duration="0.6" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Toast2.Text" startDelay="0" smoothing="OUT" duration="0.3" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Toast.Background" startDelay="0" smoothing="OUT" duration="0.6" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Toast2.Background" startDelay="0" smoothing="OUT" duration="0.3" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Toast2.Line" startDelay="0" smoothing="NONE" duration="0.4" order="1" fromAlpha="0" toAlpha="0.5"/>
				<Scale childKey="Toast.Background" startDelay="0" duration="0.6" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.8" toScaleY="0.8"/>
				<Scale childKey="Toast2.Background" startDelay="0" duration="0.3" order="1" fromScaleX="1.2" fromScaleY="1.2" toScaleX="1" toScaleY="1"/>
				<Scale childKey="Toast2.Line" startDelay="0.2" duration="0.6" order="1" fromScaleX="0" fromScaleY="1" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="FlashingLayer" startDelay="0.3" duration="0.7" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="Toast2.Line" startDelay="0.4" smoothing="NONE" duration="0.4" order="1" fromAlpha="0.5" toAlpha="0"/>
				<Scripts>
					<OnFinished>
						self:GetParent():ToastToToastFinished();
					</OnFinished>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="ToastToFriendAnim" setToFinalAlpha="true">
				<Alpha childKey="QueueButton" startDelay="0" smoothing="IN" duration="0.4" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="QueueCount" startDelay="0" smoothing="IN" duration="0.4" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Toast.Text" startDelay="0" smoothing="OUT" duration="0.6" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha childKey="Toast.Background" startDelay="0" smoothing="OUT" duration="0.6" order="1" fromAlpha="1" toAlpha="0"/>
				<Scale childKey="Toast.Background" startDelay="0" duration="0.6" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.8" toScaleY="0.8"/>
				<Scripts>
					<OnFinished>
						self:GetParent():ToastToFriendFinished();
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
	</Button>
</Ui>
