<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="RaidFinder.lua"/>
	<Button name="RaidFinderRoleButtonTemplate" inherits="LFGRoleButtonWithBackgroundAndRewardTemplate" virtual="true">
		<Scripts>
			<OnLoad>
				self:GetNormalTexture():SetTexCoord(GetTexCoordsForRole(self.role));
				self.background:SetTexCoord(GetBackgroundTexCoordsForRole(self.role));
				self.background:SetAlpha(0.6);
				self.checkButton.onClick = RaidFinderFrameRoleCheckButton_OnClick;
				LFGRoleButtonTemplate_OnLoad(self);
			</OnLoad>
		</Scripts>
	</Button>
	
	<Frame name="RaidFinderFrame" useParentLevel="true" parent="GroupFinderFrame" hidden="true">
		<Size x="338" y="428"/>
		<Anchors>
			<Anchor point="TOPLEFT" x="224" y="0"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<!--Texture name="$parentRoleBackground" file="Interface\LFGFrame\UI-LFG-BlueBG">
					<Size x="512" y="128"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="2" y="270"/>
					</Anchors>
				</Texture-->
				<Texture name="$parentRoleBackground">
					<Size x="321" y="64"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="-10" y="-28"/>
					</Anchors>
					<Color r="1" g="1" b="1"/>
					<Gradient orientation="VERTICAL">
						<MinColor r="1" g="0" b="0" a="0.0"/>
						<MaxColor r="1" g="0.5" b="0" a="0.2"/>
					</Gradient>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture name="$parentBtnCornerRight" inherits="UI-Frame-BtnCornerRight" hidden="false">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="1" y="-1"/>
					</Anchors>
				</Texture>
				<Texture name="$parentButtonBottomBorder" inherits="_UI-Frame-BtnBotTile">
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-23" y="2"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentBtnCornerRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="NoRaidsCover" enableMouse="true" hidden="true" frameStrata="HIGH" toplevel="true">
				<Anchors>
					<Anchor point="TOPRIGHT" x="-6" y="-25"/>
					<Anchor point="BOTTOMLEFT" x="3" y="4"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture>
							<Color r="0" g="0" b="0" a="0.8"/>
						</Texture>
					</Layer>
					<Layer level="ARTWORK">
						<FontString parentKey="Label" inherits="GameFontNormalLarge" justifyH="CENTER" justifyV="MIDDLE">
							<Size x="320" y="0"/>
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="$parentRoleInset" useParentLevel="true" inherits="InsetFrameTemplate" parentKey="Inset">
				<Anchors>
					<Anchor point="TOPLEFT" x="3" y="-26" />
					<Anchor point="BOTTOMRIGHT" relativeTo="$parent" relativePoint="TOPRIGHT" x="-6" y="-109" />
				</Anchors>
			</Frame>
			<Frame name="$parentBottomInset" useParentLevel="true" inherits="InsetFrameTemplate">
				<Size x="217" y="496"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="2" y="-144" />
					<Anchor point="BOTTOMRIGHT" x="-4" y="26" />
				</Anchors>
			</Frame>
			<Frame name="RaidFinderQueueFrame">
				<Size x="338" y="428"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="0" y="-2"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture name="$parentBackground" file="Interface\LFGFrame\UI-LFG-BACKGROUND-QUESTPAPER">
							<Size>
								<AbsDimension x="512" y="256"/>
							</Size>
							<Anchors>
								<Anchor point="BOTTOMLEFT">
									<Offset>
										<AbsDimension x="6" y="28"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentRoleButtonTank" inherits="RaidFinderRoleButtonTemplate" id="1">
						<KeyValues>
							<KeyValue key="role" value="TANK" type="string"/>
						</KeyValues>
						<Anchors>
							<Anchor point="BOTTOMLEFT">
								<Offset>
									<AbsDimension x="50" y="336"/>
								</Offset>
							</Anchor>
						</Anchors>
					</Button>
					<Button name="$parentRoleButtonHealer" inherits="RaidFinderRoleButtonTemplate" id="2">
						<KeyValues>
							<KeyValue key="role" value="HEALER" type="string"/>
						</KeyValues>
						<Anchors>
							<Anchor point="LEFT" relativeTo="$parentRoleButtonTank" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="25" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</Button>
					<Button name="$parentRoleButtonDPS" inherits="RaidFinderRoleButtonTemplate" id="3">
						<KeyValues>
							<KeyValue key="role" value="DAMAGER" type="string"/>
						</KeyValues>
						<Anchors>
							<Anchor point="LEFT" relativeTo="$parentRoleButtonHealer" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="23" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
					</Button>
					<Button name="$parentRoleButtonLeader" inherits="LFGRoleButtonTemplate" id="4">
						<Anchors>
							<Anchor point="LEFT" relativeTo="$parentRoleButtonDPS" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="32" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								self:GetNormalTexture():SetTexCoord(GetTexCoordsForRole("GUIDE"));
								self.checkButton.onClick = RaidFinderFrameRoleCheckButton_OnClick;
							</OnLoad>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								GameTooltip:SetText(GUIDE_TOOLTIP, nil, nil, nil, nil, true);
								LFGFrameRoleCheckButton_OnEnter(self);
							</OnEnter>
						</Scripts>
					</Button>
					<Frame name="$parentSelectionDropDown" inherits="UIDropDownMenuTemplate">
						<Anchors>
							<Anchor point="BOTTOMLEFT">
								<Offset>
									<AbsDimension x="117" y="287"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Layers>
							<Layer level="OVERLAY">
								<FontString name="$parentName" inherits="GameFontNormal" justifyH="RIGHT" text="RAID">
									<Size>
										<AbsDimension x="115" y="24"/>
									</Size>
									<Anchors>
										<Anchor point="RIGHT" relativePoint="LEFT">
											<Offset>
												<AbsDimension x="0" y="2"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnShow function="RaidFinderQueueFrameSelectionDropDown_SetUp"/>
						</Scripts>
					</Frame>
					<ScrollFrame name="$parentScrollFrame" inherits="UIPanelScrollFrameTemplate">
						<Size>
							<AbsDimension x="303" y="253"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMRIGHT">
								<Offset>
									<AbsDimension x="-29" y="28"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentScrollBackground">
									<Anchors>
										<Anchor point="TOPLEFT" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="-4" y="0"/>
											</Offset>
										</Anchor>
										<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT">
											<Offset>
												<AbsDimension x="24" y="-2"/>
											</Offset>
										</Anchor>
									</Anchors>
									<Color r="0" b="0" g="0" a=".65"/>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<Texture name="$parentScrollBackgroundTopLeft" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
									<Size>
										<AbsDimension x="31" y="256"/>
									</Size>
									<Anchors>
										<Anchor point="TOPLEFT" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="-5" y="5"/>
											</Offset>
										</Anchor>
									</Anchors>
									<TexCoords left="0" right="0.484375" top="0" bottom="1.0"/>
								</Texture>
								<Texture name="$parentScrollBackgroundBottomRight" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
									<Size>
										<AbsDimension x="31" y="106"/>
									</Size>
									<Anchors>
										<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT">
											<Offset>
												<AbsDimension x="-5" y="-4"/>
											</Offset>
										</Anchor>
									</Anchors>
									<TexCoords left="0.515625" right="1.0" top="0" bottom="0.4140625"/>
								</Texture>
							</Layer>
						</Layers>
						<Scripts>
							<OnLoad>
								local myName = self:GetName();
								_G[myName.."ScrollBackground"]:SetParent(_G[myName.."ScrollBar"]);
								_G[myName.."ScrollBackgroundTopLeft"]:SetParent(_G[myName.."ScrollBar"]);
								_G[myName.."ScrollBackgroundBottomRight"]:SetParent(_G[myName.."ScrollBar"]);
								
								local scrollBar = _G[myName.."ScrollBar"];
								scrollBar:ClearAllPoints();
								scrollBar:SetPoint("BOTTOMLEFT", self, "BOTTOMRIGHT", 3, 14);
								scrollBar:SetPoint("TOPLEFT", self, "TOPRIGHT", 3, -16);
								
								self.scrollBarHideable = true;
								ScrollFrame_OnLoad(self);
							</OnLoad>
						</Scripts>
						<ScrollChild>
							<Frame name="$parentChildFrame" inherits="LFGRewardFrameTemplate">
								<Scripts>
									<OnShow>
										RaidFinderQueueFrameRewards_UpdateFrame();
									</OnShow>
								</Scripts>
							</Frame>
						</ScrollChild>
					</ScrollFrame>
					<Frame name="$parentPartyBackfill" inherits="LFGBackfillCoverTemplate">
						<Size>
							<AbsDimension x="330" y="257"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMRIGHT">
								<Offset>
									<AbsDimension x="-6" y="27"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								LFGBackfillCover_SetUp(self, {LFG_SUBTYPEID_RAID}, LE_LFG_CATEGORY_RF, RaidFinderFrameFindRaidButton_Update);
							</OnLoad>
						</Scripts>
					</Frame>
					<Frame name="$parentCooldownFrame" parentKey="CooldownFrame" inherits="LFGCooldownCoverTemplate">
						<Size>
							<AbsDimension x="330" y="257"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMRIGHT">
								<Offset>
									<AbsDimension x="-6" y="27"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								LFGCooldownCover_SetUp(self, self:GetParent().PartyBackfill);
								LFGCooldownCover_ChangeSettings(self, false, false);
							</OnLoad>
							<OnShow>
								RaidFinderFrameFindRaidButton_Update();
							</OnShow>
							<OnHide>
								RaidFinderFrameFindRaidButton_Update();
							</OnHide>
						</Scripts>
					</Frame>
					<Frame name="$parentIneligibleFrame" hidden="true" enableMouse="true">
						<Size>
							<AbsDimension x="330" y="257"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMRIGHT">
								<Offset>
									<AbsDimension x="-6" y="27"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentBlackFilter" setAllPoints="true">
									<Color r="0" b="0" g="0" a="0.93"/>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<FontString name="$parentDescription" inherits="GameFontNormal" text="" justifyH="CENTER" parentKey="description">
									<Size>
										<AbsDimension x="300" y="0"/>
									</Size>
									<Anchors>
										<Anchor point="TOP">
											<Offset>
												<AbsDimension x="-0" y="-70"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Button name="$parentLeaveQueueButton" inherits="UIPanelButtonTemplate" parentKey="leaveQueueButton">
								<Size>
									<AbsDimension x="153" y="22"/>
								</Size>
								<Anchors>
									<Anchor point="TOP" relativeTo="$parentDescription" relativePoint="BOTTOM">
										<Offset>
											<AbsDimension x="0" y="-10"/>
										</Offset>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnClick>
										LeaveLFG();
									</OnClick>
								</Scripts>
							</Button>
						</Frames>
						<Scripts>
							<OnLoad>
								self:SetFrameLevel(RaidFinderQueueFrameCooldownFrame:GetFrameLevel() + 2);
							</OnLoad>
						</Scripts>
					</Frame>
				</Frames>
			</Frame>
			<Button name="$parentFindRaidButton" inherits="MagicButtonTemplate" text="FIND_RAID" motionScriptsWhileDisabled="true">
				<Size x="135" y="22"/>
				<Anchors>
					<Anchor point="BOTTOM" x="0" y="4"/>
				</Anchors>
				<Scripts>
					<OnClick>
						local mode, subMode = GetLFGMode(LE_LFG_CATEGORY_RF, RaidFinderQueueFrame.raid);
						if ( mode == "queued" or mode == "listed" or mode == "rolecheck" or mode == "suspended" ) then
							LeaveSingleLFG(LE_LFG_CATEGORY_RF, RaidFinderQueueFrame.raid);
						else
							RaidFinderQueueFrame_Join();
						end
					</OnClick>
					<OnEnter>
						if ( self.tooltip ) then
							GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
							GameTooltip:SetText(self.tooltip, NORMAL_FONT_COLOR.r, NORMAL_FONT_COLOR.g, NORMAL_FONT_COLOR.b, 1, true);
							GameTooltip:Show();
						end
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad function="RaidFinderFrame_OnLoad"/>
			<OnEvent function="RaidFinderFrame_OnEvent"/>
			<OnShow function="RaidFinderFrame_OnShow"/>
		</Scripts>
	</Frame>
</Ui>
