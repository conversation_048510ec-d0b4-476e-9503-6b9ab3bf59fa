<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="RaidFrame.lua"/>
	<Frame name="RaidInfoHeaderTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentLeft" file="Interface\FriendsFrame\WhoFrame-ColumnTabs">
					<Size x="5" y="24"/>
					<Anchors>
						<Anchor point="TOPLEFT"/>
						<Anchor point="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.078125" top="0" bottom="0.75"/>
				</Texture>
				<Texture name="$parentRight" file="Interface\FriendsFrame\WhoFrame-ColumnTabs">
					<Size x="4" y="24"/>
					<Anchors>
						<Anchor point="TOPRIGHT"/>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
					<TexCoords left="0.90625" right="0.96875" top="0" bottom="0.75"/>
				</Texture>
				<Texture name="$parentMiddle" file="Interface\FriendsFrame\WhoFrame-ColumnTabs">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0.078125" right="0.90625" top="0" bottom="0.75"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString inherits="GameFontHighlightSmall" justifyH="CENTER" text="TEST" parentKey="text">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Frame>
	<Button name="RaidInfoInstanceTemplate" virtual="true">
		<Size x="300" y="30"/>
		<Layers>
			<Layer level="BACKGROUND">
				<FontString name="$parentName" inherits="GameFontNormal" justifyH="LEFT" parentKey="name">
					<Size x="150" y="1"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="5" y="-10"/>
					</Anchors>
				</FontString>
				<FontString name="$parentDifficulty" inherits="GameFontHighlightSmall" justifyH="LEFT" parentKey="difficulty">
					<Size x="160" y="1"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentName" relativePoint="BOTTOMLEFT" x="10" y="-10"/>
						<Anchor point="TOPRIGHT" relativeTo="$parentName" relativePoint="BOTTOMRIGHT" x="0" y="-10"/>
					</Anchors>
				</FontString>
				<FontString name="$parentReset" inherits="GameFontHighlightSmall" justifyH="RIGHT" parentKey="reset">
					<Size x="150" y="1"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="0" y="-11"/>
					</Anchors>
				</FontString>
				<FontString name="$parentExtended" inherits="GameFontHighlightSmall" text="EXTENDED" justifyH="RIGHT" parentKey="extended">
					<Size x="150" y="1"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentReset" relativePoint="BOTTOMLEFT" x="0" y="-10"/>
						<Anchor point="TOPRIGHT" relativeTo="$parentReset" relativePoint="BOTTOMRIGHT" x="0" y="-10"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnMouseDown>
				RaidInfoInstance_OnMouseDown(self);
			</OnMouseDown>
			<OnMouseUp>
				RaidInfoInstance_OnMouseUp(self);
			</OnMouseUp>
			<OnClick>
				RaidInfoInstance_OnClick(self);
			</OnClick>
			<OnEnter>
				RaidInfoInstance_OnEnter(self);
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
		<HighlightTexture file="Interface\QuestFrame\UI-QuestTitleHighlight" alphaMode="ADD">
			<Anchors>
				<Anchor point="TOPLEFT" x="-10" y="-2"/>
				<Anchor point="BOTTOMRIGHT" x="10" y="-2"/>
			</Anchors>
		</HighlightTexture>
	</Button>
	<Frame name="RaidParentFrame"  toplevel="true" parent="UIParent" movable="true" enableMouse="true" hidden="true" inherits="ButtonFrameTemplate">
		<Frames>
			<Button name="$parentTab1" inherits="CharacterFrameTabButtonTemplate" text="RAID" id="1">
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativeTo="$parent" relativePoint="BOTTOMLEFT">
						<Offset x="19" y="-30"/>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_CHARACTER_INFO_TAB);
						RaidParentFrame_SetView(1);
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentTab2" inherits="CharacterFrameTabButtonTemplate" text="LOOKING_FOR_RAID" id="2">
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentTab1" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="-16" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_CHARACTER_INFO_TAB);
						RaidParentFrame_SetView(2);
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad function="RaidParentFrame_OnLoad"/>
			<OnShow>
				UpdateMicroButtons();
				PlaySound(SOUNDKIT.IG_CHARACTER_INFO_OPEN);
				RequestLFDPlayerLockInfo(); --This info coming back might affect the tab state.
				RequestLFDPartyLockInfo();
				RaidFinderFrame_UpdateTab();
			</OnShow>
			<OnHide>
				UpdateMicroButtons();
				PlaySound(SOUNDKIT.IG_CHARACTER_INFO_CLOSE);
			</OnHide>
		</Scripts>
	</Frame>
	<Frame name="RaidFrame"><!-- Parent set dynamically.   See ClaimRaidFrame()  -->
		<Frames>
			<CheckButton name="RaidFrameAllAssistCheckButton" parent="RaidFrame" motionScriptsWhileDisabled="true" inherits="UICheckButtonTemplate">
				<Size x="24" y="24"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="58" y="-23"/>
				</Anchors>
				<HitRectInsets>
					<AbsInset left="0" right="-30" top="0" bottom="0"/>
				</HitRectInsets>
				<Scripts>
					<OnLoad>
						_G[self:GetName().."Text"]:SetText(ALL_ASSIST_LABEL);
						self:RegisterEvent("GROUP_ROSTER_UPDATE");
						self:RegisterEvent("PARTY_LEADER_CHANGED");
						RaidFrameAllAssistCheckButton_UpdateAvailable(self);
					</OnLoad>
					<OnEvent>
						RaidFrameAllAssistCheckButton_UpdateAvailable(self);
					</OnEvent>
					<OnClick>
						if (self.enabled) then 
							PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						else
							PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_OFF);
						end
						SetEveryoneIsAssistant(self:GetChecked());	
					</OnClick>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText(ALL_ASSIST_DESCRIPTION, nil, nil, nil, nil, true);
						if ( not self:IsEnabled() ) then
							GameTooltip:AddLine(ALL_ASSIST_NOT_LEADER_ERROR, RED_FONT_COLOR.r, RED_FONT_COLOR.g, RED_FONT_COLOR.b, true);
						end
						GameTooltip:Show();
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</CheckButton>
			<Frame name="RaidFrameNotInRaid" setAllPoints="true">
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="RaidFrameRaidDescription" inherits="GameFontNormal" text="RAID_DESCRIPTION" justifyH="LEFT">
							<Size x="300" y="0"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="15" y="-73"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
			<Button name="RaidFrameConvertToRaidButton" inherits="UIPanelButtonTemplate" text="CONVERT_TO_RAID">
				<Size x="115" y="21"/>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" x="-7" y="5"/>
				</Anchors>
				<Scripts>
					<OnClick function="ConvertToRaid"/>
				</Scripts>
				<NormalFont style="GameFontNormalSmall"/>
				<HighlightFont style="GameFontHighlightSmall"/>
				<DisabledFont style="GameFontDisableSmall"/>
			</Button>
			<!--Button name="RaidFrameRaidBrowserButton" inherits="UIPanelButtonTemplate" text="LOOKING_FOR_RAID" >
				<Size x="100" y="21"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="-100" y="-24"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						ToggleRaidBrowser();
					</OnClick>
				</Scripts>
				<NormalFont style="GameFontNormalSmall"/>
				<HighlightFont style="GameFontHighlightSmall"/>
				<DisabledFont style="GameFontDisableSmall"/>
			</Button-->
			<Button name="RaidFrameRaidInfoButton" inherits="UIPanelButtonTemplate" text="RAID_INFO" >
				<Size x="90" y="21"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="-7" y="-24"/>
				</Anchors>
				<Scripts>
					<OnClick>
						if ( RaidInfoFrame:IsShown() ) then
							RaidInfoFrame:Hide();
						else
							RaidInfoFrame:Show();
						end
					</OnClick>
				</Scripts>
				<NormalFont style="GameFontNormalSmall"/>
				<HighlightFont style="GameFontHighlightSmall"/>
				<DisabledFont style="GameFontDisableSmall"/>
			</Button>
			<Frame name="RaidInfoFrame" enableMouse="true" hidden="true">
				<Size x="345" y="250"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="0" y="-28"/>
				</Anchors>
				<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background-Dark" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
					<BackgroundInsets>
						<AbsInset left="11" right="12" top="12" bottom="11"/>
					</BackgroundInsets>
					<TileSize>
						<AbsValue val="32"/>
					</TileSize>
					<EdgeSize>
						<AbsValue val="32"/>
					</EdgeSize>
				</Backdrop>
				<Layers>
					<Layer level="ARTWORK">
						<Texture name="RaidInfoDetailHeader" file="Interface\RaidFrame\UI-RaidInfo-Header">
							<Size x="325" y="47"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="10" y="-9"/>
							</Anchors>
							<TexCoords left="0.18164" right="0.81640" top="0.578125" bottom="0.94531"/>
						</Texture>
						<Texture name="RaidInfoDetailFooter" file="Interface\RaidFrame\UI-RaidInfo-Header">
							<Size x="326" y="29"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" x="9" y="9"/>
							</Anchors>
							<TexCoords left="0.18164" right="0.81640" top="0.08593" bottom="0.3125"/>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture name="RaidInfoDetailCorner" file="Interface\DialogFrame\UI-DialogBox-Corner">
							<Size x="32" y="32"/>
							<Anchors>
								<Anchor point="TOPRIGHT" x="-6" y="-7"/>
							</Anchors>
						</Texture>
						<Texture name="$parentHeader" file="Interface\DialogFrame\UI-DialogBox-Header">
							<Size x="256" y="64"/>
							<Anchors>
								<Anchor point="TOP" x="0" y="12"/>
							</Anchors>
						</Texture>
						<FontString name="$parentHeaderText" inherits="GameFontNormal" text="RAID_INFORMATION">
							<Size x="185" y="12"/>
							<Anchors>
								<Anchor point="TOP" relativeTo="$parentHeader" x="0" y="-14"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Frame name="RaidInfoInstanceLabel" inherits="RaidInfoHeaderTemplate">
						<Size x="173" y="22"/>
						<Anchors>
							<Anchor point="TOPLEFT" relativePoint="TOPLEFT" x="13" y="-31"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								self.text:SetText(INSTANCE);
							</OnLoad>
						</Scripts>
					</Frame>
					<Frame name="RaidInfoIDLabel" inherits="RaidInfoHeaderTemplate">
						<Size x="150" y="22"/>
						<Anchors>
							<Anchor point="LEFT" relativeTo="RaidInfoInstanceLabel" relativePoint="RIGHT" x="-1" y="0"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								self.text:SetText(LOCK_EXPIRE);
							</OnLoad>
						</Scripts>
					</Frame>
					<Button name="RaidInfoCloseButton" inherits="UIPanelCloseButton">
						<Anchors>
							<Anchor point="TOPRIGHT" x="-2" y="-3"/>
						</Anchors>
					</Button>
					<ScrollFrame name="RaidInfoScrollFrame" inherits="HybridScrollFrameTemplate">
						<Size x="305" y="157"/>
						<Anchors>
							<Anchor point="TOPLEFT" x="16" y="-58"/>
						</Anchors>
						<Frames>
							<Slider name="$parentScrollBar" inherits="HybridScrollBarTemplate" parentKey="scrollBar">
								<Anchors>
									<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="0" y="-13"/>
									<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="0" y="14"/>
								</Anchors>
								<Scripts>
									<OnShow>
										local parent = self:GetParent();
										parent:SetWidth(295);
										RaidInfoInstanceLabel:SetWidth(143);
										for _, frame in pairs(parent.buttons) do
											frame:SetWidth(275);
											frame.name:SetWidth(120);
										end
									</OnShow>
									<OnHide>
										local parent = self:GetParent();
										parent:SetWidth(310);
										RaidInfoInstanceLabel:SetWidth(173);
										for _, frame in pairs(parent.buttons) do
											frame:SetWidth(305);
											frame.name:SetWidth(150);
										end
									</OnHide>
								</Scripts>
							</Slider>
						</Frames>
						<Scripts>
							<OnLoad function="RaidInfoScrollFrame_OnLoad"/>
							<OnShow>
								RaidInfoFrame_Update()
							</OnShow>
						</Scripts>
					</ScrollFrame>
					<Button name="RaidInfoExtendButton" inherits="UIPanelButtonTemplate" text="EXTEND_RAID_LOCK">
						<Size x="200" y="20"/>
						<Anchors>
							<Anchor point="BOTTOMLEFT" x="14" y="13"/>
						</Anchors>
						<Scripts>
							<OnClick function="RaidInfoExtendButton_OnClick"/>
						</Scripts>
						<NormalFont style="GameFontNormalSmall"/>
						<HighlightFont style="GameFontHighlightSmall"/>
						<DisabledFont style="GameFontDisableSmall"/>
					</Button>
					<Button name="RaidInfoCancelButton" inherits="UIPanelButtonTemplate" text="CLOSE">
						<Size x="90" y="20"/>
						<Anchors>
							<Anchor point="BOTTOMRIGHT" x="-14" y="13"/>
						</Anchors>
						<Scripts>
							<OnClick>
								RaidInfoFrame:Hide();
							</OnClick>
						</Scripts>
						<NormalFont style="GameFontNormalSmall"/>
						<HighlightFont style="GameFontHighlightSmall"/>
						<DisabledFont style="GameFontDisableSmall"/>
					</Button>
				</Frames>
				<Scripts>
					<OnShow>
						PlaySound(SOUNDKIT.U_CHAT_SCROLL_BUTTON);
					</OnShow>
					<OnHide>
						PlaySound(SOUNDKIT.U_CHAT_SCROLL_BUTTON);
					</OnHide>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="RaidFrame_OnLoad"/>
			<OnShow>
				RaidFrame_OnShow(self);
			</OnShow>
			<OnHide>
				UpdateMicroButtons();
			</OnHide>
			<OnEvent function="RaidFrame_OnEvent"/>
		</Scripts>
		<HitRectInsets>
			<AbsInset left="0" right="100" top="0" bottom="100"/>
		</HitRectInsets>
	</Frame>
</Ui>

