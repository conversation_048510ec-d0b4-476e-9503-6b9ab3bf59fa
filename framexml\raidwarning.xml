<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="RaidWarning.lua"/>
	<Frame name="RaidBossEmoteFrameTemplate" hidden="true" virtual="true">
		<Size>
			<AbsDimension x="512" y="80"/>
		</Size>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentSlot1" inherits="GameFontNormalHuge" justifyH="CENTER" hidden="true" parentKey="slot1">
					<Size x="800" y="0"/>
					<Anchors>
						<Anchor point="TOP"/>
					</Anchors>
				</FontString>
				<FontString name="$parentSlot2" inherits="GameFontNormalHuge" justifyH="CENTER" hidden="true" parentKey="slot2">
					<Size x="800" y="0"/>
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentSlot1" relativePoint="BOTTOM"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad function="RaidBossEmoteFrame_OnLoad"/>
			<OnUpdate function="RaidNotice_OnUpdate"/>
			<OnEvent function="RaidBossEmoteFrame_OnEvent"/>
		</Scripts>
	</Frame>
	<Frame name="RaidWarningFrame" parent="UIParent" frameStrata="HIGH" toplevel="true" hidden="true">
		<Size>
			<AbsDimension x="512" y="70"/>
		</Size>
		<Anchors>
			<Anchor point="TOP" relativeTo="UIErrorsFrame" relativePoint="BOTTOM"/>
		</Anchors>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="RaidWarningFrameSlot1" inherits="GameFontNormalHuge" justifyH="CENTER" hidden="true">
					<Size x="800" y="0"/>
					<Anchors>
						<Anchor point="TOP"/>
					</Anchors>
				</FontString>
				<FontString name="RaidWarningFrameSlot2" inherits="GameFontNormalHuge" justifyH="CENTER" hidden="true">
					<Size x="800" y="0"/>
					<Anchors>
						<Anchor point="TOP" relativeTo="RaidWarningFrameSlot1" relativePoint="BOTTOM"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad function="RaidWarningFrame_OnLoad"/>
			<OnEvent function="RaidWarningFrame_OnEvent"/>
			<OnUpdate function="RaidNotice_OnUpdate"/>
		</Scripts>
	</Frame>

	<Frame name="RaidBossEmoteFrame" inherits="RaidBossEmoteFrameTemplate" parent="UIParent" frameStrata="HIGH" toplevel="true">
		<Anchors>
			<Anchor point="TOP" relativeTo="RaidWarningFrame" relativePoint="BOTTOM"/>
		</Anchors>
	</Frame>

</Ui>