<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="RecruitAFriendFrame.lua"/>

	<Frame name="RecruitInfoDialogTemplate" frameStrata="HIGH" hidden="true" virtual="true">
		<Size x="220" y="100"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentBg" parentKey="BG">
					<Anchors>
						<Anchor point="TOPLEFT" x="1" y="-1"/>
						<Anchor point="BOTTOMRIGHT" x="-1" y="1"/>
					</Anchors>
					<Color r="1" g="1" b="1"/>
					<Gradient orientation="VERTICAL">
						<MinColor r="0.23" g="0.19" b="0"/>
						<MaxColor r="0" g="0" b="0"/>
					</Gradient>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="-1">
				<Texture name="$parentGlowTopLeft" file="Interface\Common\HelpBox" parentKey="GlowTopLeft" alphaMode="ADD">
					<Size x="11" y="11"/>	
					<Anchors>
						<Anchor point="TOPLEFT" x="-6" y="6"/>
					</Anchors>
					<TexCoords left="0.10937500" right="0.19531250" top="0.00781250" bottom="0.09375000"/>	
				</Texture>
				<Texture name="$parentGlowTopRight" file="Interface\Common\HelpBox" parentKey="GlowTopRight" alphaMode="ADD">
					<Size x="11" y="11"/>	
					<Anchors>
						<Anchor point="TOPRIGHT" x="6" y="6"/>
					</Anchors>
					<TexCoords left="0.10937500" right="0.19531250" top="0.00781250" bottom="0.09375000"/>	
				</Texture>
				
				<Texture name="$parentGlowBottomLeft" file="Interface\Common\HelpBox" parentKey="GlowBottomLeft" alphaMode="ADD">
					<Size x="11" y="11"/>	
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-6" y="-6"/>
					</Anchors>
					<TexCoords left="0.10937500" right="0.19531250" top="0.00781250" bottom="0.09375000"/>	
				</Texture>
				
				<Texture name="$parentGlowBottomRight" file="Interface\Common\HelpBox" parentKey="GlowBottomRight" alphaMode="ADD">
					<Size x="11" y="11"/>	
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="6" y="-6"/>
					</Anchors>
					<TexCoords left="0.10937500" right="0.19531250" top="0.00781250" bottom="0.09375000"/>	
				</Texture>

				<Texture name="$parentGlowTop" file="Interface\Common\HelpBox"  parentKey="GlowTop" alphaMode="ADD">
					<Size x="32" y="11"/>	
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentGlowTopLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="TOPRIGHT" relativeTo="$parentGlowTopRight" relativePoint="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.09375000" top="0.00781250" bottom="0.09375000"/>	
				</Texture>
				<Texture name="$parentGlowBottom" file="Interface\Common\HelpBox" parentKey="GlowBottom" alphaMode="ADD">
					<Size x="32" y="11"/>	
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentGlowBottomLeft" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentGlowBottomRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.09375000" top="0.09375000" bottom="0.00781250"/>	
				</Texture>
				<Texture name="$parentGlowLeft" file="Interface\Common\HelpBox" parentKey="GlowLeft" alphaMode="ADD">
					<Size x="11" y="32"/>	
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentGlowTopLeft" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentGlowBottomLeft" relativePoint="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0.21093750" right="0.29687500" top="0.00781250" bottom="0.09375000"/>	
				</Texture>
				<Texture name="$parentGlowRight" file="Interface\Common\HelpBox" parentKey="GlowRight" alphaMode="ADD">
					<Size x="11" y="32"/>	
					<Anchors>
						<Anchor point="TOPRIGHT" relativeTo="$parentGlowTopRight" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentGlowBottomRight" relativePoint="TOPRIGHT"/>
					</Anchors>
					<TexCoords left="0.29687500" right="0.21093750" top="0.00781250" bottom="0.09375000"/>	
				</Texture>	
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="-2">
				<Texture name="$parentShadowTopLeft" file="Interface\Common\HelpBox" parentKey="ShadowTopLeft" >
					<Size x="25" y="25"/>	
					<Anchors>
						<Anchor point="TOPLEFT" x="-16" y="16"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.20312500" top="0.46093750" bottom="0.65625000"/>	
				</Texture>
				<Texture name="$parentShadowTopRight" file="Interface\Common\HelpBox" parentKey="ShadowTopRight" >
					<Size x="25" y="25"/>	
					<Anchors>
						<Anchor point="TOPRIGHT" x="16" y="16"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.20312500" top="0.46093750" bottom="0.65625000"/>	
				</Texture>
				
				<Texture name="$parentShadowBottomLeft" file="Interface\Common\HelpBox" parentKey="ShadowBottomLeft" >
					<Size x="25" y="25"/>	
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-16" y="-16"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.20312500" top="0.46093750" bottom="0.65625000"/>	
				</Texture>
				
				<Texture name="$parentShadowBottomRight" file="Interface\Common\HelpBox" parentKey="ShadowBottomRight" >
					<Size x="25" y="25"/>	
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="16" y="-16"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.20312500" top="0.46093750" bottom="0.65625000"/>	
				</Texture>

				<Texture name="$parentShadowTop" file="Interface\Common\HelpBox" parentKey="ShadowTop" >
					<Size x="32" y="25"/>	
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentShadowTopLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="TOPRIGHT" relativeTo="$parentShadowTopRight" relativePoint="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.13281250" top="0.25000000" bottom="0.44531250"/>	
				</Texture>
				<Texture name="$parentShadowBottom" file="Interface\Common\HelpBox" parentKey="ShadowBottom" >
					<Size x="32" y="25"/>	
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentShadowBottomLeft" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentShadowBottomRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.13281250" top="0.44531250" bottom="0.25000000"/>	
				</Texture>
				<Texture name="$parentShadowLeft" file="Interface\Common\HelpBox" parentKey="ShadowLeft" >
					<Size x="25" y="32"/>	
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentShadowTopLeft" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentShadowBottomLeft" relativePoint="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.20312500" top="0.10937500" bottom="0.23437500"/>	
				</Texture>
				<Texture name="$parentShadowRight" file="Interface\Common\HelpBox" parentKey="ShadowRight" >
					<Size x="25" y="32"/>	
					<Anchors>
						<Anchor point="TOPRIGHT" relativeTo="$parentShadowTopRight" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentShadowBottomRight" relativePoint="TOPRIGHT"/>
					</Anchors>
					<TexCoords left="0.20312500" right="0.00781250" top="0.10937500" bottom="0.23437500"/>	
				</Texture>	
			</Layer>
			<Layer level="OVERLAY">
				<FontString parentKey="Text" inherits="GameFontHighlightLeft">
					<Size x="200" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="15" y="-15"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="-3">
				<Texture parentKey="ArrowShadow" file="Interface\Common\HelpBox" hidden="false">
					<Size x="86" y="35"/>	
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT" x="3" y="0"/>
					</Anchors>
					<TexCoords left="0.21875000" right="0.89062500" top="0.52343750" bottom="0.79687500"/>	
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Arrow" file="Interface\Common\HelpBox" hidden="false">
					<Size x="53" y="21"/>	
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT" x="3" y="0"/>
					</Anchors>
					<TexCoords left="0.21875000" right="0.63281250" top="0.10937500" bottom="0.27343750"/>	
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="ArrowGlow" file="Interface\Common\HelpBox" hidden="false" alphaMode="ADD" alpha="0.5">
					<Size x="65" y="28"/>
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT" x="3" y="0"/>
					</Anchors>
					<TexCoords left="0.21875000" right="0.72656250" top="0.28906250" bottom="0.50781250"/>	
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="OkayButton" inherits="UIPanelButtonTemplate" text="OKAY">
				<Size x="150" y="22"/>
				<Anchors>
					<Anchor point="BOTTOM" relativeKey="$parent" relativePoint="BOTTOM" x="0" y="15"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						self:GetParent():Hide();
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self.Text:SetSpacing(4);
				SetClampedTextureRotation(self.GlowTopRight, 90);
				SetClampedTextureRotation(self.GlowBottomRight, 180);
				SetClampedTextureRotation(self.GlowBottomLeft, 270);
				SetClampedTextureRotation(self.ShadowTopRight, 90);
				SetClampedTextureRotation(self.ShadowBottomRight, 180);
				SetClampedTextureRotation(self.ShadowBottomLeft, 270);

				SetClampedTextureRotation(self.ArrowShadow, 90);
				SetClampedTextureRotation(self.Arrow, 90);
				SetClampedTextureRotation(self.ArrowGlow, 90);
			</OnLoad>
			<OnShow>
				local height = self.Text:GetHeight()+30;
				if ( self.OkayButton:IsShown() ) then
					height = height + 40;
				end
				height = max(height, 55);
				self:SetHeight(height);
			</OnShow>
		</Scripts>
	</Frame>
	<Frame name="RecruitAFriendFrame" parent="UIParent" hidden="true" enableMouse="true" frameStrata="DIALOG">
		<Size x="485" y="366"/>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString inherits="GameFontNormalHuge" text="RECRUIT_A_FRIEND">
					<Anchors>
						<Anchor point="TOP" x="0" y="-23"/>
					</Anchors>
				</FontString>
				<FontString inherits="GameFontNormal" text="RAF_DESCRIPTION" justifyV="MIDDLE">
					<Size x="400" y="40"/>
					<Anchors>
						<Anchor point="TOP" x="0" y="-51"/>
					</Anchors>
					<Color r="0.8" g="0.8" b="0.8"/>
				</FontString>
				<FontString parentKey="EmailLabel" inherits="GameFontNormal" text="RAF_ENTER_EMAIL">
					<Anchors>
						<Anchor point="TOPLEFT" x="24" y="-99"/>
					</Anchors>
				</FontString>
				<FontString parentKey="NoteLabel" inherits="GameFontNormal" text="RAF_ENTER_NOTE">
					<Anchors>
						<Anchor point="TOPLEFT" x="24" y="-156"/>
					</Anchors>
				</FontString>
				<FontString parentKey="BenefitsLabel" inherits="GameFontNormalLarge" text="RAF_BENEFITS">
					<Anchors>
						<Anchor point="TOPLEFT" x="277" y="-107"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Benefit1" inherits="GameFontHighlight" justifyH="LEFT" text="RAF_BENEFIT1">
					<Size x="176" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BenefitsLabel" relativePoint="BOTTOMLEFT" x="12" y="-4"/>
					</Anchors>
					<Color r="0.8" g="0.8" b="0.8"/>
				</FontString>
				<Texture file="Interface\Scenarios\ScenarioIcon-Combat">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.Benefit1" relativePoint="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Benefit2" inherits="GameFontHighlight" justifyH="LEFT" text="RAF_BENEFIT2">
					<Size x="176" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Benefit1" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
					</Anchors>
					<Color r="0.8" g="0.8" b="0.8"/>
				</FontString>
				<Texture file="Interface\Scenarios\ScenarioIcon-Combat">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.Benefit2" relativePoint="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Benefit3" inherits="GameFontHighlight" justifyH="LEFT" text="RAF_BENEFIT3">
					<Size x="176" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Benefit2" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
					</Anchors>
					<Color r="0.8" g="0.8" b="0.8"/>
				</FontString>
				<Texture file="Interface\Scenarios\ScenarioIcon-Combat">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.Benefit3" relativePoint="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Benefit4" inherits="GameFontHighlight" justifyH="LEFT" text="RAF_BENEFIT4">
					<Size x="176" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Benefit3" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
					</Anchors>
					<Color r="0.8" g="0.8" b="0.8"/>
				</FontString>
				<Texture file="Interface\Scenarios\ScenarioIcon-Combat">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.Benefit4" relativePoint="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<FontString parentKey="Benefit5" inherits="GameFontHighlight" justifyH="LEFT" text="RAF_BENEFIT5">
					<Size x="176" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Benefit4" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
					</Anchors>
					<Color r="0.8" g="0.8" b="0.8"/>
				</FontString>
				<Texture file="Interface\Scenarios\ScenarioIcon-Combat">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.Benefit5" relativePoint="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="CharacterInfo" inherits="ThinBorderTemplate">
				<Size x="400" y="50"/>
				<Anchors>
					<Anchor point="BOTTOM" x="0" y="29"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString parentKey="Text" inherits="GameFontNormal" justifyV="MIDDLE" setAllPoints="true">
							<Color r="0.8" g="0.8" b="0.8"/>
						</FontString>
					</Layer>
					<Layer level="BACKGROUND">
						<Texture name="$parentBg" parentKey="BG">
							<Anchors>
								<Anchor point="TOPLEFT" x="1" y="-1"/>
								<Anchor point="BOTTOMRIGHT" x="-1" y="1"/>
							</Anchors>
							<Color r="1" g="1" b="1"/>
							<Gradient orientation="VERTICAL">
								<MinColor r="0.23" g="0.19" b="0"/>
								<MaxColor r="0" g="0" b="0"/>
							</Gradient>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						for i=1, #self.Textures do
							self.Textures[i]:SetVertexColor( 1, 0.82, 0 );
						end
					</OnLoad>
				</Scripts>
			</Frame>
			<Button parentKey="MoreDetails" text="RAF_MORE_DETAILS">
				<Size x="176" y="30"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.Benefit5" relativePoint="BOTTOMLEFT" x="-12" y="-27"/>
				</Anchors>					
				<Scripts>
					<OnClick>
						StaticPopup_Show("CONFIRM_LAUNCH_URL", nil, nil, {index = 12});
					</OnClick>
				</Scripts>
				<ButtonText parentKey="Text" inherits="GameFontNormalLeft" setAllPoints="true">
					<Color r="0.345" g="0.667" b="0.867"/>
				</ButtonText>
			</Button>
			<EditBox name="RecruitAFriendNameEditBox" autoFocus="false" letters="255">
				<Size x="210" y="20"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.EmailLabel" relativePoint="BOTTOMLEFT" x="5" y="-6"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border">
							<Size x="8" y="20"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="-5" y="0"/>
							</Anchors>
							<TexCoords left="0" right="0.0625" top="0" bottom="0.625"/>
						</Texture>
						<Texture name="$parentRight" file="Interface\Common\Common-Input-Border">
							<Size x="8" y="20"/>
							<Anchors>
								<Anchor point="RIGHT" x="0" y="0"/>
							</Anchors>
							<TexCoords left="0.9375" right="1.0" top="0" bottom="0.625"/>
						</Texture>
						<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border">
							<Size x="0" y="20"/>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
								<Anchor point="RIGHT" relativeTo="$parentRight" relativePoint="LEFT"/>
							</Anchors>
							<TexCoords left="0.0625" right="0.9375" top="0" bottom="0.625"/>
						</Texture>
					</Layer>
					<Layer level="BORDER">
						<FontString parentKey="Fill" inherits="FriendsFont_Small" justifyH="LEFT" justifyV="MIDDLE" text="ENTER_EMAIL">
							<Anchors>
								<Anchor point="LEFT" x="3" y="0"/>
							</Anchors>
							<Color r="0.35" g="0.35" b="0.35"/>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnterPressed>
						RecruitAFriendNoteEditBox:SetFocus();
					</OnEnterPressed>
					<OnTabPressed>
						RecruitAFriendNoteEditBox:SetFocus();
					</OnTabPressed>
					<OnEscapePressed>
						self:ClearFocus();
					</OnEscapePressed>
					<OnTextChanged function="RecruitAFriend_OnEmailTextChanged"/>
				</Scripts>
				<FontString inherits="FriendsFont_Small">
					<Color r="1" g="1" b="1" />
				</FontString>
			</EditBox>
			<Frame name="RecruitAFriendNoteFrame" parentKey="NoteFrame">
				<Size x="215" y="60"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.NoteLabel" relativePoint="BOTTOMLEFT" x="0" y="-5"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentTopLeft" file="Interface\Common\Common-Input-Border-TL">
							<Size x="8" y="8"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentTopRight" file="Interface\Common\Common-Input-Border-TR">
							<Size x="8" y="8"/>
							<Anchors>
								<Anchor point="TOPRIGHT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentTop" file="Interface\Common\Common-Input-Border-T">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTopLeft" relativePoint="TOPRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentTopRight" relativePoint="BOTTOMLEFT"/>
							</Anchors>
						</Texture>
						<Texture name="$parentBottomLeft" file="Interface\Common\Common-Input-Border-BL">
							<Size x="8" y="8"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentBottomRight" file="Interface\Common\Common-Input-Border-BR">
							<Size x="8" y="8"/>
							<Anchors>
								<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentBottom" file="Interface\Common\Common-Input-Border-B">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentBottomLeft" relativePoint="TOPRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRight" relativePoint="BOTTOMLEFT"/>
							</Anchors>
						</Texture>
						<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border-L">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTopLeft" relativePoint="BOTTOMLEFT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomLeft" relativePoint="TOPRIGHT"/>
							</Anchors>
						</Texture>
						<Texture name="$parentRight" file="Interface\Common\Common-Input-Border-R">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTopRight" relativePoint="BOTTOMLEFT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRight" relativePoint="TOPRIGHT"/>
							</Anchors>
						</Texture>
						<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border-M">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentRight" relativePoint="BOTTOMLEFT"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<ScrollFrame name="$parentScrollFrame" inherits="UIPanelScrollFrameTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parent" relativePoint="TOPLEFT" x="6" y="-6"/>
							<Anchor point="BOTTOMRIGHT" relativeTo="$parent" relativePoint="BOTTOMRIGHT" x="0" y="6"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								local scrollBar = _G[self:GetName().."ScrollBar"];
								scrollBar:ClearAllPoints();
								scrollBar:SetPoint("TOPLEFT", self, "TOPRIGHT", -18, -10);
								scrollBar:SetPoint("BOTTOMLEFT", self, "BOTTOMRIGHT", -18, 8);
								-- reposition the up and down buttons
								_G[self:GetName().."ScrollBarScrollDownButton"]:SetPoint("TOP", scrollBar, "BOTTOM", 0, 4);
								_G[self:GetName().."ScrollBarScrollUpButton"]:SetPoint("BOTTOM", scrollBar, "TOP", 0, -4);
								-- make the scroll bar hideable and force it to start off hidden so positioning calculations can be done
								-- as soon as it needs to be shown
								self.scrollBarHideable = 1;
								scrollBar:Hide();
							</OnLoad>
							<OnMouseDown>
								RecruitAFriendNoteEditBox:SetFocus();
							</OnMouseDown>
						</Scripts>
						<ScrollChild>
							<EditBox name="RecruitAFriendNoteEditBox" multiLine="true" letters="127" countInvisibleLetters="true" autoFocus="false">
								<Size x="188" y="1"/>
								<Layers>
									<Layer level="BORDER">
										<FontString name="$parentFill" parentKey="Fill" inherits="GameFontNormalSmall" justifyH="LEFT" justifyV="MIDDLE" text="RAF_ENTER_NOTE_PROMPT">
											<Anchors>
												<Anchor point="LEFT" x="3" y="0"/>
											</Anchors>
											<Color r="0.35" g="0.35" b="0.35"/>
										</FontString>
									</Layer>
								</Layers>
								<Scripts>
									<OnLoad>
										self.cursorOffset = 0;
									</OnLoad>
									<OnTabPressed>
										RecruitAFriendNameEditBox:SetFocus()
									</OnTabPressed>
									<OnTextChanged function="RecruitAFriend_OnNoteChanged"/>
									<OnCursorChanged function="ScrollingEdit_OnCursorChanged"/>
									<OnUpdate>
										ScrollingEdit_OnUpdate(self, elapsed, self:GetParent());
									</OnUpdate>
									<OnEscapePressed>
										self:ClearFocus();
									</OnEscapePressed>
								</Scripts>
								<FontString inherits="FriendsFont_Small"/>
							</EditBox>
						</ScrollChild>
					</ScrollFrame>
				</Frames>
			</Frame>		
			<Button name="$parentSendButton" parentKey="SendButton" inherits="UIPanelButtonTemplate" text="RAF_SEND_INVITATION">
				<Size x="219" y="22"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.NoteFrame" relativePoint="BOTTOMLEFT" x="-2" y="-15"/>
				</Anchors>
				<Scripts>
					<OnClick>
						RecruitAFriend_Send()
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentCloseButton" inherits="UIPanelCloseButton">
				<Anchors>
					<Anchor point="TOPRIGHT" x="-4" y="-4"/>
				</Anchors>
				<Scripts>
					<OnClick>
						StaticPopupSpecial_Hide(RecruitAFriendFrame);
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad function="RecruitAFriend_OnLoad"/>
			<OnEvent function="RecruitAFriend_OnEvent"/>
			<OnShow function="RecruitAFriend_OnShow"/>
			<OnHide>
				PlaySound(SOUNDKIT.IG_MAINMENU_CLOSE);
			</OnHide>
		</Scripts>
	</Frame>
	<Frame name="RecruitAFriendSentFrame" parent="UIParent" hidden="true" enableMouse="true" frameStrata="DIALOG">
		<Size x="385" y="256"/>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString parentKey="Title" inherits="GameFontNormalHuge" text="RECRUIT_A_FRIEND">
					<Anchors>
						<Anchor point="TOP" x="0" y="-23"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Description" inherits="GameFontHighlight" text="RAF_INVITATION_SENT" justifyH="CENTER" justifyV="MIDDLE">
					<Size x="300" y="200"/>
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent" relativePoint="CENTER" x="0" y="15"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="$parentCloseButton" inherits="UIPanelCloseButton">
				<Anchors>
					<Anchor point="TOPRIGHT" x="-4" y="-4"/>
				</Anchors>
				<Scripts>
					<OnClick>
						StaticPopupSpecial_Hide(RecruitAFriendSentFrame);
					</OnClick>
				</Scripts>
			</Button>
			<Button parentKey="MoreDetails" text="RAF_FAQ">
				<Size x="176" y="30"/>
				<Anchors>
					<Anchor point="BOTTOM" relativeKey="$parent" relativePoint="BOTTOM" x="0" y="10"/>
				</Anchors>
				<ButtonText parentKey="Text" inherits="GameFontNormal" justifyH="CENTER" justifyV="MIDDLE" setAllPoints="true">
					<Color r="0.345" g="0.667" b="0.867"/>
				</ButtonText>
				<Scripts>
					<OnClick>
						StaticPopup_Show("CONFIRM_LAUNCH_URL", nil, nil, {index = 12});
					</OnClick>
				</Scripts>
			</Button>
			<Button parentKey="OKButton" inherits="UIPanelButtonTemplate" text="OKAY">
				<Size x="170" y="22"/>
				<Anchors>
					<Anchor point="BOTTOM" relativeKey="$parent.MoreDetails" relativePoint="TOP" x="0" y="10"/>
				</Anchors>
				<Scripts>
					<OnClick>
						StaticPopupSpecial_Hide(RecruitAFriendSentFrame);
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnHide>
				PlaySound(SOUNDKIT.IG_CHARACTER_INFO_CLOSE);
			</OnHide>
		</Scripts>
	</Frame>
</Ui>
