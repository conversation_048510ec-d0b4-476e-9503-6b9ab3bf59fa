<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="ReputationFrame.lua"/>

	<Frame name="ReputationParagonFrameTemplate" frameStrata="HIGH" enableMouse="true" virtual="true">
		<Size x="20" y="20"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Glow" atlas="ParagonReputation_Glow" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="-1" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Icon" atlas="ParagonReputation_Bag" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="Check" atlas="ParagonReputation_Checkmark" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER" x="5" y="-2"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="HIGHLIGHT">
				<Texture parentKey="Highlight" atlas="ParagonReputation_Bag" useAtlasSize="true" alphaMode="ADD">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnEnter function="ReputationParagonFrame_OnEnter"/>
			<OnLeave function="ReputationParagonFrame_OnLeave"/>
			<OnUpdate function="ReputationParagonFrame_OnUpdate"/>
		</Scripts>
	</Frame>

	<Button name="ReputationBarTemplate" virtual="true">
		<Size>
			<AbsDimension x="295" y="20"/>
		</Size>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="$parentBackground" file="Interface\PaperDollInfoFrame\UI-Character-ReputationBar">
					<Size>
						<AbsDimension x="0" y="21"/>
					</Size>
					<Anchors>
						<Anchor point="LEFT">
							<Offset x="0" y="0"/>
						</Anchor>
					</Anchors>
					<TexCoords left="0.0" right="0.7578125" top="0.0" bottom="0.328125"/>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<FontString name="$parentFactionName" inherits="GameFontHighlightSmall" justifyH="LEFT">
					<Size x="135" y="10"/> <!--This is resized in Lua-->
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<CheckButton parentKey="LFGBonusRepButton" motionScriptsWhileDisabled="true">
				<Size x="16" y="16"/>
				<Anchors>
					<Anchor point="RIGHT" relativeTo="$parentBackground" relativePoint="LEFT" x="-2" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="OVERLAY">
						<Texture parentKey="Glow" file="Interface\Common\ReputationStar-Glow" alphaMode="ADD">
							<Size x="32" y="32"/>
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<NormalTexture file="Interface\Common\ReputationStar">
					<TexCoords left="0.5" right="1" top="0" bottom="0.5"/>
				</NormalTexture>
				<HighlightTexture file="Interface\Common\ReputationStar">
					<TexCoords left="0" right="0.5" top="0.5" bottom="1"/>
				</HighlightTexture>
				<CheckedTexture file="Interface\Common\ReputationStar">
					<TexCoords left="0" right="0.5" top="0" bottom="0.5"/>
				</CheckedTexture>
				<Animations>
					<AnimationGroup parentKey="GlowAnim" looping="BOUNCE">
						<Alpha childKey="Glow" fromAlpha="0.75" toAlpha="0" duration="1.0" smoothing="IN_OUT" order="1"/>
						<Scripts>
							<OnPlay>
								self.loopsRemaining = 5;
							</OnPlay>
							<OnLoop>
								self.loopsRemaining = self.loopsRemaining - 1;
								if ( self.loopsRemaining == 0 ) then
									self:Stop();
									self:GetParent().Glow:Hide();
								end
							</OnLoop>
						</Scripts>
					</AnimationGroup>
				</Animations>
				<Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText(LFG_BONUS_REPUTATION_TOOLTIP, nil, nil, nil, nil, true);
						GameTooltip:Show();
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick function="ReputationBarLFGBonusRepButton_OnClick"/>
				</Scripts>
			</CheckButton>
			<Button name="$parentExpandOrCollapseButton">
				<Size>
					<AbsDimension x="13" y="13"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT">
						<Offset>
							<AbsDimension x="3" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<HitRectInsets>
					<AbsInset left="1" right="-4" top="-2" bottom="-2"/>
				</HitRectInsets>
				<Scripts>
					<OnClick>
						if (self:GetParent().isCollapsed) then
							ExpandFactionHeader(self:GetParent().index);
						else
							CollapseFactionHeader(self:GetParent().index);
						end
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-MinusButton-UP">
					<Size>
						<AbsDimension x="16" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="LEFT">
							<Offset>
								<AbsDimension x="3" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</NormalTexture>
				<HighlightTexture name="$parentHighlight" file="Interface\Buttons\UI-PlusButton-Hilight" alphaMode="ADD">
					<Size>
						<AbsDimension x="16" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="LEFT">
							<Offset>
								<AbsDimension x="3" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</HighlightTexture>
			</Button>
			<StatusBar name="$parentReputationBar" drawLayer="BACKGROUND" minValue="0" maxValue="1" defaultValue="1">
				<Size>
					<AbsDimension x="101" y="13"/>
				</Size>
				<Anchors>
					<Anchor point="RIGHT">
						<Offset>
							<AbsDimension x="0" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="OVERLAY">
						<Texture name="$parentAtWarHighlight2" file="Interface\PaperDollInfoFrame\UI-Character-ReputationBar" alphaMode="ADD" hidden="true">
							<Size>
								<AbsDimension x="103" y="17"/>
							</Size>
							<Anchors>
								<Anchor point="TOPRIGHT">
									<Offset x="0" y="0"/>
								</Anchor>
							</Anchors>
							<TexCoords left="0" right="0.40234375" top="0.734375" bottom="1.0"/>
						</Texture>
						<Texture name="$parentAtWarHighlight1" file="Interface\PaperDollInfoFrame\UI-Character-ReputationBar" alphaMode="ADD" hidden="true">
							<Size>
								<AbsDimension x="0" y="17"/>
							</Size>
							<Anchors>
								<Anchor point="RIGHT" relativeTo="$parentAtWarHighlight2" relativePoint="LEFT">
									<Offset x="0" y="0"/>
								</Anchor>
							</Anchors>
							<TexCoords left="0.25390625" right="1" top="0.375" bottom="0.640625"/>
						</Texture>
					</Layer>
					<Layer level="ARTWORK">
						<Texture name="$parentLeftTexture" file="Interface\PaperDollInfoFrame\UI-Character-ReputationBar">
							<Size>
								<AbsDimension x="62" y="21"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT">
									<Offset>
										<AbsDimension x="0" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0.691" right="1.0" top="0.047" bottom="0.281"/>
						</Texture>
						<Texture name="$parentRightTexture" file="Interface\PaperDollInfoFrame\UI-Character-ReputationBar">
							<Size>
								<AbsDimension x="42" y="21"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentLeftTexture" relativePoint="RIGHT">
									<Offset>
										<AbsDimension x="0" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0.0" right="0.164" top="0.3906" bottom="0.625"/>
						</Texture>
						<FontString name="$parentFactionStanding" inherits="GameFontHighlightSmall" text="Revered">
							<Anchors>
								<Anchor point="CENTER">
									<Offset>
										<AbsDimension x="0" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
					<Layer level="OVERLAY">
						<Texture name="$parentHighlight2" file="Interface\PaperDollInfoFrame\UI-Character-ReputationBar-Highlight" alphaMode="ADD" hidden="true">
							<Size>
								<AbsDimension x="17" y="28"/>
							</Size>
							<Anchors>
								<Anchor point="RIGHT">
									<Offset x="4" y="0"/>
								</Anchor>
							</Anchors>
							<TexCoords left="0" right="0.06640625" top="0.4375" bottom="0.875"/>
						</Texture>
						<Texture name="$parentHighlight1" file="Interface\PaperDollInfoFrame\UI-Character-ReputationBar-Highlight" alphaMode="ADD" hidden="true">
							<Size>
								<AbsDimension x="0" y="28"/>
							</Size>
							<Anchors>
								<Anchor point="RIGHT" relativeTo="$parentHighlight2" relativePoint="LEFT"/>
							</Anchors>
							<TexCoords left="0" right="1" top="0" bottom="0.4375"/>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Frame parentKey="BonusIcon" hidden="true">
						<Size x="16" y="16"/>
						<Anchors>
							<Anchor point="CENTER" relativeKey="$parent" relativePoint="LEFT" x="4" y="0"/>
						</Anchors>
						<Layers>
							<Layer level="ARTWORK">
								<Texture parentKey="Texture" file="Interface\Common\ReputationStar" setAllPoints="true">
									<TexCoords left="0.5" right="1" top="0.5" bottom="1"/>
								</Texture>
							</Layer>
						</Layers>
						<Scripts>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								GameTooltip:SetText(BONUS_REPUTATION_TITLE, 1, 1, 1);
								GameTooltip:AddLine(BONUS_REPUTATION_TOOLTIP, nil, nil, nil, true);
								GameTooltip:Show();
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</Frame>
				</Frames>
				<BarTexture file="Interface\PaperDollInfoFrame\UI-Character-Skills-Bar"/>
				<BarColor r=".25" g=".25" b=".75"/>
			</StatusBar>
		</Frames>
		<Scripts>
			<OnLoad>
				_G[self:GetName().."ReputationBarHighlight1"]:SetPoint("TOPLEFT",self,"TOPLEFT",-2, 4);
				_G[self:GetName().."ReputationBarHighlight1"]:SetPoint("BOTTOMRIGHT",self,"BOTTOMRIGHT",-10, -4);
				_G[self:GetName().."ReputationBarAtWarHighlight1"]:SetPoint("TOPLEFT",self,"TOPLEFT",3,-2);
				_G[self:GetName().."ReputationBarAtWarHighlight2"]:SetPoint("TOPRIGHT",self,"TOPRIGHT",-1,-2);
				_G[self:GetName().."ReputationBarAtWarHighlight1"]:SetAlpha(0.2);
				_G[self:GetName().."ReputationBarAtWarHighlight2"]:SetAlpha(0.2);
				_G[self:GetName().."Background"]:SetPoint("TOPRIGHT", self:GetName().."ReputationBarLeftTexture", "TOPLEFT", 0, 0);
			</OnLoad>
			<OnClick>
				ReputationBar_OnClick(self, button, down);
			</OnClick>
			<OnEnter>
				if (self.rolloverText) then
					_G[self:GetName().."ReputationBarFactionStanding"]:SetText(self.rolloverText);
				end
				_G[self:GetName().."ReputationBarHighlight1"]:Show();
				_G[self:GetName().."ReputationBarHighlight2"]:Show();
				if ( self.friendshipID ) then
					ShowFriendshipReputationTooltip(self.friendshipID, self, "ANCHOR_BOTTOMRIGHT");
				elseif (_G[self:GetName().."FactionName"]:IsTruncated()) then
					GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
					GameTooltip:SetText(_G[self:GetName().."FactionName"]:GetText(), nil, nil, nil, nil, true);
					GameTooltip:Show();
				end
			</OnEnter>
			<OnLeave>
				_G[self:GetName().."ReputationBarFactionStanding"]:SetText(self.standingText);
				if ((GetSelectedFaction() ~= self.index) or (not ReputationDetailFrame:IsShown())) then
					_G[self:GetName().."ReputationBarHighlight1"]:Hide();
					_G[self:GetName().."ReputationBarHighlight2"]:Hide();
				end
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
	</Button>
	
	<Frame name="ReputationParagonTooltipStatusBar" inherits="TooltipProgressBarTemplate"/>

	<Frame name="ReputationFrame" setAllPoints="true" enableMouse="true" parent="CharacterFrame" useParentLevel="true" hidden="true" id="2">
		<HitRectInsets>
			<AbsInset left="0" right="30" top="0" bottom="75"/>
		</HitRectInsets>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="ReputationFrameFactionLabel" inherits="GameFontHighlight" text="FACTION">
					<Anchors>
						<Anchor point="TOPLEFT" x="70" y="-42"/>
					</Anchors>
				</FontString>
				<FontString name="ReputationFrameStandingLabel" inherits="GameFontHighlight" text="STANDING">
					<Anchors>
						<Anchor point="TOPLEFT" x="215" y="-42"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="ReputationBar1" inherits="ReputationBarTemplate" id="1">
				<Anchors>
					<Anchor point="TOPRIGHT">
						<Offset>
							<AbsDimension x="-68" y="-63"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar2" inherits="ReputationBarTemplate" id="2">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar1" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar3" inherits="ReputationBarTemplate" id="3">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar2" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar4" inherits="ReputationBarTemplate" id="4">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar3" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar5" inherits="ReputationBarTemplate" id="5">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar4" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar6" inherits="ReputationBarTemplate" id="6">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar5" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar7" inherits="ReputationBarTemplate" id="7">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar6" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar8" inherits="ReputationBarTemplate" id="8">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar7" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar9" inherits="ReputationBarTemplate" id="9">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar8" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar10" inherits="ReputationBarTemplate" id="10">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar9" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar11" inherits="ReputationBarTemplate" id="11">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar10" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar12" inherits="ReputationBarTemplate" id="12">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar11" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar13" inherits="ReputationBarTemplate" id="13">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar12" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar14" inherits="ReputationBarTemplate" id="14">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar13" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="ReputationBar15" inherits="ReputationBarTemplate" id="15">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ReputationBar14" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="0" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			
			<ScrollFrame name="ReputationListScrollFrame" inherits="FauxScrollFrameTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="CharacterFrameInset" x="0" y="-4"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="CharacterFrameInset" x="-27" y="2"/>
				</Anchors>
				<Scripts>
					<OnVerticalScroll>
						FauxScrollFrame_OnVerticalScroll(self, offset, REPUTATIONFRAME_FACTIONHEIGHT, ReputationFrame_Update);
					</OnVerticalScroll>
					<OnShow>
						ReputationBar1:SetPoint("TOPRIGHT", ReputationFrame, "TOPRIGHT", -50, -68);
					</OnShow>
					<OnHide>
						ReputationBar1:SetPoint("TOPRIGHT", ReputationFrame, "TOPRIGHT", -26, -68);
					</OnHide>
				</Scripts>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
							<Size>
								<AbsDimension x="31" y="256"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT" relativePoint="TOPRIGHT">
									<Offset>
										<AbsDimension x="-2" y="5"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0" right="0.484375" top="0" bottom="1.0"/>
						</Texture>
						<Texture file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
							<Size>
								<AbsDimension x="31" y="108"/>
							</Size>
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT">
									<Offset>
										<AbsDimension x="-2" y="-4"/>
									</Offset>
								</Anchor>
							</Anchors>
							<TexCoords left="0.515625" right="1.0" top="0" bottom="0.421875"/>
						</Texture>
					</Layer>
				</Layers>
			</ScrollFrame>
			<Frame name="FactionMouseOver">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="ReputationFrameFactionLabel"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="ReputationFrameFactionLabel"/>
				</Anchors>
				<Scripts>
					<OnEnter>
						GameTooltip_AddNewbieTip(self, FACTION, 1.0, 1.0, 1.0, REPUTATION_FACTION_DESCRIPTION, 1);
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Frame>
			<Frame name="StandingMouseOver">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="ReputationFrameStandingLabel"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="ReputationFrameStandingLabel"/>
				</Anchors>
				<Scripts>
					<OnEnter>
						GameTooltip_AddNewbieTip(self, STANDING, 1.0, 1.0, 1.0, REPUTATION_STANDING_DESCRIPTION, 1);
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Frame>
			<Frame name="ReputationDetailFrame" enableMouse="true" hidden="true">
				<Size>
					<AbsDimension x="212" y="203"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="ReputationFrame" relativePoint="TOPRIGHT">
						<Offset>
							<AbsDimension x="0" y="-28"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
					<BackgroundInsets>
						<AbsInset left="11" right="12" top="12" bottom="11"/>
					</BackgroundInsets>
					<TileSize>
						<AbsValue val="32"/>
					</TileSize>
					<EdgeSize>
						<AbsValue val="32"/>
					</EdgeSize>
				</Backdrop>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="ReputationDetailFactionName" inherits="GameFontNormal" justifyH="LEFT" nonspacewrap="true">
							<Size>
								<AbsDimension x="160" y="0"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="20" y="-21"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
						<FontString name="ReputationDetailFactionDescription" inherits="ReputationDetailFont" justifyH="LEFT">
							<Size>
								<AbsDimension x="170" y="0"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="ReputationDetailFactionName" relativePoint="BOTTOMLEFT">
									<Offset>
										<AbsDimension x="0" y="-2"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
						<Texture file="Interface\PaperDollInfoFrame\UI-Character-Reputation-DetailBackground">
							<Size>
								<AbsDimension x="256" y="128"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="11" y="-11"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="OVERLAY">
						<Texture name="ReputationDetailCorner" file="Interface\DialogFrame\UI-DialogBox-Corner">
							<Size>
								<AbsDimension x="32" y="32"/>
							</Size>
							<Anchors>
								<Anchor point="TOPRIGHT">
									<Offset>
										<AbsDimension x="-6" y="-7"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
						<Texture name="ReputationDetailDivider" file="Interface\DialogFrame\UI-DialogBox-Divider">
							<Size>
								<AbsDimension x="256" y="32"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="9" y="-131"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Button name="ReputationDetailCloseButton" inherits="UIPanelCloseButton">
						<Anchors>
							<Anchor point="TOPRIGHT">
								<Offset>
									<AbsDimension x="-3" y="-3"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnClick>
								PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
								HideParentPanel(self);
							</OnClick>
						</Scripts>
					</Button>
					<CheckButton name="ReputationDetailAtWarCheckBox">
						<Size>
							<AbsDimension x="26" y="26"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT" x="14" y="-143"/>
						</Anchors>
						<Layers>
							<Layer level="ARTWORK">
								<FontString name="$parentText" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="LEFT" relativePoint="RIGHT">
											<Offset>
												<AbsDimension x="-2" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnLoad>
								_G[self:GetName().."Text"]:SetText(AT_WAR);
								_G[self:GetName().."Text"]:SetVertexColor(RED_FONT_COLOR.r, RED_FONT_COLOR.g, RED_FONT_COLOR.b);
							</OnLoad>
							<OnClick>
								FactionToggleAtWar(GetSelectedFaction());
								if ( self:GetChecked() ) then
									PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
								else
									PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_OFF);
								end
								ReputationFrame_Update();
							</OnClick>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								GameTooltip:SetText(REPUTATION_AT_WAR_DESCRIPTION, nil, nil, nil, nil, true);
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
						<NormalTexture file="Interface\Buttons\UI-CheckBox-Up"/>
						<PushedTexture file="Interface\Buttons\UI-CheckBox-Down"/>
						<HighlightTexture file="Interface\Buttons\UI-CheckBox-Highlight" alphaMode="ADD"/>
						<CheckedTexture file="Interface\Buttons\UI-CheckBox-SwordCheck">
							<Size>
								<AbsDimension x="32" y="32"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="3" y="-5"/>
									</Offset>
								</Anchor>
							</Anchors>
						</CheckedTexture>
						<DisabledCheckedTexture file="Interface\Buttons\UI-CheckBox-Check-Disabled"/>
					</CheckButton>
					<CheckButton name="ReputationDetailInactiveCheckBox" inherits="OptionsSmallCheckButtonTemplate">
						<Size>
							<AbsDimension x="26" y="26"/>
						</Size>
						<Anchors>
							<Anchor point="LEFT" relativeTo="ReputationDetailAtWarCheckBoxText" relativePoint="RIGHT">
								<Offset>
									<AbsDimension x="3" y="0"/>
								</Offset>
							</Anchor>
						</Anchors>
						<HitRectInsets>
							<AbsInset left="0" right="0" top="0" bottom="0"/>
						</HitRectInsets>
						<Scripts>
							<OnLoad>
								_G[self:GetName().."Text"]:SetText(MOVE_TO_INACTIVE);
							</OnLoad>
							<OnClick>
								if ( self:GetChecked() ) then
									PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
									SetFactionInactive(GetSelectedFaction());
								else
									PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_OFF);
									SetFactionActive(GetSelectedFaction());
								end
							</OnClick>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								GameTooltip:SetText(REPUTATION_MOVE_TO_INACTIVE, nil, nil, nil, nil, true);
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</CheckButton>
					<CheckButton name="ReputationDetailMainScreenCheckBox" inherits="OptionsSmallCheckButtonTemplate">
						<Size>
							<AbsDimension x="26" y="26"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="ReputationDetailAtWarCheckBox" relativePoint="BOTTOMLEFT">
								<Offset>
									<AbsDimension x="0" y="3"/>
								</Offset>
							</Anchor>
						</Anchors>
						<HitRectInsets>
							<AbsInset left="0" right="0" top="0" bottom="0"/>
						</HitRectInsets>
						<Scripts>
							<OnLoad>
								_G[self:GetName().."Text"]:SetText(SHOW_FACTION_ON_MAINSCREEN);
							</OnLoad>
							<OnClick>
								if ( self:GetChecked() ) then
									PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
									SetWatchedFactionIndex(GetSelectedFaction());
									SetWatchingHonorAsXP(false);
									if (C_ArtifactUI.GetEquippedArtifactInfo()) then
										SetCVar("showArtifactXPBar", false);
									end
								else
									PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_OFF);
									SetWatchedFactionIndex(0);
									SetCVar("showArtifactXPBar", true);
								end
								MainMenuBar_UpdateExperienceBars();
							</OnClick>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								GameTooltip:SetText(REPUTATION_SHOW_AS_XP, nil, nil, nil, nil, true);
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</CheckButton>
					<CheckButton name="ReputationDetailLFGBonusReputationCheckBox" inherits="OptionsSmallCheckButtonTemplate">
						<Size>
							<AbsDimension x="26" y="26"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="ReputationDetailMainScreenCheckBox" relativePoint="BOTTOMLEFT">
								<Offset>
									<AbsDimension x="0" y="3"/>
								</Offset>
							</Anchor>
						</Anchors>
						<HitRectInsets>
							<AbsInset left="0" right="0" top="0" bottom="0"/>
						</HitRectInsets>
						<Scripts>
							<OnLoad>
								_G[self:GetName().."Text"]:SetText(SET_FACTION_LFG_BONUS_REP);
							</OnLoad>
							<OnClick>
								if ( self:GetChecked() ) then
									PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
									ReputationBar_SetLFBonus(self.factionID)
								else
									PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_OFF);
									ReputationBar_SetLFBonus(0)
								end
							</OnClick>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								GameTooltip:SetText(SET_FACTION_LFG_BONUS_REP_TOOLTIP, nil, nil, nil, nil, true);
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</CheckButton>
				</Frames>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="ReputationFrame_OnLoad"/>
			<OnShow function="ReputationFrame_OnShow"/>
			<OnHide function="ReputationFrame_OnHide"/>
			<OnEvent function="ReputationFrame_OnEvent"/>
			<OnMouseWheel function=""/>
		</Scripts>
	</Frame>
	<Frame name="ReputationWatchBar" enableMouse="true" parent="MainMenuBar" hidden="true" inherits="MainMenuBarWatchBarTemplate">
		<Scripts>
			<OnLoad>
				self:RegisterEvent("UPDATE_FACTION");
				self:RegisterEvent("PLAYER_LEVEL_UP");
				self:RegisterEvent("ENABLE_XP_GAIN");
				self:RegisterEvent("DISABLE_XP_GAIN");
				self:RegisterEvent("CVAR_UPDATE");
				self:RegisterEvent("UPDATE_EXPANSION_LEVEL");
			</OnLoad>
			<OnEvent>
				local arg1, arg2 = ...;
				if( event == "UPDATE_FACTION" ) then
					if ( self:IsShown() ) then
						ReputationFrame_Update();
					end
					MainMenuBar_UpdateExperienceBars();
				elseif( event == "PLAYER_LEVEL_UP" or event == "ENABLE_XP_GAIN" or event == "DISABLE_XP_GAIN" or event == "UPDATE_EXPANSION_LEVEL" ) then
					ReputationWatchBar_UpdateMaxLevel();
					
					if event == "PLAYER_LEVEL_UP" then
						MainMenuBar_UpdateExperienceBars(arg1);
					else
						MainMenuBar_UpdateExperienceBars();
					end
					
					UIParent_ManageFramePositions();
				elseif( event == "CVAR_UPDATE" and arg1 == "XP_BAR_TEXT" ) then
					if( arg2 == "1" ) then
						ShowWatchBarText(ReputationWatchBar, "lock");
					else
						HideWatchBarText(ReputationWatchBar, "unlock");
					end
				end
			</OnEvent>
			<OnShow>
				if ( GetCVarBool("xpBarText") ) then
					ShowWatchBarText(ReputationWatchBar, "lock");
				end
				UIParent_ManageFramePositions();
			</OnShow>
			<OnHide function="UIParent_ManageFramePositions"/>
			<OnEnter>
				self.OverlayFrame.Text:Show();
				ReputationParagonWatchBar_OnEnter(self);
			</OnEnter>
			<OnLeave>
				if(not ReputationWatchBar.textLocked) then
				self.OverlayFrame.Text:Hide();
				end
				ReputationParagonWatchBar_OnLeave(self);
			</OnLeave>
		</Scripts>
	</Frame>

	<GameTooltip name="ReputationParagonTooltip" frameStrata="TOOLTIP" hidden="true" parent="ReputationFrame" inherits="GameTooltipTemplate">
		<Frames>
			<Frame parentKey="ItemTooltip" inherits="EmbeddedItemTooltip" hidden="true">
				<Size x="100" y="100"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMLEFT" x="10" y="13"/>
				</Anchors>
				<KeyValues>
					<KeyValue key="yspacing" value="13" type="number"/>
				</KeyValues>
				<Scripts>
					<OnLoad inherit="prepend">
						self.Tooltip.shoppingTooltips = { ShoppingTooltip1, ShoppingTooltip2 };
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b);
				self:SetBackdropColor(TOOLTIP_DEFAULT_BACKGROUND_COLOR.r, TOOLTIP_DEFAULT_BACKGROUND_COLOR.g, TOOLTIP_DEFAULT_BACKGROUND_COLOR.b);
				self.shoppingTooltips = { ShoppingTooltip1, ShoppingTooltip2 };
			</OnLoad>
			<OnUpdate>
				if ( self.recalculatePadding ) then
					self.recalculatePadding = nil;
					GameTooltip_CalculatePadding(self);
				end
				self.updateTooltip = (self.updateTooltip or TOOLTIP_UPDATE_TIME) - elapsed;
				if ( self.updateTooltip > 0 ) then
					return;
				end
				self.updateTooltip = TOOLTIP_UPDATE_TIME;
				
				ReputationParagonFrame_SetupParagonTooltip(self.owner, self.factionID);
			</OnUpdate>
			<OnHide inherit="prepend">
				self.ItemTooltip:Hide();
				self:SetPadding(0, 0);
			</OnHide>
			<OnSizeChanged inherit="prepend">
				self.recalculatePadding = true;
			</OnSizeChanged>
		</Scripts>
	</GameTooltip>
</Ui>
