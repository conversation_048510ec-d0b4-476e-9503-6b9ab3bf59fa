<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="RuneFrame.lua" />
	<Button name="RuneButtonIndividualTemplate" mixin="RuneButtonMixin" virtual="true" hidden="false">
		<Animations>
			<AnimationGroup parentKey="energize" setToFinalAlpha="true">
				<Alpha childKey="EnergizeGlow" smoothing="IN_OUT" duration="0.125" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="EnergizeGlow" startDelay="0.25" smoothing="IN_OUT" duration="0.325" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha parentKey="RuneFade" childKey="Rune" duration=".1" order="1" fromAlpha="0" toAlpha="1"/>
			</AnimationGroup>
			<AnimationGroup parentKey="spent" setToFinalAlpha="true">
				<Alpha parentKey="RuneFade" childKey="Rune" startDelay="0.15" duration="0.1" order="1" fromAlpha="1" toAlpha="0"/>
				<Alpha parentKey="RuneFlash" childKey="Rune2" duration="0.15" order="1" fromAlpha="1" toAlpha="0"/>
				<Scripts>
					<OnPlay>
						self:GetParent():GetParent():OnSpentAnimStarted();
					</OnPlay>
					<OnFinished>
						self:GetParent():GetParent():OnSpentAnimStopped();
					</OnFinished>
					<OnStop>
						self:GetParent():GetParent():OnSpentAnimStopped();
					</OnStop>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Size x="24" y="24"/>
		<Frames>
			<Cooldown parentKey="Cooldown" reverse="true" drawBling="false" hideCountdownNumbers="true" useCircularEdge="true" inherits="CooldownFrameTemplate">
				<Size x="40" y="40" />
				<Anchors>
					<Anchor point="CENTER" />
				</Anchors>
				<SwipeTexture file="Interface\PlayerFrame\DK-Blood-Rune-CDFill"/>
				<EdgeTexture file="Interface\PlayerFrame\DK-BloodUnholy-Rune-CDSpark"/>
			</Cooldown>
		</Frames>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="EmptyRune" atlas="DK-Rune-CD">
					<Size x="24" y="24"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Rune" file="Interface\PlayerFrame\UI-PlayerFrame-Deathknight-SingleRune">
					<Size x="24" y="24" />
					<Anchors>
						<Anchor point="CENTER" />
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="Rune2" alpha="0.0" alphaMode="ADD" file="Interface\PlayerFrame\UI-PlayerFrame-Deathknight-SingleRune">
					<Size x="24" y="24" />
					<Anchors>
						<Anchor point="CENTER" />
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="EnergizeGlow" atlas="DK-Rune-Glow" alpha="0">
					<Size x="24" y="24"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0" />
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<KeyValues>
			<KeyValue key="tooltipText" value="COMBAT_TEXT_RUNE_DEATH" type="global"/>
		</KeyValues>
		<Scripts>
			<OnEnter method="OnEnter"/>
			<OnLeave function="GameTooltip_Hide"/>
		</Scripts>
	</Button>
	<!-- If you change anything about RuneFrame (specifically anchors), make sure to change the relevant information in UnitFrame_SetUnit -->
	<Frame name="RuneFrame" parent="PlayerFrame" frameStrata="LOW" toplevel="true" hidden="false" mixin="RuneFrameMixin">
		<Size x="130" y="24" />
		<Anchors>
			<Anchor point="TOP" relativePoint="BOTTOM" x="54" y="34" />
		</Anchors>
		<Frames>
			<Button parentKey="Rune1" parentArray="Runes" inherits="RuneButtonIndividualTemplate">
				<Anchors>
					<Anchor point="LEFT" relativePoint="LEFT" x="-6" y="0" />
				</Anchors>
			</Button>
			<Button parentKey="Rune2" parentArray="Runes" inherits="RuneButtonIndividualTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.Rune1" relativePoint="RIGHT" x="-3" y="0" />
				</Anchors>
			</Button>
			<Button parentKey="Rune3" parentArray="Runes" inherits="RuneButtonIndividualTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.Rune2" relativePoint="RIGHT" x="-3" y="0" />
				</Anchors>
			</Button>
			<Button parentKey="Rune4" parentArray="Runes" inherits="RuneButtonIndividualTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.Rune3" relativePoint="RIGHT" x="-3" y="0" />
				</Anchors>
			</Button>
			<Button parentKey="Rune5" parentArray="Runes" inherits="RuneButtonIndividualTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.Rune4" relativePoint="RIGHT" x="-3" y="0" />
				</Anchors>
			</Button>
			<Button parentKey="Rune6" parentArray="Runes" inherits="RuneButtonIndividualTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeKey="$parent.Rune5" relativePoint="RIGHT" x="-3" y="0" />
				</Anchors>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad method="OnLoad" />
		</Scripts>
	</Frame>
</Ui>
