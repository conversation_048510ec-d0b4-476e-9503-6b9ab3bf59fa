<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="ScenarioFinder.lua"/>
	<Frame name="ScenarioSpecificChoiceTemplate" enableMouse="true" inherits="LFGSpecificChoiceTemplate" virtual="true">
		<Scripts>
			<OnLoad>
				self.enableButton:SetScript("OnClick", ScenarioQueueFrameChoiceEnableButton_OnClick);
				self.expandOrCollapseButton:SetScript("OnClick", ScenarioQueueFrameExpandOrCollapseButton_OnClick);
				self:SetScript("OnEnter", ScenarioQueueFrameChoiceButton_OnEnter);
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="ScenarioFinderFrame" useParentLevel="true" hidden="true" parent="GroupFinderFrame">
		<Size x="338" y="428"/>
		<Anchors>
			<Anchor point="TOPLEFT" x="224" y="0"/>
		</Anchors>
		<Layers>
			<Layer level="BORDER">
				<Texture name="$parentTopTileStreaks" inherits="_UI-Frame-TopTileStreaks" parentKey="TopTileStreaks">
					<Anchors>
						<Anchor point="TOPLEFT" x="-5" y="-21"/>
						<Anchor point="TOPRIGHT" x="-2" y="-21"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBtnCornerRight" inherits="UI-Frame-BtnCornerRight" hidden="false">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="0" y="-1"/>
					</Anchors>
				</Texture>
				<Texture name="$parentButtonBottomBorder" inherits="_UI-Frame-BtnBotTile">
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-23" y="2"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentBtnCornerRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="NoScenariosCover" enableMouse="true" hidden="true" toplevel="true">
				<Anchors>
					<Anchor point="TOPRIGHT" x="-6" y="-25"/>
					<Anchor point="BOTTOMLEFT" x="3" y="4"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture>
							<Color r="0" g="0" b="0" a="0.8"/>
						</Texture>
					</Layer>
					<Layer level="ARTWORK">
						<FontString parentKey="Label" inherits="GameFontNormalLarge" justifyH="CENTER" justifyV="MIDDLE">
							<Size x="320" y="0"/>
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="$parentInset" useParentLevel="true" inherits="InsetFrameTemplate" parentKey="Inset">
				<Anchors>
					<Anchor point="TOPLEFT" x="2" y="-72" />
					<Anchor point="BOTTOMRIGHT" x="-6" y="26" />
				</Anchors>
			</Frame>
			<Frame name="ScenarioQueueFrame" setAllPoints="true" parentKey="Queue">
				<Layers>
					<Layer level="BORDER">
						<Texture file="Interface\LFGFrame\UI-LFG-SCENARIO-Random" parentKey="Bg">
							<Size x="512" y="512"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="4" y="-75"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Frame name="$parentTypeDropDown" inherits="UIDropDownMenuTemplate" parentKey="Dropdown" id="1">
						<Anchors>
							<Anchor point="TOPLEFT">
								<Offset>
									<AbsDimension x="117" y="-40"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Layers>
							<Layer level="OVERLAY">
								<FontString name="$parentName" inherits="GameFontNormal" justifyH="RIGHT" text="CHOOSE_YOUR_DUNGEON">
									<Size>
										<AbsDimension x="115" y="24"/>
									</Size>
									<Anchors>
										<Anchor point="RIGHT" relativePoint="LEFT">
											<Offset>
												<AbsDimension x="0" y="2"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnShow function="ScenarioQueueFrameTypeDropDown_SetUp"/>
						</Scripts>
					</Frame>
					<Frame name="$parentRandom" setAllPoints="true" parentKey="Random">
						<Frames>
							<ScrollFrame name="$parentScrollFrame" inherits="UIPanelScrollFrameTemplate" parentKey="ScrollFrame">
								<Size>
									<AbsDimension x="303" y="308"/>
								</Size>
								<Anchors>
									<Anchor point="BOTTOMRIGHT">
										<Offset>
											<AbsDimension x="-29" y="36"/>
										</Offset>
									</Anchor>
								</Anchors>
								<Layers>
									<Layer level="BACKGROUND">
										<Texture name="$parentScrollBackground">
											<Anchors>
												<Anchor point="TOPLEFT" relativePoint="TOPRIGHT">
													<Offset>
														<AbsDimension x="-4" y="0"/>
													</Offset>
												</Anchor>
												<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMRIGHT">
													<Offset>
														<AbsDimension x="24" y="-2"/>
													</Offset>
												</Anchor>
											</Anchors>
											<Color r="0" b="0" g="0" a="1"/>
										</Texture>
									</Layer>
									<Layer level="ARTWORK">
										<Texture name="$parentScrollBackgroundTopLeft" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
											<Size>
												<AbsDimension x="31" y="256"/>
											</Size>
											<Anchors>
												<Anchor point="TOPLEFT" relativePoint="TOPRIGHT">
													<Offset>
														<AbsDimension x="-5" y="12"/>
													</Offset>
												</Anchor>
											</Anchors>
											<TexCoords left="0" right="0.484375" top="0" bottom="1.0"/>
										</Texture>
										<Texture name="$parentScrollBackgroundBottomRight" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
											<Size>
												<AbsDimension x="31" y="106"/>
											</Size>
											<Anchors>
												<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT">
													<Offset>
														<AbsDimension x="-5" y="-11"/>
													</Offset>
												</Anchor>
											</Anchors>
											<TexCoords left="0.515625" right="1.0" top="0" bottom="0.4140625"/>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnLoad>
										local myName = self:GetName();
										_G[myName.."ScrollBackground"]:SetParent(_G[myName.."ScrollBar"]);
										_G[myName.."ScrollBackgroundTopLeft"]:SetParent(_G[myName.."ScrollBar"]);
										_G[myName.."ScrollBackgroundBottomRight"]:SetParent(_G[myName.."ScrollBar"]);
										
										local scrollBar = _G[myName.."ScrollBar"];
										scrollBar:ClearAllPoints();
										scrollBar:SetPoint("BOTTOMLEFT", self, "BOTTOMRIGHT", 3, 7);
										scrollBar:SetPoint("TOPLEFT", self, "TOPRIGHT", 3, -8);
										
										self.scrollBarHideable = true;
										ScrollFrame_OnLoad(self);
									</OnLoad>
								</Scripts>
								<ScrollChild>
									<Frame name="$parentChildFrame" inherits="LFGRewardFrameTemplate" parentKey="Child">
										<Scripts>
											<OnShow>
												ScenarioQueueFrameRandom_UpdateFrame();
											</OnShow>
										</Scripts>
									</Frame>
								</ScrollChild>
							</ScrollFrame>
						</Frames>
					</Frame>
					<Frame name="$parentSpecific" setAllPoints="true" hidden="true" parentKey="Specific">
						<Frames>
							<Frame name="$parentButton1" parentKey="Button1" inherits="ScenarioSpecificChoiceTemplate" id="1">
								<Anchors>
									<Anchor point="TOPLEFT" x="10" y="-86"/>
								</Anchors>
							</Frame>
							<ScrollFrame name="$parentScrollFrame" inherits="FauxScrollFrameTemplate" parentKey="ScrollFrame">
								<Size x="296" y="324"/>
								<Anchors>
									<Anchor point="TOPLEFT">
										<Offset>
											<AbsDimension x="10" y="-77"/>
										</Offset>
									</Anchor>
								</Anchors>
								<Layers>
									<Layer level="BACKGROUND">
										<Texture name="$parentScrollBackgroundTopLeft" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
											<Size>
												<AbsDimension x="31" y="256"/>
											</Size>
											<Anchors>
												<Anchor point="TOPLEFT" relativePoint="TOPRIGHT">
													<Offset>
														<AbsDimension x="-2" y="5"/>
													</Offset>
												</Anchor>
											</Anchors>
											<TexCoords left="0" right="0.484375" top="0" bottom="1.0"/>
										</Texture>
										<Texture name="$parentScrollBackgroundBottomRight" file="Interface\PaperDollInfoFrame\UI-Character-ScrollBar">
											<Size>
												<AbsDimension x="31" y="106"/>
											</Size>
											<Anchors>
												<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT">
													<Offset>
														<AbsDimension x="-2" y="-2"/>
													</Offset>
												</Anchor>
											</Anchors>
											<TexCoords left="0.515625" right="1.0" top="0" bottom="0.4140625"/>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnVerticalScroll>
										FauxScrollFrame_OnVerticalScroll(self, offset, 16, ScenarioQueueFrameSpecific_Update);
									</OnVerticalScroll>
								</Scripts>
							</ScrollFrame>
						</Frames>
						<Scripts>
							<OnShow>
								ScenarioQueueFrame_Update();
							</OnShow>
						</Scripts>
					</Frame>
					<!--
					<Frame name="$parentCooldownFrame" hidden="true" enableMouse="true">
						<Size>
							<AbsDimension x="330" y="257"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMRIGHT">
								<Offset>
									<AbsDimension x="-6" y="27"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture name="$parentBlackFilter" setAllPoints="true">
									<Color r="0" b="0" g="0" a="0.93"/>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<FontString name="$parentDescription" parentKey="description" inherits="GameFontNormal" text="LFG_RANDOM_COOLDOWN_OTHER" justifyH="CENTER">
									<Size>
										<AbsDimension x="300" y="0"/>
									</Size>
									<Anchors>
										<Anchor point="TOP">
											<Offset>
												<AbsDimension x="0" y="-30"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentTime" parentKey="time" inherits="GameFontHighlightLarge" justifyH="CENTER">
									<Anchors>
										<Anchor point="TOP" relativeTo="$parentDescription" relativePoint="BOTTOM">
											<Offset>
												<AbsDimension x="0" y="-10"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentName1" inherits="GameFontNormal" justifyH="LEFT">
									<Size>
										<AbsDimension x="120" y="0"/>
									</Size>
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentDescription" relativePoint="BOTTOMLEFT">
											<Offset>
												<AbsDimension x="25" y="-60"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentStatus1" inherits="GameFontNormal" justifyH="RIGHT">
									<Size>
										<AbsDimension x="110" y="0"/>
									</Size>
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentName1" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="0" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentName2" inherits="GameFontNormal" justifyH="LEFT">
									<Size>
										<AbsDimension x="120" y="0"/>
									</Size>
									<Anchors>
										<Anchor point="TOP" relativeTo="$parentName1" relativePoint="BOTTOM">
											<Offset>
												<AbsDimension x="0" y="-5"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentStatus2" inherits="GameFontNormal" justifyH="RIGHT">
									<Size>
										<AbsDimension x="110" y="0"/>
									</Size>
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentName2" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="0" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentName3" inherits="GameFontNormal" justifyH="LEFT">
									<Size>
										<AbsDimension x="120" y="0"/>
									</Size>
									<Anchors>
										<Anchor point="TOP" relativeTo="$parentName2" relativePoint="BOTTOM">
											<Offset>
												<AbsDimension x="0" y="-5"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentStatus3" inherits="GameFontNormal" justifyH="RIGHT">
									<Size>
										<AbsDimension x="110" y="0"/>
									</Size>
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentName3" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="0" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentName4" inherits="GameFontNormal" justifyH="LEFT">
									<Size>
										<AbsDimension x="120" y="0"/>
									</Size>
									<Anchors>
										<Anchor point="TOP" relativeTo="$parentName3" relativePoint="BOTTOM">
											<Offset>
												<AbsDimension x="0" y="-5"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentStatus4" inherits="GameFontNormal" justifyH="RIGHT">
									<Size>
										<AbsDimension x="110" y="0"/>
									</Size>
									<Anchors>
										<Anchor point="TOPLEFT" relativeTo="$parentName4" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="0" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnLoad function="LFDQueueFrameRandomCooldownFrame_OnLoad"/>
							<OnEvent function="LFDQueueFrameRandomCooldownFrame_OnEvent"/>
							<OnShow>
								LFDQueueFrameFindGroupButton_Update();
							</OnShow>
							<OnHide>
								LFDQueueFrameFindGroupButton_Update();
							</OnHide>
						</Scripts>
					</Frame>
					-->
					<Frame name="$parentPartyBackfill" inherits="LFGBackfillCoverTemplate">
						<Size>
							<AbsDimension x="330" y="329"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMRIGHT">
								<Offset>
									<AbsDimension x="-6" y="24"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								LFGBackfillCover_SetUp(self, {LFG_SUBTYPEID_SCENARIO}, LE_LFG_CATEGORY_SCENARIO, ScenarioQueueFrameFindGroupButton_Update);
							</OnLoad>
						</Scripts>
					</Frame>
					<Frame name="$parentCooldownFrame" parentKey="CooldownFrame" inherits="LFGCooldownCoverTemplate">
						<Size>
							<AbsDimension x="330" y="329"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMRIGHT">
								<Offset>
									<AbsDimension x="-6" y="24"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								LFGCooldownCover_SetUp(self, self:GetParent().PartyBackfill);
							</OnLoad>
							<OnShow>
								ScenarioQueueFrameFindGroupButton_Update();
							</OnShow>
							<OnHide>
								ScenarioQueueFrameFindGroupButton_Update();
							</OnHide>
						</Scripts>
					</Frame>
					<Button name="$parentFindGroupButton" inherits="MagicButtonTemplate" text="FIND_A_GROUP" motionScriptsWhileDisabled="true">
						<Size>
							<AbsDimension x="135" y="22"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOM">
								<Offset>
									<AbsDimension x="0" y="4"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnClick>
								local mode, subMode = GetLFGMode(LE_LFG_CATEGORY_SCENARIO);
								if ( mode == "queued" or mode == "listed" or mode == "rolecheck" or mode == "suspended" ) then
									LeaveLFG(LE_LFG_CATEGORY_SCENARIO);
								else
									ScenarioQueueFrame_Join();
								end
							</OnClick>
							<OnEnter>
								if ( self.tooltip ) then
									GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
									GameTooltip:SetText(self.tooltip, NORMAL_FONT_COLOR.r, NORMAL_FONT_COLOR.g, NORMAL_FONT_COLOR.b, 1, true);
									GameTooltip:Show();
								end
							</OnEnter>
							<OnLeave function="GameTooltip_Hide"/>
						</Scripts>
					</Button>
				</Frames>
				<Scripts>
					<OnShow>
						PlaySound(SOUNDKIT.IG_CHARACTER_INFO_OPEN);
						RequestLFDPlayerLockInfo();
						RequestLFDPartyLockInfo();
					</OnShow>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="ScenarioFinderFrame_OnLoad"/>
			<OnEvent function="ScenarioFinderFrame_OnEvent"/>
			<OnShow function="ScenarioFinderFrame_OnShow"/>
		</Scripts>
	</Frame>
</Ui>
