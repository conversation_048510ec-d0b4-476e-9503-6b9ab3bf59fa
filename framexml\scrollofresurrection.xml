<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="ScrollOfResurrection.lua"/>
	
	<Button name="ScrollOfResurrectionSelectionButtonTemplate" virtual="true">
		<Size x="230" y="16"/>
		<Layers>
			<Layer level="ARTWORK">
				<FontString inherits="GameFontNormal" parentKey="name" justifyH="LEFT">
					<Anchors>
						<Anchor point="TOPLEFT" x="10" y="0"/>
						<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<HighlightTexture file="Interface\QuestFrame\UI-QuestLogTitleHighlight" alphaMode="ADD" parentKey="highlight" />
		<Scripts>
			<OnClick function="ScrollOfResurrectionSelectionButton_OnClick"/>
		</Scripts>
	</Button>
	
	<Frame name="ScrollOfResurrectionSelectionFrame" parent="UIParent" hidden="true" frameStrata="DIALOG">
		<Size x="300" y="400"/>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		<Backdrop edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentBackground" setAllPoints="true">
					<Anchors>
						<Anchor point="TOPLEFT" x="11" y="-11"/>
						<Anchor point="BOTTOMRIGHT" x="-11" y="11"/>
					</Anchors>
					<Color r="0" g="0" b="0" a="0.8"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString name="$parentTitle" inherits="GameFontNormalLarge" parentKey="title" text="SCROLL_OF_RESURRECTION" justifyH="CENTER">
					<Anchors>
						<Anchor point="TOP" x="0" y="-20"/>
					</Anchors>
				</FontString>
				<FontString name="$parentLimit" inherits="GameFontNormal" parentKey="limit" justifyH="CENTER">
					<Size x="280" y="0"/>
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentTitle" relativePoint="BOTTOM" x="0" y="-5"/>
					</Anchors>
				</FontString>
				<FontString name="$parentInstructionSelect" inherits="GameFontHighlight" parentKey="instructionSelect" text="SOR_INSTRUCTION_SELECT" justifyH="LEFT" justifyV="BOTTOM">
					<Size x="0" y="32"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parent" relativePoint="TOPLEFT" x="30" y="-82"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parent" relativePoint="TOPRIGHT" x="-20" y="-82"/>
					</Anchors>
				</FontString>
				<Texture name="$parentSelectionCheck" file="Interface\RaidFrame\ReadyCheck-Ready" parentKey="selectionCheck">
					<Size x="17" y="17"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentInstructionSelect" relativePoint="BOTTOMLEFT" x="-2" y="0"/>
					</Anchors>
				</Texture>
				<FontString name="$parentOrString" inherits="GameFontNormalLarge" parentKey="orString" text="JUST_OR" justifyH="CENTER">
					<Anchors>
						<Anchor point="TOP" relativeTo="$parent" relativePoint="TOP" x="0" y="-260"/>
					</Anchors>
				</FontString>
				<FontString name="$parentInstructionEmail" inherits="GameFontHighlight" parentKey="instructionEmail" text="SOR_INSTRUCTION_EMAIL" justifyH="LEFT" justifyV="BOTTOM">
					<Size x="0" y="32"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parent" relativePoint="TOPLEFT" x="30" y="-306"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parent" relativePoint="TOPRIGHT" x="-20" y="-306"/>
					</Anchors>
				</FontString>
				<Texture name="$parentEmailCheck" file="Interface\RaidFrame\ReadyCheck-Ready" parentKey="emailCheck">
					<Size x="17" y="17"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentInstructionEmail" relativePoint="BOTTOMLEFT" x="-2" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<EditBox name="$parentTargetEditBox" autoFocus="false" letters="255" parentKey="targetEditBox">
				<Size x="250" y="20"/>
				<Anchors>
					<Anchor point="TOP" relativeTo="$parentInstructionEmail" relativePoint="BOTTOM" x="0" y="-5"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border">
							<Size x="8" y="20"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="-5" y="0"/>
							</Anchors>
							<TexCoords left="0" right="0.0625" top="0" bottom="0.625"/>
						</Texture>
						<Texture name="$parentRight" file="Interface\Common\Common-Input-Border">
							<Size x="8" y="20"/>
							<Anchors>
								<Anchor point="RIGHT" x="0" y="0"/>
							</Anchors>
							<TexCoords left="0.9375" right="1.0" top="0" bottom="0.625"/>
						</Texture>
						<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border">
							<Size x="0" y="20"/>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
								<Anchor point="RIGHT" relativeTo="$parentRight" relativePoint="LEFT"/>
							</Anchors>
							<TexCoords left="0.0625" right="0.9375" top="0" bottom="0.625"/>
						</Texture>
					</Layer>
					<Layer level="BORDER">
						<FontString name="$parentFill" inherits="FriendsFont_Small" justifyH="LEFT" justifyV="MIDDLE" text="ENTER_EMAIL" parentKey="fill">
							<Anchors>
								<Anchor point="LEFT" x="3" y="0"/>
							</Anchors>
							<Color r="0.35" g="0.35" b="0.35"/>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnTextChanged>
						local text = self:GetText();
						if ( text ~= "" ) then
							self.fill:Hide();
							local oldSendType = ScrollOfResurrectionSelectionFrame.sendType;
							local oldTarget = ScrollOfResurrectionSelectionFrame.target;
							ScrollOfResurrectionSelectionFrame.sendType = "email";
							if ( strfind(text, ".@.") ) then
								ScrollOfResurrectionSelectionFrame.target = text;
								ScrollOfResurrectionSelectionFrame.text = text;
							else
								ScrollOfResurrectionSelectionFrame.target = nil;
								ScrollOfResurrectionSelectionFrame.text = nil;
							end
							if ( oldSendType ~= "email" or not oldTarget ) then
								ScrollOfResurrectionSelectionList_Update();
							end
						else
							self.fill:Show();
						end
					</OnTextChanged> 
					<OnEditFocusGained>
						if ( ScrollOfResurrectionSelectionFrame.sendType ~= "email" ) then
							ScrollOfResurrectionSelectionFrame.sendType = "email";
							ScrollOfResurrectionSelectionFrame.target = nil;
							ScrollOfResurrectionSelectionFrame.text = nil;
							ScrollOfResurrectionSelectionList_Update();
						end
					</OnEditFocusGained>
					<OnEscapePressed>
						self:ClearFocus();
					</OnEscapePressed>	
				</Scripts>
				<FontString inherits="FriendsFont_Small">
					<Color r="1" g="1" b="1" />
				</FontString>
			</EditBox>
			<Frame name="$parentList" parentKey="list">
				<Size x="0" y="150"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="20" y="-90"/>
					<Anchor point="TOPRIGHT" x="-20" y="-90"/>
				</Anchors>
				<Backdrop edgeFile="Interface\Tooltips\UI-Tooltip-Border">
					<EdgeSize>
						<AbsValue val="16"/>
					</EdgeSize>
				</Backdrop>
				<Frames>
					<ScrollFrame name="$parentScrollFrame" inherits="HybridScrollFrameTemplate" parentKey="scrollFrame">
						<Anchors>
							<Anchor point="TOPLEFT" x="0" y="-5"/>
							<Anchor point="BOTTOMRIGHT" x="0" y="5"/>
						</Anchors>
						<Frames>
							<Slider name="$parentScrollBar" inherits="MinimalHybridScrollBarTemplate" parentKey="scrollBar">
								<Anchors>
									<Anchor point="TOPRIGHT" relativePoint="TOPRIGHT" x="-7" y="-15"/>
									<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMRIGHT" x="-19" y="15"/>
								</Anchors>
							</Slider>
						</Frames>
					</ScrollFrame>
				</Frames>
			</Frame>
			<Button inherits="UIPanelButtonTemplate" name="$parentAcceptButton" text="ACCEPT" motionScriptsWhileDisabled="true" parentKey="acceptButton">
				<Size x="120" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT" x="15" y="15"/>
				</Anchors>
				<Scripts>
					<OnClick>
						local sendType = ScrollOfResurrectionSelectionFrame.sendType;
						local target = ScrollOfResurrectionSelectionFrame.target; 
						local text = ScrollOfResurrectionSelectionFrame.text;
						StaticPopupSpecial_Hide(self:GetParent());
						ScrollOfResurrection_Show(sendType, target, text);
					</OnClick>
					<OnEnter>
						if ( self.disableText and not self:IsEnabled() ) then
							GameTooltip:SetOwner(self, "ANCHOR_TOP");
							GameTooltip:SetText(self.disableText);
							GameTooltip:Show();
						end
					</OnEnter>
					<OnLeave>
						if ( GameTooltip:GetOwner() == self ) then
							GameTooltip:Hide();
						end
					</OnLeave>
				</Scripts>
			</Button>
			<Button inherits="UIPanelButtonTemplate" name="$parentCancelButton" text="CANCEL">
				<Size x="120" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" x="-15" y="15"/>
				</Anchors>
				<Scripts>
					<OnClick>
						StaticPopupSpecial_Hide(self:GetParent());
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad function="ScrollOfResurrectionSelection_OnLoad"/>
			<OnEvent function="ScrollOfResurrectionSelection_OnEvent"/>
			<OnShow function="ScrollOfResurrectionSelection_OnShow"/>
			<OnHide>
				PlaySound(SOUNDKIT.IG_SPELLBOOK_CLOSE);
			</OnHide>
		</Scripts>
	</Frame>
	<Frame name="ScrollOfResurrectionFrame" parent="UIParent" hidden="true" enableMouse="true" frameStrata="DIALOG">
		<Size x="386" y="300"/>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentTitle" inherits="GameFontNormalLarge" parentKey="title" text="SCROLL_OF_RESURRECTION" justifyH="CENTER">
					<Anchors>
						<Anchor point="TOP" x="0" y="-20"/>
					</Anchors>
				</FontString>
				<FontString name="$parentName" inherits="GameFontHighlightHuge" parentKey="name" justifyH="CENTER">
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentTitle" relativePoint="BOTTOM" x="0" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<EditBox name="$parentTargetEditBox" autoFocus="false" letters="255" parentKey="targetEditBox">
				<Size x="280" y="20"/>
				<Anchors>
					<Anchor point="TOP" relativeTo="$parentTitle" relativePoint="BOTTOM" x="0" y="-5"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border">
							<Size x="8" y="20"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="-5" y="0"/>
							</Anchors>
							<TexCoords left="0" right="0.0625" top="0" bottom="0.625"/>
						</Texture>
						<Texture name="$parentRight" file="Interface\Common\Common-Input-Border">
							<Size x="8" y="20"/>
							<Anchors>
								<Anchor point="RIGHT" x="0" y="0"/>
							</Anchors>
							<TexCoords left="0.9375" right="1.0" top="0" bottom="0.625"/>
						</Texture>
						<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border">
							<Size x="0" y="20"/>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
								<Anchor point="RIGHT" relativeTo="$parentRight" relativePoint="LEFT"/>
							</Anchors>
							<TexCoords left="0.0625" right="0.9375" top="0" bottom="0.625"/>
						</Texture>
					</Layer>
					<Layer level="BORDER">
						<FontString name="$parentFill" inherits="FriendsFont_Small" justifyH="LEFT" justifyV="MIDDLE" text="ENTER_EMAIL" parentKey="fill">
							<Anchors>
								<Anchor point="LEFT" x="3" y="0"/>
							</Anchors>
							<Color r="0.35" g="0.35" b="0.35"/>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnTextChanged>
						if ( self:GetText() ~= "" ) then
							self.fill:Hide();
						else
							self.fill:Show();
						end
					</OnTextChanged> 
					<OnEnterPressed>
						ScrollOfResurrectionFrame.noteFrame.scrollFrame.editBox:SetFocus();
					</OnEnterPressed>
					<OnTabPressed>
						ScrollOfResurrectionFrame.noteFrame.scrollFrame.editBox:SetFocus();
					</OnTabPressed>
					<OnEscapePressed>
						self:ClearFocus();
					</OnEscapePressed>	
				</Scripts>
				<FontString inherits="FriendsFont_Small">
					<Color r="1" g="1" b="1" />
				</FontString>
			</EditBox>
			<Frame name="$parentNoteFrame" parentKey="noteFrame">
				<Size x="286" y="150"/>
				<Anchors>
					<Anchor point="TOP" x="-2" y="-74"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentTopLeft" file="Interface\Common\Common-Input-Border-TL">
							<Size x="8" y="8"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentTopRight" file="Interface\Common\Common-Input-Border-TR">
							<Size x="8" y="8"/>
							<Anchors>
								<Anchor point="TOPRIGHT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentTop" file="Interface\Common\Common-Input-Border-T">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTopLeft" relativePoint="TOPRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentTopRight" relativePoint="BOTTOMLEFT"/>
							</Anchors>
						</Texture>
						<Texture name="$parentBottomLeft" file="Interface\Common\Common-Input-Border-BL">
							<Size x="8" y="8"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentBottomRight" file="Interface\Common\Common-Input-Border-BR">
							<Size x="8" y="8"/>
							<Anchors>
								<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentBottom" file="Interface\Common\Common-Input-Border-B">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentBottomLeft" relativePoint="TOPRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRight" relativePoint="BOTTOMLEFT"/>
							</Anchors>
						</Texture>
						<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border-L">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTopLeft" relativePoint="BOTTOMLEFT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomLeft" relativePoint="TOPRIGHT"/>
							</Anchors>
						</Texture>
						<Texture name="$parentRight" file="Interface\Common\Common-Input-Border-R">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentTopRight" relativePoint="BOTTOMLEFT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRight" relativePoint="TOPRIGHT"/>
							</Anchors>
						</Texture>
						<Texture name="$parentMiddle" file="Interface\Common\Common-Input-Border-M">
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeTo="$parentRight" relativePoint="BOTTOMLEFT"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<ScrollFrame name="$parentScrollFrame" inherits="UIPanelScrollFrameTemplate" parentKey="scrollFrame">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parent" relativePoint="TOPLEFT" x="6" y="-6"/>
							<Anchor point="BOTTOMRIGHT" relativeTo="$parent" relativePoint="BOTTOMRIGHT" x="0" y="6"/>
						</Anchors>
						<Scripts>
							<OnLoad>
								local scrollBar = _G[self:GetName().."ScrollBar"];
								scrollBar:ClearAllPoints();
								scrollBar:SetPoint("TOPLEFT", self, "TOPRIGHT", -18, -10);
								scrollBar:SetPoint("BOTTOMLEFT", self, "BOTTOMRIGHT", -18, 8);
								-- reposition the up and down buttons
								_G[self:GetName().."ScrollBarScrollDownButton"]:SetPoint("TOP", scrollBar, "BOTTOM", 0, 4);
								_G[self:GetName().."ScrollBarScrollUpButton"]:SetPoint("BOTTOM", scrollBar, "TOP", 0, -4);
								-- make the scroll bar hideable and force it to start off hidden so positioning calculations can be done
								-- as soon as it needs to be shown
								self.scrollBarHideable = 1;
								scrollBar:Hide();
							</OnLoad>
							<OnMouseDown>
								self.editBox:SetFocus();
							</OnMouseDown>
						</Scripts>
						<ScrollChild>
							<EditBox name="$parentEditBox" multiLine="true" letters="250" countInvisibleLetters="true" autoFocus="false" parentKey="editBox">
								<Size x="258" y="1"/>
								<Layers>
									<Layer level="BORDER">
										<FontString name="$parentFill" inherits="FriendsFont_Small" justifyH="LEFT" justifyV="MIDDLE" text="ENTER_INVITE_NOTE" parentKey="fill">
											<Anchors>
												<Anchor point="LEFT" x="3" y="0"/>
											</Anchors>
											<Color r="0.35" g="0.35" b="0.35"/>
										</FontString>
									</Layer>
								</Layers>
								<Scripts>
									<OnTextChanged>
										ScrollingEdit_OnTextChanged(self, self:GetParent());
										if ( self:GetText() ~= "" ) then
											self.fill:Hide();
										else
											self.fill:Show();
										end
									</OnTextChanged> 
									<OnCursorChanged function="ScrollingEdit_OnCursorChanged"/>
									<OnUpdate>
										ScrollingEdit_OnUpdate(self, elapsed, self:GetParent());
									</OnUpdate>
									<OnEscapePressed>
										self:ClearFocus();
									</OnEscapePressed>
									<OnEditFocusGained>
										self:GetParent().focusButton:Hide();
									</OnEditFocusGained>
									<OnEditFocusLost>
										self:GetParent().focusButton:Show();
									</OnEditFocusLost>
								</Scripts>
								<FontString inherits="FriendsFont_Small"/>
							</EditBox>
						</ScrollChild>
					</ScrollFrame>
				</Frames>
			</Frame>
			<Button name="$parentAcceptButton" inherits="UIPanelButtonTemplate" text="ACCEPT">
				<Size x="118" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT" x="45" y="30"/>
				</Anchors>
				<Scripts>
					<OnClick function="ScrollOfResurrectionAcceptButton_OnClick"/>
				</Scripts>
			</Button>
			<Button name="$parentCancelButton" inherits="UIPanelButtonTemplate" text="CANCEL">
				<Size x="118" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" x="-50" y="30"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						StaticPopupSpecial_Hide(ScrollOfResurrectionFrame);
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self.exclusive = true;
			</OnLoad>
		</Scripts>
	</Frame>
</Ui>
