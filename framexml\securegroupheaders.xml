<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="SecureGroupHeaders.lua"/>
  
  <!-- A frame used for managing group members -->
    <Frame name="SecureGroupHeaderTemplate" inherits="SecureFrameTemplate" hidden="true" virtual="true">
        <Scripts>
            <OnLoad function="SecureGroupHeader_OnLoad"/>
            <OnEvent function="SecureGroupHeader_OnEvent"/>
            <OnShow function="SecureGroupHeader_Update"/>
            <OnAttributeChanged function="SecureGroupHeader_OnAttributeChanged"/>
        </Scripts>
    </Frame>

    <!-- A frame used for managing party members -->
    <Frame name="SecurePartyHeaderTemplate" inherits="SecureGroupHeaderTemplate" hidden="true" virtual="true">
        <Attributes>
            <Attribute name="showParty" type="boolean" value="true"/>
        </Attributes>
    </Frame>

    <!-- A frame used for managing raid members -->
    <Frame name="SecureRaidGroupHeaderTemplate" inherits="SecureGroupHeaderTemplate" hidden="true" virtual="true">
        <Attributes>
            <Attribute name="showRaid" type="boolean" value="true"/>
        </Attributes>
    </Frame>

    <!-- A frame used for managing group pets -->
    <Frame name="SecureGroupPetHeaderTemplate" inherits="SecureFrameTemplate" hidden="true" virtual="true">
        <Scripts>
            <OnLoad function="SecureGroupPetHeader_OnLoad"/>
            <OnEvent function="SecureGroupPetHeader_OnEvent"/>
            <OnShow function="SecureGroupPetHeader_Update"/>
            <OnAttributeChanged function="SecureGroupPetHeader_OnAttributeChanged"/>
        </Scripts>
    </Frame>

    <!-- A frame used for managing party pets -->
    <Frame name="SecurePartyPetHeaderTemplate" inherits="SecureGroupPetHeaderTemplate" hidden="true" virtual="true">
        <Attributes>
            <Attribute name="showParty" type="boolean" value="true"/>
        </Attributes>
    </Frame>

    <!-- A frame used for managing raid pets -->
    <Frame name="SecureRaidPetHeaderTemplate" inherits="SecureGroupPetHeaderTemplate" hidden="true" virtual="true">
        <Attributes>
            <Attribute name="showRaid" type="boolean" value="true"/>
        </Attributes>
    </Frame>

    <!-- A frame used for managing group pets -->
    <Frame name="SecureAuraHeaderTemplate" inherits="SecureFrameTemplate" hidden="true" virtual="true">
        <Scripts>
            <OnLoad function="SecureAuraHeader_OnLoad"/>
            <OnEvent function="SecureAuraHeader_OnEvent"/>
            <OnShow function="SecureAuraHeader_Update"/>
            <OnUpdate function="SecureAuraHeader_OnUpdate" />
            <OnAttributeChanged function="SecureAuraHeader_OnAttributeChanged"/>
        </Scripts>
    </Frame>
    
</Ui>
