<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
  <Script file="ShardBar.lua"/>

	<Frame name="ShardTemplate" parentArray="Shards" virtual="true" mixin="WarlockShardMixin">
		<Animations>
			<AnimationGroup parentKey="animIn" setToFinalAlpha="true">
				<Alpha childKey="ShardFill" fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
				<Alpha childKey="ShardGlow" fromAlpha="0" toAlpha="1" duration="0.2" order="1"/>
				<Alpha childKey="ShardGlow" fromAlpha="1" toAlpha="0" duration="0.1" order="2"/>
			</AnimationGroup>
			<AnimationGroup parentKey="animOut" setToFinalAlpha="true">
				<Alpha		childKey="ShardFill" fromAlpha="1" toAlpha="0" duration="0.2" order="1"/>
				<Alpha		childKey="ShardSmokeA" fromAlpha="0" toAlpha="1" duration="0.1" order="1"/>
				<Alpha		childKey="ShardSmokeB" fromAlpha="0" toAlpha="1" duration="0.1" order="1"/>
				<Rotation 	childKey="ShardSmokeA" radians="1.1" duration="0.3" order="1"/>
				<Rotation	childKey="ShardSmokeB" radians="-0.9" duration="0.4" order="1"/>
				<Scale 		childKey="ShardSmokeA" fromScaleX="1" toScaleX="1.2" fromScaleY="1" toScaleY="1.2" duration="0.3" order="1"/>
				<Scale		childKey="ShardSmokeB" fromScaleX="1" toScaleX="2.5" fromScaleY="1" toScaleY="2.5" duration="0.4" order="1"/>
				<Alpha		childKey="ShardSmokeA" fromAlpha="1" toAlpha="0" duration="0.25" order="2"/>
				<Alpha		childKey="ShardSmokeB" fromAlpha="1" toAlpha="0" duration="0.25" order="2"/>
				<Scale		childKey="ShardSmokeA" fromScaleX="1" toScaleX="0.4" fromScaleY="1" toScaleY="0.4" duration="0.25" order="2"/>
				<Scale		childKey="ShardSmokeB" fromScaleX="1" toScaleX="0.4" fromScaleY="1" toScaleY="0.4" duration="0.25" order="2"/>
			</AnimationGroup>
		</Animations>
		<Size x="52" y="29"/>
		<Layers>
			<Layer level="OVERLAY">
				<Texture atlas="Warlock-ReadyShard-Glow" parentKey="ShardGlow" alphaMode="ADD" alpha="0">
					<Size x="26" y="23"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-2" y="1"/>
					</Anchors>
				</Texture>
				<Texture file="Interface\PlayerFrame\UI-WarlockShard" alpha="0" parentKey="ShardSmokeA">
					<Size x="32" y="32"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-8" y="5"/>
					</Anchors>
					<TexCoords left="0.01562500" right="0.51562500" top="0.34375000" bottom="0.59375000"/>
				</Texture>
				<Texture file="Interface\PlayerFrame\UI-WarlockShard" alpha="0" parentKey="ShardSmokeB">
					<Size x="32" y="32"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.ShardSmokeA"/>
					</Anchors>
					<TexCoords right="0.01562500" left="0.51562500" bottom="0.34375000" top="0.59375000"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture atlas="Warlock-ReadyShard" alpha="0" useAtlasSize="true" parentKey="ShardFill">
					<Anchors>
						<Anchor point="TOPLEFT" x="3" y="-2"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture atlas="Warlock-Shard-Spark" parentKey="Spark"/>
			</Layer>
		</Layers>
		<Frames>
			<StatusBar parentKey="PartialFill" setAllPoints="true" orientation="VERTICAL" minValue="0" maxValue="1" alpha="0.7">
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.ShardFill" relativePoint="TOPLEFT"/>
					<Anchor point="BOTTOMRIGHT" relativeKey="$parent.ShardFill" relativePoint="BOTTOMRIGHT"/>
				</Anchors>
				<BarTexture atlas="Warlock-FillShard"/>
			</StatusBar>
		</Frames>
	</Frame>

	<Frame name="WarlockPowerFrame" inherits="ClassPowerBarFrame" mixin="ClassPowerBar, WarlockPowerBar">
		<Size x="110" y="25"/>
		<Anchors>
			<Anchor point="TOP" relativeTo="PlayerFrame" relativePoint="BOTTOM" x="50" y="34"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture atlas="Warlock-Bar-EmptyShards" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOP"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>
</Ui>
