<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
UI.xsd">
	<Script file="SharedPetBattleTemplates.lua"/>
	<Texture name="SharedPetBattleStrengthPetTypeTemplate" virtual="true">
		<Size x="32" y="32"/>
		<TexCoords left="0.79687500" right="0.49218750" top="0.50390625" bottom="0.65625000"/>
	</Texture>
	<Frame name="SharedPetBattleAbilityTooltipTemplate" parent="UIParent" frameStrata="TOOLTIP" clampedToScreen="true" virtual="true" hidden="true" inherits="TooltipBorderedFrameTemplate">
		<Size x="260" y="90"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="AbilityPetType">
					<Size x="33" y="33"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="11" y="-10"/>
					</Anchors>
					<TexCoords left="0.79687500" right="0.49218750" top="0.50390625" bottom="0.65625000"/>
				</Texture>
				<FontString parentKey="Name" inherits="GameFontHighlightLarge" text="NAME" justifyH="LEFT" justifyV="MIDDLE">
					<!--Size and Anchor changed in SetAbility-->
					<Size x="190" y="32"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.AbilityPetType" relativePoint="RIGHT" x="5" y="0"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Duration" inherits="GameFontHighlight" text="BATTLE_PET_ABILITY_MULTIROUND">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.AbilityPetType" relativePoint="BOTTOMLEFT" x="0" y="-5"/>
					</Anchors>
				</FontString>
				<FontString parentKey="MaxCooldown" inherits="GameFontHighlight" text="MAX_COOLDOWN">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Duration" relativePoint="BOTTOMLEFT" x="0" y="-5"/>
					</Anchors>
				</FontString>
				<FontString parentKey="CurrentCooldown" inherits="GameFontHighlight">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.MaxCooldown" relativePoint="BOTTOMLEFT" x="0" y="-5"/>
					</Anchors>
				</FontString>
				<FontString parentKey="AdditionalText" inherits="GameFontHighlight" justifyH="LEFT">
					<Size x="239" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.CurrentCooldown" relativePoint="BOTTOMLEFT" x="5" y="-5"/>
					</Anchors>
				</FontString>
				<FontString parentKey="Description" inherits="GameFontNormal" justifyH="LEFT">
					<Size x="239" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.CurrentCooldown" relativePoint="BOTTOMLEFT" x="5" y="-5"/>
					</Anchors>
				</FontString>
				<Texture parentKey="Delimiter1">
					<Size x="251" y="2"/>
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.Description" relativePoint="BOTTOM" x="0" y="-7"/>
					</Anchors>
					<Color r="0.2" g="0.2" b="0.2"/>
				</Texture>
				<Texture parentKey="StrongAgainstIcon" file="Interface\PetBattles\BattleBar-AbilityBadge-Strong">
					<Size x="32" y="32"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Delimiter1" relativePoint="BOTTOMLEFT" x="5" y="-2"/>
					</Anchors>
				</Texture>
				<FontString parentKey="StrongAgainstLabel" inherits="GameFontHighlight" text="PET_BATTLE_EFFECTIVENESS_VS">
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.StrongAgainstIcon" relativePoint="RIGHT" x="5" y="0"/>
					</Anchors>
				</FontString>
				<Texture parentKey="StrongAgainstType1" inherits="SharedPetBattleStrengthPetTypeTemplate">
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.StrongAgainstLabel" relativePoint="RIGHT" x="5" y="-2"/>
					</Anchors>
				</Texture>
				<FontString parentKey="StrongAgainstType1Label" inherits="GameFontHighlight" justifyH="LEFT">
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.StrongAgainstType1" relativePoint="RIGHT" x="5" y="0"/>
					</Anchors>
				</FontString>
				<Texture parentKey="Delimiter2">
					<Size x="251" y="2"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.StrongAgainstIcon" relativePoint="BOTTOMLEFT" x="-5" y="-5"/>
					</Anchors>
					<Color r="0.2" g="0.2" b="0.2"/>
				</Texture>
				<Texture parentKey="WeakAgainstIcon" file="Interface\PetBattles\BattleBar-AbilityBadge-Weak">
					<Size x="32" y="32"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.Delimiter2" relativePoint="BOTTOMLEFT" x="5" y="-2"/>
					</Anchors>
				</Texture>
				<FontString parentKey="WeakAgainstLabel" inherits="GameFontHighlight" text="PET_BATTLE_EFFECTIVENESS_VS">
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.WeakAgainstIcon" relativePoint="RIGHT" x="5" y="0"/>
					</Anchors>
				</FontString>
				<Texture parentKey="WeakAgainstType1" inherits="SharedPetBattleStrengthPetTypeTemplate">
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.WeakAgainstLabel" relativePoint="RIGHT" x="5" y="-2"/>
					</Anchors>
				</Texture>
				<FontString parentKey="WeakAgainstType1Label" inherits="GameFontHighlight" justifyH="LEFT">
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.WeakAgainstType1" relativePoint="RIGHT" x="5" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad function="SharedPetBattleAbilityTooltip_OnLoad"/>
		</Scripts>
	</Frame>
</Ui>
