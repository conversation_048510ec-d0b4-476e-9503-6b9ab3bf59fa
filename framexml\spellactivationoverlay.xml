<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="SpellActivationOverlay.lua"/>
	<Frame name="SpellActivationOverlayTemplate" virtual="true">
		<Animations>
			<AnimationGroup name="$parentAnimIn" parentKey="animIn">
				<Alpha fromAlpha="0" toAlpha="1" duration="0.2"/>
				<Scripts>
					<OnPlay function="SpellActivationOverlayTexture_OnFadeInPlay"/>
					<OnFinished function="SpellActivationOverlayTexture_OnFadeInFinished"/>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup name="$parentAnimOut" parentKey="animOut">
				<Alpha fromAlpha="1" toAlpha="0" duration="0.1">
					<Scripts>
						<OnFinished function="SpellActivationOverlayTexture_OnFadeOutFinished"/>
					</Scripts>
				</Alpha>
			</AnimationGroup>
			<AnimationGroup name="$parentPulse" looping="REPEAT" parentKey="pulse">
				<Scale scaleX="1.08" scaleY="1.08" duration="0.5" smoothing="IN_OUT" order="1"/>
				<Scale scaleX="0.9259" scaleY="0.9259" duration="0.5" smoothing="IN_OUT" order="2"/>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="texture" setAllPoints="true"/>
			</Layer>
		</Layers>
		<Scripts>
			<OnShow function="SpellActivationOverlayTexture_OnShow"/>
			<OnHide function="SpellActivationOverlayTexture_OnHide"/>
		</Scripts>
	</Frame>
	<Frame name="SpellActivationOverlayFrame" parent="UIParent">
		<Size x="256" y="256"/>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		<Scripts>
			<OnLoad function="SpellActivationOverlay_OnLoad"/>
			<OnEvent function="SpellActivationOverlay_OnEvent"/>
		</Scripts>
	</Frame>
</Ui>