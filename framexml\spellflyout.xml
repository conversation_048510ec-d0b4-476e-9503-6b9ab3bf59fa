<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="SpellFlyout.lua"/>

	<Texture name="ActionBarFlyoutButton-FlyoutTop" file="Interface\Buttons\ActionBarFlyoutButton" virtual="true" >
		<Size x="37" y="22"/>
		<TexCoords left="0.01562500" right="0.59375000" top="0.74218750" bottom="0.91406250"/>
	</Texture>

	<CheckButton name="SpellFlyoutButtonTemplate" inherits="ActionButtonTemplate,SecureFrameTemplate" virtual="true" motionScriptsWhileDisabled="true">
		<!-- This doesn't actually use any of the behavior of an ActionButtonTemplate, just the appearance -->
		<Size x="28" y="28"/>
		<Layers>
			<Layer level="OVERLAY">
				<Texture parentKey="GlyphIcon" hidden="true" alpha="1" alphaMode="BLEND" atlas="GlyphIcon-Spellbook" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPRIGHT" x="5" y="4"/>
					</Anchors>
				</Texture>
				<Texture parentKey="GlyphActivate" hidden="true" alpha="1" alphaMode="ADD" file="Interface\Buttons\CheckButtonHilight-Blue" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
				<Texture parentKey="AbilityHighlight" hidden="true" alpha="1" alphaMode="ADD" file="Interface\Buttons\CheckButtonHilight-Blue" useAtlasSize="true">
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="1">
				<Texture parentKey="GlyphTranslation" hidden="true" alpha="1" alphaMode="ADD" atlas="GlyphIcon-Spellbook" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPRIGHT" x="5" y="4"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				self:RegisterForDrag("LeftButton");
				_G[self:GetName().."Count"]:SetPoint("BOTTOMRIGHT", 0, 0);
				self.maxDisplayCount = 99;
				_G[self:GetName().."Icon"]:SetTexCoord(4/64, 60/64, 4/64, 60/64);
			</OnLoad>
			<OnClick>
				SpellFlyoutButton_OnClick(self);
			</OnClick>
			<OnEnter>
				SpellFlyoutButton_SetTooltip(self);
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
			<OnDragStart>
				SpellFlyoutButton_OnDrag(self);
			</OnDragStart>
		</Scripts>
		<Animations>
			<AnimationGroup parentKey="GlyphActivateAnim" setToFinalAlpha="true">
				<Alpha childKey="GlyphActivate" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Alpha childKey="GlyphActivate" startDelay="0.25" duration="0.75" order="1" fromAlpha="1" toAlpha="0"/>
				<Scale childKey="GlyphActivate" smoothing="OUT" duration="0.25" order="1" fromScaleX="1" fromScaleY="1" toScaleX="1.35" toScaleY="1.35"/>
				<Alpha childKey="GlyphIcon" smoothing="OUT" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Scale childKey="GlyphIcon" smoothing="IN" duration="0.25" order="1" fromScaleX="1.75" fromScaleY="1.75" toScaleX="1" toScaleY="1"/>
				<Alpha childKey="GlyphTranslation" smoothing="OUT" duration="0.25" order="1" fromAlpha="0" toAlpha="1"/>
				<Translation childKey="GlyphTranslation" smoothing="OUT" duration="0.25" order="1" offsetX="-2" offsetY="-2"/>
				<Alpha childKey="GlyphTranslation" startDelay="0.25" smoothing="OUT" duration="0.25" order="1" fromAlpha="1" toAlpha="0"/>
				<Scripts>
					<OnFinished>
						SpellFlyout.glyphActivating = nil;
						self:GetParent().GlyphActivate:Hide();
					</OnFinished>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="AbilityHighlightAnim" setToFinalAlpha="true" looping="REPEAT">
				<Alpha childKey="AbilityHighlight" duration="0.25" order="1" fromAlpha="0.5" toAlpha="1"/>
				<Alpha childKey="AbilityHighlight" startDelay="0.5" duration="0.75" order="1" fromAlpha="1" toAlpha="0.5"/>
			</AnimationGroup>
		</Animations>
		<NormalTexture/>
	</CheckButton>

	<Frame name="SpellFlyout" toplevel="true" hidden="true" frameStrata="DIALOG" frameLevel="10" inherits="SecureFrameTemplate" enableMouse="true">
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="SpellFlyoutBackgroundEnd" inherits="ActionBarFlyoutButton-FlyoutTop" parentKey="BgEnd"/>
				<Texture name="SpellFlyoutHorizontalBackground" file="Interface\Buttons\ActionBarFlyoutButton-FlyoutMidLeft" parentKey="HorizBg" horizTile="true" hidden="true">
					<Size x="32" y="37"/>
					<TexCoords left="0" right="1" top="0" bottom="0.578125"/>
				</Texture>
				<Texture name="SpellFlyoutVerticalBackground" file="Interface\Buttons\ActionBarFlyoutButton-FlyoutMid" parentKey="VertBg" vertTile="true" hidden="true">
					<Size x="37" y="32"/>
					<TexCoords left="0" right="0.578125" top="0" bottom="1"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<CheckButton name="SpellFlyoutButton1" inherits="SpellFlyoutButtonTemplate"/>
		</Frames>
		<Scripts>
			<OnLoad function="SpellFlyout_OnLoad"/>
			<OnShow function="SpellFlyout_OnShow"/>
			<OnHide function="SpellFlyout_OnHide"/>
			<OnEvent function="SpellFlyout_OnEvent"/>
		</Scripts>
	</Frame>
</Ui>
