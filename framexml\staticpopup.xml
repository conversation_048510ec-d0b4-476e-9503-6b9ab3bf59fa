<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="StaticPopup.lua"/>
	<Button name="StaticPopupButtonTemplate" virtual="true">
		<Size x="128" y="21"/>
		<Animations>
			<AnimationGroup parentKey="PulseAnim" looping="BOUNCE">
				<Alpha childKey="Flash" fromAlpha="0" toAlpha="1" duration="0.5" order="1"/>
				<Alpha childKey="Flash" fromAlpha="1" toAlpha="0" duration="0.5" order="2"/>
			</AnimationGroup>
		</Animations>
		<Layers>
			<Layer level="OVERLAY" textureSubLevel="1">
				<Texture parentKey="Flash" file="Interface\Buttons\UI-Panel-Button-Glow" alphaMode="ADD" alpha="0">
					<Size x="140" y="40"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
					<TexCoords left="0.0" right="0.75" top="0.0" bottom="0.609375"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick>
				StaticPopup_OnClick(self:GetParent(), self:GetID());
			</OnClick>
		</Scripts>
		<ButtonText name="$parentText">
			<Anchors>
				<Anchor point="CENTER">
					<Offset x="0" y="1"/>
				</Anchor>
			</Anchors>
		</ButtonText>
		<NormalTexture file="Interface\Buttons\UI-DialogBox-Button-Up">
			<TexCoords left="0.0" right="1.0" top="0.0" bottom="0.71875"/>
		</NormalTexture>
		<PushedTexture file="Interface\Buttons\UI-DialogBox-Button-Down">
			<TexCoords left="0.0" right="1.0" top="0.0" bottom="0.71875"/>
		</PushedTexture>
		<DisabledTexture file="Interface\Buttons\UI-DialogBox-Button-Disabled">
			<TexCoords left="0.0" right="1.0" top="0.0" bottom="0.71875"/>
		</DisabledTexture>
		<HighlightTexture file="Interface\Buttons\UI-DialogBox-Button-Highlight" alphaMode="ADD">
			<TexCoords left="0.0" right="1.0" top="0.0" bottom="0.71875"/>
		</HighlightTexture>
		<NormalFont style="GameFontNormal"/>
		<DisabledFont style="GameFontDisable"/>
		<HighlightFont style="GameFontHighlight"/>
	</Button>
	<Frame name="StaticPopupTemplate" toplevel="true" enableKeyboard="true" enableMouse="true" frameStrata="DIALOG" hidden="true" hyperlinksEnabled="true" virtual="true">
		<Size>
			<AbsDimension x="320" y="72"/>
		</Size>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentText" parentKey="text" inherits="GameFontHighlight">
					<Size>
						<AbsDimension x="290" y="0"/>
					</Size>
					<Anchors>
						<Anchor point="TOP">
							<Offset>
								<AbsDimension x="0" y="-16"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				<Texture name="$parentAlertIcon">
					<Size>
						<AbsDimension x="36" y="36"/>
					</Size>
					<Anchors>
						<Anchor point="LEFT" x="24" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame parentKey="CoverFrame" frameLevel="1" enableMouse="true" enableKeyboard="true" hidden="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="UIParent"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="UIParent"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND" textureSubLevel="-1">
						<Texture setAllPoints="true">
							<Color r="0" g="0" b="0" a="0.5"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnKeyDown function="nop"/>
					<OnKeyUp function="nop"/>
				</Scripts>
			</Frame>
			<Frame name="$parentExtraFrame" parentKey="extraFrame" enableMouse="true"/>
			<Button name="$parentCloseButton" inherits="UIPanelCloseButton">
				<Anchors>
					<Anchor point="TOPRIGHT">
						<Offset>
							<AbsDimension x="-3" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="$parentButton1" inherits="StaticPopupButtonTemplate" parentKey="button1" id="1"/>
			<Button name="$parentButton2" inherits="StaticPopupButtonTemplate" parentKey="button2" id="2"/>
			<Button name="$parentButton3" inherits="StaticPopupButtonTemplate" parentKey="button3" id="3"/>
			<Button name="$parentButton4" inherits="StaticPopupButtonTemplate" parentKey="button4" id="4"/>
			<EditBox name="$parentEditBox" historyLines="1" hidden="true" inherits="AutoCompleteEditBoxTemplate">
				<Size>
					<AbsDimension x="130" y="32"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOM" >
						<Offset>
							<AbsDimension x="0" y="45"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentLeft" file="Interface\ChatFrame\UI-ChatInputBorder-Left2">
							<Size>
								<AbsDimension x="32" y="32"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT" x="-10" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentRight" file="Interface\ChatFrame\UI-ChatInputBorder-Right2">
							<Size>
								<AbsDimension x="32" y="32"/>
							</Size>
							<Anchors>
								<Anchor point="RIGHT" x="10" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentMid" file="Interface\ChatFrame\UI-ChatInputBorder-Mid2" horizTile="true">
							<Size>
								<AbsDimension x="0" y="32"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT">
									<Offset>
										<AbsDimension x="0" y="0"/>
									</Offset>
								</Anchor>
								<Anchor point="TOPRIGHT" relativeTo="$parentRight" relativePoint="TOPLEFT">
									<Offset>
										<AbsDimension x="0" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnterPressed>
						StaticPopup_EditBoxOnEnterPressed(self);
					</OnEnterPressed>
					<OnEscapePressed>
						StaticPopup_EditBoxOnEscapePressed(self);
					</OnEscapePressed>
					<OnTextChanged>
						StaticPopup_EditBoxOnTextChanged(self, userInput);
					</OnTextChanged>
					<OnLoad>
						self:GetParent().editBox = self;
					</OnLoad>
				</Scripts>
				<FontString inherits="ChatFontNormal"/>
			</EditBox>
			<Frame name="$parentMoneyFrame" inherits="SmallMoneyFrameTemplate" hidden="true">
				<Anchors>
					<Anchor point="TOP" relativeTo="$parentText" relativePoint="BOTTOM">
						<Offset>
							<AbsDimension x="0" y="-5"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						SmallMoneyFrame_OnLoad(self);
						MoneyFrame_SetType(self, "STATIC");
						self:GetParent().moneyFrame = self;
					</OnLoad>
				</Scripts>
			</Frame>
			<Frame name="$parentMoneyInputFrame" inherits="MoneyInputFrameTemplate">
				<Anchors>
					<Anchor point="TOP" relativeTo="$parentText" relativePoint="BOTTOM">
						<Offset>
							<AbsDimension x="0" y="-5"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Button parentKey="ItemFrame" name="$parentItemFrame" inherits="ItemButtonTemplate" hidden="true">
				<Size>
					<AbsDimension x="37" y="37"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOM" relativeTo="$parentButton1" relativePoint="TOP">
						<Offset>
							<AbsDimension x="0" y="8"/>
						</Offset>
					</Anchor>
					<Anchor point="LEFT">
						<Offset>
							<AbsDimension x="82" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture name="$parentNameFrame" file="Interface\QuestFrame\UI-QuestItemNameFrame">
							<Size>
								<AbsDimension x="140" y="62"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT">
									<Offset>
										<AbsDimension x="30" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
						<FontString name="$parentText" inherits="GameFontNormal" justifyH="LEFT">
							<Size>
								<AbsDimension x="103" y="38"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT" relativePoint="RIGHT">
									<Offset>
										<AbsDimension x="8" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						if ( self.link ) then
							GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
							GameTooltip:SetHyperlink(self.link);
						end
					</OnEnter>
					<OnLeave>
						GameTooltip:Hide();
					</OnLeave>
					<OnLoad>
						self:GetParent().itemFrame = self;
						self:RegisterEvent("GET_ITEM_INFO_RECEIVED");
					</OnLoad>
					<OnEvent function="StaticPopupItemFrame_OnEvent"/>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnUpdate>
				StaticPopup_OnUpdate(self, elapsed);
			</OnUpdate>
			<OnEvent function="StaticPopup_OnEvent" />
			<OnShow>
				StaticPopup_OnShow(self);
			</OnShow>
			<OnHide>
				StaticPopup_OnHide(self);
			</OnHide>
			<OnLoad>
				local name = self:GetName();
				self.button1 = _G[name .. "Button1"];
				self.button2 = _G[name .. "Button2"];
				self.button3 = _G[name .. "Button3"];
				self.text = _G[name .. "Text"];
				self.icon = _G[name .. "AlertIcon"];
				self.moneyInputFrame = _G[name .. "MoneyInputFrame"];
				self:RegisterEvent("DISPLAY_SIZE_CHANGED");
			</OnLoad>
			<OnHyperlinkClick function="StaticPopup_OnHyperlinkClick"/>
			<OnHyperlinkEnter function="StaticPopup_OnHyperlinkEnter"/>
			<OnHyperlinkLeave function="StaticPopup_OnHyperlinkLeave"/>
		</Scripts>
	</Frame>
	<Frame name="StaticPopup1" inherits="StaticPopupTemplate" parent="UIParent" id="1">
		<Anchors>
			<Anchor point="TOP" x="0" y="-135"/>
		</Anchors>
	</Frame>
	<Frame name="StaticPopup2" inherits="StaticPopupTemplate" parent="UIParent" id="2">
		<Anchors>
			<Anchor point="TOP" relativeTo="StaticPopup1" relativePoint="BOTTOM"/>
		</Anchors>
	</Frame>
	<Frame name="StaticPopup3" inherits="StaticPopupTemplate" parent="UIParent" id="3">
		<Anchors>
			<Anchor point="TOP" relativeTo="StaticPopup2" relativePoint="BOTTOM"/>
		</Anchors>
	</Frame>
	<Frame name="StaticPopup4" inherits="StaticPopupTemplate" parent="UIParent" id="4">
		<Anchors>
			<Anchor point="TOP" relativeTo="StaticPopup3" relativePoint="BOTTOM"/>
		</Anchors>
	</Frame>
</Ui>
