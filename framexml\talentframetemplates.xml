<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Texture name="TalentBranchTemplate" file="Interface\TalentFrame\UI-TalentBranches" virtual="true">
		<Size>
			<AbsDimension x="30" y="30"/>
		</Size>
		<Anchors>
			<Anchor point="TOPLEFT"/>
		</Anchors>
	</Texture>
	<Texture name="TalentArrowTemplate" file="Interface\TalentFrame\UI-TalentArrows" virtual="true">
		<Size>
			<AbsDimension x="30" y="30"/>
		</Size>
		<Anchors>
			<Anchor point="TOPLEFT"/>
		</Anchors>
	</Texture>
	<Button name="TalentButtonTemplate" inherits="ItemButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="30" y="30"/>
		</Size>
		<Anchors>
			<Anchor point="TOPLEFT"/>
		</Anchors>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="$parentSlot" inherits="Talent-SingleBorder" parentKey="Slot">
					<Anchors>
						<Anchor point="TOPLEFT" x="-1" y="0"/>
					</Anchors>
				</Texture>
				<Texture name="$parentSlotShadow" inherits="Talent-SingleBorder-Shadow" parentKey="SlotShadow">
					<Anchors>
						<Anchor point="TOPLEFT" x="-4" y="3"/>
					</Anchors>
				</Texture>
				<Texture name="$parentGoldBorder" inherits="Talent-GoldMedal-Border" parentKey="GoldBorder" hidden="true">
					<Anchors>
						<Anchor point="TOPLEFT" x="-7" y="7"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentRankBorder" inherits="Talent-PointBg" parentKey="RankBorder">
					<Anchors>
						<Anchor point="CENTER" relativePoint="BOTTOMRIGHT" x="-3" y="3" />
					</Anchors>
				</Texture>
				<FontString name="$parentRank" inherits="GameFontNormalSmall" parentKey="Rank">
					<Anchors>
						<Anchor point="CENTER" relativeTo="$parentRankBorder">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				_G[self:GetName().."NormalTexture"]:ClearAllPoints();
			</OnLoad>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
	</Button>
</Ui>
