<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="TargetFrame.lua"/>
	<Frame name="TargetofTargetDebuffFrameTemplate" virtual="true" enableMouse="true" hidden="true">
		<Size x="12" y="12"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="$parentIcon" setAllPoints="true"/>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentBorder" file="Interface\Buttons\UI-Debuff-Overlays">
					<Size x="13" y="13"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-1" y="1"/>
						<Anchor point="BOTTOMRIGHT" x="1" y="-1"/>
					</Anchors>
					<TexCoords left="0.296875" right="0.5703125" top="0" bottom="0.515625"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Cooldown name="$parentCooldown" inherits="CooldownFrameTemplate" hideCountdownNumbers="true" reverse="true" drawEdge="true" hidden="true">
				<Anchors>
					<Anchor point="CENTER" x="0" y="-1"/>
				</Anchors>
			</Cooldown>
		</Frames>
		<Scripts>
			<OnEnter>
				if ( self:GetCenter() > GetScreenWidth()/2 ) then
					GameTooltip:SetOwner(self, "ANCHOR_LEFT");
				else
					GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
				end
				local unit = self:GetParent().unit;
				local filter;
				if ( SHOW_DISPELLABLE_DEBUFFS == "1" and UnitCanAssist("player", unit) ) then
					filter = "RAID";
				end
				GameTooltip:SetUnitDebuff(unit, self:GetID(), filter);
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
	</Frame>
	<Frame name="TargetDebuffFrameTemplate" virtual="true" enableMouse="true">
		<Size x="17" y="17"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="$parentIcon" setAllPoints="true"/>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentBorder" file="Interface\Buttons\UI-Debuff-Overlays">
					<Size x="17" y="17"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-1" y="1"/>
						<Anchor point="BOTTOMRIGHT" x="1" y="-1"/>
					</Anchors>
					<TexCoords left="0.296875" right="0.5703125" top="0" bottom="0.515625"/>
				</Texture>
				<FontString name="$parentCount" inherits="NumberFontNormalSmall" justifyH="RIGHT">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="5" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Cooldown name="$parentCooldown" inherits="CooldownFrameTemplate" hideCountdownNumbers="true" reverse="true" drawEdge="true" hidden="true">
				<Anchors>
					<Anchor point="CENTER" x="0" y="-1"/>
				</Anchors>
			</Cooldown>
		</Frames>
		<Scripts>
			<OnUpdate>
				if ( GameTooltip:IsOwned(self) ) then
					GameTooltip:SetUnitDebuff(self.unit, self:GetID());
				end
			</OnUpdate>
			<OnEnter>
				GameTooltip:SetOwner(self, "ANCHOR_BOTTOMRIGHT", 15, -25);
				GameTooltip:SetUnitDebuff(self.unit, self:GetID());
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
	</Frame>
	<Frame name="TargetBuffFrameTemplate" virtual="true" enableMouse="true">
		<Size x="21" y="21"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentIcon" setAllPoints="true"/>
			</Layer>
			<Layer level="ARTWORK">
				<FontString name="$parentCount" inherits="NumberFontNormalSmall" justifyH="RIGHT">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="3" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentStealable" file="Interface\TargetingFrame\UI-TargetingFrame-Stealable" hidden="true" alphaMode="ADD">
					<Size x="24" y="24"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Cooldown name="$parentCooldown" inherits="CooldownFrameTemplate" hideCountdownNumbers="true" reverse="true" drawEdge="true">
				<Anchors>
					<Anchor point="CENTER" x="0" y="-1"/>
				</Anchors>
			</Cooldown>
		</Frames>
		<Scripts>
			<OnUpdate>
				if ( GameTooltip:IsOwned(self) ) then
					GameTooltip:SetUnitBuff(self.unit, self:GetID());
				end
			</OnUpdate>
			<OnEnter>
				GameTooltip:SetOwner(self, "ANCHOR_BOTTOMRIGHT", 15, -25);
				GameTooltip:SetUnitBuff(self.unit, self:GetID());
			</OnEnter>
			<OnLeave>
				GameTooltip:Hide();
			</OnLeave>
		</Scripts>
	</Frame>
	<Button name="TargetFrameTemplate" frameStrata="LOW" toplevel="true" parent="UIParent" inherits="SecureUnitButtonTemplate" virtual="true">
		<Size x="232" y="100"/>
		<HitRectInsets>
			<AbsInset left="0" right="40" top="10" bottom="20"/>
		</HitRectInsets>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentFlash" file="Interface\TargetingFrame\UI-TargetingFrame-Flash" hidden="true">
					<Size x="242" y="93"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-24" y="0"/>
					</Anchors>
					<TexCoords left="0" right="0.9453125" top="0" bottom="0.181640625"/>
				</Texture>
				<Texture name="$parentBackground" parentKey="Background">
					<Size x="119" y="41"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="7" y="35"/>
					</Anchors>
					<Color r="0" g="0" b="0" a="0.5"/>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture name="$parentNameBackground" file="Interface\TargetingFrame\UI-TargetingFrame-LevelBackground" parentKey="nameBackground">
					<Size x="119" y="19"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="-106" y="-22"/>
					</Anchors>
				</Texture>
				<Texture name="$parentPortrait">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="-42" y="-12"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture name="$parentTotalAbsorbBar" inherits="TotalAbsorbBarTemplate"/>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="1">
				<Texture name="$parentMyHealPredictionBar" inherits="MyHealPredictionBarTemplate"/>
				<Texture name="$parentOtherHealPredictionBar" inherits="OtherHealPredictionBarTemplate"/>
				<Texture name="$parentHealAbsorbBar" inherits="HealAbsorbBarTemplate"/>
				<Texture name="$parentHealAbsorbBarLeftShadow" inherits="HealAbsorbBarLeftShadowTemplate"/>
				<Texture name="$parentHealAbsorbBarRightShadow" inherits="HealAbsorbBarRightShadowTemplate"/>
				<Texture name="$parentTotalAbsorbBarOverlay" inherits="TotalAbsorbBarOverlayTemplate"/>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentTextureFrame" parentKey="textureFrame" setAllPoints="true">
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentTexture" parentKey="texture"  file="Interface\TargetingFrame\UI-TargetingFrame">
							<TexCoords left="0.09375" right="1.0" top="0" bottom="0.78125"/>
						</Texture>
						<FontString name="$parentName" inherits="GameFontNormalSmall">
							<Size x="100" y="10"/>
							<Anchors>
								<Anchor point="CENTER" x="-50" y="19"/>
							</Anchors>
						</FontString>
						<FontString name="$parentLevelText" inherits="GameNormalNumberFont" justifyH="CENTER">
							<!--WARNING:: This is re-anchored in code.-->
							<Anchors>
								<Anchor point="CENTER" x="61" y="-17"/>
							</Anchors>
						</FontString>
						<FontString name="$parentDeadText" inherits="GameFontNormalSmall" text="DEAD">
							<Anchors>
								<Anchor point="CENTER" x="-50" y="3"/>
							</Anchors>
						</FontString>
						<FontString name="$parentUnconsciousText" inherits="GameFontNormalSmall" text="UNCONSCIOUS">
							<Anchors>
								<Anchor point="CENTER" x="-50" y="3"/>
							</Anchors>
						</FontString>
						<FontString name="$parentHealthBarText" inherits="TextStatusBarText">
							<Anchors>
								<Anchor point="CENTER" x="-50" y="3"/>
							</Anchors>
						</FontString>
						<FontString name="$parentHealthBarTextLeft" inherits="TextStatusBarText">
							<Anchors>
								<Anchor point="LEFT" x="8" y="3"/>
							</Anchors>
						</FontString>
						<FontString name="$parentHealthBarTextRight" inherits="TextStatusBarText">
							<Anchors>
								<Anchor point="RIGHT" x="-110" y="3"/>
							</Anchors>
						</FontString>
						<FontString name="$parentManaBarText" inherits="TextStatusBarText">
							<Anchors>
								<Anchor point="CENTER" x="-50" y="-8"/>
							</Anchors>
						</FontString>
						<FontString name="$parentManaBarTextLeft" inherits="TextStatusBarText">
							<Anchors>
								<Anchor point="LEFT" x="8" y="-8"/>
							</Anchors>
						</FontString>
						<FontString name="$parentManaBarTextRight" inherits="TextStatusBarText">
							<Anchors>
								<Anchor point="RIGHT" x="-110" y="-8"/>
							</Anchors>
						</FontString>
					</Layer>
					<Layer level="ARTWORK">
						<Texture name="$parentHighLevelTexture" file="Interface\TargetingFrame\UI-TargetingFrame-Skull">
							<Size x="16" y="16"/>
							<Anchors>
								<Anchor point="CENTER" relativeTo="$parentLevelText" x="0" y="0"/>
							</Anchors>
						</Texture>
						<Texture name="$parentLeaderIcon" file="Interface\GroupFrame\UI-Group-LeaderIcon" hidden="true">
							<Size x="16" y="16"/>
							<Anchors>
								<Anchor point="TOPRIGHT" x="-44" y="-10"/>
							</Anchors>
						</Texture>
						<Texture name="$parentPVPIcon" hidden="true">
							<Size x="64" y="64"/>
							<Anchors>
								<Anchor point="TOPRIGHT" x="3" y="-20"/>
							</Anchors>
						</Texture>
						<Texture name="$parentPrestigePortrait" hidden="true">
							<Size x="50" y="52"/>
							<Anchors>
								<Anchor point="TOPRIGHT" x="-15" y="-13"/>
							</Anchors>
						</Texture>
						<Texture name="$parentRaidTargetIcon" file="Interface\TargetingFrame\UI-RaidTargetingIcons" hidden="true">
							<Size x="26" y="26"/>
							<Anchors>
								<Anchor point="CENTER" relativePoint="TOPRIGHT" x="-73" y="-14"/>
							</Anchors>
						</Texture>
						<Texture name="$parentQuestIcon" file="Interface\TargetingFrame\PortraitQuestBadge" hidden="true">
							<Size x="32" y="32"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="-120" y="-12"/>
							</Anchors>
						</Texture>
						<Texture name="$parentPetBattleIcon" file="Interface\TargetingFrame\PetBadge-Beast" hidden="true">
							<Size x="32" y="32"/>
							<Anchors>
								<Anchor point="CENTER" relativePoint="RIGHT" x="-44" y="10"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="ARTWORK" textureSubLevel="1">
						<Texture name="$parentOverAbsorbGlow" inherits="OverAbsorbGlowTemplate"/>
						<Texture name="$parentOverHealAbsorbGlow" inherits="OverHealAbsorbGlowTemplate"/>
						<Texture name="$parentPrestigeBadge" hidden="true">
							<Size x="30" y="30"/>
							<Anchors>
								<Anchor point="CENTER" relativeTo="$parentPrestigePortrait" relativePoint="CENTER"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="$parentDropDown" inherits="UIDropDownMenuTemplate" id="1" hidden="true">
				<Size x="10" y="10"/>
				<Anchors>
					<Anchor point="TOP" x="10" y="-60"/>
				</Anchors>
			</Frame>
			<StatusBar name="$parentHealthBar" inherits="TextStatusBar" useParentLevel="true">
				<Size x="119" y="12"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="-106" y="-41"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						TextStatusBar_Initialize(self);
						self.textLockable = 1;
						self.cvar = "statusText";
						self.cvarLabel = "STATUS_TEXT_TARGET";
						self.zeroText = "";
					</OnLoad>
					<OnValueChanged>
						UnitFrameHealthBar_OnValueChanged(self, value);
						TargetHealthCheck(self, value);
					</OnValueChanged>
					<OnSizeChanged>
						UnitFrameHealPredictionBars_UpdateSize(self:GetParent());
					</OnSizeChanged>
				</Scripts>
				<BarTexture file="Interface\TargetingFrame\UI-StatusBar"/>
			</StatusBar>
			<StatusBar name="$parentManaBar" inherits="TextStatusBar" useParentLevel="true">
				<Size x="119" y="12"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="-106" y="-52"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						TextStatusBar_Initialize(self);
						self.textLockable = 1;
						self.cvar = "statusText";
						self.cvarLabel = "STATUS_TEXT_TARGET";
					</OnLoad>
				</Scripts>
				<BarTexture file="Interface\TargetingFrame\UI-StatusBar"/>
				<BarColor r="0" g="0" b="1.0"/>
			</StatusBar>
			<Frame name="$parentBuffs" parentKey="buffs" hidden="true">
				<Size x="10" y="10"/>
			</Frame>
			<Frame name="$parentDebuffs" parentKey="debuffs" hidden="true">
				<Size x="10" y="10"/>
			</Frame>
			<Frame name="$parentNumericalThreat" hidden="true">
				<Size x="49" y="18"/>
				<Anchors>
					<Anchor point="BOTTOM" relativePoint="TOP" x="-50" y="-22"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString name="$parentValue" inherits="GameFontHighlight" text="100%" parentKey="text">
							<Anchors>
								<Anchor point="TOP" x="0" y="-4"/>
							</Anchors>
						</FontString>
						<Texture name="$parentBG" file="Interface\TargetingFrame\UI-StatusBar" parentKey="bg">
							<Size x="37" y="14"/>
							<Anchors>
								<Anchor point="TOP" x="0" y="-3"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="ARTWORK">
						<Texture file="Interface\TargetingFrame\NumericThreatBorder">
							<TexCoords left="0" right="0.765625" top="0" bottom="0.5625"/>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
		<Scripts>
			<OnEvent function="TargetFrame_OnEvent"/>
			<OnUpdate>
				TargetFrame_OnUpdate(self, elapsed);
				TargetFrame_HealthUpdate(self, elapsed, self.unit);
			</OnUpdate>
			<OnHide function="TargetFrame_OnHide"/>
			<OnEnter function="UnitFrame_OnEnter"/>
			<OnLeave function="UnitFrame_OnLeave"/>
		</Scripts>
	</Button>
	<Button name="BossTargetFrameTemplate" inherits="TargetFrameTemplate" virtual="true">
		<Frames>
			<Frame name="$parentPowerBarAlt" parentKey="powerBarAlt" inherits="UnitPowerBarAltTemplate">
				<Anchors>
					<Anchor point="RIGHT" relativeTo="$parent" relativePoint="LEFT" x="0" y="5"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						UnitPowerBarAlt_Initialize(self, "boss"..self:GetParent():GetID(), 0.5, "INSTANCE_ENCOUNTER_ENGAGE_UNIT");
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				local id = self:GetID();
				if ( id == 1 ) then
					BossTargetFrame_OnLoad(self, "boss1", "INSTANCE_ENCOUNTER_ENGAGE_UNIT");
				else
					BossTargetFrame_OnLoad(self, "boss"..id);
				end
				TargetFrame_CreateSpellbar(self, "INSTANCE_ENCOUNTER_ENGAGE_UNIT", true);
			</OnLoad>
		</Scripts>
	</Button>
	<Button name="TargetofTargetFrameTemplate" movable="true" inherits="SecureUnitButtonTemplate" virtual="true" hidden="true">
		<Size x="93" y="45"/>
		<Anchors>
			<Anchor point="BOTTOMRIGHT" x="-35" y="-10"/>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentBackground" parentKey="background">
					<Size x="46" y="15"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="42" y="13"/>
					</Anchors>
					<Color r="0" g="0" b="0" a="0.5"/>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture name="$parentPortrait">
					<Size x="35" y="35"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="5" y="-5"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentTextureFrame" setAllPoints="true">
				<Layers>
					<Layer level="BORDER">
						<Texture name="$parentTexture" file="Interface\TargetingFrame\UI-TargetofTargetFrame">
							<TexCoords left="0.015625" right="0.7265625" top="0" bottom="0.703125"/>
						</Texture>
						<FontString name="$parentName" inherits="GameFontNormalSmall" justifyH="LEFT">
							<Size x="100" y="10"/>
							<Anchors>
								<Anchor point="BOTTOMLEFT" x="42" y="2"/>
							</Anchors>
						</FontString>
						<FontString name="$parentDeadText" inherits="GameFontNormalSmall" text="DEAD">
							<Anchors>
								<Anchor point="LEFT" x="48" y="1"/>
							</Anchors>
						</FontString>
						<FontString name="$parentUnconsciousText" inherits="GameFontNormalSmall" justifyH="LEFT" text="UNCONSCIOUS">
							<Anchors>
								<Anchor point="LEFT" x="48" y="1"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad function="RaiseFrameLevel"/>
				</Scripts>
			</Frame>
			<StatusBar name="$parentHealthBar" inherits="TextStatusBar">
				<Size x="46" y="7"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="-2" y="-15"/>
				</Anchors>
				<Scripts>
					<OnValueChanged>
						TargetofTargetHealthCheck(self:GetParent());
					</OnValueChanged>
				</Scripts>
				<BarTexture file="Interface\TargetingFrame\UI-StatusBar"/>
				<BarColor r="0" g="1.0" b="0"/>
			</StatusBar>
			<StatusBar name="$parentManaBar" inherits="TextStatusBar">
				<Size x="46" y="7"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="-2" y="-23"/>
				</Anchors>
				<BarTexture file="Interface\TargetingFrame\UI-StatusBar"/>
				<BarColor r="0" g="0" b="1.0"/>
			</StatusBar>
			<Frame name="$parentDebuff1" inherits="TargetofTargetDebuffFrameTemplate" id="1">
				<Anchors>
					<Anchor point="TOPLEFT" relativePoint="TOPRIGHT" x="4" y="-10"/>
				</Anchors>
			</Frame>
			<Frame name="$parentDebuff2" inherits="TargetofTargetDebuffFrameTemplate" id="2">
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentDebuff1" relativePoint="RIGHT" x="1" y="0"/>
				</Anchors>
			</Frame>
			<Frame name="$parentDebuff3" inherits="TargetofTargetDebuffFrameTemplate" id="3">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentDebuff1" relativePoint="BOTTOMLEFT" x="0" y="-1"/>
				</Anchors>
			</Frame>
			<Frame name="$parentDebuff4" inherits="TargetofTargetDebuffFrameTemplate" id="4">
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentDebuff3" relativePoint="RIGHT" x="1" y="0"/>
				</Anchors>
			</Frame>
		</Frames>
		<Scripts>
			<OnShow>
				TargetFrame_UpdateAuras(self:GetParent());
			</OnShow>
			<OnHide function="TargetofTarget_OnHide"/>
			<OnEvent function="UnitFrame_OnEvent"/>
			<OnUpdate function="TargetofTarget_Update"/>
		</Scripts>
	</Button>
	<StatusBar name="TargetSpellBarTemplate" inherits="SmallCastingBarFrameTemplate" hidden="true" virtual="true">
		<Size x="150" y="10"/>
		<Scripts>
			<OnShow function="Target_Spellbar_AdjustPosition"/>
			<OnEvent function="Target_Spellbar_OnEvent"/>
		</Scripts>
	</StatusBar>
	<StatusBar name="BossSpellBarTemplate" inherits="SmallCastingBarFrameTemplate" hidden="true" virtual="true">
		<Size x="120" y="10"/>
		<Scripts>
			<OnEvent function="Target_Spellbar_OnEvent"/>
		</Scripts>
	</StatusBar>

	<Button name="TargetFrame" inherits="TargetFrameTemplate" movable="true">
		<!-- This frame gets positioned in UIParent_UpdateTopFramePositions() -->
		<Frames>
			<Frame name="$parentPowerBarAlt" parentKey="powerBarAlt" inherits="UnitPowerBarAltTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parent" relativePoint="RIGHT" x="-25" y="5"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						UnitPowerBarAlt_Initialize(self, "target", 0.5, "PLAYER_TARGET_CHANGED");
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self.noTextPrefix = true;
				self.showLevel = true;
				self.showPVP = true;
				self.showLeader = true;
				self.showThreat = true;
				self.showPortrait = true;
				self.showClassification = true;
				self.showAuraCount = true;
				self:SetHitRectInsets(96, 40, 10, 9);		-- allows mouseover over health and mana bars
				TargetFrame_OnLoad(self, "target", TargetFrameDropDown_Initialize);
				TargetFrame_CreateSpellbar(self, "PLAYER_TARGET_CHANGED");
				TargetFrame_CreateTargetofTarget(self, "targettarget");
				self:RegisterEvent("PLAYER_TARGET_CHANGED");
				self.threatNumericIndicator:SetScript("OnShow", function() TargetFrame_UpdateAuras(self) end);
				self.threatNumericIndicator:SetScript("OnHide", function() TargetFrame_UpdateAuras(self) end);
				UIParent_UpdateTopFramePositions();
			</OnLoad>
			<OnDragStart function="TargetFrame_OnDragStart"/>
			<OnDragStop function="TargetFrame_OnDragStop"/>
		</Scripts>
	</Button>
	<Button name="FocusFrame" movable="true" inherits="TargetFrameTemplate" clampedToScreen="true">
		<Anchors>
			<Anchor point="TOPLEFT" x="250" y="-240"/>
		</Anchors>
		<Frames>
			<Frame name="$parentPowerBarAlt" parentKey="powerBarAlt" inherits="UnitPowerBarAltTemplate">
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parent" relativePoint="RIGHT" x="-25" y="5"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						UnitPowerBarAlt_Initialize(self, "focus", 0.5, "PLAYER_FOCUS_CHANGED");
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self.noTextPrefix = true;
				self.showLevel = true;
				self.showPVP = true;
				self.showLeader = true;
				self.showThreat = true;
				self.showPortrait = true;
				self.showClassification = true;
				self.showAuraCount = true;
				TargetFrame_OnLoad(self, "focus", FocusFrameDropDown_Initialize);
				TargetFrame_CreateSpellbar(self, "PLAYER_FOCUS_CHANGED");
				TargetFrame_CreateTargetofTarget(self, "focus-target");
				self:RegisterEvent("PLAYER_FOCUS_CHANGED");
				self:RegisterForDrag("LeftButton");
			</OnLoad>
			<OnDragStart function="FocusFrame_OnDragStart"/>
			<OnDragStop function="FocusFrame_OnDragStop"/>
		</Scripts>
	</Button>
	<Button name="Boss1TargetFrame" inherits="BossTargetFrameTemplate" id="1">
		<Anchors>
			<Anchor point="TOPRIGHT" x="55" y="-236"/>
		</Anchors>
	</Button>
	<Button name="Boss2TargetFrame" inherits="BossTargetFrameTemplate" id="2">
		<Anchors>
			<Anchor point="TOPLEFT" relativeTo="Boss1TargetFrame" relativePoint="BOTTOMLEFT" x="0" y="-30"/>
		</Anchors>
	</Button>
	<Button name="Boss3TargetFrame" inherits="BossTargetFrameTemplate" id="3">
		<Anchors>
			<Anchor point="TOPLEFT" relativeTo="Boss2TargetFrame" relativePoint="BOTTOMLEFT" x="0" y="-30"/>
		</Anchors>
	</Button>
	<Button name="Boss4TargetFrame" inherits="BossTargetFrameTemplate" id="4">
		<Anchors>
			<Anchor point="TOPLEFT" relativeTo="Boss3TargetFrame" relativePoint="BOTTOMLEFT" x="0" y="-30"/>
		</Anchors>
	</Button>
	<Button name="Boss5TargetFrame" inherits="BossTargetFrameTemplate" id="5">
		<Anchors>
			<Anchor point="TOPLEFT" relativeTo="Boss4TargetFrame" relativePoint="BOTTOMLEFT" x="0" y="-30"/>
		</Anchors>
	</Button>
</Ui>
