<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="TradeFrame.lua"/>
	<Frame name="TradeHighlightTemplate" hidden="false" virtual="true">
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentTop" file="Interface\TradeFrame\UI-TradeFrame-Highlight">
					<Size x="161" y="16"/>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.62890625" top="0" bottom="0.0625"/>
				</Texture>
				<Texture name="$parentBottom" file="Interface\TradeFrame\UI-TradeFrame-Highlight">
					<Size x="161" y="16"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.62890625" top="0.9375" bottom="1.0"/>
				</Texture>
				<Texture name="$parentMiddle" file="Interface\TradeFrame\UI-TradeFrame-Highlight">
					<Size x="161" y="10"/>
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentTop" relativePoint="BOTTOM"/>
						<Anchor point="BOTTOM" relativeTo="$parentBottom" relativePoint="TOP"/>
					</Anchors>
					<TexCoords left="0" right="0.62890625" top="0.0625" bottom="0.9375"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>
	<Frame name="TradeItemTemplate" virtual="true">
		<Size>
			<AbsDimension x="153" y="37"/>
		</Size>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentSlotTexture" parentKey="SlotTexture" file="Interface\Buttons\UI-EmptySlot">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-13" y="13"/>
					</Anchors>
				</Texture>
				<Texture name="$parentNameFrame" file="Interface\QuestFrame\UI-QuestItemNameFrame">
					<Size x="124" y="64"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentSlotTexture" relativePoint="RIGHT" x="-20" y="0"/>
					</Anchors>
				</Texture>
				<FontString name="$parentName" inherits="GameFontNormalSmall" text="Item Name" justifyH="LEFT">
					<Size x="90" y="30"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentNameFrame" x="15" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Frame>
	<Frame name="RecipientTradeItemTemplate" inherits="TradeItemTemplate" virtual="true">
		<Frames>
			<Button name="$parentItemButton" inherits="ItemButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parent"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						RaiseFrameLevelByTwo(self);
					</OnLoad>
					<OnClick>
						if ( IsModifiedClick() ) then
							HandleModifiedItemClick(GetTradeTargetItemLink(self:GetParent():GetID()));
						else
							ClickTargetTradeButton(self:GetParent():GetID());
						end
					</OnClick>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						local speciesID, level, breedQuality, maxHealth, power, speed, name = GameTooltip:SetTradeTargetItem(self:GetParent():GetID());
						if(speciesID and speciesID > 0) then
							BattlePetToolTip_Show(speciesID, level, breedQuality, maxHealth, power, speed, name);
						end
						CursorUpdate(self);
					</OnEnter>
					<OnLeave>
						GameTooltip_Hide();
						ResetCursor();
					</OnLeave>
					<OnUpdate>
						if ( GameTooltip:IsOwned(self) ) then
							GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
							local speciesID, level, breedQuality, maxHealth, power, speed, name = GameTooltip:SetTradeTargetItem(self:GetParent():GetID());
							if(speciesID and speciesID > 0) then
								BattlePetToolTip_Show(speciesID, level, breedQuality, maxHealth, power, speed, name);
							end
						end
						CursorOnUpdate(self);
					</OnUpdate>
				</Scripts>
			</Button>
		</Frames>
	</Frame>
	<Frame name="PlayerTradeItemTemplate" inherits="TradeItemTemplate" virtual="true">
		<Frames>
			<Button name="$parentItemButton" inherits="ItemButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parent"/>
				</Anchors>
				<Scripts>
					<OnClick>
						if ( IsModifiedClick() ) then
							HandleModifiedItemClick(GetTradePlayerItemLink(self:GetParent():GetID()));
						else
							ClickTradeButton(self:GetParent():GetID(), button == "RightButton");
						end
					</OnClick>
					<OnDragStart>
						ClickTradeButton(self:GetParent():GetID());
					</OnDragStart>
					<OnReceiveDrag>
						ClickTradeButton(self:GetParent():GetID());
					</OnReceiveDrag>
					<OnLoad>
						self:RegisterForDrag("LeftButton");
						self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
						self:SetFrameLevel(self:GetFrameLevel() + 2);
					</OnLoad>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						local speciesID, level, breedQuality, maxHealth, power, speed, name = GameTooltip:SetTradePlayerItem(self:GetParent():GetID());
						if(speciesID and speciesID > 0) then
							BattlePetToolTip_Show(speciesID, level, breedQuality, maxHealth, power, speed, name);
						end
						CursorUpdate(self);
					</OnEnter>
					<OnLeave>
						GameTooltip_Hide();
						ResetCursor();
					</OnLeave>
					<OnUpdate>
						if ( GameTooltip:IsOwned(self) ) then
							GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
							local speciesID, level, breedQuality, maxHealth, power, speed, name = GameTooltip:SetTradePlayerItem(self:GetParent():GetID());
							if(speciesID and speciesID > 0) then
								BattlePetToolTip_Show(speciesID, level, breedQuality, maxHealth, power, speed, name);
							end
						end
						CursorOnUpdate(self);
					</OnUpdate>
				</Scripts>
			</Button>
		</Frames>
	</Frame>

	<Frame name="TradeFrame" inherits="ButtonFrameTemplate" toplevel="true" parent="UIParent" movable="true" enableMouse="true" hidden="true">
		<Size x="344" y="446"/>
		<Layers>
			<Layer level="OVERLAY" textureSubLevel="-1">
				<Texture name="TradeFramePlayerPortrait">
					<Size x="60" y="60"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-5" y="5"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="1">
				<Texture name="TradeFrameRecipientPortrait">
					<Size x="60" y="60"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="TradeFrame" relativePoint="TOPRIGHT" x="-180" y="7"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="2">
				<Texture name="TradeRecipientPortraitFrame" inherits="UI-Frame-Portrait" parentKey="portraitFrame">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="TradeFrameRecipientPortrait" x="-6" y="4"/>
					</Anchors>
				</Texture>
				<Texture name="TradeRecipientBotLeftCorner" inherits="UI-Frame-BotCornerLeft">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="TradeFrame" relativePoint="BOTTOMRIGHT" x="-178" y="-3"/>
					</Anchors>
				</Texture>
				<Texture name="TradeRecipientLeftBorder" inherits="!UI-Frame-LeftTile" parentKey="leftBorderBar">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="TradeRecipientPortraitFrame" relativePoint="BOTTOMLEFT" x="8" y="0" />
						<Anchor point="BOTTOMLEFT" relativeTo="TradeRecipientBotLeftCorner" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture name="TradeRecipientBG" >
					<Color r="1" g="1" b="1" a=".15"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="TradeFrame" relativePoint="TOPRIGHT" x="-172" y="-20"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="TradeFrame" />
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString name="TradeFrameRecipientNameText" inherits="GameFontNormal" text="Player Name">
					<Size x="80" y="12"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="TradeFrame" x="230" y="-5"/>
					</Anchors>
				</FontString>
				<FontString name="TradeFramePlayerNameText" inherits="GameFontNormal" text="Recipient Name">
					<Size x="100" y="12"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="TradeFrame" x="65" y="-5"/>
					</Anchors>
				</FontString>
				<FontString name="TradeFramePlayerEnchantText" inherits="GameFontHighlightSmall" text="TRADEFRAME_ENCHANT_SLOT_LABEL">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="TradeFrame" x="15" y="-360"/>
					</Anchors>
				</FontString>
				<FontString name="TradeFrameRecipientEnchantText" inherits="GameFontHighlightSmall" text="TRADEFRAME_ENCHANT_SLOT_LABEL">
					<Anchors>
						<Anchor point="LEFT" relativeTo="TradeFramePlayerEnchantText" x="166" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="TradeHighlightPlayer" inherits="TradeHighlightTemplate">
				<Size x="150" y="266"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradeFrame" x="6" y="-85"/>
				</Anchors>
			</Frame>
			<Frame name="TradeHighlightRecipient" inherits="TradeHighlightTemplate">
				<Size x="150" y="266"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="176" y="-85"/>
				</Anchors>
			</Frame>
			<Frame name="TradeHighlightPlayerEnchant" inherits="TradeHighlightTemplate">
				<Size x="150" y="61"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradeHighlightPlayer" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
				</Anchors>
			</Frame>
			<Frame name="TradeHighlightRecipientEnchant" inherits="TradeHighlightTemplate">
				<Size x="150" y="61"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradeHighlightRecipient" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
				</Anchors>
			</Frame>
			<Frame name="TradeRecipientItemsInset" inherits="InsetFrameTemplate" parentKey="LeftInset" useParentLevel="true">
				<Anchors>
					<Anchor point="TOPLEFT" x="175" y="-83"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="TradeFrame" relativePoint="TOPLEFT" x="338" y="-352"/>
				</Anchors>
			</Frame>
			<Frame name="TradeRecipientItem1" inherits="RecipientTradeItemTemplate" id="1">
				<Anchors>
					<Anchor point="TOPLEFT" x="182" y="-89"/>
				</Anchors>
			</Frame>
			<Frame name="TradeRecipientItem2" inherits="RecipientTradeItemTemplate" id="2">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradeRecipientItem1" relativePoint="BOTTOMLEFT" x="0" y="-7"/>
				</Anchors>
			</Frame>
			<Frame name="TradeRecipientItem3" inherits="RecipientTradeItemTemplate" id="3">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradeRecipientItem2" relativePoint="BOTTOMLEFT" x="0" y="-7"/>
				</Anchors>
			</Frame>
			<Frame name="TradeRecipientItem4" inherits="RecipientTradeItemTemplate" id="4">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradeRecipientItem3" relativePoint="BOTTOMLEFT" x="0" y="-7"/>
				</Anchors>
			</Frame>
			<Frame name="TradeRecipientItem5" inherits="RecipientTradeItemTemplate" id="5">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradeRecipientItem4" relativePoint="BOTTOMLEFT" x="0" y="-7"/>
				</Anchors>
			</Frame>
			<Frame name="TradeRecipientItem6" inherits="RecipientTradeItemTemplate" id="6">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradeRecipientItem5" relativePoint="BOTTOMLEFT" x="0" y="-7"/>
				</Anchors>
			</Frame>
			<Frame name="TradeRecipientEnchantInset" inherits="InsetFrameTemplate" parentKey="LeftInset" useParentLevel="true">
				<Anchors>
					<Anchor point="TOPLEFT" x="175" y="-354"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="TradeFrame" relativePoint="TOPLEFT" x="338" y="-418"/>
				</Anchors>
			</Frame>
			<Frame name="TradeRecipientItem7" inherits="RecipientTradeItemTemplate" id="7">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradeRecipientItem6" relativePoint="BOTTOMLEFT" x="0" y="-28"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture file="Interface\TradeFrame\UI-TradeFrame-EnchantIcon">
							<Size x="62" y="62"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="TradePlayerItemsInset" inherits="InsetFrameTemplate" parentKey="LeftInset" useParentLevel="true">
				<Anchors>
					<Anchor point="TOPLEFT" x="4" y="-83"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="TradeFrame" relativePoint="TOPLEFT" x="166" y="-352"/>
				</Anchors>
			</Frame>
			<Frame name="TradePlayerItem1" inherits="PlayerTradeItemTemplate" id="1">
				<Anchors>
					<Anchor point="TOPLEFT" x="14" y="-89"/>
				</Anchors>
			</Frame>
			<Frame name="TradePlayerItem2" inherits="PlayerTradeItemTemplate" id="2">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradePlayerItem1" relativePoint="BOTTOMLEFT" x="0" y="-7"/>
				</Anchors>
			</Frame>
			<Frame name="TradePlayerItem3" inherits="PlayerTradeItemTemplate" id="3">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradePlayerItem2" relativePoint="BOTTOMLEFT" x="0" y="-7"/>
				</Anchors>
			</Frame>
			<Frame name="TradePlayerItem4" inherits="PlayerTradeItemTemplate" id="4">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradePlayerItem3" relativePoint="BOTTOMLEFT" x="0" y="-7"/>
				</Anchors>
			</Frame>
			<Frame name="TradePlayerItem5" inherits="PlayerTradeItemTemplate" id="5">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradePlayerItem4" relativePoint="BOTTOMLEFT" x="0" y="-7"/>
				</Anchors>
			</Frame>
			<Frame name="TradePlayerItem6" inherits="PlayerTradeItemTemplate" id="6">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradePlayerItem5" relativePoint="BOTTOMLEFT" x="0" y="-7"/>
				</Anchors>
			</Frame>
			<Frame name="TradePlayerEnchantInset" inherits="InsetFrameTemplate" parentKey="LeftInset" useParentLevel="true">
				<Anchors>
					<Anchor point="TOPLEFT" x="4" y="-354"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="TradeFrame" relativePoint="TOPLEFT" x="166" y="-418"/>
				</Anchors>
			</Frame>
			<Frame name="TradePlayerItem7" inherits="PlayerTradeItemTemplate" id="7">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradePlayerItem6" relativePoint="BOTTOMLEFT" x="0" y="-28"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture file="Interface\TradeFrame\UI-TradeFrame-EnchantIcon">
							<Size x="62" y="62"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						if ( self.hasItem ) then
							GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
							local speciesID, level, breedQuality, maxHealth, power, speed, name = GameTooltip:SetTradePlayerItem(self:GetParent():GetID());
							if(speciesID and speciesID > 0) then
								BattlePetToolTip_Show(speciesID, level, breedQuality, maxHealth, power, speed, name);
							end
						else
							GameTooltip_AddNewbieTip(self, ENCHANT_SLOT, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_ENCHANTSLOT, 1);
						end
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Frame>
			<Button name="TradeFrameTradeButton" inherits="UIPanelButtonTemplate" text="TRADE">
				<Size x="85" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" relativeTo="TradeFrame" relativePoint="BOTTOMRIGHT" x="-85" y="5"/>
				</Anchors>
				<Scripts>
					<OnClick function="AcceptTrade"/>
				</Scripts>
				<ButtonText text="TRADE"/>
				<NormalFont style="GameFontNormal"/>
				<HighlightFont style="GameFontHighlight"/>
				<DisabledFont style="GameFontDisable"/>
			</Button>
			<Button name="TradeFrameCancelButton" inherits="UIPanelButtonTemplate" text="CANCEL">
				<Size x="77" y="22"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="TradeFrameTradeButton" relativePoint="TOPRIGHT" x="3" y="0"/>
				</Anchors>
				<Scripts>
					<OnClick function="TradeFrameCancelButton_OnClick"/>
				</Scripts>
				<ButtonText text="CANCEL"/>
				<NormalFont style="GameFontNormal"/>
				<HighlightFont style="GameFontHighlight"/>
				<DisabledFont style="GameFontDisable"/>
			</Button>
			<Frame name="TradePlayerInputMoneyInset" inherits="InsetFrameTemplate" parentKey="LeftInset" useParentLevel="true">
				<Anchors>
					<Anchor point="TOPLEFT" x="4" y="-58"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="TradeFrame" relativePoint="TOPLEFT" x="166" y="-82"/>
				</Anchors>
			</Frame>
			<Frame name="TradePlayerInputMoneyFrame" inherits="MoneyInputFrameTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" x="11" y="-61"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						MoneyInputFrame_SetCompact(self, 62, 7);
						MoneyInputFrame_SetOnValueChangedFunc(self, TradeFrame_UpdateMoney);
						self:RegisterEvent("PLAYER_TRADE_MONEY");
					</OnLoad>
					<OnEvent>
						MoneyInputFrame_SetCopper(self, GetPlayerTradeMoney());
					</OnEvent>
				</Scripts>
			</Frame>
			<Frame name="TradeRecipientMoneyInset" inherits="InsetFrameTemplate" parentKey="LeftInset" useParentLevel="true">
				<Anchors>
					<Anchor point="TOPLEFT" x="175" y="-58"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="TradeFrame" relativePoint="TOPLEFT" x="338" y="-81"/>
				</Anchors>
			</Frame>
			<Frame name="TradeRecipientMoneyBg" inherits="ThinGoldEdgeTemplate" >
				<Size x="120" y="30"/>
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="TradeFrame" relativePoint="TOPRIGHT" x="-7" y="-60"/>
					<Anchor point="BOTTOMLEFT" relativeTo="TradeFrame" relativePoint="TOPRIGHT" x="-168" y="-80"/>
				</Anchors>
			</Frame>
			<Frame name="TradeRecipientMoneyFrame" inherits="SmallMoneyFrameTemplate">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="TradeFrame" relativePoint="TOPRIGHT" x="-5" y="-64"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						SmallMoneyFrame_OnLoad(self);
						MoneyFrame_SetType(self, "TARGET_TRADE");
						MoneyFrame_SetMaxDisplayWidth(self, 160);
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnEvent function="TradeFrame_OnEvent"/>
			<OnShow>
				TradeFrame_OnShow(self);
				PlaySound(SOUNDKIT.IG_CHARACTER_INFO_OPEN);
			</OnShow>
			<OnHide>
				TradeFrame_OnHide();
				PlaySound(SOUNDKIT.IG_CHARACTER_INFO_CLOSE);
			</OnHide>
			<OnLoad function="TradeFrame_OnLoad"/>
			<OnMouseUp function="TradeFrame_OnMouseUp"/>
		</Scripts>
	</Frame>
</Ui>