<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2005 sp2 U (http://www.altova.com) by <PERSON> (Blizzard Entertainment) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.blizzard.com/wow/ui/" xmlns:ui="http://www.blizzard.com/wow/ui/" targetNamespace="http://www.blizzard.com/wow/ui/" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:simpleType name="FRAMEPOINT">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="TOPLEFT"/>
			<xs:enumeration value="TOPRIGHT"/>
			<xs:enumeration value="BOTTOMLEFT"/>
			<xs:enumeration value="BOTTOMRIGHT"/>
			<xs:enumeration value="TOP"/>
			<xs:enumeration value="BOTTOM"/>
			<xs:enumeration value="LEFT"/>
			<xs:enumeration value="RIGHT"/>
			<xs:enumeration value="CENTER"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="FRAMESTRATA">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="PARENT"/>
			<xs:enumeration value="BACKGROUND"/>
			<xs:enumeration value="LOW"/>
			<xs:enumeration value="MEDIUM"/>
			<xs:enumeration value="HIGH"/>
			<xs:enumeration value="DIALOG"/>
			<xs:enumeration value="FULLSCREEN"/>
			<xs:enumeration value="FULLSCREEN_DIALOG"/>
			<xs:enumeration value="TOOLTIP"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="DRAWLAYER">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="BACKGROUND"/>
			<xs:enumeration value="BORDER"/>
			<xs:enumeration value="ARTWORK"/>
			<xs:enumeration value="OVERLAY"/>
			<xs:enumeration value="HIGHLIGHT"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ALPHAMODE">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="DISABLE"/>
			<xs:enumeration value="BLEND"/>
			<xs:enumeration value="ALPHAKEY"/>
			<xs:enumeration value="ADD"/>
			<xs:enumeration value="MOD"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="OUTLINETYPE">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="NONE"/>
			<xs:enumeration value="NORMAL"/>
			<xs:enumeration value="THICK"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="JUSTIFYVTYPE">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="TOP"/>
			<xs:enumeration value="MIDDLE"/>
			<xs:enumeration value="BOTTOM"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="JUSTIFYHTYPE">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="LEFT"/>
			<xs:enumeration value="CENTER"/>
			<xs:enumeration value="RIGHT"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="INSERTMODE">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="TOP"/>
			<xs:enumeration value="BOTTOM"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ORIENTATION">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="HORIZONTAL"/>
			<xs:enumeration value="VERTICAL"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ATTRIBUTETYPE">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="nil"/>
			<xs:enumeration value="boolean"/>
			<xs:enumeration value="number"/>
			<xs:enumeration value="string"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="KEYVALUETYPE">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="nil"/>
			<xs:enumeration value="boolean"/>
			<xs:enumeration value="number"/>
			<xs:enumeration value="string"/>
			<xs:enumeration value="global"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="SCRIPTINHERITTYPE">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="prepend"/>
			<xs:enumeration value="append"/>
			<xs:enumeration value="none"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="SCRIPTINTRINSICORDERTYPE">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="precall"/>
			<xs:enumeration value="postcall"/>
			<xs:enumeration value="none"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="FONTALPHABET">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="roman"/>
			<xs:enumeration value="korean"/>
			<xs:enumeration value="simplifiedchinese"/>
			<xs:enumeration value="traditionalchinese"/>
			<xs:enumeration value="russian"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="AbsValue">
		<xs:complexType>
			<xs:attribute name="val" type="xs:int" use="required"/>
		</xs:complexType>
	</xs:element>

	<xs:element name="RelValue">
		<xs:complexType>
			<xs:attribute name="val" type="xs:float" use="required"/>
		</xs:complexType>
	</xs:element>

	<xs:complexType name="Value">
		<xs:choice minOccurs="0">
			<xs:element ref="AbsValue"/>
			<xs:element ref="RelValue"/>
		</xs:choice>
		<xs:attribute name="val" type="xs:int" use="optional"/>
	</xs:complexType>

	<xs:element name="AbsDimension">
		<xs:complexType>
			<xs:attribute name="x" type="xs:float" use="required"/>
			<xs:attribute name="y" type="xs:float" use="required"/>
		</xs:complexType>
	</xs:element>

	<xs:element name="RelDimension">
		<xs:complexType>
			<xs:attribute name="x" type="xs:float" use="required"/>
			<xs:attribute name="y" type="xs:float" use="required"/>
		</xs:complexType>
	</xs:element>

	<xs:complexType name="Dimension">
		<xs:choice minOccurs="0">
			<xs:element ref="AbsDimension"/>
			<xs:element ref="RelDimension"/>
		</xs:choice>
		<xs:attribute name="x" type="xs:float" use="optional"/>
		<xs:attribute name="y" type="xs:float" use="optional"/>
	</xs:complexType>

	<xs:element name="AbsInset">
		<xs:complexType>
			<xs:attribute name="left" type="xs:float" use="optional"/>
			<xs:attribute name="right" type="xs:float" use="optional"/>
			<xs:attribute name="top" type="xs:float" use="optional"/>
			<xs:attribute name="bottom" type="xs:float" use="optional"/>
		</xs:complexType>
	</xs:element>

	<xs:element name="RelInset">
		<xs:complexType>
			<xs:attribute name="left" type="xs:float" use="required"/>
			<xs:attribute name="right" type="xs:float" use="required"/>
			<xs:attribute name="top" type="xs:float" use="required"/>
			<xs:attribute name="bottom" type="xs:float" use="required"/>
		</xs:complexType>
	</xs:element>

	<xs:complexType name="Inset">
		<xs:choice minOccurs="0">
			<xs:element ref="AbsInset"/>
			<xs:element ref="RelInset"/>
		</xs:choice>
		<xs:attribute name="left" type="xs:int" use="optional"/>
		<xs:attribute name="right" type="xs:int" use="optional"/>
		<xs:attribute name="top" type="xs:int" use="optional"/>
		<xs:attribute name="bottom" type="xs:int" use="optional"/>
	</xs:complexType>

	<xs:simpleType name="ColorFloat">
		<xs:restriction base="xs:float">
			<xs:minInclusive value="0.0"/>
			<xs:maxInclusive value="1.0"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="ColorType">
		<xs:attribute name="r" type="ColorFloat" use="required"/>
		<xs:attribute name="g" type="ColorFloat" use="required"/>
		<xs:attribute name="b" type="ColorFloat" use="required"/>
		<xs:attribute name="a" type="ColorFloat" default="1.0"/>
	</xs:complexType>

	<xs:complexType name="ShadowType">
		<xs:sequence minOccurs="0">
			<xs:choice maxOccurs="unbounded">
				<xs:element name="Color" type="ColorType"/>
				<xs:element name="Offset" type="Dimension"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="GradientType">
		<xs:sequence>
			<xs:element name="MinColor" type="ColorType"/>
			<xs:element name="MaxColor" type="ColorType"/>
		</xs:sequence>
		<xs:attribute name="orientation" type="ORIENTATION" default="HORIZONTAL"/>
	</xs:complexType>

	<xs:complexType name="KeyValueType">
		<xs:attribute name="key" type="xs:string" use="required"/>
		<xs:attribute name="value" type="xs:string" use="required"/>
		<xs:attribute name="keyType" type="KEYVALUETYPE" use="optional" default="string"/>
		<xs:attribute name="type" type="KEYVALUETYPE" use="optional" default="string"/>
	</xs:complexType>

	<xs:complexType name="KeyValuesType">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="KeyValue" type="KeyValueType"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="AttributeType">
		<xs:attribute name="name" type="xs:string" use="required"/>
		<xs:attribute name="type" type="ATTRIBUTETYPE" use="optional" default="string"/>
		<xs:attribute name="value" type="xs:string" use="optional"/>
	</xs:complexType>

	<xs:complexType name="AttributesType">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="Attribute" type="AttributeType"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="ButtonStyleType">
		<xs:attribute name="style" type="xs:string" use="required"/>
	</xs:complexType>

	<xs:complexType name="ScriptsType">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="OnLoad" type="ScriptType"/>
				<xs:element name="OnAttributeChanged" type="ScriptType"/>
				<xs:element name="OnSizeChanged" type="ScriptType"/>
				<xs:element name="OnEvent" type="ScriptType"/>
				<xs:element name="OnUpdate" type="ScriptType"/>
				<xs:element name="OnShow" type="ScriptType"/>
				<xs:element name="OnHide" type="ScriptType"/>
				<xs:element name="OnEnter" type="ScriptType"/>
				<xs:element name="OnLeave" type="ScriptType"/>
				<xs:element name="OnMouseDown" type="ScriptType"/>
				<xs:element name="OnMouseUp" type="ScriptType"/>
				<xs:element name="OnMouseWheel" type="ScriptType"/>
				<xs:element name="OnJoystickStickMotion" type="ScriptType"/>
				<xs:element name="OnJoystickAxisMotion" type="ScriptType"/>
				<xs:element name="OnJoystickButtonDown" type="ScriptType"/>
				<xs:element name="OnJoystickButtonUp" type="ScriptType"/>
				<xs:element name="OnJoystickHatMotion" type="ScriptType"/>
				<xs:element name="OnDragStart" type="ScriptType"/>
				<xs:element name="OnDragStop" type="ScriptType"/>
				<xs:element name="OnReceiveDrag" type="ScriptType"/>
				<xs:element name="PreClick" type="ScriptType"/>
				<xs:element name="OnClick" type="ScriptType"/>
				<xs:element name="PostClick" type="ScriptType"/>
				<xs:element name="OnDoubleClick" type="ScriptType"/>
				<xs:element name="OnValueChanged" type="ScriptType"/>
				<xs:element name="OnMinMaxChanged" type="ScriptType"/>
				<xs:element name="OnUpdateModel" type="ScriptType"/>
				<xs:element name="OnModelLoaded" type="ScriptType"/>
				<xs:element name="OnAnimFinished" type="ScriptType"/>
				<xs:element name="OnEnterPressed" type="ScriptType"/>
				<xs:element name="OnEscapePressed" type="ScriptType"/>
				<xs:element name="OnSpacePressed" type="ScriptType"/>
				<xs:element name="OnTabPressed" type="ScriptType"/>
				<xs:element name="OnTextChanged" type="ScriptType"/>
				<xs:element name="OnTextSet" type="ScriptType"/>
				<xs:element name="OnCursorChanged" type="ScriptType"/>
				<xs:element name="OnInputLanguageChanged" type="ScriptType"/>
				<xs:element name="OnEditFocusGained" type="ScriptType"/>
				<xs:element name="OnEditFocusLost" type="ScriptType"/>
				<xs:element name="OnHorizontalScroll" type="ScriptType"/>
				<xs:element name="OnVerticalScroll" type="ScriptType"/>
				<xs:element name="OnScrollRangeChanged" type="ScriptType"/>
				<xs:element name="OnCharComposition" type="ScriptType"/>
				<xs:element name="OnChar" type="ScriptType"/>
				<xs:element name="OnKeyDown" type="ScriptType"/>
				<xs:element name="OnKeyUp" type="ScriptType"/>
				<xs:element name="OnColorSelect" type="ScriptType"/>
				<xs:element name="OnHyperlinkEnter" type="ScriptType"/>
				<xs:element name="OnHyperlinkLeave" type="ScriptType"/>
				<xs:element name="OnHyperlinkClick" type="ScriptType"/>
				<xs:element name="OnMessageScrollChanged" type="ScriptType"/>
				<xs:element name="OnMovieFinished" type="ScriptType"/>
				<xs:element name="OnMovieShowSubtitle" type="ScriptType"/>
				<xs:element name="OnMovieHideSubtitle" type="ScriptType"/>
				<xs:element name="OnTooltipSetDefaultAnchor" type="ScriptType"/>
				<xs:element name="OnTooltipCleared" type="ScriptType"/>
				<xs:element name="OnTooltipAddMoney" type="ScriptType"/>
				<xs:element name="OnTooltipSetUnit" type="ScriptType"/>
				<xs:element name="OnTooltipSetItem" type="ScriptType"/>
				<xs:element name="OnTooltipSetSpell" type="ScriptType"/>
				<xs:element name="OnTooltipSetQuest" type="ScriptType"/>
				<xs:element name="OnTooltipSetAchievement" type="ScriptType"/>
				<xs:element name="OnTooltipSetFramestack" type="ScriptType"/>
				<xs:element name="OnTooltipSetEquipmentSet" type="ScriptType"/>
				<xs:element name="OnEnable" type="ScriptType"/>
				<xs:element name="OnDisable" type="ScriptType"/>
				<xs:element name="OnArrowPressed" type="ScriptType"/>
				<xs:element name="OnExternalLink" type="ScriptType"/>
				<xs:element name="OnButtonUpdate" type="ScriptType"/>
				<xs:element name="OnError" type="ScriptType"/>
				<xs:element name="OnDressModel" type="ScriptType"/>
				<xs:element name="OnCooldownDone" type="ScriptType"/>
				<xs:element name="OnPanFinished" type="ScriptType"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="ScriptType">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="function" type="xs:string"/>
				<xs:attribute name="method" type="xs:string"/>
				<xs:attribute name="inherit" type="SCRIPTINHERITTYPE" use="optional" default="none"/>
				<xs:attribute name="intrinsicOrder" type="SCRIPTINTRINSICORDERTYPE" use="optional" default="none"/>
				<xs:attribute name="autoEnableInput" type="xs:boolean" default="true"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:complexType name="FontType">
		<xs:sequence minOccurs="0">
			<xs:choice maxOccurs="unbounded">
				<xs:element name="FontHeight" type="Value"/>
				<xs:element name="Color" type="ColorType"/>
				<xs:element name="Shadow" type="ShadowType"/>
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="name" type="xs:string"/>
		<xs:attribute name="inherits" type="xs:string"/>
		<xs:attribute name="virtual" type="xs:boolean" default="false"/>
		<xs:attribute name="font" type="xs:string"/>
		<xs:attribute name="spacing" type="xs:float" default="0"/>
		<xs:attribute name="outline" type="OUTLINETYPE" default="NONE"/>
		<xs:attribute name="monochrome" type="xs:boolean" default="false"/>
		<xs:attribute name="justifyV" type="JUSTIFYVTYPE" default="MIDDLE"/>
		<xs:attribute name="justifyH" type="JUSTIFYHTYPE" default="CENTER"/>
		<xs:attribute name="height" type="xs:float"/>
		<xs:attribute name="fixedSize" type="xs:boolean" default="false"/>
		<xs:attribute name="filter" type="xs:boolean" default="false"/>
	</xs:complexType>

	<xs:element name="Font">
		<xs:complexType>
			<xs:complexContent>
				<xs:extension base="FontType"/>
			</xs:complexContent>
		</xs:complexType>
	</xs:element>

	<xs:complexType name="FontMemberType">
		<xs:sequence minOccurs="1" maxOccurs="1">
			<xs:element name="Font" type="FontType"/>
		</xs:sequence>
		<xs:attribute name="alphabet" type="FONTALPHABET" use="required"/>
	</xs:complexType>

	<xs:complexType name="FontFamilyType">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="Member" type="FontMemberType"/>
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="name" type="xs:string" use="required"/>
		<xs:attribute name="virtual" type="xs:boolean" default="false"/>
	</xs:complexType>

	<xs:element name="FontFamily" type="FontFamilyType"/>

	<xs:complexType name="LayoutFrameType">
		<xs:sequence minOccurs="0">
			<xs:choice maxOccurs="unbounded">
				<xs:element name="Size" type="Dimension"/>
				<xs:element name="Anchors">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="Anchor" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence minOccurs="0">
										<xs:element name="Offset" type="Dimension"/>
									</xs:sequence>
									<xs:attribute name="point" type="FRAMEPOINT" use="required"/>
									<xs:attribute name="relativeKey" type="xs:string"/>
									<xs:attribute name="relativeTo" type="xs:string"/>
									<xs:attribute name="relativePoint" type="FRAMEPOINT"/>
									<xs:attribute name="x" type="xs:float"/>
									<xs:attribute name="y" type="xs:float"/>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="KeyValues" type="KeyValuesType"/>
				<xs:element name="Animations">
					<xs:complexType>
						<xs:sequence>
							<xs:element ref="AnimationGroup" maxOccurs="unbounded"/>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="name" type="xs:string"/>
		<xs:attribute name="parentKey" type="xs:string"/>
		<xs:attribute name="parentArray" type="xs:string"/>
		<xs:attribute name="inherits" type="xs:string"/>
		<xs:attribute name="mixin" type="xs:string"/>
		<xs:attribute name="secureMixin" type="xs:string"/>
		<xs:attribute name="virtual" type="xs:boolean" default="false"/>
		<xs:attribute name="setAllPoints" type="xs:boolean" default="false"/>
		<xs:attribute name="hidden" type="xs:boolean" default="false"/>
	</xs:complexType>
	<xs:element name="LayoutFrame" type="LayoutFrameType"/>

	<xs:complexType name="BackdropType">
		<xs:sequence minOccurs="0">
			<xs:choice maxOccurs="unbounded">
				<xs:element name="BackgroundInsets" type="Inset"/>
				<xs:element name="TileSize" type="Value"/>
				<xs:element name="EdgeSize" type="Value"/>
				<xs:element name="Color" type="ColorType"/>
				<xs:element name="BorderColor" type="ColorType"/>
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="bgFile" type="xs:string"/>
		<xs:attribute name="edgeFile" type="xs:string"/>
		<xs:attribute name="tile" type="xs:boolean" default="false"/>
		<xs:attribute name="alphaMode" type="ALPHAMODE" default="BLEND"/>
	</xs:complexType>

	<xs:complexType name="RectType">
		<xs:attribute name="ULx" type="xs:float"/>
		<xs:attribute name="ULy" type="xs:float"/>
		<xs:attribute name="LLx" type="xs:float"/>
		<xs:attribute name="LLy" type="xs:float"/>
		<xs:attribute name="URx" type="xs:float"/>
		<xs:attribute name="URy" type="xs:float"/>
		<xs:attribute name="LRx" type="xs:float"/>
		<xs:attribute name="LRy" type="xs:float"/>
	</xs:complexType>
	
	<xs:complexType name="TextureType">
		<xs:complexContent>
			<xs:extension base="LayoutFrameType">
				<xs:sequence minOccurs="0">
					<xs:choice maxOccurs="unbounded">
						<xs:element name="TexCoords">
							<xs:complexType>
								<xs:sequence minOccurs="0" maxOccurs="1">
									<xs:element name="Rect" type="RectType"/>
								</xs:sequence>								
								<xs:attribute name="left" type="xs:float"/>
								<xs:attribute name="right" type="xs:float"/>
								<xs:attribute name="top" type="xs:float"/>
								<xs:attribute name="bottom" type="xs:float"/>
							</xs:complexType>
						</xs:element>
						<xs:element name="Color" type="ColorType"/>
						<xs:element name="Gradient" type="GradientType"/>
					</xs:choice>
				</xs:sequence>
				<xs:attribute name="file" type="xs:string"/>
				<xs:attribute name="mask" type="xs:string"/>
				<xs:attribute name="alphaMode" type="ALPHAMODE" default="BLEND"/>
				<xs:attribute name="alpha" type="xs:float" default="1.0"/>
				<xs:attribute name="ignoreParentAlpha" type="xs:boolean" default="false"/>
				<xs:attribute name="ignoreParentScale" type="xs:boolean" default="false"/>
				<xs:attribute name="nonBlocking" type="xs:boolean" default="false"/>
				<xs:attribute name="horizTile" type="xs:boolean" default="false"/>
				<xs:attribute name="vertTile" type="xs:boolean" default="false"/>
				<xs:attribute name="atlas" type="xs:string"/>
				<xs:attribute name="useAtlasSize" type="xs:boolean"/>
				<xs:attribute name="desaturated" type="xs:boolean" default="false"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Texture" type="TextureType" substitutionGroup="LayoutFrame"/>

	<xs:complexType name="MaskTextureType">
		<xs:complexContent>
			<xs:extension base="TextureType">
				<xs:sequence minOccurs="0" maxOccurs="1">
					<xs:element name="MaskedTextures">
						<xs:complexType>
							<xs:sequence>
								<xs:element name="MaskedTexture" maxOccurs="unbounded">
									<xs:complexType>
										<xs:attribute name="childKey" type="xs:string"/>
										<xs:attribute name="target" type="xs:string"/>
									</xs:complexType>
								</xs:element>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="MaskTexture" type="MaskTextureType" substitutionGroup="LayoutFrame"/>

	<xs:complexType name="FontStringType">
		<xs:complexContent>
			<xs:extension base="LayoutFrameType">
				<xs:sequence minOccurs="0">
					<xs:choice maxOccurs="unbounded">
						<xs:element name="FontHeight" type="Value"/>
						<xs:element name="Color" type="ColorType"/>
						<xs:element name="Shadow" type="ShadowType"/>
					</xs:choice>
				</xs:sequence>
				<xs:attribute name="font" type="xs:string"/>
				<xs:attribute name="bytes" default="255">
					<xs:simpleType>
						<xs:restriction base="xs:int">
							<xs:minInclusive value="0"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="text" type="xs:string"/>
				<xs:attribute name="spacing" type="xs:float" default="0"/>
				<xs:attribute name="outline" type="OUTLINETYPE" default="NONE"/>
				<xs:attribute name="monochrome" type="xs:boolean" default="false"/>
				<xs:attribute name="nonspacewrap" type="xs:boolean" default="false"/>
				<xs:attribute name="wordwrap" type="xs:boolean" default="true"/>
				<xs:attribute name="justifyV" type="JUSTIFYVTYPE" default="MIDDLE"/>
				<xs:attribute name="justifyH" type="JUSTIFYHTYPE" default="CENTER"/>
				<xs:attribute name="maxLines" type="xs:unsignedInt" default="0"/>
				<xs:attribute name="indented" type="xs:boolean" default="false"/>
				<xs:attribute name="alpha" type="xs:float" default="1.0"/>
				<xs:attribute name="ignoreParentAlpha" type="xs:boolean" default="false"/>
				<xs:attribute name="ignoreParentScale" type="xs:boolean" default="false"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="FontString" type="FontStringType" substitutionGroup="LayoutFrame"/>

	<xs:complexType name="LineType">
		<xs:complexContent>
			<xs:extension base="TextureType">
				<xs:sequence minOccurs="0">
					<xs:element name="StartAnchor" maxOccurs="1">
						<xs:complexType>
							<xs:sequence minOccurs="0">
								<xs:element name="Offset" type="Dimension"/>
							</xs:sequence>
							<xs:attribute name="relativeTo" type="xs:string"/>
							<xs:attribute name="relativePoint" type="FRAMEPOINT"/>
							<xs:attribute name="relativeKey" type="xs:string"/>
							<xs:attribute name="x" type="xs:float"/>
							<xs:attribute name="y" type="xs:float"/>
						</xs:complexType>
					</xs:element>
					<xs:element name="EndAnchor" maxOccurs="1">
						<xs:complexType>
							<xs:sequence minOccurs="0">
								<xs:element name="Offset" type="Dimension"/>
							</xs:sequence>
							<xs:attribute name="relativeTo" type="xs:string"/>
							<xs:attribute name="relativePoint" type="FRAMEPOINT"/>
							<xs:attribute name="relativeKey" type="xs:string"/>
							<xs:attribute name="x" type="xs:float"/>
							<xs:attribute name="y" type="xs:float"/>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
				<xs:attribute name="thickness" type="xs:float" default="4"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Line" type="LineType" substitutionGroup="LayoutFrame"/>

	<xs:complexType name="FrameType">
		<xs:complexContent>
			<xs:extension base="LayoutFrameType">
				<xs:sequence minOccurs="0">
					<xs:choice maxOccurs="unbounded">
						<xs:element name="TitleRegion" type="ui:LayoutFrameType"/>
						<xs:element name="ResizeBounds">
							<xs:complexType>
								<xs:sequence>
									<xs:choice maxOccurs="unbounded">
										<xs:element name="minResize" type="Dimension"/>
										<xs:element name="maxResize" type="Dimension"/>
									</xs:choice>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="Backdrop" type="BackdropType"/>
						<xs:element name="HitRectInsets" type="Inset"/>
						<xs:element name="Layers">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Layer" maxOccurs="unbounded">
										<xs:complexType>
											<xs:sequence>
												<xs:choice maxOccurs="unbounded">
													<xs:element ref="Texture"/>
													<xs:element ref="MaskTexture"/>
													<xs:element ref="FontString"/>
													<xs:element ref="Line"/>
												</xs:choice>
											</xs:sequence>
											<xs:attribute name="level" type="DRAWLAYER" default="ARTWORK"/>
											<xs:attribute name="textureSubLevel" default="0">
												<xs:simpleType>
													<xs:restriction base="xs:int">
														<xs:minInclusive value="-8"/>
														<xs:maxInclusive value="7"/>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="Attributes" type="AttributesType"/>
						<xs:element name="Frames">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="Frame" maxOccurs="unbounded"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="Scripts" type="ScriptsType"/>
					</xs:choice>
				</xs:sequence>
				<xs:attribute name="alpha" type="xs:float" default="1.0"/>
				<xs:attribute name="parent" type="xs:string"/>
				<xs:attribute name="toplevel" type="xs:boolean" default="false"/>
				<xs:attribute name="flattenRenderLayers" type="xs:boolean" default="false"/>
				<xs:attribute name="useParentLevel" type="xs:boolean" default="false"/>
				<xs:attribute name="movable" type="xs:boolean" default="false"/>
				<xs:attribute name="resizable" type="xs:boolean" default="false"/>
				<xs:attribute name="frameStrata" type="ui:FRAMESTRATA" default="PARENT"/>
				<xs:attribute name="frameLevel" type="xs:int"/>
				<xs:attribute name="id" type="xs:int" default="0"/>
				<xs:attribute name="enableMouse" type="xs:boolean" default="false"/>
				<xs:attribute name="enableMouseClicks" type="xs:boolean" default="false"/>
				<xs:attribute name="enableMouseMotion" type="xs:boolean" default="false"/>
				<xs:attribute name="enableKeyboard" type="xs:boolean" default="false"/>
				<xs:attribute name="clampedToScreen" type="xs:boolean" default="false"/>
				<xs:attribute name="protected" type="xs:boolean" default="false"/>
				<xs:attribute name="depth" type="xs:float" default="0.0"/>
				<xs:attribute name="dontSavePosition" type="xs:boolean" default="false"/>
				<xs:attribute name="propagateKeyboardInput" type="xs:boolean" default="false"/>
				<xs:attribute name="ignoreParentAlpha" type="xs:boolean" default="false"/>
				<xs:attribute name="ignoreParentScale" type="xs:boolean" default="false"/>
				<xs:attribute name="intrinsic" type="xs:boolean" default="false"/>
				<xs:attribute name="clipChildren" type="xs:boolean" default="false"/>
				<xs:attribute name="propagateHyperlinksToParent" type="xs:boolean" default="false"/>
				<xs:attribute name="hyperlinksEnabled" type="xs:boolean" default="false"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Frame" type="FrameType" substitutionGroup="LayoutFrame"/>

	<xs:complexType name="UnitPositionFrameType">
		<xs:complexContent>
			<xs:extension base="FrameType" />
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="UnitPositionFrame" type="UnitPositionFrameType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="ButtonType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:sequence minOccurs="0">
					<xs:choice maxOccurs="unbounded">
						<xs:element name="NormalTexture" type="ui:TextureType"/>
						<xs:element name="PushedTexture" type="ui:TextureType"/>
						<xs:element name="DisabledTexture" type="ui:TextureType"/>
						<xs:element name="HighlightTexture" type="ui:TextureType"/>
						<xs:element name="ButtonText" type="FontStringType"/>
						<xs:element name="NormalFont" type="ButtonStyleType"/>
						<xs:element name="HighlightFont" type="ButtonStyleType"/>
						<xs:element name="DisabledFont" type="ButtonStyleType"/>
						<xs:element name="NormalColor" type="ColorType"/>
						<xs:element name="HighlightColor" type="ColorType"/>
						<xs:element name="DisabledColor" type="ColorType"/>
						<xs:element name="PushedTextOffset" type="Dimension"/>
					</xs:choice>
				</xs:sequence>
				<xs:attribute name="text" type="xs:string"/>
				<xs:attribute name="registerForClicks" type="xs:string"/>
				<xs:attribute name="motionScriptsWhileDisabled" type="xs:boolean" default="false"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Button" type="ButtonType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="CheckButtonType">
		<xs:complexContent>
			<xs:extension base="ButtonType">
				<xs:sequence minOccurs="0">
					<xs:choice maxOccurs="unbounded">
						<xs:element name="CheckedTexture" type="TextureType"/>
						<xs:element name="DisabledCheckedTexture" type="TextureType"/>
					</xs:choice>
				</xs:sequence>
				<xs:attribute name="checked" type="xs:boolean" default="false"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="CheckButton" type="CheckButtonType" substitutionGroup="ui:Button"/>

	<xs:complexType name="StatusBarType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:sequence minOccurs="0">
					<xs:choice maxOccurs="unbounded">
						<xs:element name="BarTexture" type="TextureType"/>
						<xs:element name="BarColor" type="ui:ColorType"/>
					</xs:choice>
				</xs:sequence>
				<xs:attribute name="drawLayer" type="ui:DRAWLAYER" default="ARTWORK"/>
				<xs:attribute name="minValue" type="xs:float"/>
				<xs:attribute name="maxValue" type="xs:float"/>
				<xs:attribute name="defaultValue" type="xs:float"/>
				<xs:attribute name="orientation" type="ui:ORIENTATION" default="HORIZONTAL"/>
				<xs:attribute name="rotatesTexture" type="xs:boolean" default="false"/>
				<xs:attribute name="reverseFill" type="xs:boolean" default="false"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="StatusBar" type="StatusBarType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="SliderType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:sequence minOccurs="0">
					<xs:choice maxOccurs="unbounded">
						<xs:element name="ThumbTexture" type="TextureType"/>
					</xs:choice>
				</xs:sequence>
				<xs:attribute name="drawLayer" type="ui:DRAWLAYER" default="OVERLAY"/>
				<xs:attribute name="minValue" type="xs:float"/>
				<xs:attribute name="maxValue" type="xs:float"/>
				<xs:attribute name="defaultValue" type="xs:float"/>
				<xs:attribute name="valueStep" type="xs:float"/>
				<xs:attribute name="orientation" type="ui:ORIENTATION" default="VERTICAL"/>
				<xs:attribute name="obeyStepOnDrag" type="xs:boolean"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Slider" type="SliderType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="EditBoxType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:sequence minOccurs="0">
					<xs:choice maxOccurs="unbounded">
						<xs:element ref="FontString"/>
						<xs:element name="HighlightColor" type="ColorType"/>
						<xs:element name="TextInsets" type="Inset"/>
					</xs:choice>
				</xs:sequence>
				<xs:attribute name="font" type="xs:string"/>
				<xs:attribute name="letters" type="xs:int" default="0"/>
				<xs:attribute name="blinkSpeed" type="xs:float" default="0.5"/>
				<xs:attribute name="numeric" type="xs:boolean" default="false"/>
				<xs:attribute name="password" type="xs:boolean" default="false"/>
				<xs:attribute name="multiLine" type="xs:boolean" default="false"/>
				<xs:attribute name="historyLines" type="xs:int" default="0"/>
				<xs:attribute name="autoFocus" type="xs:boolean" default="true"/>
				<xs:attribute name="ignoreArrows" type="xs:boolean" default="false"/>
				<xs:attribute name="countInvisibleLetters" type="xs:boolean" default="false"/>
				<xs:attribute name="invisibleBytes" type="xs:int"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="EditBox" type="EditBoxType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="ColorSelectType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:sequence>
					<xs:choice maxOccurs="unbounded">
						<xs:element name="ColorWheelTexture" type="TextureType"/>
						<xs:element name="ColorWheelThumbTexture" type="TextureType"/>
						<xs:element name="ColorValueTexture" type="TextureType"/>
						<xs:element name="ColorValueThumbTexture" type="TextureType"/>
					</xs:choice>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ColorSelect" type="ui:ColorSelectType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="ModelType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:sequence minOccurs="0">
					<xs:element name="FogColor" type="ColorType"/>
				</xs:sequence>
				<xs:attribute name="file" type="xs:string"/>
				<xs:attribute name="scale" type="xs:float" default="1.0"/>
				<xs:attribute name="fogNear" default="0.0">
					<xs:simpleType>
						<xs:restriction base="xs:float"/>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="fogFar" default="1.0">
					<xs:simpleType>
						<xs:restriction base="xs:float"/>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="glow" default="1.0">
					<xs:simpleType>
						<xs:restriction base="xs:float"/>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="drawLayer" type="ui:DRAWLAYER" default="ARTWORK"/>
			</xs:extension>
			<xs:element name="ViewInsets" type="Inset"/>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Model" type="ModelType" substitutionGroup="ui:Frame"/>
	<xs:element name="ModelFFX" type="ModelType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="SimpleHTMLType">
		<xs:complexContent>
			<xs:extension base="ui:FrameType">
				<xs:sequence>
					<xs:element ref="FontString"/>
					<xs:sequence minOccurs="0">
						<xs:choice maxOccurs="unbounded">
							<xs:element name="FontStringHeader1" type="ui:FontStringType"/>
							<xs:element name="FontStringHeader2" type="ui:FontStringType"/>
							<xs:element name="FontStringHeader3" type="ui:FontStringType"/>
						</xs:choice>
					</xs:sequence>
				</xs:sequence>
				<xs:attribute name="font" type="xs:string"/>
				<xs:attribute name="file" type="xs:string"/>
				<xs:attribute name="hyperlinkFormat" type="xs:string" default="|H%s|h%s|h"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="SimpleHTML" type="ui:SimpleHTMLType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="MessageFrameType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:sequence minOccurs="0">
					<xs:choice maxOccurs="unbounded">
						<xs:element ref="FontString"/>
						<xs:element name="TextInsets" type="Inset"/>
					</xs:choice>
				</xs:sequence>
				<xs:attribute name="font" type="xs:string"/>
				<xs:attribute name="fade" type="xs:boolean" default="true"/>
				<xs:attribute name="fadeDuration" type="xs:float" default="3.0"/>
				<xs:attribute name="fadePower" type="xs:float" default="1.0"/>
				<xs:attribute name="displayDuration" type="xs:float" default="10.0"/>
				<xs:attribute name="insertMode" type="INSERTMODE" default="BOTTOM"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="MessageFrame" type="MessageFrameType" substitutionGroup="ui:Frame"/>

	<!-- This is now an intrinsic type-->
	<xs:complexType name="ScrollingMessageFrameType">
		<xs:complexContent>
			<xs:extension base="FrameType" />
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ScrollingMessageFrame" type="ScrollingMessageFrameType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="ScrollFrameType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:sequence minOccurs="0">
					<xs:choice>
						<xs:element name="ScrollChild">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="Frame"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:choice>
				</xs:sequence>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ScrollFrame" type="ScrollFrameType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="MovieFrameType">
		<xs:complexContent>
			<xs:extension base="FrameType"/>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="MovieFrame" type="MovieFrameType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="ActorScriptsType">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="OnLoad" type="ScriptType"/>
				<xs:element name="OnUpdate" type="ScriptType"/>
				<xs:element name="OnModelLoading" type="ScriptType"/>
				<xs:element name="OnModelLoaded" type="ScriptType"/>
				<xs:element name="OnAnimFinished" type="ScriptType"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ModeSceneActorType">
		<xs:sequence>
			<xs:element name="Scripts" type="ActorScriptsType" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Animations">
				<xs:complexType>
					<xs:sequence>
						<xs:element ref="AnimationGroup" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>

		<xs:attribute name="name" type="xs:string"/>
		<xs:attribute name="inherits" type="xs:string"/>
		<xs:attribute name="virtual" type="xs:boolean" default="false"/>
		<xs:attribute name="parentKey" type="xs:string"/>
		<xs:attribute name="parentArray" type="xs:string"/>
		<xs:attribute name="mixin" type="xs:string"/>

		<xs:attribute name="file" type="xs:string"/>
		<xs:attribute name="fileID" type="xs:int"/>

		<xs:attribute name="alpha" type="xs:float" default="1.0" />
		<xs:attribute name="scale" type="xs:float" default="1.0" />
		<xs:attribute name="desaturation" type="xs:float" default="0.0" />
	</xs:complexType>
	<xs:element name="Actor" type="ModeSceneActorType"/>

	<xs:element name="Ui">
		<xs:complexType>
			<xs:sequence>
				<xs:choice maxOccurs="unbounded">
					<xs:element name="Include" minOccurs="0" maxOccurs="unbounded">
						<xs:complexType>
							<xs:simpleContent>
								<xs:extension base="xs:string">
									<xs:attribute name="file" type="xs:string" use="required"/>
								</xs:extension>
							</xs:simpleContent>
						</xs:complexType>
					</xs:element>
					<xs:element name="Script" minOccurs="0" maxOccurs="unbounded">
						<xs:complexType>
							<xs:simpleContent>
								<xs:extension base="xs:string">
									<xs:attribute name="file" type="xs:string" use="optional"/>
								</xs:extension>
							</xs:simpleContent>
						</xs:complexType>
					</xs:element>
					<xs:element ref="Font" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element ref="FontFamily" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element ref="LayoutFrame" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element ref="Animation" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element ref="AnimationGroup" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element ref="Actor" minOccurs="0" maxOccurs="unbounded"/>
				</xs:choice>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:complexType name="WorldFrameType">
		<xs:complexContent>
			<xs:extension base="FrameType"/>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="WorldFrame" type="WorldFrameType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="GameTooltipType">
		<xs:complexContent>
			<xs:extension base="FrameType"/>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="GameTooltip" type="GameTooltipType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="CooldownType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:sequence minOccurs="0">
					<xs:choice maxOccurs="unbounded">
						<xs:element name="SwipeTexture" type="TextureType"/>
						<xs:element name="EdgeTexture" type="TextureType"/>
						<xs:element name="BlingTexture" type="TextureType"/>
					</xs:choice>
				</xs:sequence>
				<xs:attribute name="reverse" type="xs:boolean" default="false"/>
				<xs:attribute name="hideCountdownNumbers" type="xs:boolean" default="false"/>
				<xs:attribute name="drawEdge" type="xs:boolean" default="false"/>
				<xs:attribute name="drawBling" type="xs:boolean" default="false"/>
				<xs:attribute name="drawSwipe" type="xs:boolean" default="false"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Cooldown" type="CooldownType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="QuestPOIFrameType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:attribute name="filltexture" type="xs:string"/>
				<xs:attribute name="bordertexture" type="xs:string"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="QuestPOIFrame" type="QuestPOIFrameType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="ArchaeologyDigSiteFrameType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:attribute name="filltexture" type="xs:string"/>
				<xs:attribute name="bordertexture" type="xs:string"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ArchaeologyDigSiteFrame" type="ArchaeologyDigSiteFrameType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="ScenarioPOIFrameType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:attribute name="filltexture" type="xs:string"/>
				<xs:attribute name="bordertexture" type="xs:string"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ScenarioPOIFrame" type="ScenarioPOIFrameType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="MinimapType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:attribute name="questBlobInsideTexture" type="xs:string"/>
				<xs:attribute name="questBlobOutsideTexture" type="xs:string"/>
				<xs:attribute name="questBlobOutsideSelectedTexture" type="xs:string"/>
				<xs:attribute name="questBlobRingTexture" type="xs:string"/>
				<xs:attribute name="taskBlobInsideTexture" type="xs:string"/>
				<xs:attribute name="taskBlobOutsideTexture" type="xs:string"/>
				<xs:attribute name="taskBlobOutsideSelectedTexture" type="xs:string"/>
				<xs:attribute name="taskBlobRingTexture" type="xs:string"/>
				<xs:attribute name="archBlobInsideTexture" type="xs:string"/>
				<xs:attribute name="archBlobOutsideTexture" type="xs:string"/>
				<xs:attribute name="archBlobRingTexture" type="xs:string"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Minimap" type="MinimapType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="PlayerModelType">
		<xs:complexContent>
			<xs:extension base="ModelType"/>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="PlayerModel" type="PlayerModelType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="DressUpModelType">
		<xs:complexContent>
			<xs:extension base="PlayerModelType"/>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="DressUpModel" type="DressUpModelType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="TabardModelType">
		<xs:complexContent>
			<xs:extension base="PlayerModelType"/>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="TabardModel" type="TabardModelType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="CinematicModelType">
		<xs:complexContent>
			<xs:extension base="ModelType">
				<xs:attribute name="facing" type="xs:boolean" default="false"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="CinematicModel" type="CinematicModelType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="UiCameraType">
		<xs:complexContent>
			<xs:extension base="ModelType"/>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="UiCamera" type="UiCameraType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="UnitButtonType">
		<xs:complexContent>
			<xs:extension base="ButtonType"/>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="TaxiRouteFrameType">
		<xs:complexContent>
			<xs:extension base="FrameType"/>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="TaxiRouteFrame" type="TaxiRouteFrameType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="BrowserType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:attribute name="IMEFont" type="xs:string"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Browser" type="BrowserType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="CheckoutType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:attribute name="IMEFont" type="xs:string"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Checkout" type="CheckoutType" substitutionGroup="ui:Frame"/>

	<xs:complexType name="ModelSceneType">
		<xs:complexContent>
			<xs:extension base="FrameType">
				<xs:sequence minOccurs="0">
					<xs:choice maxOccurs="unbounded">
						<xs:element name="FogColor" type="ColorType"/>

						<xs:element name="Actors">
							<xs:complexType>
								<xs:sequence>
									<xs:element ref="Actor" maxOccurs="unbounded"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:choice>
				</xs:sequence>
				
				<xs:element name="ViewInsets" type="Inset"/>
				<xs:attribute name="fogNear" type="xs:float" default="0.0" />
				<xs:attribute name="fogFar" type="xs:float" default="1.0" />
				<xs:attribute name="drawLayer" type="ui:DRAWLAYER" default="ARTWORK"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="ModelScene" type="ModelSceneType" substitutionGroup="ui:Frame"/>

	<xs:simpleType name="ANIMLOOPTYPE">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="NONE"/>
			<xs:enumeration value="REPEAT"/>
			<xs:enumeration value="BOUNCE"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ANIMSMOOTHTYPE">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="NONE"/>
			<xs:enumeration value="IN"/>
			<xs:enumeration value="OUT"/>
			<xs:enumeration value="IN_OUT"/>
			<xs:enumeration value="OUT_IN"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ANIMCURVETYPE">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="NONE"/>
			<xs:enumeration value="SMOOTH"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="AnimOrderType">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="AnimScriptsType">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="OnLoad" type="ScriptType"/>
				<xs:element name="OnPlay" type="ScriptType"/>
				<xs:element name="OnPause" type="ScriptType"/>
				<xs:element name="OnStop" type="ScriptType"/>
				<xs:element name="OnUpdate" type="ScriptType"/>
				<xs:element name="OnFinished" type="ScriptType"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="AnimGroupScriptsType">
		<xs:sequence>
			<xs:choice maxOccurs="unbounded">
				<xs:element name="OnLoad" type="ScriptType"/>
				<xs:element name="OnPlay" type="ScriptType"/>
				<xs:element name="OnPause" type="ScriptType"/>
				<xs:element name="OnStop" type="ScriptType"/>
				<xs:element name="OnUpdate" type="ScriptType"/>
				<xs:element name="OnFinished" type="ScriptType"/>
				<xs:element name="OnLoop" type="ScriptType"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="AnimOriginType">
		<xs:sequence minOccurs="0">
			<xs:element name="Offset" type="Dimension"/>
		</xs:sequence>
		<xs:attribute name="point" type="FRAMEPOINT" default="CENTER"/>
	</xs:complexType>

	<xs:complexType name="AnimationType">
		<xs:sequence>
			<xs:element name="Scripts" type="AnimScriptsType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="name" type="xs:string"/>
		<xs:attribute name="inherits" type="xs:string"/>
		<xs:attribute name="virtual" type="xs:boolean" default="false"/>
		<xs:attribute name="target" type="xs:string"/>
		<xs:attribute name="targetKey" type="xs:string"/>
		<xs:attribute name="parentKey" type="xs:string"/>
		<xs:attribute name="childKey" type="xs:string"/>
		<xs:attribute name="startDelay" type="xs:float" default="0.0"/>
		<xs:attribute name="endDelay" type="xs:float" default="0.0"/>
		<xs:attribute name="duration" type="xs:float"/>
		<xs:attribute name="order" type="AnimOrderType" default="1"/>
		<xs:attribute name="smoothing" type="ANIMSMOOTHTYPE" default="NONE"/>
	</xs:complexType>
	<xs:element name="Animation" type="AnimationType"/>

	<xs:complexType name="TranslationType">
		<xs:complexContent>
			<xs:extension base="AnimationType">
				<xs:attribute name="offsetX" type="xs:float" default="0.0"/>
				<xs:attribute name="offsetY" type="xs:float" default="0.0"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Translation" type="TranslationType" substitutionGroup="Animation"/>

	<xs:complexType name="LineTranslationType">
		<xs:complexContent>
			<xs:extension base="TranslationType" />
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="LineTranslation" type="LineTranslationType" substitutionGroup="Animation"/>

	<xs:complexType name="RotationType">
		<xs:complexContent>
			<xs:extension base="AnimationType">
				<xs:sequence>
					<xs:element name="Origin" type="AnimOriginType" minOccurs="0"/>
				</xs:sequence>
				<xs:attribute name="degrees" type="xs:float" default="0.0"/>
				<xs:attribute name="radians" type="xs:float" default="0.0"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Rotation" type="RotationType" substitutionGroup="Animation"/>

	<xs:complexType name="ScaleType">
		<xs:complexContent>
			<xs:extension base="AnimationType">
				<xs:sequence>
					<xs:element name="Origin" type="AnimOriginType" minOccurs="0"/>
				</xs:sequence>
				<xs:attribute name="scaleX" type="xs:float" default="1.0"/>
				<xs:attribute name="scaleY" type="xs:float" default="1.0"/>
				<xs:attribute name="fromScaleX" type="xs:float" default="1.0"/>
				<xs:attribute name="fromScaleY" type="xs:float" default="1.0"/>
				<xs:attribute name="toScaleX" type="xs:float" default="1.0"/>
				<xs:attribute name="toScaleY" type="xs:float" default="1.0"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Scale" type="ScaleType" substitutionGroup="Animation"/>

	<xs:complexType name="LineScaleType">
		<xs:complexContent>
			<xs:extension base="ScaleType"/>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="LineScale" type="LineScaleType" substitutionGroup="Animation"/>

	<xs:complexType name="AlphaType">
		<xs:complexContent>
			<xs:extension base="AnimationType">
				<xs:attribute name="fromAlpha" type="xs:float" default="0.0"/>
				<xs:attribute name="toAlpha" type="xs:float" default="1.0"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Alpha" type="AlphaType" substitutionGroup="Animation"/>

	<xs:complexType name="ControlPointsType">
		<xs:sequence minOccurs="0">
			<xs:choice maxOccurs="unbounded">
				<xs:element name="ControlPoint" maxOccurs="unbounded">
					<xs:complexType>
						<xs:attribute name="name" type="xs:string"/>
						<xs:attribute name="offsetX" type="xs:float" default="0.0"/>
						<xs:attribute name="offsetY" type="xs:float" default="0.0"/>
					</xs:complexType>
				</xs:element>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ControlPoints" type="ControlPointsType"/>

	<xs:complexType name="PathType">
		<xs:complexContent>
			<xs:extension base="AnimationType">
				<xs:sequence minOccurs="0">
					<xs:choice maxOccurs="unbounded">
						<xs:element name="ControlPoints" type="ControlPointsType"/>
					</xs:choice>
				</xs:sequence>
				<xs:attribute name="curve" type="ANIMCURVETYPE" default="NONE"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:element name="Path" type="PathType" substitutionGroup="Animation"/>

	<xs:complexType name="AnimationGroupType">
		<xs:sequence minOccurs="0">
			<xs:choice maxOccurs="unbounded">
				<xs:element ref="Animation"/>
				<xs:element name="Scripts" type="AnimGroupScriptsType"/>
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="name" type="xs:string"/>
		<xs:attribute name="inherits" type="xs:string"/>
		<xs:attribute name="virtual" type="xs:boolean" default="false"/>
		<xs:attribute name="parentKey" type="xs:string"/>
		<xs:attribute name="looping" type="ANIMLOOPTYPE" default="NONE"/>
		<xs:attribute name="setToFinalAlpha" type="xs:boolean" default="false"/>
	</xs:complexType>
	<xs:element name="AnimationGroup" type="AnimationGroupType"/>
</xs:schema>
