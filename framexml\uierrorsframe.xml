<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="UIErrorsFrame.lua"/>
	
	<MessageFrame name="UIErrorsFrame" displayDuration="2" fadeDuration=".5" fadePower="3" insertMode="TOP" parent="UIParent" frameStrata="DIALOG" frameLevel="1" toplevel="true" mixin="UIErrorsMixin">
		<Size x="512" y="60" />
		<Anchors>
			<Anchor point="TOP" x="0" y="-122" /> <!--If you change this, make sure to change the dynamic positioning in BarberShopUI.xml -->
		</Anchors>
		<Scripts>
			<OnLoad method="OnLoad"/>
			<OnEvent method="OnEvent"/>
		</Scripts>
		<FontString inherits="ErrorFont" justifyH="CENTER"/>
	</MessageFrame>
</Ui>
