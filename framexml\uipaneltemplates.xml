<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">

	<EditBox name="SearchBoxTemplate" inherits="InputBoxInstructionsTemplate" autoFocus="false" virtual="true">
		<Layers>
			<Layer level="OVERLAY">
				<Texture name="$parentSearchIcon" file="Interface\Common\UI-Searchbox-Icon" parentKey="searchIcon">
					<Size x="14" y="14"/>
					<Anchors>
						<Anchor point="LEFT" x="0" y="-2"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Button name="$parentClearButton" parentKey="clearButton" hidden="true">
				<Size x="17" y="17"/>
				<Anchors>
					<Anchor point="RIGHT" x="-3" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture file="Interface\FriendsFrame\ClearBroadcastIcon" alpha="0.5" parentKey="texture">
							<Size>
								<AbsDimension x="17" y="17"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						self.texture:SetAlpha(1.0);
					</OnEnter>
					<OnLeave>
						self.texture:SetAlpha(0.5);
					</OnLeave>
					<OnMouseDown>
						if self:IsEnabled() then
							self.texture:SetPoint("TOPLEFT", self, "TOPLEFT", 1, -1);
						end
					</OnMouseDown>
					<OnMouseUp>
						self.texture:SetPoint("TOPLEFT", self, "TOPLEFT", 0, 0);
					</OnMouseUp>
					<OnClick function="SearchBoxTemplateClearButton_OnClick"/>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad function="SearchBoxTemplate_OnLoad"/>
			<OnEscapePressed function="EditBox_ClearFocus"/>
			<OnEnterPressed function="EditBox_ClearFocus"/>
			<OnEditFocusLost function="SearchBoxTemplate_OnEditFocusLost"/>
			<OnEditFocusGained function="SearchBoxTemplate_OnEditFocusGained"/>
			<OnTextChanged function="SearchBoxTemplate_OnTextChanged"/>
		</Scripts>
	</EditBox>
	<EditBox name="BagSearchBoxTemplate" inherits="SearchBoxTemplate" autoFocus="false" virtual="true">
		<Scripts>
			<OnHide function="BagSearch_OnHide"/>
			<OnTextChanged function="BagSearch_OnTextChanged"/>
			<OnChar function="BagSearch_OnChar"/>
		</Scripts>
	</EditBox>
	<CheckButton name="UICheckButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="32" y="32"/>
		</Size>
		<Layers>
			<Layer level="ARTWORK">
				<FontString name="$parentText" inherits="GameFontNormalSmall" parentKey="text">
					<Anchors>
						<Anchor point="LEFT" relativePoint="RIGHT">
							<Offset>
								<AbsDimension x="-2" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<NormalTexture file="Interface\Buttons\UI-CheckBox-Up"/>
		<PushedTexture file="Interface\Buttons\UI-CheckBox-Down"/>
		<HighlightTexture file="Interface\Buttons\UI-CheckBox-Highlight" alphaMode="ADD"/>
		<CheckedTexture file="Interface\Buttons\UI-CheckBox-Check"/>
		<DisabledCheckedTexture file="Interface\Buttons\UI-CheckBox-Check-Disabled"/>
	</CheckButton>
	<Button name="GameMenuButtonTemplate" inherits="UIPanelButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="144" y="21"/>
		</Size>
		<NormalFont style="GameFontHighlight"/>
		<HighlightFont style="GameFontHighlight"/>
		<DisabledFont style="GameFontDisable"/>
	</Button>
	<Frame name="AnimatedShineTemplate" virtual="true">
		<Size>
			<AbsDimension x="37" y="37"/>
		</Size>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parent1" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="13" y="13"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent2" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="13" y="13"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent3" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="13" y="13"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent4" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="13" y="13"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>
	<Frame name="AutoCastShineTemplate" virtual="true">
		<Anchors>
			<Anchor point="CENTER">
				<Offset>
					<AbsDimension x="0" y="0"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Size>
			<AbsDimension x="28" y="28"/>
		</Size>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parent1" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="13" y="13"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent2" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="10" y="10"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent3" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="7" y="7"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent4" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="4" y="4"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent5" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="13" y="13"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent6" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="10" y="10"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent7" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="7" y="7"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent8" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="4" y="4"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent9" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="13" y="13"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent10" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="10" y="10"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent11" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="7" y="7"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent12" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="4" y="4"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent13" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="13" y="13"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent14" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="10" y="10"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent15" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="7" y="7"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
				<Texture name="$parent16" file="Interface\ItemSocketingFrame\UI-ItemSockets" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="4" y="4"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.3984375" right="0.4453125" top="0.40234375" bottom="0.44921875"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				AutoCastShine_OnLoad(self);
			</OnLoad>
		</Scripts>
	</Frame>
	<Button name="UIServiceButtonTemplate" virtual="true">
		<Size x="293" y="47"/>
		<Frames>
			<Frame name="$parentMoneyFrame" inherits="SmallMoneyFrameTemplate" parentKey="money">
				<Anchors>
					<Anchor point="TOPRIGHT" x="5" y="-7"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						SmallMoneyFrame_OnLoad(self);
						MoneyFrame_SetType(self, "STATIC");
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
		<Layers>
			<Layer level="OVERLAY">
				<Texture name="$parentIcon" parentKey="icon">
					<Size x="36" y="36"/>
					<Anchors>
						<Anchor point="LEFT" x="6" y="0"/>
					</Anchors>
				</Texture>
				<FontString name="$parentName" inherits="GameFontNormal" justifyH="LEFT" parentKey="name">
					<Size x="0" y="12"/>
					<Anchors>
						<Anchor point="TOPLEFT"  relativeTo="$parentIcon" relativePoint="TOPRIGHT" x="6" y="-1"/>
						<Anchor point="RIGHT" relativeTo="$parentMoneyFrame" relativePoint="LEFT" x="-2" y="0"/>
					</Anchors>
				</FontString>
				<FontString name="$parentSubText" inherits="SystemFont_Shadow_Small" justifyH="LEFT" justifyV="MIDDLE" parentKey="subText">
					<Size x="240" y="30"/>
					<Anchors>
						<Anchor point="LEFT"  relativeTo="$parentName" relativePoint="LEFT" x="0" y="-19"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="OVERLAY" textureSubLevel="1">
				<Texture parentKey="selectedTex" file="Interface\ClassTrainerFrame\TrainerTextures" hidden="true" alphaMode="ADD">
					<TexCoords left="0.00195313" right="0.57421875" top="0.84960938" bottom="0.94140625"/>
				</Texture>
				<Texture parentKey="lock" file="Interface\GuildFrame\GuildFrame" hidden="true">
					<Size x="17" y="21"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentIcon" relativePoint="BOTTOMRIGHT" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.51660156" right="0.53320313" top="0.92578125" bottom="0.96679688"/>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND">
				<Texture parentKey="disabledBG" hidden="true" alphaMode="MOD">
					<Anchors>
						<Anchor point="TOPLEFT" x="2" y="-2"/>
						<Anchor point="BOTTOMRIGHT" x="-2" y="2"/>
					</Anchors>
					<Color r="0.55" g="0.55" b="0.55" a="1"/>
				</Texture>
			</Layer>
		</Layers>
		<NormalTexture file="Interface\ClassTrainerFrame\TrainerTextures">
			<TexCoords left="0.00195313" right="0.57421875" top="0.65820313" bottom="0.75000000"/>
		</NormalTexture>
		<HighlightTexture name="$parentHighlight" file="Interface\ClassTrainerFrame\TrainerTextures" alphaMode="ADD">
			<TexCoords left="0.00195313" right="0.57421875" top="0.75390625" bottom="0.84570313"/>
		</HighlightTexture>
	</Button>
	<Button name="UIPanelInfoButton" virtual="true">
		<Size>
			<AbsDimension x="16" y="16"/>
		</Size>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="$parentTexture" file="Interface\FriendsFrame\InformationIcon" parentKey="texture">
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnMouseDown>
				self.texture:SetPoint("TOPLEFT", 1, -1);
			</OnMouseDown>
			<OnMouseUp>
				self.texture:SetPoint("TOPLEFT", 0, 0);
			</OnMouseUp>
		</Scripts>
		<HighlightTexture file="Interface\FriendsFrame\InformationIcon-Highlight" alphaMode="ADD">
			<Anchors>
				<Anchor point="TOPLEFT" relativeTo="$parentTexture" relativePoint="TOPLEFT"/>
			</Anchors>
			<Color r="1" g="1" b="1" a="0.4"/>
		</HighlightTexture>
	</Button>

	<!-- size=64,64
	<Texture name="UI-SilverButton-Delete.png" >
		<Size x="12" y="12"/>
		<TexCoords left="0.01562500" right="0.20312500" top="0.01562500" bottom="0.20312500"/>
	</Texture>
	<Texture name="UI-SilverButton-LeftArrow.png" >
		<Size x="12" y="12"/>
		<TexCoords left="0.23437500" right="0.42187500" top="0.01562500" bottom="0.20312500"/>
	</Texture>
	<Texture name="UI-SilverButton-UpArrow.png" >
		<Size x="12" y="12"/>
		<TexCoords left="0.45312500" right="0.64062500" top="0.01562500" bottom="0.20312500"/>
	</Texture>
	<Texture name="UI-SilverButtonSquare-Up.png" >
		<Size x="26" y="26"/>
		<TexCoords left="0.01562500" right="0.42187500" top="0.23437500" bottom="0.64062500"/>
	</Texture>
	<Texture name="UI-SilverButtonSquare-Down.png" >
		<Size x="26" y="26"/>
		<TexCoords left="0.45312500" right="0.85937500" top="0.23437500" bottom="0.64062500"/>
	</Texture-->
	<Button name="UIPanelSquareButton" virtual="true">
		<Size x="26" y="26"/>
		<Layers>
			<Layer level="ARTWORK" textureSubLevel="1">
				<Texture name="$parentIcon" file="Interface\Buttons\SquareButtonTextures" parentKey="icon">
					<Size x="12" y="12"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.01562500" right="0.20312500" top="0.01562500" bottom="0.20312500"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnMouseDown>
				if self:IsEnabled() then
					self.icon:SetPoint("CENTER", -1, -1);
				end
			</OnMouseDown>
			<OnMouseUp>
				self.icon:SetPoint("CENTER", 0, 0);
			</OnMouseUp>
			<OnDisable>
				SetDesaturation(self.icon, true);
				self.icon:SetAlpha(0.5);
			</OnDisable>
			<OnEnable>
				SetDesaturation(self.icon, false);
				self.icon:SetAlpha(1.0);
			</OnEnable>
		</Scripts>
		<NormalTexture file="Interface\Buttons\SquareButtonTextures">
			<TexCoords left="0.01562500" right="0.42187500" top="0.23437500" bottom="0.64062500"/>
		</NormalTexture>
		<PushedTexture file="Interface\Buttons\SquareButtonTextures">
			<TexCoords left="0.45312500" right="0.85937500" top="0.23437500" bottom="0.64062500"/>
		</PushedTexture>
		<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
	</Button>
	<Button name="UIPanelLargeSilverButton" virtual="true">
		<Size>
			<AbsDimension x="96" y="46"/>
		</Size>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentLeft" file="Interface\Buttons\UI-SilverButtonLG-Left-Up">
					<Size>
						<AbsDimension x="32" y="64"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture name="$parentRight" file="Interface\Buttons\UI-SilverButtonLG-Right-Up">
					<Size>
						<AbsDimension x="32" y="64"/>
					</Size>
					<Anchors>
						<Anchor point="TOPRIGHT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture name="$parentMiddle" file="Interface\Buttons\UI-SilverButtonLG-Mid-Up">
					<Size>
						<AbsDimension x="32" y="64"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT" x="0" y="0"/>
						<Anchor point="TOPRIGHT" relativeTo="$parentRight" relativePoint="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="HIGHLIGHT">
				<Texture name="$parentLeft" file="Interface\Buttons\UI-SilverButtonLG-Left-Hi">
					<Size>
						<AbsDimension x="32" y="64"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture name="$parentRight" file="Interface\Buttons\UI-SilverButtonLG-Right-Hi">
					<Size>
						<AbsDimension x="32" y="64"/>
					</Size>
					<Anchors>
						<Anchor point="TOPRIGHT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture name="$parentMiddle" file="Interface\Buttons\UI-SilverButtonLG-Mid-Hi">
					<Size>
						<AbsDimension x="32" y="64"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT" x="0" y="0"/>
						<Anchor point="TOPRIGHT" relativeTo="$parentRight" relativePoint="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnMouseDown>
				if ( self:IsEnabled() ) then
					local name = self:GetName();
					_G[name.."Left"]:SetTexture("Interface\\Buttons\\UI-SilverButtonLG-Left-Down");
					_G[name.."Middle"]:SetTexture("Interface\\Buttons\\UI-SilverButtonLG-Mid-Down");
					_G[name.."Right"]:SetTexture("Interface\\Buttons\\UI-SilverButtonLG-Right-Down");
					local contentsFrame = _G[name.."ContentsFrame"];
					if ( contentsFrame ) then
						contentsFrame:SetPoint("TOPLEFT", -2, -1);
					end
				end
			</OnMouseDown>
			<OnMouseUp>
				if ( self:IsEnabled() ) then
					local name = self:GetName();
					_G[name.."Left"]:SetTexture("Interface\\Buttons\\UI-SilverButtonLG-Left-Up");
					_G[name.."Middle"]:SetTexture("Interface\\Buttons\\UI-SilverButtonLG-Mid-Up");
					_G[name.."Right"]:SetTexture("Interface\\Buttons\\UI-SilverButtonLG-Right-Up");
					local contentsFrame = _G[name.."ContentsFrame"];
					if ( contentsFrame ) then
						contentsFrame:SetPoint("TOPLEFT", 0, 0);
					end
				end
			</OnMouseUp>
		</Scripts>
	</Button>

	<!-- Talent Frame -->

	<Texture name="Talent-RingGlow" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="184" y="184"/>
		<TexCoords left="0.00390625" right="0.72265625" top="0.00195313" bottom="0.36132813"/>
	</Texture>
	<Texture name="Talent-Ring" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="67" y="67"/>
		<TexCoords left="0.73046875" right="0.99218750" top="0.00195313" bottom="0.13281250"/>
	</Texture>
	<Texture name="PetTalent-TalentIconBorder" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="59" y="59"/>
		<TexCoords left="0.73046875" right="0.96093750" top="0.13671875" bottom="0.25195313"/>
	</Texture>
	<Texture name="Talent-GoldMedal-Glow" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="54" y="54"/>
		<TexCoords left="0.73046875" right="0.94140625" top="0.25585938" bottom="0.36132813"/>
	</Texture>
	<Texture name="Talent-TreeLockoutGradient" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="8" y="32"/>
		<TexCoords left="0.94921875" right="0.98046875" top="0.25585938" bottom="0.31835938"/>
	</Texture>
	<Texture name="HelpPlateBox-Glow-BottomRight" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="11" y="11"/>
		<TexCoords left="0.94921875" right="0.99218750" top="0.32226563" bottom="0.34375000"/>
	</Texture>
	<Texture name="PetTalent-TreeBorder" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="225" y="45"/>
		<TexCoords left="0.00390625" right="0.88281250" top="0.36523438" bottom="0.45312500"/>
	</Texture>
	<Texture name="HelpPlateBox-Shadow-BottomRight" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="25" y="25"/>
		<TexCoords left="0.89062500" right="0.98828125" top="0.36523438" bottom="0.41406250"/>
	</Texture>
	<Texture name="Talent-TitleGlow-Right" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="18" y="18"/>
		<TexCoords left="0.89062500" right="0.96093750" top="0.41796875" bottom="0.45312500"/>
	</Texture>
	<Texture name="PetTalent-TreeTitle-BG" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="225" y="44"/>
		<TexCoords left="0.00390625" right="0.88281250" top="0.45703125" bottom="0.54296875"/>
	</Texture>
	<Texture name="HelpPlateBox-Shadow-TopLeft" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="25" y="25"/>
		<TexCoords left="0.89062500" right="0.98828125" top="0.45703125" bottom="0.50585938"/>
	</Texture>
	<Texture name="TalentCover-SmallIconBorder" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="16" y="16"/>
		<TexCoords left="0.89062500" right="0.95312500" top="0.50976563" bottom="0.54101563"/>
	</Texture>
	<Texture name="Talent-PrimaryHighlight-BottomRight" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="9" y="9"/>
		<TexCoords left="0.96093750" right="0.99609375" top="0.50976563" bottom="0.52734375"/>
	</Texture>
	<Texture name="TalentHeader-ParchmentBG" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="198" y="33"/>
		<TexCoords left="0.00390625" right="0.77734375" top="0.54687500" bottom="0.61132813"/>
	</Texture>
	<Texture name="HelpPlateArrowDOWN" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="53" y="21"/>
		<TexCoords left="0.78515625" right="0.99218750" top="0.54687500" bottom="0.58789063"/>
	</Texture>
	<Texture name="HelpPlateArrowUP" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="53" y="21"/>
		<TexCoords left="0.78515625" right="0.99218750" bottom="0.54687500" top="0.58789063"/>
	</Texture>
	<Texture name="Talent-PrimaryHighlight-TopLeft" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="9" y="9"/>
		<TexCoords left="0.78515625" right="0.82031250" top="0.59179688" bottom="0.60937500"/>
	</Texture>
	<Texture name="Talent-PrimaryHighlight-TopRight" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="9" y="9"/>
		<TexCoords left="0.82812500" right="0.86328125" top="0.59179688" bottom="0.60937500"/>
	</Texture>
	<Texture name="Talent-PrimaryHighlight-BottomLeft" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="9" y="9"/>
		<TexCoords left="0.87109375" right="0.90625000" top="0.59179688" bottom="0.60937500"/>
	</Texture>
	<Texture name="TalentCover-Overlay-BottomLeft" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="8" y="8"/>
		<TexCoords left="0.91406250" right="0.94531250" top="0.59179688" bottom="0.60742188"/>
	</Texture>
	<Texture name="TalentCover-Overlay-BottomRight" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="8" y="8"/>
		<TexCoords left="0.95312500" right="0.98437500" top="0.59179688" bottom="0.60742188"/>
	</Texture>
	<Texture name="TalentHeader-GoldBorder" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="198" y="33"/>
		<TexCoords left="0.00390625" right="0.77734375" top="0.61523438" bottom="0.67968750"/>
	</Texture>
	<Texture name="PetTalent-SingleBorder" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="32" y="32"/>
		<TexCoords left="0.78515625" right="0.91015625" top="0.61523438" bottom="0.67773438"/>
	</Texture>
	<Texture name="Talent-PointBg-Green" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="20" y="19"/>
		<TexCoords left="0.91796875" right="0.99609375" top="0.61523438" bottom="0.65234375"/>
	</Texture>
	<Texture name="HelpPlateBox-Glow-TopLeft" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="11" y="11"/>
		<TexCoords left="0.91796875" right="0.96093750" top="0.65625000" bottom="0.67773438"/>
	</Texture>
	<Texture name="HelpPlateArrow-Shadow" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="101" y="41"/>
		<TexCoords left="0.00390625" right="0.39843750" top="0.68359375" bottom="0.76367188"/>
	</Texture>
	<Texture name="PetTalent-PetIconBorder" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="54" y="54"/>
		<TexCoords left="0.00390625" right="0.21484375" top="0.76757813" bottom="0.87304688"/>
	</Texture>
	<Texture name="TalentHeader-PrimaryIconBorder" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="44" y="44"/>
		<TexCoords left="0.22265625" right="0.39453125" top="0.76757813" bottom="0.85351563"/>
	</Texture>
	<Texture name="TalentCover-Overlay-TopRight" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="8" y="8"/>
		<TexCoords left="0.22265625" right="0.25390625" top="0.85742188" bottom="0.87304688"/>
	</Texture>
	<Texture name="TalentCover-Overlay-TopLeft" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="8" y="8"/>
		<TexCoords left="0.26171875" right="0.29296875" top="0.85742188" bottom="0.87304688"/>
	</Texture>
	<Texture name="TalentHeader-SecondaryIconBorder" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="44" y="44"/>
		<TexCoords left="0.00390625" right="0.17578125" top="0.87695313" bottom="0.96289063"/>
	</Texture>
	<Texture name="Talent-Inner-BotRight" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="14" y="14"/>
		<TexCoords left="0.00390625" right="0.05859375" top="0.96679688" bottom="0.99414063"/>
	</Texture>
	<Texture name="Talent-Inner-TopRight" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="14" y="14"/>
		<TexCoords left="0.06640625" right="0.12109375" top="0.96679688" bottom="0.99414063"/>
	</Texture>
	<Texture name="HelpPlateBox-Glow-TopRight" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="11" y="11"/>
		<TexCoords left="0.12890625" right="0.17187500" top="0.96679688" bottom="0.98828125"/>
	</Texture>
	<Texture name="PetTalent-SingleBorder-Glow" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="44" y="44"/>
		<TexCoords left="0.18359375" right="0.35546875" top="0.87695313" bottom="0.96289063"/>
	</Texture>
	<Texture name="Talent-Inner-BotLeft" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="14" y="14"/>
		<TexCoords left="0.18359375" right="0.23828125" top="0.96679688" bottom="0.99414063"/>
	</Texture>
	<Texture name="Talent-Inner-TopLeft" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="14" y="14"/>
		<TexCoords left="0.24609375" right="0.30078125" top="0.96679688" bottom="0.99414063"/>
	</Texture>
	<Texture name="HelpPlateBox-Glow-BottomLeft" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="11" y="11"/>
		<TexCoords left="0.30859375" right="0.35156250" top="0.96679688" bottom="0.98828125"/>
	</Texture>
	<Texture name="Talent-GoldMedal-Border" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="44" y="44"/>
		<TexCoords left="0.40625000" right="0.57812500" top="0.68359375" bottom="0.76953125"/>
	</Texture>
	<Texture name="Talent-SingleBorder-Glow" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="44" y="44"/>
		<TexCoords left="0.58593750" right="0.75781250" top="0.68359375" bottom="0.76953125"/>
	</Texture>
	<Texture name="PetTalent-SingleBorder-Shadow" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="40" y="38"/>
		<TexCoords left="0.76562500" right="0.92187500" top="0.68359375" bottom="0.75781250"/>
	</Texture>
	<Texture name="GoldLockIcon" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="17" y="21"/>
		<TexCoords left="0.92968750" right="0.99609375" top="0.68359375" bottom="0.72460938"/>
	</Texture>
	<Texture name="HelpPlateArrow-GlowDOWN" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="65" y="28"/>
		<TexCoords left="0.40625000" right="0.66015625" top="0.77343750" bottom="0.82812500"/>
	</Texture>
	<Texture name="HelpPlateArrow-GlowUP" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="65" y="28"/>
		<TexCoords left="0.40625000" right="0.66015625" bottom="0.77343750" top="0.82812500"/>
	</Texture>
	<Texture name="HelpPlateBox-Shadow-TopRight" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="25" y="25"/>
		<TexCoords left="0.66796875" right="0.76562500" top="0.77343750" bottom="0.82226563"/>
	</Texture>
	<Texture name="HelpPlateBox-Shadow-BottomLeft" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="25" y="25"/>
		<TexCoords left="0.77343750" right="0.87109375" top="0.77343750" bottom="0.82226563"/>
	</Texture>
	<Texture name="TalentHeader-PointCircle-Gold" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="23" y="23"/>
		<TexCoords left="0.87890625" right="0.96875000" top="0.77343750" bottom="0.81835938"/>
	</Texture>
	<Texture name="Talent-SingleBorder-Shadow" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="37" y="36"/>
		<TexCoords left="0.40625000" right="0.55078125" top="0.83203125" bottom="0.90234375"/>
	</Texture>
	<Texture name="Talent-SingleBorder" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="31" y="30"/>
		<TexCoords left="0.40625000" right="0.52734375" top="0.90625000" bottom="0.96484375"/>
	</Texture>
	<Texture name="TalentHeader-PointCircle-Silver" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="23" y="23"/>
		<TexCoords left="0.55859375" right="0.64843750" top="0.83203125" bottom="0.87695313"/>
	</Texture>
	<Texture name="Talent-PointBg" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="20" y="19"/>
		<TexCoords left="0.55859375" right="0.63671875" top="0.88085938" bottom="0.91796875"/>
	</Texture>
	<Texture name="Talent-TitleGlow-Left" file="Interface\TalentFrame\TalentFrame-Parts" virtual="true" >
		<Size x="18" y="18"/>
		<TexCoords left="0.55859375" right="0.62890625" top="0.92187500" bottom="0.95703125"/>
	</Texture>


	<Texture name="Dialog-BorderTopLeft" file="Interface\DialogFrame\DialogFrame-Corners" virtual="true" >
		<Size x="32" y="32"/>
		<TexCoords left="0.0" right="0.5" top="0.0" bottom="0.5"/>
	</Texture>
	<Texture name="Dialog-BorderBottomLeft" file="Interface\DialogFrame\DialogFrame-Corners" virtual="true" >
		<Size x="32" y="32"/>
		<TexCoords left="0.0" right="0.5" top="0.5" bottom="1.0"/>
	</Texture>
	<Texture name="Dialog-BorderTopRight" file="Interface\DialogFrame\DialogFrame-Corners" virtual="true" >
		<Size x="32" y="32"/>
		<TexCoords left="0.5" right="1.0" top="0.0" bottom="0.5"/>
	</Texture>
	<Texture name="Dialog-BorderBottomRight" file="Interface\DialogFrame\DialogFrame-Corners" virtual="true" >
		<Size x="32" y="32"/>
		<TexCoords left="0.5" right="1.0" top="0.5" bottom="1.0"/>
	</Texture>
	<Texture name="Thin-BorderTopLeft" file="Interface\Common\ThinBorder-TopLeft" virtual="true" >
		<Size x="32" y="32"/>
	</Texture>
	<Texture name="Thin-BorderBottomLeft" file="Interface\Common\ThinBorder-BottomLeft" virtual="true" >
		<Size x="32" y="32"/>
	</Texture>
	<Texture name="Thin-BorderTopRight" file="Interface\Common\ThinBorder-TopRight" virtual="true" >
		<Size x="32" y="32"/>
	</Texture>
	<Texture name="Thin-BorderBottomRight" file="Interface\Common\ThinBorder-BottomRight" virtual="true" >
		<Size x="32" y="32"/>
	</Texture>


<!-- Vertically tiling pieces -->
	<Texture name="!Talent-Inner-RightTile" file="Interface\TalentFrame\TalentFrame-Vertical" virtual="true" vertTile="true" >
		<Size x="7" y="256"/>
		<TexCoords left="0.15625000" right="0.26562500" top="0.00000000" bottom="1.00000000"/>
	</Texture>
	<Texture name="!Talent-Inner-LeftTile" file="Interface\TalentFrame\TalentFrame-Vertical" virtual="true" vertTile="true" >
		<Size x="7" y="256"/>
		<TexCoords left="0.01562500" right="0.12500000" top="0.00000000" bottom="1.00000000"/>
	</Texture>

	<Texture name="!HelpPlateBox-Right" file="Interface\TalentFrame\TalentFrame-Vertical2" virtual="true" vertTile="true" >
		<Size x="8" y="32"/>
		<TexCoords left="0.00390625" right="0.03515625" top="0.00000000" bottom="1.00000000"/>
	</Texture>
	<Texture name="!HelpPlateBox-Left" file="Interface\TalentFrame\TalentFrame-Vertical2" virtual="true" vertTile="true" >
		<Size x="8" y="32"/>
		<TexCoords left="0.04296875" right="0.07421875" top="0.00000000" bottom="1.00000000"/>
	</Texture>
	<Texture name="!TalentCover-Overlay-Left" file="Interface\TalentFrame\TalentFrame-Vertical2" virtual="true" vertTile="true" >
		<Size x="8" y="32"/>
		<TexCoords left="0.08203125" right="0.11328125" top="0.00000000" bottom="1.00000000"/>
	</Texture>
	<Texture name="!Talent-PrimaryHighlight-Left" file="Interface\TalentFrame\TalentFrame-Vertical2" virtual="true" vertTile="true" >
		<Size x="9" y="32"/>
		<TexCoords left="0.12109375" right="0.15625000" top="0.00000000" bottom="1.00000000"/>
	</Texture>
	<Texture name="!Talent-PrimaryHighlight-Right" file="Interface\TalentFrame\TalentFrame-Vertical2" virtual="true" vertTile="true" >
		<Size x="9" y="32"/>
		<TexCoords left="0.16406250" right="0.19921875" top="0.00000000" bottom="1.00000000"/>
	</Texture>
	<Texture name="!HelpPlateBox-Glow-Right" file="Interface\TalentFrame\TalentFrame-Vertical2" virtual="true" vertTile="true" >
		<Size x="11" y="32"/>
		<TexCoords left="0.20703125" right="0.25000000" top="0.00000000" bottom="1.00000000"/>
	</Texture>
	<Texture name="!HelpPlateBox-Glow-Left" file="Interface\TalentFrame\TalentFrame-Vertical2" virtual="true" vertTile="true" >
		<Size x="11" y="32"/>
		<TexCoords left="0.25781250" right="0.30078125" top="0.00000000" bottom="1.00000000"/>
	</Texture>
	<Texture name="!HelpPlateBox-Shadow-Left" file="Interface\TalentFrame\TalentFrame-Vertical2" virtual="true" vertTile="true" >
		<Size x="25" y="32"/>
		<TexCoords left="0.30859375" right="0.40625000" top="0.00000000" bottom="1.00000000"/>
	</Texture>
	<Texture name="!HelpPlateBox-Shadow-Right" file="Interface\TalentFrame\TalentFrame-Vertical2" virtual="true" vertTile="true" >
		<Size x="25" y="32"/>
		<TexCoords left="0.41406250" right="0.51171875" top="0.00000000" bottom="1.00000000"/>
	</Texture>

	<Texture name="Dialog-BorderLeft" file="Interface\DialogFrame\DialogFrame-Left" virtual="true" vertTile="true">
		<Size x="16" y="32"/>
	</Texture>
	<Texture name="Dialog-BorderRight" file="Interface\DialogFrame\DialogFrame-Right" virtual="true" vertTile="true">
		<Size x="16" y="32"/>
	</Texture>
	<Texture name="!Thin-BorderRight" file="Interface\Common\ThinBorder-Right" virtual="true" vertTile="true">
		<Size x="32" y="32"/>
	</Texture>
	<Texture name="!Thin-BorderLeft" file="Interface\Common\ThinBorder-Left" virtual="true" vertTile="true">
		<Size x="32" y="32"/>
	</Texture>

<!-- Horizontally tiling pieces -->
	<Texture name="_UI-Frame-InnerSplitTile" file="Interface\FrameGeneral\_UI-Frame" virtual="true" horizTile="true" >
		<Size x="256" y="8"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.12500000" bottom="0.18750000"/>
	</Texture>
	<Texture name="_UI-Frame-Top" file="Interface\FrameGeneral\_UI-Frame" virtual="true" horizTile="true" >
		<Size x="256" y="9"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.27343750" bottom="0.20312500"/>
	</Texture>

	<Texture name="_Talent-Inner-TopTile" file="Interface\TalentFrame\TalentFrame-Horizontal" virtual="true" horizTile="true" >
		<Size x="256" y="8"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.00781250" bottom="0.07031250"/>
	</Texture>
	<Texture name="_Talent-Inner-BotTile" file="Interface\TalentFrame\TalentFrame-Horizontal" virtual="true" horizTile="true" >
		<Size x="256" y="8"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.08593750" bottom="0.14843750"/>
	</Texture>
	<Texture name="_Talent-TitleGlowTile" file="Interface\TalentFrame\TalentFrame-Horizontal" virtual="true" horizTile="true" >
		<Size x="256" y="18"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.16406250" bottom="0.30468750"/>
	</Texture>
	<Texture name="_Talent-Mastery-Background" file="Interface\TalentFrame\TalentFrame-Horizontal" virtual="true" horizTile="true" >
		<Size x="256" y="36"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.32031250" bottom="0.60156250"/>
	</Texture>

	<Texture name="_HelpPlateBox-Top" file="Interface\TalentFrame\TalentFrame-Horizontal2" virtual="true" horizTile="true" >
		<Size x="32" y="8"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.00390625" bottom="0.03515625"/>
	</Texture>
	<Texture name="_HelpPlateBox-Bottom" file="Interface\TalentFrame\TalentFrame-Horizontal2" virtual="true" horizTile="true" >
		<Size x="32" y="8"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.04296875" bottom="0.07421875"/>
	</Texture>
	<Texture name="_TalentCover-Overlay-Bottom" file="Interface\TalentFrame\TalentFrame-Horizontal2" virtual="true" horizTile="true" >
		<Size x="32" y="8"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.08203125" bottom="0.11328125"/>
	</Texture>
	<Texture name="_TalentCover-Overlay-Top" file="Interface\TalentFrame\TalentFrame-Horizontal2" virtual="true" horizTile="true" >
		<Size x="32" y="8"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.12109375" bottom="0.15234375"/>
	</Texture>
	<Texture name="_Talent-PrimaryHighlight-Bottom" file="Interface\TalentFrame\TalentFrame-Horizontal2" virtual="true" horizTile="true" >
		<Size x="32" y="9"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.16015625" bottom="0.19531250"/>
	</Texture>
	<Texture name="_Talent-PrimaryHighlight-Top" file="Interface\TalentFrame\TalentFrame-Horizontal2" virtual="true" horizTile="true" >
		<Size x="32" y="9"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.20312500" bottom="0.23828125"/>
	</Texture>
	<Texture name="_HelpPlateBox-Glow-Bottom" file="Interface\TalentFrame\TalentFrame-Horizontal2" virtual="true" horizTile="true" >
		<Size x="32" y="11"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.24609375" bottom="0.28906250"/>
	</Texture>
	<Texture name="_HelpPlateBox-Glow-Top" file="Interface\TalentFrame\TalentFrame-Horizontal2" virtual="true" horizTile="true" >
		<Size x="32" y="11"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.29687500" bottom="0.33984375"/>
	</Texture>
	<Texture name="_HelpPlateBox-Shadow-Top" file="Interface\TalentFrame\TalentFrame-Horizontal2" virtual="true" horizTile="true" >
		<Size x="32" y="25"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.34765625" bottom="0.44531250"/>
	</Texture>
	<Texture name="_HelpPlateBox-Shadow-Bottom" file="Interface\TalentFrame\TalentFrame-Horizontal2" virtual="true" horizTile="true" >
		<Size x="32" y="25"/>
		<TexCoords left="0.00000000" right="1.00000000" top="0.45312500" bottom="0.55078125"/>
	</Texture>


	<Texture name="Dialog-BorderTop" file="Interface\DialogFrame\DialogFrame-Top" virtual="true" horizTile="true">
		<Size x="32" y="16"/>
	</Texture>
	<Texture name="Dialog-BorderBottom" file="Interface\DialogFrame\DialogFrame-Bot" virtual="true" horizTile="true">
		<Size x="32" y="16"/>
	</Texture>
	<Texture name="_Thin-BorderTop" file="Interface\Common\ThinBorder-Top" virtual="true" horizTile="true">
		<Size x="32" y="32"/>
	</Texture>
	<Texture name="_Thin-BorderBottom" file="Interface\Common\ThinBorder-Bottom" virtual="true" horizTile="true">
		<Size x="32" y="32"/>
	</Texture>

<!--
*******************************************************************************
    GlowBoxTemplate
	This is a bright yellow box with a glow around it
*******************************************************************************
-->
	<Frame name="GlowBoxTemplate" virtual="true">
		<Layers>
			<!--
			<Layer level="BACKGROUND" textureSubLevel="-1">
				<Texture name="$parentColorBorder" setAllPoints="true">
					<Color r="1" g="0.82353" b="0"/>
				</Texture>
			</Layer>
			-->
			<Layer level="BACKGROUND">
				<Texture name="$parentBg" parentKey="BG">
					<Anchors>
						<Anchor point="TOPLEFT" x="1" y="-1"/>
						<Anchor point="BOTTOMRIGHT" x="-1" y="1"/>
					</Anchors>
					<Color r="1" g="1" b="1"/>
					<Gradient orientation="VERTICAL">
						<MinColor r="0.23" g="0.19" b="0"/>
						<MaxColor r="0" g="0" b="0"/>
					</Gradient>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="-1">
				<Texture name="$parentGlowTopLeft" inherits="HelpPlateBox-Glow-TopLeft" parentKey="GlowTopLeft" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPLEFT" x="-6" y="6"/>
					</Anchors>
				</Texture>
				<Texture name="$parentGlowTopRight" inherits="HelpPlateBox-Glow-TopRight" parentKey="GlowTopRight" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPRIGHT" x="6" y="6"/>
					</Anchors>
				</Texture>

				<Texture name="$parentGlowBottomLeft" inherits="HelpPlateBox-Glow-BottomLeft" parentKey="GlowBottomLeft" alphaMode="ADD">
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-6" y="-6"/>
					</Anchors>
				</Texture>

				<Texture name="$parentGlowBottomRight" inherits="HelpPlateBox-Glow-BottomRight" parentKey="GlowBottomRight" alphaMode="ADD">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="6" y="-6"/>
					</Anchors>
				</Texture>

				<Texture name="$parentGlowTop" inherits="_HelpPlateBox-Glow-Top" parentKey="GlowTop" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.GlowTopLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="TOPRIGHT" relativeKey="$parent.GlowTopRight" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentGlowBottom" inherits="_HelpPlateBox-Glow-Bottom" parentKey="GlowBottom" alphaMode="ADD">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.GlowBottomLeft" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.GlowBottomRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentGlowLeft" inherits="!HelpPlateBox-Glow-Left" parentKey="GlowLeft" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.GlowTopLeft" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.GlowBottomLeft" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentGlowRight" inherits="!HelpPlateBox-Glow-Right" parentKey="GlowRight" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.GlowTopRight" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.GlowBottomRight" relativePoint="TOPRIGHT"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="-2">
				<Texture name="$parentShadowTopLeft" inherits="HelpPlateBox-Shadow-TopLeft" parentKey="ShadowTopLeft" >
					<Anchors>
						<Anchor point="TOPLEFT" x="-16" y="16"/>
					</Anchors>
				</Texture>
				<Texture name="$parentShadowTopRight" inherits="HelpPlateBox-Shadow-TopRight" parentKey="ShadowTopRight" >
					<Anchors>
						<Anchor point="TOPRIGHT" x="16" y="16"/>
					</Anchors>
				</Texture>

				<Texture name="$parentShadowBottomLeft" inherits="HelpPlateBox-Shadow-BottomLeft" parentKey="ShadowBottomLeft" >
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-16" y="-16"/>
					</Anchors>
				</Texture>

				<Texture name="$parentShadowBottomRight" inherits="HelpPlateBox-Shadow-BottomRight" parentKey="ShadowBottomRight" >
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="16" y="-16"/>
					</Anchors>
				</Texture>

				<Texture name="$parentShadowTop" inherits="_HelpPlateBox-Shadow-Top" parentKey="ShadowTop" >
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.ShadowTopLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="TOPRIGHT" relativeKey="$parent.ShadowTopRight" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentShadowBottom" inherits="_HelpPlateBox-Shadow-Bottom" parentKey="ShadowBottom" >
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.ShadowBottomLeft" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.ShadowBottomRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentShadowLeft" inherits="!HelpPlateBox-Shadow-Left" parentKey="ShadowLeft" >
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.ShadowTopLeft" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.ShadowBottomLeft" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentShadowRight" inherits="!HelpPlateBox-Shadow-Right" parentKey="ShadowRight" >
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.ShadowTopRight" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.ShadowBottomRight" relativePoint="TOPRIGHT"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

<!--
*******************************************************************************
    GlowBoxArrowTemplate
	This is a bright yellow arrow with a glow around it
*******************************************************************************
-->
	<Frame name="GlowBoxArrowTemplate" virtual="true">
		<Size x="53" y="21"/>

		<Layers>
			<Layer level="ARTWORK">
				<Texture name="$parentArrow" parentKey="Arrow" inherits="HelpPlateArrowDOWN">
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture name="$parentGlow" parentKey="Glow" inherits="HelpPlateArrow-GlowDOWN" alphaMode="ADD" alpha="0.5">
					<Anchors>
						<Anchor point="TOP"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

<!--
*******************************************************************************
    ThinBorderTemplate
	Thin box to highlight areas with. Just add color!
*******************************************************************************
-->
	<Frame name="ThinBorderTemplate" virtual="true">
		<Size x="100" y="100"/>
		<Layers>
			<Layer level="BORDER">
				<Texture parentKey="TopLeft" parentArray="Textures" file="Interface\Common\ThinBorder2-Corner">
					<Size x="8" y="8"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-3" y="3"/>
					</Anchors>
				</Texture>
				<Texture parentKey="TopRight" parentArray="Textures" file="Interface\Common\ThinBorder2-Corner">
					<Size x="8" y="8"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="3" y="3"/>
					</Anchors>
					<TexCoords left="1" right="0" top="0" bottom="1"/>
				</Texture>
				<Texture parentKey="BottomLeft" parentArray="Textures" file="Interface\Common\ThinBorder2-Corner">
					<Size x="8" y="8"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-3" y="-3"/>
					</Anchors>
					<TexCoords left="0" right="1" top="1" bottom="0"/>
				</Texture>
				<Texture parentKey="BottomRight" parentArray="Textures" file="Interface\Common\ThinBorder2-Corner">
					<Size x="8" y="8"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="3" y="-3"/>
					</Anchors>
					<TexCoords left="1" right="0" top="1" bottom="0"/>
				</Texture>

				<Texture parentKey="Top" parentArray="Textures" file="Interface\Common\ThinBorder2-Top">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.TopRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Bottom" parentArray="Textures" file="Interface\Common\ThinBorder2-Top">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BottomLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0" right="1" top="1" bottom="0"/>
				</Texture>

				<Texture parentKey="Left" parentArray="Textures" file="Interface\Common\ThinBorder2-Left">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopLeft" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomLeft" relativePoint="TOPRIGHT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Right" parentArray="Textures" file="Interface\Common\ThinBorder2-Left">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopRight" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomRight" relativePoint="TOPRIGHT"/>
					</Anchors>
					<TexCoords left="1" right="0" top="0" bottom="1"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="GlowBorderTemplate" virtual="true">
		<Size x="100" y="100"/>
		<Layers>
			<Layer level="BORDER">
				<Texture parentKey="TopLeft" parentArray="Textures" file="Interface\Common\GlowBorder-Corner" alphaMode="ADD">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-8" y="8"/>
					</Anchors>
				</Texture>
				<Texture parentKey="TopRight" parentArray="Textures" file="Interface\Common\GlowBorder-Corner" alphaMode="ADD">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="8" y="8"/>
					</Anchors>
					<TexCoords left="1" right="0" top="0" bottom="1"/>
				</Texture>
				<Texture parentKey="BottomLeft" parentArray="Textures" file="Interface\Common\GlowBorder-Corner" alphaMode="ADD">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-8" y="-8"/>
					</Anchors>
					<TexCoords left="0" right="1" top="1" bottom="0"/>
				</Texture>
				<Texture parentKey="BottomRight" parentArray="Textures" file="Interface\Common\GlowBorder-Corner" alphaMode="ADD">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="8" y="-8"/>
					</Anchors>
					<TexCoords left="1" right="0" top="1" bottom="0"/>
				</Texture>

				<Texture parentKey="Top" parentArray="Textures" file="Interface\Common\GlowBorder-Top" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.TopRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Bottom" parentArray="Textures" file="Interface\Common\GlowBorder-Top" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BottomLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0" right="1" top="1" bottom="0"/>
				</Texture>

				<Texture parentKey="Left" parentArray="Textures" file="Interface\Common\GlowBorder-Left" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopLeft" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomLeft" relativePoint="TOPRIGHT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Right" parentArray="Textures" file="Interface\Common\GlowBorder-Left" alphaMode="ADD">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopRight" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomRight" relativePoint="TOPRIGHT"/>
					</Anchors>
					<TexCoords left="1" right="0" top="0" bottom="1"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>
<!--
*******************************************************************************
    BasicFrameTemplate
	This is an empty frame with a titlebar and a close button.
*******************************************************************************
-->
	<Frame name="BaseBasicFrameTemplate" virtual="true">
		<Layers>
			<Layer level="OVERLAY">
				<Texture parentKey="TopLeftCorner" inherits="UI-Frame-TopLeftCorner">
					<Anchors>
						<Anchor point="TOPLEFT" x="-6" y="1"/>
					</Anchors>
				</Texture>
				<Texture parentKey="TopRightCorner" inherits="UI-Frame-TopCornerRight">
					<Anchors>
						<Anchor point="TOPRIGHT" x="0" y="1"/>
					</Anchors>
				</Texture>
				<Texture parentKey="TopBorder" inherits="_UI-Frame-TitleTile">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopLeftCorner" relativePoint="TOPRIGHT"/>
						<Anchor point="TOPRIGHT" relativeKey="$parent.TopRightCorner" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
				<FontString parentKey="TitleText" inherits="GameFontNormal" text="">
					<Anchors>
						<Anchor point="TOP" x="-6" y="-4" />
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="BotLeftCorner" inherits="UI-Frame-BotCornerLeft">
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-6" y="-5"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BotRightCorner" inherits="UI-Frame-BotCornerRight">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="0" y="-5"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BottomBorder" inherits="_UI-Frame-Bot">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.BotLeftCorner" relativePoint="BOTTOMRIGHT" y="0"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BotRightCorner" relativePoint="BOTTOMLEFT" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="LeftBorder" inherits="!UI-Frame-LeftTile">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopLeftCorner" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.BotLeftCorner" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="RightBorder" inherits="!UI-Frame-RightTile">
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.TopRightCorner" relativePoint="BOTTOMRIGHT" x="1"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BotRightCorner" relativePoint="TOPRIGHT"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="CloseButton" inherits="UIPanelCloseButton">
				<Anchors>
					<Anchor point="TOPRIGHT" relativePoint="TOPRIGHT">
						<Offset x="4" y="5"/>
					</Anchor>
				</Anchors>
			</Button>
		</Frames>
	</Frame>

	<Frame name="BasicFrameTemplate" inherits="BaseBasicFrameTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="-1">
				<Texture parentKey="Bg" file="Interface\FrameGeneral\UI-Background-Rock" horizTile="true" vertTile="true">
					<Anchors>
						<Anchor point="TOPLEFT" x="2" y="-21"/>
						<Anchor point="BOTTOMRIGHT" x="-2" y="2"/>
					</Anchors>
				</Texture>
				<Texture parentKey="TitleBg" inherits="_UI-Frame-TitleTileBg">
					<Anchors>
						<Anchor point="TOPLEFT" x="2" y="-3"/>
						<Anchor point="TOPRIGHT" x="-25" y="-3"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND">
				<Texture parentKey="TopTileStreaks" inherits="_UI-Frame-TopTileStreaks">
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="-21"/>
						<Anchor point="TOPRIGHT" x="-2" y="-21"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="BasicFrameTemplateWithInset" inherits="BasicFrameTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture parentKey="InsetBg" file="Interface\FrameGeneral\UI-Background-Marble" horizTile="true" vertTile="true">
					<Anchors>
						<Anchor point="TOPLEFT" x="4" y="-24" />
						<Anchor point="BOTTOMRIGHT" x="-6" y="4" />
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="-1">
				<Texture parentKey="InsetBorderTopLeft" inherits="UI-Frame-InnerTopLeft">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.InsetBg"/>
					</Anchors>
				</Texture>

				<Texture parentKey="InsetBorderTopRight" inherits="UI-Frame-InnerTopRight">
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.InsetBg"/>
					</Anchors>
				</Texture>

				<Texture parentKey="InsetBorderBottomLeft" inherits="UI-Frame-InnerBotLeftCorner">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.InsetBg" y="-1"/>
					</Anchors>
				</Texture>

				<Texture parentKey="InsetBorderBottomRight" inherits="UI-Frame-InnerBotRight">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.InsetBg" y="-1"/>
					</Anchors>
				</Texture>

				<Texture parentKey="InsetBorderTop" inherits="_UI-Frame-InnerTopTile">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.InsetBorderTopLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="TOPRIGHT" relativeKey="$parent.InsetBorderTopRight" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="InsetBorderBottom" inherits="_UI-Frame-InnerBotTile">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.InsetBorderBottomLeft" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.InsetBorderBottomRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="InsetBorderLeft" inherits="!UI-Frame-InnerLeftTile">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.InsetBorderTopLeft" relativePoint="BOTTOMLEFT" x="0" y="0" />
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.InsetBorderBottomLeft" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="InsetBorderRight" inherits="!UI-Frame-InnerRightTile">
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.InsetBorderTopRight" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.InsetBorderBottomRight" relativePoint="TOPRIGHT"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

<!--
*******************************************************************************
    InsetFrameTemplate

	This is the inner frame used by ButtonFrameTemplate
	InsetFrameTemplate is shared
*******************************************************************************
-->
	<Frame name="InsetFrameTemplate2" virtual="true">
		<Layers>
			<Layer level="BORDER">
				<Texture parentKey="TopLeftCorner" inherits="Talent-Inner-TopLeft">
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="TopRightCorner" inherits="Talent-Inner-TopRight">
					<Anchors>
						<Anchor point="TOPRIGHT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BotLeftCorner" inherits="Talent-Inner-BotLeft">
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BotRightCorner" inherits="Talent-Inner-BotRight">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="TopBorder" inherits="_Talent-Inner-TopTile">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopLeftCorner" relativePoint="TOPRIGHT" x="0" y="0"/>
						<Anchor point="TOPRIGHT" relativeKey="$parent.TopRightCorner" relativePoint="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BottomBorder" inherits="_Talent-Inner-BotTile">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.BotLeftCorner" relativePoint="BOTTOMRIGHT" x="0" y="0"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BotRightCorner" relativePoint="BOTTOMLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="LeftBorder" inherits="!Talent-Inner-LeftTile">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopLeftCorner" relativePoint="BOTTOMLEFT" x="0" y="0"/>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.BotLeftCorner" relativePoint="TOPLEFT" x="0" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="RightBorder" inherits="!Talent-Inner-RightTile">
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.TopRightCorner" relativePoint="BOTTOMRIGHT" x="0" y="0"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BotRightCorner" relativePoint="TOPRIGHT" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="InsetFrameTemplate3" virtual="true">
		<Layers>
			<Layer level="BORDER">
				<Texture parentKey="BorderTopRight" file="Interface\Common\Common-Input-Border">
					<Size x="8" y="8"/>
					<Anchors>
						<Anchor point="TOPRIGHT"/>
					</Anchors>
					<TexCoords left="0.9375" right="1.0" top="0" bottom="0.25"/>
				</Texture>
				<Texture parentKey="BorderBottomRight" file="Interface\Common\Common-Input-Border">
					<Size x="8" y="8"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
					<TexCoords left="0.9375" right="1.0" top="0.375" bottom="0.625"/>
				</Texture>
				<Texture parentKey="BorderRightMiddle" file="Interface\Common\Common-Input-Border">
					<Size x="8" y="0"/>
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.BorderTopRight" relativePoint="BOTTOM"/>
						<Anchor point="BOTTOM" relativeKey="$parent.BorderBottomRight" relativePoint="TOP"/>
					</Anchors>
					<TexCoords left="0.9375" right="1.0" top="0.25" bottom="0.375"/>
				</Texture>
				<Texture parentKey="BorderTopLeft" file="Interface\Common\Common-Input-Border">
					<Size x="8" y="8"/>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.0625" top="0" bottom="0.25"/>
				</Texture>
				<Texture parentKey="BorderBottomLeft" file="Interface\Common\Common-Input-Border">
					<Size x="8" y="8"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.0625" top="0.375" bottom="0.625"/>
				</Texture>
				<Texture parentKey="BorderLeftMiddle" file="Interface\Common\Common-Input-Border">
					<Size x="8" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BorderTopLeft" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.BorderBottomLeft" relativePoint="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.0625" top="0.25" bottom="0.375"/>
				</Texture>
				<Texture parentKey="BorderTopMiddle" file="Interface\Common\Common-Input-Border">
					<Size x="100" y="8"/>
					<Anchors>
						<Anchor point="RIGHT" relativeKey="$parent.BorderTopRight" relativePoint="LEFT"/>
						<Anchor point="LEFT" relativeKey="$parent.BorderTopLeft" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.0625" right="0.9375" top="0" bottom="0.25"/>
				</Texture>
				<Texture parentKey="BorderBottomMiddle" file="Interface\Common\Common-Input-Border">
					<Size x="100" y="8"/>
					<Anchors>
						<Anchor point="RIGHT" relativeKey="$parent.BorderBottomRight" relativePoint="LEFT"/>
						<Anchor point="LEFT" relativeKey="$parent.BorderBottomLeft" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.0625" right="0.9375" top="0.375" bottom="0.625"/>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND">
				<Texture parentKey="Bg" file="Interface\Common\Common-Input-Border">
					<Size x="100" y="4"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BorderTopLeft" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BorderBottomRight" relativePoint="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0.0625" right="0.9375" top="0.25" bottom="0.375"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

	<!-- Similar to InsetFrameTemplate3, but without the dark background -->
	<Frame name="InsetFrameTemplate4" virtual="true">
		<Layers>
			<Layer level="BORDER">
				<Texture parentKey="BorderTopRight" atlas="GeneralFrame-InsetFrame-TopRight" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPRIGHT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BorderBottomRight" atlas="GeneralFrame-InsetFrame-BottomRight" useAtlasSize="true">
					<Anchors>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BorderRightMiddle" atlas="!GeneralFrame-InsetFrame-Right" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOP" relativeKey="$parent.BorderTopRight" relativePoint="BOTTOM"/>
						<Anchor point="BOTTOM" relativeKey="$parent.BorderBottomRight" relativePoint="TOP"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BorderTopLeft" atlas="GeneralFrame-InsetFrame-TopLeft" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BorderBottomLeft" atlas="GeneralFrame-InsetFrame-BottomLeft" useAtlasSize="true">
					<Anchors>
						<Anchor point="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BorderLeftMiddle" atlas="!GeneralFrame-InsetFrame-Left" useAtlasSize="true">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BorderTopLeft" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.BorderBottomLeft" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BorderTopMiddle" atlas="_GeneralFrame-InsetFrame-Top" useAtlasSize="true">
					<Anchors>
						<Anchor point="RIGHT" relativeKey="$parent.BorderTopRight" relativePoint="LEFT"/>
						<Anchor point="LEFT" relativeKey="$parent.BorderTopLeft" relativePoint="RIGHT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BorderBottomMiddle" atlas="_GeneralFrame-InsetFrame-Bottom" useAtlasSize="true">
					<Anchors>
						<Anchor point="RIGHT" relativeKey="$parent.BorderBottomRight" relativePoint="LEFT"/>
						<Anchor point="LEFT" relativeKey="$parent.BorderBottomLeft" relativePoint="RIGHT"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

<!--
*******************************************************************************
    EtherealFrameTemplate
	This is a Portrait Frame that also has the Ethereal themed border

*******************************************************************************
-->
	<Frame name="EtherealFrameTemplate" inherits="PortraitFrameTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="-2">
				<Texture name="$parentCornerTL" file="Interface\Transmogrify\Textures.png">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-2" y="-8"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.50781250" top="0.00195313" bottom="0.12695313"/>
				</Texture>
				<Texture name="$parentCornerTR" file="Interface\Transmogrify\Textures.png">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="0" y="-8"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.50781250" top="0.38476563" bottom="0.50781250"/>
				</Texture>
				<Texture name="$parentCornerBL" file="Interface\Transmogrify\Textures.png">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-2" y="16"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.50781250" top="0.25781250" bottom="0.38085938"/>
				</Texture>
				<Texture name="$parentCornerBR" file="Interface\Transmogrify\Textures.png">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="0" y="16"/>
					</Anchors>
					<TexCoords left="0.00781250" right="0.50781250" top="0.13085938" bottom="0.25390625"/>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="-3">
				<Texture name="$parentLeftEdge" file="Interface\Transmogrify\VerticalTiles.png" vertTile="true">
					<Size x="23" y="64"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentCornerTL" relativePoint="BOTTOMLEFT" x="3" y="16"/>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentCornerBL" relativePoint="TOPLEFT" x="3" y="-16"/>
					</Anchors>
					<TexCoords left="0.40625000" right="0.76562500" top="0.00000000" bottom="1.00000000"/>
				</Texture>
				<Texture name="$parentRightEdge" file="Interface\Transmogrify\VerticalTiles.png" vertTile="true">
					<Size x="23" y="64"/>
					<Anchors>
						<Anchor point="TOPRIGHT" relativeTo="$parentCornerTR" relativePoint="BOTTOMRIGHT" x="-3" y="16"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentCornerBR" relativePoint="TOPRIGHT" x="-3" y="-16"/>
					</Anchors>
					<TexCoords left="0.01562500" right="0.37500000" top="0.00000000" bottom="1.00000000"/>
				</Texture>
				<Texture name="$parentTopEdge" file="Interface\Transmogrify\HorizontalTiles.png" horizTile="true">
					<Size x="64" y="23"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentCornerTL" relativePoint="TOPRIGHT" x="-30" y="-6"/>
						<Anchor point="TOPRIGHT" relativeTo="$parentCornerTR" relativePoint="TOPLEFT" x="30" y="-6"/>
					</Anchors>
					<TexCoords left="0.00000000" right="1.00000000" top="0.40625000" bottom="0.76562500"/>
				</Texture>
				<Texture name="$parentBottomEdge" file="Interface\Transmogrify\HorizontalTiles.png" horizTile="true">
					<Size x="64" y="23"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentCornerBL" relativePoint="BOTTOMRIGHT" x="-30" y="4"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentCornerBR" relativePoint="BOTTOMLEFT" x="30" y="4"/>
					</Anchors>
					<TexCoords left="0.00000000" right="1.00000000" top="0.01562500" bottom="0.37500000"/>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTitleBg"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentTitleBg"/>
					</Anchors>
					<Color r="0.302" g="0.102" b="0.204" a="1"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>


<!--
*******************************************************************************
	TruncatedButtonTemplate
	This button template will truncate any text that goes out of the button. If text is truncated,
	the button will have a tooltip with the full text.
*******************************************************************************
-->
	<Button name="TruncatedButtonTemplate" motionScriptsWhileDisabled="true" virtual="true">
		<Scripts>
			<OnSizeChanged>
				local text = _G[self:GetName().."Text"];
				text:SetWidth(self:GetWidth() - 5);
				text:SetHeight(self:GetHeight());
			</OnSizeChanged>
			<OnEnter>
				TruncatedButton_OnEnter(self);
			</OnEnter>
			<OnLeave>
				TruncatedButton_OnLeave(self);
			</OnLeave>
		</Scripts>
	</Button>
	
	<Frame name="TruncatedTooltipScriptTemplate" virtual="true">
		<Scripts>
			<OnEnter function="TruncatedTooltipScript_OnEnter"/>
			<OnLeave function="TruncatedTooltipScript_OnLeave"/>
		</Scripts>
	</Frame>

	<Frame name="HorizontalBarTemplate" virtual="true">
		<Size x="300" y="6"/>
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="-5">
				<Texture name="$parentBg" file="Interface\FrameGeneral\UI-Background-Rock" horizTile="true" vertTile="true">
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="-4">
				<Texture name="$parentTopLeftCorner" inherits="UI-Frame-InnerBotLeftCorner">
					<Anchors>
						<Anchor point="TOPLEFT" y="5"/>
					</Anchors>
				</Texture>
				<Texture name="$parentTopRightCorner" inherits="UI-Frame-InnerBotRight">
					<Anchors>
						<Anchor point="TOPRIGHT" y="5"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBotLeftCorner" inherits="UI-Frame-InnerTopLeft">
					<Anchors>
						<Anchor point="BOTTOMLEFT" y="-5"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBotRightCorner" inherits="UI-Frame-InnerTopRight">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" y="-5"/>
					</Anchors>
				</Texture>

				<Texture name="$parentTopBorder" inherits="_UI-Frame-InnerBotTile">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTopLeftCorner" relativePoint="TOPRIGHT" y="-3"/>
						<Anchor point="TOPRIGHT" relativeTo="$parentTopRightCorner" relativePoint="TOPLEFT" y="-3"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBottomBorder" inherits="_UI-Frame-InnerTopTile">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentBotLeftCorner" relativePoint="BOTTOMRIGHT" y="3"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentBotRightCorner" relativePoint="BOTTOMLEFT" y="3"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>


	<Frame name="TranslucentFrameTemplate" virtual="true">
		<Size x="338" y="424"/>
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="-8">
				<Texture name="$parentBg" parentKey="Bg">
					<Anchors>
						<Anchor point="TOPLEFT"  x="10" y="-10"/>
						<Anchor point="BOTTOMRIGHT"  x="-10" y="10"/>
					</Anchors>
					<Color r="0" g="0" b="0" a="0.5"/>
				</Texture>
			</Layer>
			<Layer level="BORDER" textureSubLevel="-5">
				<Texture name="$parentTopLeftCorner" parentKey="TopLeftCorner" inherits="Dialog-BorderTopLeft">
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentTopRightCorner" parentKey="TopRightCorner" inherits="Dialog-BorderTopRight">
					<Anchors>
						<Anchor point="TOPRIGHT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBottomLeftCorner" parentKey="BottomLeftCorner" inherits="Dialog-BorderBottomLeft">
					<Anchors>
						<Anchor point="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBottomRightCorner" parentKey="BottomRightCorner" inherits="Dialog-BorderBottomRight">
					<Anchors>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentTopBorder" parentKey="TopBorder" inherits="Dialog-BorderTop">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopLeftCorner" relativePoint="TOPRIGHT"  x="0" y="-1"/>
						<Anchor point="TOPRIGHT" relativeKey="$parent.TopRightCorner" relativePoint="TOPLEFT"  x="0" y="-1"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBottomBorder" parentKey="BottomBorder" inherits="Dialog-BorderBottom">
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.BottomLeftCorner" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomRightCorner" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentLeftBorder" parentKey="LeftBorder" inherits="Dialog-BorderLeft">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopLeftCorner" relativePoint="BOTTOMLEFT" x="1" y="0"/>
						<Anchor point="BOTTOMLEFT" relativeKey="$parent.BottomLeftCorner" relativePoint="TOPLEFT" x="1" y="0"/>
					</Anchors>
				</Texture>
				<Texture name="$parentRightBorder" parentKey="RightBorder" inherits="Dialog-BorderRight">
					<Anchors>
						<Anchor point="TOPRIGHT" relativeKey="$parent.TopRightCorner" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomRightCorner" relativePoint="TOPRIGHT"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="ShadowOverlaySmallTemplate" virtual="true">
		<Layers>
			<Layer level="OVERLAY">
				<Texture name="$parentTopLeft" file="Interface\Common\ShadowOverlay-Corner" parentKey="TopLeft">
					<Size x="24" y="24"/>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentTopRight" file="Interface\Common\ShadowOverlay-Corner" parentKey="TopRight">
					<Size x="24" y="24"/>
					<Anchors>
						<Anchor point="TOPRIGHT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBottomLeft" file="Interface\Common\ShadowOverlay-Corner" parentKey="BottomLeft">
					<Size x="24" y="24"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBottomRight" file="Interface\Common\ShadowOverlay-Corner" parentKey="BottomRight">
					<Size x="24" y="24"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentTop" file="Interface\Common\ShadowOverlay-Top">
					<Size x="0" y="24"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTopLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="TOPRIGHT" relativeTo="$parentTopRight" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentBottom" file="Interface\Common\ShadowOverlay-Bottom">
					<Size x="0" y="24"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentBottomLeft" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentLeft" file="Interface\Common\ShadowOverlay-Left">
					<Size x="24" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTopLeft" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentBottomLeft" relativePoint="TOPLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="$parentRight" file="Interface\Common\ShadowOverlay-Right">
					<Size x="24" y="0"/>
					<Anchors>
						<Anchor point="TOPRIGHT" relativeTo="$parentTopRight" relativePoint="BOTTOMRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentBottomRight" relativePoint="TOPRIGHT"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				self.TopRight:SetTexCoord(0, 1, 1, 1, 0, 0, 1, 0);
				self.BottomRight:SetTexCoord(1, 1, 1, 0, 0, 1, 0, 0);
				self.BottomLeft:SetTexCoord(1, 0, 0, 0, 1, 1, 0, 1);
			</OnLoad>
		</Scripts>
	</Frame>
<!--
*******************************************************************************
    Cap progress bar
*******************************************************************************
-->

	<Texture name="CapProgressBarDividerTemplate" file="Interface\GuildFrame\GuildFrame" virtual="true">
		<Size x="7" y="14"/>
		<TexCoords left="0.41601563" right="0.42285156" top="0.91796875" bottom="0.94531250"/>
	</Texture>
	<Frame name="CapProgressBarTemplate" virtual="true">
		<Size>
			<AbsDimension x="254" y="20"/>
		</Size>
		<Layers>
			<Layer level="BORDER">
				<Texture name="$parentLeft" file="Interface\GuildFrame\GuildFrame">
					<Size x="18" y="18"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parent" relativePoint="BOTTOMLEFT" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.60742188" right="0.62500000" top="0.78710938" bottom="0.82226563"/>
				</Texture>
				<Texture name="$parentRight" file="Interface\GuildFrame\GuildFrame">
					<Size x="18" y="18"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parent" relativePoint="BOTTOMRIGHT" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.60742188" right="0.62500000" top="0.82617188" bottom="0.86132813"/>
				</Texture>
				<Texture name="$parentMiddle" file="Interface\GuildFrame\GuildFrame">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT" x="0" y="0"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentRight" relativePoint="BOTTOMLEFT" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.60742188" right="0.62500000" top="0.74804688" bottom="0.78320313"/>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND">
				<Texture name="$parentBG" file="Interface\GuildFrame\GuildFrame">
					<Size x="0" y="14"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="LEFT" x="0" y="0"/>
						<Anchor point="RIGHT" relativeTo="$parentRight" relativePoint="RIGHT" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.39843750" right="0.41210938" top="0.96875000" bottom="0.99609375"/>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture name="$parentProgress" file="Interface\TargetingFrame\UI-StatusBar" parentKey="progress">
					<Size x="1" y="14"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="LEFT" x="1" y="0"/>
					</Anchors>
					<Color r="0.4706" g="0.2627" b="0.8588"/>
				</Texture>
				<Texture name="$parentShadow" file="Interface\GuildFrame\GuildFrame" parentKey="shadow">
					<Size x="9" y="14"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentProgress" relativePoint="RIGHT" x="0" y="0"/>
					</Anchors>
					<TexCoords left="0.41601563" right="0.42480469" top="0.88671875" bottom="0.91406250"/>
				</Texture>
				<Texture name="$parentCap1" file="Interface\TargetingFrame\UI-StatusBar" parentKey="cap1">
					<Size x="20" y="14"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentProgress" relativePoint="RIGHT" x="0" y="0"/>
					</Anchors>
					<Color r="0.4863" g="0.5529" b="0.6353" a="0.3"/>
				</Texture>
				<Texture name="$parentCap2" file="Interface\TargetingFrame\UI-StatusBar" parentKey="cap2">
					<Size x="20" y="14"/>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentCap1" relativePoint="RIGHT" x="0" y="0"/>
					</Anchors>
					<Color r="0.8588" g="0.5961" b="0.2627" a="0.3"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<FontString name="$parentLabel" inherits="GameFontNormalSmall" parentKey="label">
					<Anchors>
						<Anchor point="BOTTOM" relativeTo="$parent" relativePoint="TOP"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="ARTWORK" textureSubLevel="1">
				<FontString name="$parentText" inherits="GameFontHighlightSmall">
					<Anchors>
						<Anchor point="BOTTOM" x="0" y="4"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentCap1Marker" parentKey="cap1Marker" id="1">
				<Size x="9" y="20"/>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentCap1" relativePoint="RIGHT" x="-4" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture name="$parentTexture" file="Interface\GuildFrame\GuildFrame" parentKey="tex" >
							<TexCoords left="0.38378906" right="0.39257813" top="0.95898438" bottom="0.99804688"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Frame>
			<Frame name="$parentCap2Marker" parentKey="cap2Marker" id="2">
				<Size x="9" y="20"/>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentCap2" relativePoint="RIGHT" x="-4" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture name="$parentTexture" file="Interface\GuildFrame\GuildFrame" parentKey="tex" >
							<TexCoords left="0.38378906" right="0.39257813" top="0.95898438" bottom="0.99804688"/>
							<Color r="1.0" g="0.7567" b="0.0" a="1"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnLeave function="GameTooltip_Hide"/>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLeave function="GameTooltip_Hide"/>
		</Scripts>
	</Frame>

<!--
*******************************************************************************
    Frame Template Examples
*******************************************************************************
-->

	<!--<Frame name="ExampleButtonFrame" inherits="ButtonFrameTemplate" parent="UIParent" hidden="true">
		<Size x="384" y="512"/>
		<Anchors>
			<Anchor point="CENTER" x="0" y="0"/>
		</Anchors>
		<Frames>

			> Two buttons anchored in the lower right
			<Button name="ExampleButtonFrameBtn1" inherits="MagicButtonTemplate" text="Button 1">
				<Size x="80" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" />
				</Anchors>
			</Button>
			<Button name="ExampleButtonFrameBtn2" inherits="MagicButtonTemplate" text="Button 2">
				<Size x="80" y="22"/>
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ExampleButtonFrameBtn1" relativePoint="TOPLEFT" />
				</Anchors>
			</Button>

			> A third button that stretches all the way across
			<Button name="ExampleButtonFrameBtn3" inherits="MagicButtonTemplate" text="Button 3">
				<Size x="80" y="22"/>
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="ExampleButtonFrameBtn2" relativePoint="TOPLEFT" />
					<Anchor point="BOTTOMLEFT" />
				</Anchors>
			</Button>

			> Two buttons anchored in the lower left
			<Button name="ExampleButtonFrameBtn4" inherits="MagicButtonTemplate" text="Button 4">
				<Size x="80" y="22"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT" />
				</Anchors>
			</Button>
			<Button name="ExampleButtonFrameBtn5" inherits="MagicButtonTemplate" text="Button 5">
				<Size x="80" y="22"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="ExampleButtonFrameBtn4" relativePoint="TOPRIGHT" />
				</Anchors>
			</Button>

			> A single button in the center
			<Button name="ButtonFrameTestButton6" inherits="MagicButtonTemplate" text="Centered Button">
				<Size x="180" y="22"/>
				<Anchors>
					<Anchor point="BOTTOM"/>
				</Anchors>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				ExampleButtonFramePortrait:SetTexture("Interface\\FriendsFrame\\FriendsFrameScrollIcon");
				ExampleButtonFrameTitleText:SetText("Example Button Frame");
			</OnLoad>
		</Scripts>
	</Frame>
	-->

	<!--
*******************************************************************************
    Gold edge designed to go around money frames
*******************************************************************************
-->

	<Frame name="ThinGoldEdgeTemplate" virtual="true">
		<Size>
			<AbsDimension x="100" y="20"/>
		</Size>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentLeft" file="Interface\Common\Moneyframe">
					<Size x="7" y="20"/>
					<Anchors>
						<Anchor point="TOPLEFT"/>
						<Anchor point="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0.953125" right="0.9921875" top="0" bottom="0.296875"/>
				</Texture>
				<Texture name="$parentRight" file="Interface\Common\Moneyframe">
					<Size x="7" y="20"/>
					<Anchors>
						<Anchor point="TOPRIGHT"/>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
					<TexCoords left="0" right="0.0546875" top="0" bottom="0.296875"/>
				</Texture>
				<Texture name="$parentMiddle" file="Interface\Common\Moneyframe">
					<Size x="80" y="20"/>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentLeft" relativePoint="TOPRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeTo="$parentRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.9921875" top="0.3125" bottom="0.609375"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

<!--
*******************************************************************************
    Help Plate System
*******************************************************************************
-->
	<Button name="MainHelpPlateButton" virtual="true">
		<Size x="64" y="64"/>
		<HitRectInsets>
			<AbsInset left="20" right="20" top="20" bottom="20"/>
		</HitRectInsets>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="I" file="Interface\common\help-i">
					<Size x="46" y="46"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="Ring" file="Interface\Minimap\MiniMap-TrackingBorder">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="CENTER" x="12" y="-13"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentBigIPulse" file="Interface\common\help-i" alphaMode="ADD" hidden="true">
					<Size x="46" y="46"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
				<Texture name="$parentRingPulse" file="Interface\TutorialFrame\minimap-glow" alphaMode="ADD" hidden="true">
					<Size x="52" y="52"/>
					<Anchors>
						<Anchor point="CENTER" x="0" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnEnter function="Main_HelpPlate_Button_OnEnter"/>
			<OnLeave function="Main_HelpPlate_Button_OnLeave"/>
			<OnMouseDown>
				self.I:SetPoint("CENTER", 1, -1);
			</OnMouseDown>
			<OnMouseUp>
				PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
				self.I:SetPoint("CENTER", 0, 0);
			</OnMouseUp>
			<OnHide function="Main_HelpPlate_Button_OnLeave"/>
		</Scripts>
		<HighlightTexture alphaMode="ADD" file="Interface\Minimap\UI-Minimap-ZoomButton-Highlight">
			<Size x="46" y="46"/>
			<Anchors>
				<Anchor point="CENTER" x="-1" y="1"/>
			</Anchors>
		</HighlightTexture>
		<Animations>
			<AnimationGroup parentKey="Pulse" looping="BOUNCE">
				<Alpha target="$parentBigIPulse" fromAlpha="0.75" toAlpha="0" duration="1.0" smoothing="IN_OUT" order="1"/>
				<Alpha target="$parentRingPulse" fromAlpha="0.5" toAlpha="0" duration="1.0" smoothing="IN_OUT" order="1"/>
			</AnimationGroup>
		</Animations>
	</Button>

	<Button name="HelpPlate" parent="UIParent" hidden="true" toplevel="true" enableMouse="true" enableKeyboard="true" frameStrata="DIALOG">
		<Size x="100" y="100"/>
<!--
		<Layers>
			<Layer level="BACKGROUND">
				<Texture setAllPoints="true">
					<Color r="0" g="0" b="0" a="0.5"/>
				</Texture>
			</Layer>
		</Layers>
-->
	</Button>

	<Frame name="HelpPlateTooltip" inherits="GlowBoxTemplate" parent="UIParent" hidden="true" frameStrata="FULLSCREEN_DIALOG" frameLevel="2">
		<Size x="220" y="100"/>
		<Layers>
			<Layer level="OVERLAY">
				<FontString parentKey="Text" inherits="GameFontHighlightLeft">
					<Size x="200" y="0"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="15" y="-15"/>
					</Anchors>
				</FontString>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="ArrowUP" inherits="HelpPlateArrowDOWN" hidden="true">
					<Size x="53" y="21"/>
					<Anchors>
						<Anchor point="TOP" relativePoint="BOTTOM" x="0" y="3"/>
					</Anchors>
				</Texture>
				<Texture parentKey="ArrowDOWN" inherits="HelpPlateArrowUP" hidden="true">
					<Size x="53" y="21"/>
					<Anchors>
						<Anchor point="BOTTOM" relativePoint="TOP" x="0" y="-3"/>
					</Anchors>
				</Texture>
				<Texture parentKey="ArrowRIGHT" inherits="HelpPlateArrowDOWN" hidden="true">
					<Size x="53" y="21"/>
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT" x="3" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="ArrowLEFT" inherits="HelpPlateArrowDOWN" hidden="true">
					<Size x="53" y="21"/>
					<Anchors>
						<Anchor point="LEFT" relativePoint="RIGHT" x="-3" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="ArrowGlowUP" inherits="HelpPlateArrow-GlowDOWN" hidden="true" alphaMode="ADD" alpha="0.5">
					<Size x="53" y="21"/>
					<Anchors>
						<Anchor point="TOP" relativePoint="BOTTOM" x="0" y="3"/>
					</Anchors>
				</Texture>
				<Texture parentKey="ArrowGlowDOWN" inherits="HelpPlateArrow-GlowUP" hidden="true" alphaMode="ADD" alpha="0.5">
					<Size x="53" y="21"/>
					<Anchors>
						<Anchor point="BOTTOM" relativePoint="TOP" x="0" y="-3"/>
					</Anchors>
				</Texture>
				<Texture parentKey="ArrowGlowRIGHT" inherits="HelpPlateArrow-GlowDOWN" hidden="true" alphaMode="ADD" alpha="0.5">
					<Size x="53" y="21"/>
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT" x="3" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="ArrowGlowLEFT" inherits="HelpPlateArrow-GlowDOWN" hidden="true" alphaMode="ADD" alpha="0.5">
					<Size x="53" y="21"/>
					<Anchors>
						<Anchor point="LEFT" relativePoint="RIGHT" x="-3" y="0"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="LingerAndFade">
				<Alpha childKey="FadeOut" startDelay="5" fromAlpha="1" toAlpha="0" duration="1" smoothing="IN_OUT" order="2"/>
				<Scripts>
					<OnFinished>
						self:GetParent():Hide();
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Scripts>
			<OnLoad>
				self.Text:SetSpacing(4);
				SetClampedTextureRotation(self.ArrowLEFT, 270);
				SetClampedTextureRotation(self.ArrowRIGHT, 90);
				SetClampedTextureRotation(self.ArrowGlowLEFT, 270);
				SetClampedTextureRotation(self.ArrowGlowRIGHT, 90);
			</OnLoad>
			<OnShow>
				self:SetHeight(self.Text:GetHeight()+30);
			</OnShow>
		</Scripts>
	</Frame>

	<Button name="HelpPlateButton" virtual="true" frameStrata="DIALOG" hidden="true">
		<Size x="46" y="46"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture parentKey="HelpI" hidden="false" alpha="1" alphaMode="BLEND" file="Interface\common\help-i"/>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="HelpIGlow" hidden="false" alpha="1" alphaMode="ADD" file="Interface\common\help-i"/>
				<Texture parentKey="BgGlow" hidden="false" alpha="1" alphaMode="ADD" file="Interface\Minimap\UI-Minimap-ZoomButton-Highlight"/>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad function="HelpPlate_Button_OnLoad"/>
			<OnShow function="HelpPlate_Button_OnShow"/>
			<OnEnter function="HelpPlate_Button_OnEnter"/>
			<OnLeave function="HelpPlate_Button_OnLeave"/>
		</Scripts>
		<HighlightTexture alphaMode="ADD" file="Interface\Minimap\UI-Minimap-ZoomButton-Highlight">
			<Size x="46" y="46"/>
			<Anchors>
				<Anchor point="CENTER" x="-1" y="-1"/>
			</Anchors>
		</HighlightTexture>
		<Animations>
			<AnimationGroup parentKey="Pulse" setToFinalAlpha="true" looping="REPEAT">
				<Alpha childKey="HelpIGlow" duration="0.5" order="1" fromAlpha="0.25" toAlpha="1"/>
				<Alpha childKey="HelpIGlow" startDelay="0.5" duration="1" order="1" fromAlpha="1" toAlpha="0.25"/>
				<Alpha childKey="BgGlow" duration="0.5" order="1" fromAlpha="0.25" toAlpha="1"/>
				<Alpha childKey="BgGlow" startDelay="0.5" duration="1" order="1" fromAlpha="1" toAlpha="0.25"/>
			</AnimationGroup>
		</Animations>
	</Button>

	<Frame name="HelpPlateBox" inherits="ThinBorderTemplate" virtual="true" hidden="true">
		<Size x="200" y="200"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="BG">
					<Anchors>
						<Anchor point="TOPLEFT" x="0" y="0"/>
						<Anchor point="BOTTOMRIGHT" x="0" y="0"/>
					</Anchors>
					<Color r="0" g="0" b="0" a="0.4"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad function="HelpPlateBox_OnLoad"/>
			<OnEnter function="HelpPlateBox_OnEnter"/>
			<OnLeave function="HelpPlateBox_OnLeave"/>
		</Scripts>
	</Frame>

	<Frame name="HelpPlateBoxHighlight" inherits="GlowBorderTemplate" virtual="true" hidden="true">
		<Size x="200" y="200"/>
	</Frame>

<!--
*******************************************************************************
    Utility Frames
*******************************************************************************
-->

	<ScrollFrame name="InputScrollFrameTemplate" inherits="UIPanelScrollFrameTemplate" virtual="true">
		<KeyValues>
			<!--
			<KeyValue key="maxLetters" value="127" type="number"/>
			<KeyValue key="instructions" value="INSTRUCTION_TEXT" type="global"/>
			<KeyValue key="hideCharCount" value="false" type="boolean"/>
			-->
		</KeyValues>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="TopLeftTex" file="Interface\Common\Common-Input-Border-TL">
					<Size x="8" y="8"/>
					<Anchors>
						<Anchor point="TOPLEFT" x="-5" y="5"/>
					</Anchors>
				</Texture>
				<Texture parentKey="TopRightTex" file="Interface\Common\Common-Input-Border-TR">
					<Size x="8" y="8"/>
					<Anchors>
						<Anchor point="TOPRIGHT" x="5" y="5"/>
					</Anchors>
				</Texture>
				<Texture parentKey="TopTex" file="Interface\Common\Common-Input-Border-T">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopLeftTex" relativePoint="TOPRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.TopRightTex" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BottomLeftTex" file="Interface\Common\Common-Input-Border-BL">
					<Size x="8" y="8"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="-5" y="-5"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BottomRightTex" file="Interface\Common\Common-Input-Border-BR">
					<Size x="8" y="8"/>
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="5" y="-5"/>
					</Anchors>
				</Texture>
				<Texture parentKey="BottomTex" file="Interface\Common\Common-Input-Border-B">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.BottomLeftTex" relativePoint="TOPRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomRightTex" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="LeftTex" file="Interface\Common\Common-Input-Border-L">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopLeftTex" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomLeftTex" relativePoint="TOPRIGHT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="RightTex" file="Interface\Common\Common-Input-Border-R">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.TopRightTex" relativePoint="BOTTOMLEFT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.BottomRightTex" relativePoint="TOPRIGHT"/>
					</Anchors>
				</Texture>
				<Texture parentKey="MiddleTex" file="Interface\Common\Common-Input-Border-M">
					<Anchors>
						<Anchor point="TOPLEFT" relativeKey="$parent.LeftTex" relativePoint="TOPRIGHT"/>
						<Anchor point="BOTTOMRIGHT" relativeKey="$parent.RightTex" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<FontString inherits="GameFontDisableLarge" parentKey="CharCount">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" x="-6" y="0"/>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad function="InputScrollFrame_OnLoad"/>
			<OnMouseDown>
				self.EditBox:SetFocus();
			</OnMouseDown>
		</Scripts>
		<ScrollChild>
			<EditBox parentKey="EditBox" multiLine="true" countInvisibleLetters="true" autoFocus="false">
				<Size x="1" y="1"/>
				<Anchors>
					<Anchor point="TOPLEFT"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<FontString parentKey="Instructions" inherits="GameFontNormalSmall" justifyH="LEFT" justifyV="TOP">
							<Anchors>
								<Anchor point="TOPLEFT" x="0" y="0"/>
							</Anchors>
							<Color r="0.35" g="0.35" b="0.35"/>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnTabPressed>
						if ( self.nextEditBox ) then
							self.nextEditBox:SetFocus();
						end
					</OnTabPressed>
					<OnTextChanged function="InputScrollFrame_OnTextChanged"/>
					<OnCursorChanged function="ScrollingEdit_OnCursorChanged"/>
					<OnUpdate>
						ScrollingEdit_OnUpdate(self, elapsed, self:GetParent());
					</OnUpdate>
					<OnEscapePressed>
						self:ClearFocus();
					</OnEscapePressed>
				</Scripts>
				<FontString inherits="GameFontHighlightSmall"/>
			</EditBox>
		</ScrollChild>
	</ScrollFrame>

	<Frame name="InlineHyperlinkFrameTemplate" hyperlinksEnabled="true" virtual="true">
		<KeyValues>
			<KeyValue key="tooltipFrame" value="GameTooltip" type="global"/>
		</KeyValues>
		<Scripts>
			<OnHyperlinkEnter function="InlineHyperlinkFrame_OnEnter"/>
			<OnHyperlinkLeave function="InlineHyperlinkFrame_OnLeave"/>
			<OnHyperlinkClick function="InlineHyperlinkFrame_OnClick"/>
		</Scripts>
	</Frame>
</Ui>
