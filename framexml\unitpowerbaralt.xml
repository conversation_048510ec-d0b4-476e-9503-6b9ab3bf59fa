<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="UnitPowerBarAlt.lua"/>
	<Frame name="UnitPowerBarAltTexturableTemplate" virtual="true">
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="background" setAllPoints="true"/>
			</Layer>
			<Layer level="BORDER">
				<Texture name="$parentFill" parentKey="fill" setAllPoints="true"/>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="frame" setAllPoints="true"/>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentFlash" parentKey="flash" setAllPoints="true" alphaMode="ADD"/>
				<Texture parentKey="spark" alphaMode="ADD"/>
			</Layer>
		</Layers>
	</Frame>
	<Frame name="UnitPowerBarAltPillTemplate" inherits="UnitPowerBarAltTexturableTemplate" hidden="true" virtual="true">
		<Animations>
			<AnimationGroup parentKey="flashAnim">	<!--Don't give me a name or $parentFlash below won't resolve correctly. (We should really fix that.)-->
				<Alpha fromAlpha="0" toAlpha="1" duration="0.3" order="1" target="$parentFlash"/>
				<Alpha fromAlpha="1" toAlpha="0" duration="0.3" order="2" target="$parentFlash"/>
			</AnimationGroup>
			<AnimationGroup parentKey="flashAway">	<!--Don't give me a name or $parentFlash below won't resolve correctly. (We should really fix that.)-->
				<Alpha fromAlpha="0" toAlpha="1" duration="0.3" order="1" target="$parentFlash"/>
				<Alpha fromAlpha="1" toAlpha="0" duration="0.3" order="2" target="$parentFlash"/>
				<Alpha fromAlpha="1" toAlpha="0" duration="0.3" order="2" target="$parentFill"/>
				<Scripts>
					<OnFinished function="UnitPowerBarAlt_Pill_OnFlashAwayFinished"/>
				</Scripts>
			</AnimationGroup>
		</Animations>
	</Frame>
	<Frame name="UnitCounterBarNumberTemplate" virtual="true">
		<Size x="16" y="32"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="number">
					<Size x="16" y="32"/>
					<Anchors>
						<Anchor point="TOP"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BACKGROUND" textureSubLevel="1">
				<Texture parentKey="numberMask">
					<Size x="16" y="32"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>
	<Frame name="UnitPowerBarAltCounterTemplate" hidden="true" virtual="true">
		<Size x="16" y="64"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture horizTile="true" parentKey="BG" setAllPoints="true">
					<Size x="16" y="64"/>
					<TexCoords left="0.0" right="1.0" top="0.25" bottom="0.5"/>
				</Texture>
				<Texture parentKey="BGL">
					<Size x="32" y="64"/>
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT"/>
					</Anchors>
					<TexCoords left="0.0" right="0.5" top="0.0" bottom="0.25"/>
				</Texture>
				<Texture parentKey="BGR">
					<Size x="32" y="64"/>
					<Anchors>
						<Anchor point="LEFT" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.5" right="1.0" top="0.0" bottom="0.25"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="artTop">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="CENTER" relativePoint="TOP"/>
					</Anchors>
					<TexCoords left="0.0" right="1.0" top="0.5" bottom="0.75"/>
				</Texture>
				<Texture parentKey="artBottom">
					<Size x="64" y="64"/>
					<Anchors>
						<Anchor point="CENTER" relativePoint="BOTTOM"/>
					</Anchors>
					<TexCoords left="0.0" right="1.0" top="0.75" bottom="1.0"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentDigit1" inherits="UnitCounterBarNumberTemplate" parentKey="digit1">
				<Anchors>
					<Anchor point="RIGHT"/>
				</Anchors>
			</Frame>
			<Frame name="$parentDigit2" inherits="UnitCounterBarNumberTemplate" parentKey="digit2">
				<Anchors>
					<Anchor point="RIGHT" relativePoint="LEFT" relativeTo="$parentDigit1"/>
				</Anchors>
			</Frame>
			<Frame name="$parentDigit3" inherits="UnitCounterBarNumberTemplate" parentKey="digit3">
				<Anchors>
					<Anchor point="RIGHT" relativePoint="LEFT" relativeTo="$parentDigit2"/>
				</Anchors>
			</Frame>
			<Frame name="$parentDigit4" inherits="UnitCounterBarNumberTemplate" parentKey="digit4">
				<Anchors>
					<Anchor point="RIGHT" relativePoint="LEFT" relativeTo="$parentDigit3"/>
				</Anchors>
			</Frame>
			<Frame name="$parentDigit5" inherits="UnitCounterBarNumberTemplate" parentKey="digit5">
				<Anchors>
					<Anchor point="RIGHT" relativePoint="LEFT" relativeTo="$parentDigit4"/>
				</Anchors>
			</Frame>
			<Frame name="$parentDigit6" inherits="UnitCounterBarNumberTemplate" parentKey="digit6">
				<Anchors>
					<Anchor point="RIGHT" relativePoint="LEFT" relativeTo="$parentDigit5"/>
				</Anchors>
			</Frame>
			<Frame name="$parentDigit7" inherits="UnitCounterBarNumberTemplate" parentKey="digit7">
				<Anchors>
					<Anchor point="RIGHT" relativePoint="LEFT" relativeTo="$parentDigit6"/>
				</Anchors>
			</Frame>
		</Frames>
	</Frame>
	<Frame name="UnitPowerBarAltTemplate" hidden="true" inherits="UnitPowerBarAltTexturableTemplate" virtual="true">
		<Animations>
			<AnimationGroup parentKey="flashAnim">	<!--Don't give me a name or $parentFlash below won't resolve correctly. (We should really fix that.)-->
				<Alpha fromAlpha="0" toAlpha="1" duration="0.6" order="1" target="$parentFlash"/>
				<Scripts>
					<OnPlay function="UnitPowerBarAlt_OnFlashPlay"/>
					<OnFinished function="UnitPowerBarAlt_OnFlashFinished"/>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="flashOutAnim">	<!--Don't give me a name or $parentFlash below won't resolve correctly. (We should really fix that.)-->
				<Alpha fromAlpha="1" toAlpha="0" duration="0.2" target="$parentFlash"/>
				<Scripts>
					<OnFinished function="UnitPowerBarAlt_OnFlashOutFinished"/>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Frames>
			<Frame name="$parentCounterBar" inherits="UnitPowerBarAltCounterTemplate" parentKey="counterBar">
				<Anchors>
					<Anchor point="CENTER"/>
				</Anchors>
			</Frame>
			<StatusBar name="$parentStatusFrame" parentKey="statusFrame">
				<Size>
					<AbsDimension x="10" y="10"/>
				</Size>
				<Anchors>
					<Anchor point="CENTER" x="0" y="0"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentText" inherits="TextStatusBarText" parentKey="text">
							<Anchors>
								<Anchor point="CENTER" x="0" y="0"/>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnLoad>
						self.textLockable = 1;
						self.lockShow = 1;
						self.alwaysShow = 1;
						self.cvar = "alternateResourceText";
						self.cvarLabel = "ALTERNATE_RESOURCE_TEXT";
						self:RegisterEvent("CVAR_UPDATE");
					</OnLoad>
					<OnEvent function="UnitPowerBarAltStatus_OnEvent"/>
				</Scripts>
			</StatusBar>
		</Frames>
		<Scripts>
			<OnEvent>
				UnitPowerBarAlt_OnEvent(self, event, ...);
			</OnEvent>
			<OnUpdate>
				UnitPowerBarAlt_OnUpdate(self, elapsed);
			</OnUpdate>
			<OnEnter>
				UnitPowerBarAlt_OnEnter(self);
			</OnEnter>
			<OnLeave>
				UnitPowerBarAlt_OnLeave(self);
			</OnLeave>
		</Scripts>
	</Frame>
	<Frame name="PlayerPowerBarAlt" parent="UIParent" inherits="UnitPowerBarAltTemplate">
		<Scripts>
			<OnLoad>
				self.isPlayerBar = true;
				UnitPowerBarAlt_Initialize(self, "player", 1);
			</OnLoad>
			<OnShow>
				UIParent_ManageFramePositions();
			</OnShow>
			<OnHide>
				UIParent_ManageFramePositions();
			</OnHide>
		</Scripts>
	</Frame>
	
	
	<!-- Timer manager -->
	<Frame name="PlayerBuffTimerManager" parent="UIParent" frameStrata="LOW" hidden="true">
		<Size x="8" y="8"/>
		<Scripts>
			<OnLoad>
				PlayerBuffTimerManager_OnLoad(self);
			</OnLoad>
			<OnEvent>
				PlayerBuffTimerManager_OnEvent(self, event, ...);
			</OnEvent>
		</Scripts>
	</Frame>
</Ui>
