<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="VehicleSeatIndicator.lua"/>
	<Button name="VehicleSeatIndicatorButtonTemplate" virtual="true">
		<Size>
			<AbsDimension x="25" y="25"/>
		</Size>
		<Scripts>
			<OnLoad>
				self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
				_G[self:GetName().."Highlight"]:SetAlpha(0.5);
			</OnLoad>
			<OnClick>
				VehicleSeatIndicatorButton_OnClick(self, button, down);
			</OnClick>
			<OnEnter>
				VehicleSeatIndicatorButton_OnEnter(self, motion);
			</OnEnter>
			<OnLeave>
				VehicleSeatIndicatorButton_OnLeave(self, motion);
			</OnLeave>
		</Scripts>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentBG" file="Interface\Vehicles\VehicleSeats">
					<TexCoords left="0.0" right="0.1953125" top="0.0" bottom="0.78125"/>
				</Texture>
			</Layer>
			<Layer level="ARTWORK">
				<Texture name="$parentPlayerIcon" file="Interface\Vehicles\VehicleSeats">
					<Size>
						<AbsDimension x="10" y="10"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
					<TexCoords left="0.390625" right="0.4609375" top="0.0" bottom="0.28125"/>
				</Texture>
				<Texture name="$parentAllyIcon" file="Interface\Vehicles\VehicleSeats">
					<Size>
						<AbsDimension x="10" y="10"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
					<TexCoords left="0.390625" right="0.4609375" top="0.3125" bottom="0.59375"/>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentPulseTexture" alphaMode="ADD" file="Interface\Vehicles\VehicleSeats" setAllPoints="false" hidden="true">
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="-1" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
					<Size>
						<AbsDimension x="25" y="25"/>
					</Size>
					<TexCoords left="0.1875" right="0.3828125" top="0.0" bottom="0.78125"/>
					<Color r="0.2" g="0.4" b="0.8"/>
				</Texture>
			</Layer>
			<Layer level="HIGHLIGHT">
				<Texture name="$parentHighlight" file="Interface\Vehicles\VehicleSeats" >
					<Size>
						<AbsDimension x="10" y="10"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
					<TexCoords left="0.390625" right="0.4609375" top="0.0" bottom="0.28125"/>
				</Texture>
			</Layer>
		</Layers>
	</Button>
	<Frame name="VehicleSeatIndicator" enableMouse="false" parent="UIParent" hidden="true">
		<Size>
			<AbsDimension x="128" y="128"/>
		</Size>
		<Anchors>
			<Anchor point="TOPRIGHT" relativeTo="MinimapCluster" relativePoint="BOTTOMRIGHT">
				<Offset>
					<AbsDimension x="40" y="15"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentBackgroundTexture"/>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="VehicleSeatIndicatorDropDown" inherits="UIDropDownMenuTemplate" clampedToScreen="true" id="1" hidden="true" />
		</Frames>
		<Scripts>
			<OnLoad function="VehicleSeatIndicator_OnLoad"/>
			<OnEvent function="VehicleSeatIndicator_OnEvent"/>
		</Scripts>
	</Frame>
</Ui>
