<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="VoiceChat.lua"/>
	<Button name="VoiceChatSpeakerTemplate" hidden="true" virtual="true">
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentOn" file="Interface\Common\VoiceChat-Speaker" setAllPoints="true"/>
				<Texture name="$parentFlash" file="Interface\Common\VoiceChat-On" setAllPoints="true" hidden="true"/>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="$parentMuted" file="Interface\Common\VoiceChat-Muted" setAllPoints="true" hidden="true"/>
			</Layer>
		</Layers>
	</Button>
	<Frame name="VoiceChatButtonTemplate" hidden="true" virtual="true">
		<Size>
			<AbsDimension x="125" y="16"/>
		</Size>
		<Frames>
			<Button name="$parentSecureButton" setAllPoints="true" inherits="SecureFrameTemplate">
				<Scripts>
					<OnClick>
						TargetUnit(self:GetAttribute("name"), true);
					</OnClick>
				</Scripts>
			</Button>
			<Frame name="$parentSpeaker">
				<Size>
					<AbsDimension x="16" y="16"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT">
						<Offset>
							<AbsDimension x="8" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">			
						<Texture file="Interface\Common\VoiceChat-Speaker"/>
						<Texture file="Interface\Common\VoiceChat-On"/>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self.text = _G[self:GetName() .. "Text"];
				self.button = _G[self:GetName() .. "SecureButton"];
			</OnLoad>
			<OnEnter>
				self:SetAlpha(1.0);
			</OnEnter>
			<OnLeave>
				if ( not self.show ) then
					self:SetAlpha(0);
				end
			</OnLeave>
			<OnEvent>
				VoiceChatButton_OnEvent(self, event, ...);
			</OnEvent>
		</Scripts>
		<Layers>
			<Layer level="BACKGROUND">
				<FontString name="$parentText" justifyH="LEFT" justifyV="MIDDLE" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="TOPLEFT">
							<Offset>
								<AbsDimension x="30" y="0"/>
							</Offset>
						</Anchor>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>					
				</FontString>
			</Layer>
		</Layers>
	</Frame>
	
	<Frame name="VoiceChatTalkers" parent="UIParent" movable="true" clampedToScreen="true">
		<Size>
			<AbsDimension x="125" y="24"/>
		</Size>
		<Anchors>
			<Anchor point="BOTTOM" relativeTo="$parent" relativePoint="BOTTOM">
				<Offset>
					<AbsDimension x="0" y="64"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Backdrop bgFile="Interface\Tooltips\UI-Tooltip-Background" edgeFile="Interface\Tooltips\UI-Tooltip-Border" tile="true">
			<EdgeSize>	
				<AbsValue val="16"/>
			</EdgeSize>
			<TileSize>
				<AbsValue val="16"/>
			</TileSize>
			<BackgroundInsets>
				<AbsInset left="5" right="5" top="5" bottom="5"/>
			</BackgroundInsets>		
		</Backdrop>
		<HitRectInsets>
			<AbsInset left="-10" right="-10" top="-10" bottom="-10"/>
		</HitRectInsets>
		<Scripts>
			<OnLoad>
				self:SetBackdropBorderColor(0.5, 0.5, 0.5, 0.5);
				self:SetBackdropColor(TOOLTIP_DEFAULT_BACKGROUND_COLOR.r, TOOLTIP_DEFAULT_BACKGROUND_COLOR.g, TOOLTIP_DEFAULT_BACKGROUND_COLOR.b, 0.5);
				VoiceChatTalkers_OnLoad(self);
			</OnLoad>
			<OnEnter>
				self:SetAlpha(1.0);
				self.mouseoverLock = true;
			</OnEnter>
			<OnLeave>
				if ( self:IsMouseOver() ) then
					return;
				end
				
				self.mouseoverLock = nil;
				
				if ( VoiceChatTalkers_CanHide() ) then
					self:SetAlpha(0);
				end
			</OnLeave>
			<OnDragStart>
				MOVING_FRAME = self;
				self:SetFrameStrata("DIALOG");
				self:StartMoving();
				self.moving = true;
				UIParent_ManageFramePositions();
			</OnDragStart>
			<OnDragStop>
				self:StopMovingOrSizing();
				self:SetFrameStrata("DIALOG");
				ValidateFramePosition(self, 25);
				self.moving = false;
				MOVING_FRAME = nil;
			</OnDragStop>
			<OnEvent function="VoiceChatTalkers_OnEvent"/>
			<OnShow function="VoiceChatTalkers_Update"/>
		</Scripts>
		<Frames>
			<Frame name="$parentSpeaker">
				<Size>
					<AbsDimension x="16" y="16"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="8" y="-8"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">			
						<Texture file="Interface\Common\VoiceChat-Speaker"/>
						<Texture file="Interface\Common\VoiceChat-On"/>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
	</Frame>  
	<Button name="MiniMapVoiceChatFrame" parent="Minimap" hidden="true">
		<Size>
			<AbsDimension x="33" y="33"/>
		</Size>
		<Anchors>
			<Anchor point="BOTTOMLEFT" relativeTo="Minimap">
				<Offset>
					<AbsDimension x="-25" y="38"/>
				</Offset>
			</Anchor>
		</Anchors>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentBackground" file="Interface\Minimap\UI-Minimap-Background">
					<Size>
						<AbsDimension x="19" y="19"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="-1" y="2"/>
							</Offset>
						</Anchor>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture name="$parentBorder" file="Interface\Minimap\MiniMap-TrackingBorder">
					<Size>
						<AbsDimension x="52" y="52"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="VoiceChatShine" file="Interface\ComboFrame\ComboPoint" alphaMode="ADD" hidden="true">
					<Size>
						<AbsDimension x="32" y="32"/>
					</Size>
					<Anchors>
						<Anchor point="CENTER">
							<Offset>
								<AbsDimension x="3" y="3"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0.5625" right="1" top="0" bottom="1"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentIcon" hidden="false">
				<Size x="25" y="25"/>
				<Anchors>
					<Anchor point="CENTER">
						<Offset x="-1" y="1"/>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture file="Interface\Common\VoiceChat-Speaker">
							<Size x="15" y="15"/>
							<Anchors>
								<Anchor point="CENTER">
									<Offset x="0" y="1"/>
								</Anchor>
							</Anchors>
						</Texture>
						<Texture file="Interface\Common\VoiceChat-On">
							<Size x="15" y="15"/>
							<Anchors>
								<Anchor point="CENTER">
									<Offset x="0" y="1"/>
								</Anchor>
							</Anchors>
						</Texture>
						<Texture name="$parentMuted" file="Interface\Common\VoiceChat-Muted" hidden="true">
							<Size x="15" y="15"/>
							<Anchors>
								<Anchor point="CENTER">
									<Offset x="0" y="1"/>
								</Anchor>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
			<Frame name="MiniMapVoiceChatDropDown" inherits="UIDropDownMenuTemplate" clampedToScreen="true" id="1" hidden="true">
				<Scripts>
					<OnLoad function="MiniMapVoiceChatDropDown_OnLoad"/>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
				self:SetFrameLevel(self:GetFrameLevel()+1);
				self:RegisterEvent("VOICE_SESSIONS_UPDATE");
				self:RegisterEvent("PLAYER_ENTERING_WORLD");
				self:RegisterEvent("VOICE_SELF_MUTE");
			</OnLoad>
			<OnShow>
				MiniMapVoiceChatFrameBackground:SetAlpha(0.6);
			</OnShow>
			<OnEvent>
				if ( event == "VOICE_SESSIONS_UPDATE" or event == "PLAYER_ENTERING_WORLD" ) then
					MiniMapVoiceChat_Update();
					UIDropDownMenu_Initialize(MiniMapVoiceChatDropDown, MiniMapVoiceChatDropDown_Initialize, "MENU");
				end
				SetSelfMuteState();
			</OnEvent>
			<OnClick>
				if ( button == "LeftButton" ) then
					ToggleFriendsFrame(3);
				elseif ( button == "RightButton" ) then
					if ( self.count and self.count > 0 ) then
						ToggleDropDownMenu(1, nil, MiniMapVoiceChatDropDown, "MiniMapVoiceChatFrame", 0, -5);
					else
						ToggleFriendsFrame(3);
					end
				end
			</OnClick>
			<OnEnter>
				local text = VOICE_CHAT;
				if ( (GetCVar("VoiceChatSelfMute") == "1") and (GetCVar("VoiceChatMode") == "1")) then
					text = VOICE_CHAT.." "..SELFMUTED;
				end
				GameTooltip_AddNewbieTip(self, text, 1.0, 1.0, 1.0, NEWBIE_TOOLTIP_VOICE_CHAT_SELECTOR);
			</OnEnter>
			<OnLeave function="GameTooltip_Hide"/>
			<OnMouseUp function="MinimapButton_OnMouseUp"/>
			<OnMouseDown function="MinimapButton_OnMouseDown"/>
		</Scripts>
		<HighlightTexture alphaMode="ADD" file="Interface\Minimap\UI-Minimap-ZoomButton-Highlight"/>
	</Button>
</Ui>