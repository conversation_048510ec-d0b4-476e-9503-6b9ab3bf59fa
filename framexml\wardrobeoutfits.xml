<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="WardrobeOutfits.lua"/>
	
	<Button name="WardrobeOutfitButtonButtonTemplate" virtual="true">
		<Scripts>
			<OnEnter>
				self:GetParent():GetParent():StopHideCountDown();
				self.texture:SetAlpha(1.0);
				GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
				GameTooltip:SetText(self.tooltipText);
			</OnEnter>
			<OnLeave>
				self:GetParent():GetParent():StartHideCountDown();
				self.texture:SetAlpha(0.5);
				GameTooltip_Hide();
			</OnLeave>
			<OnMouseDown>
				self.texture:SetPoint("TOPLEFT", 1, -1);
			</OnMouseDown>
			<OnMouseUp>
				self.texture:SetPoint("TOPLEFT", 0, 0);
			</OnMouseUp>
			<OnShow>
				self.texture:SetPoint("TOPLEFT", 0, 0);
			</OnShow>
		</Scripts>
	</Button>
	<Button name="WardrobeOutfitButtonTemplate" parentArray="Buttons" virtual="true" mixin="WardrobeOutfitButtonMixin">
		<Size x="0" y="20"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Selection" file="Interface\QuestFrame\UI-QuestTitleHighlight" alphaMode="ADD" setAllPoints="true" desaturated="true" hidden="true">
					<Color r="0.5" g="0.5" b="0.5"/>
				</Texture>
			</Layer>
			<Layer level="BORDER">
				<Texture parentKey="Highlight" file="Interface\QuestFrame\UI-QuestTitleHighlight" alphaMode="ADD" setAllPoints="true" hidden="true"/>
			</Layer>
			<Layer level="ARTWORK">
				<Texture parentKey="Check" file="Interface\Buttons\UI-CheckBox-Check" hidden="false">
					<Size x="16" y="16"/>
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Text" relativePoint="RIGHT" x="4" y="0"/>
					</Anchors>
				</Texture>
				<Texture parentKey="Icon" hidden="false">
					<Size x="19" y="19"/>
					<Anchors>
						<Anchor point="BOTTOMLEFT" x="5" y="0"/>
					</Anchors>
					<Color r="0" g="1" b="0"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Button parentKey="EditButton" inherits="WardrobeOutfitButtonButtonTemplate">
				<KeyValues>
					<KeyValue key="tooltipText" value="TRANSMOG_OUTFIT_EDIT" type="global"/>
				</KeyValues>
				<Size x="16" y="16"/>
				<Anchors>
					<Anchor point="RIGHT" x="-2"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture file="Interface\WorldMap\GEAR_64GREY" alpha="0.5" parentKey="texture"/>
					</Layer>
				</Layers>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						WardrobeOutfitEditFrame:ShowForOutfit(self:GetParent().outfitID);
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self.Check:SetPoint("LEFT", self.Text, "RIGHT", 4, 0);
			</OnLoad>
			<OnClick method="OnClick"/>
			<OnEnter>
				self.Highlight:Show();
				self:GetParent():StopHideCountDown();
			</OnEnter>
			<OnLeave>
				self.Highlight:Hide();
				self:GetParent():StartHideCountDown();
			</OnLeave>
		</Scripts>
		<ButtonText parentKey="Text" maxLines="1">
			<Size x="0" y="0"/>
			<Anchors>
				<Anchor point="LEFT" x="29" y="0"/>
			</Anchors>
		</ButtonText>
		<NormalFont style="GameFontHighlightSmallLeft"/>
		<HighlightFont style="GameFontHighlightSmallLeft"/>
		<DisabledFont style="GameFontDisableSmallLeft"/>
	</Button>
	<Frame name="WardrobeOutfitDropDownTemplate" inherits="UIDropDownMenuTemplate" virtual="true" mixin="WardrobeOutfitDropDownMixin">
		<Size x="100" y="22"/>
		<Frames>
			<Button parentKey="SaveButton" inherits="UIPanelButtonTemplate" text="SAVE">
				<Size x="88" y="22"/>
				<Anchors>
					<Anchor point="LEFT" relativePoint="RIGHT" x="-13" y="-3"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						local dropDown = self:GetParent();
						dropDown:CheckOutfitForSave(UIDropDownMenu_GetText(dropDown));
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad method="OnLoad"/>
			<OnEvent method="OnEvent"/>
			<OnShow method="OnShow"/>
			<OnHide method="OnHide"/>
		</Scripts>
	</Frame>
	
	<Frame name="WardrobeOutfitFrame" frameStrata="DIALOG" parent="UIParent" hidden="true" enableMouse="true" mixin="WardrobeOutfitFrameMixin">
		<Size x="224" y="200"/>
		<Anchors>
			<Anchor point="TOPLEFT"/>
		</Anchors>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background-Dark" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="9"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Frames>
			<Button inherits="WardrobeOutfitButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" x="11" y="-15"/>
					<Anchor point="TOPRIGHT" x="-11" y="-15"/>
				</Anchors>
			</Button>
		</Frames>
		<Scripts>
			<OnHide method="OnHide"/>
			<OnUpdate method="OnUpdate"/>
		</Scripts>
	</Frame>
	<Frame name="WardrobeOutfitEditFrame" parent="UIParent" hidden="true" frameStrata="DIALOG" mixin="WardrobeOutfitEditFrameMixin">
		<Size x="320" y="174"/>
		<Anchors>
			<Anchor point="CENTER"/>
		</Anchors>
		<Backdrop bgFile="Interface\DialogFrame\UI-DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<FontString parentKey="Title" inherits="GameFontHighlight" text="TRANSMOG_OUTFIT_NAME">
					<Anchors>
						<Anchor point="TOP" x="0" y="-20"/>
					</Anchors>
				</FontString>
				<Texture parentKey="Separator">
					<Size x="276" y="1"/>
					<Anchors>
						<Anchor point="TOP" x="0" y="-127"/>
					</Anchors>
					<Color r="1" g="1" b="1" a="0.14"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<EditBox parentKey="EditBox" historyLines="1" letters="31">
				<Size x="260" y="32"/>
				<Anchors>
					<Anchor point="TOP" x="0" y="-40"/>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture parentKey="LeftTexture" file="Interface\ChatFrame\UI-ChatInputBorder-Left2">
							<Size x="32" y="32"/>
							<Anchors>
								<Anchor point="LEFT" x="-10" y="0"/>
							</Anchors>
						</Texture>
						<Texture parentKey="RightTexture" file="Interface\ChatFrame\UI-ChatInputBorder-Right2">
							<Size x="32" y="32"/>
							<Anchors>
								<Anchor point="RIGHT" x="10" y="0"/>
							</Anchors>
						</Texture>
						<Texture parentKey="MiddleTexture" file="Interface\ChatFrame\UI-ChatInputBorder-Mid2" horizTile="true">
							<Size x="0" y="32"/>
							<Anchors>
								<Anchor point="TOPLEFT" relativeKey="$parent.LeftTexture" relativePoint="TOPRIGHT"/>
								<Anchor point="TOPRIGHT" relativeKey="$parent.RightTexture" relativePoint="TOPLEFT"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnterPressed>
						self:GetParent():OnAccept();
					</OnEnterPressed>
					<OnEscapePressed>
						StaticPopupSpecial_Hide(self:GetParent());
					</OnEscapePressed>
					<OnTextChanged>
						if ( self:GetText() == "" ) then
							self:GetParent().AcceptButton:Disable();
						else
							self:GetParent().AcceptButton:Enable();
						end
					</OnTextChanged>
				</Scripts>
				<FontString inherits="ChatFontNormal"/>
			</EditBox>
			<Button parentKey="AcceptButton" inherits="UIPanelButtonTemplate" text="ACCEPT">
				<Size x="120" y="22"/>
				<Anchors>
					<Anchor point="TOPLEFT" x="33" y="-80"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						self:GetParent():OnAccept();
					</OnClick>
				</Scripts>
			</Button>
			<Button parentKey="CancelButton" inherits="UIPanelButtonTemplate" text="CANCEL">
				<Size x="120" y="22"/>
				<Anchors>
					<Anchor point="TOPRIGHT" x="-33" y="-80"/>
				</Anchors>
				<Scripts>
					<OnClick>
						StaticPopupSpecial_Hide(self:GetParent());
					</OnClick>
				</Scripts>
			</Button>
			<Button parentKey="DeleteButton" inherits="UIPanelButtonTemplate" text="TRANSMOG_OUTFIT_DELETE">
				<Size x="120" y="22"/>
				<Anchors>
					<Anchor point="BOTTOM" x="0" y="17"/>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
						self:GetParent():OnDelete();
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				self.exclusive = true;
				self.hideOnEscape = true;
				self.DeleteButton:SetWidth(self.DeleteButton:GetTextWidth() + 50);
			</OnLoad>
			<OnHide>
				PlaySound(SOUNDKIT.IG_MAINMENU_CLOSE);
			</OnHide>
		</Scripts>
	</Frame>

	<Frame name="WardrobeOutfitCheckAppearancesFrame" inherits="LoadingSpinnerTemplate" hidden="true" mixin="WardrobeOutfitCheckAppearancesMixin">
		<Scripts>
			<OnLoad method="OnLoad"/>
			<OnShow method="OnShow"/>
			<OnHide method="OnHide"/>
			<OnEvent method="OnEvent"/>
		</Scripts>
	</Frame>
</Ui>