<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="WorldMapBountyBoard.lua"/>
	<Script file="WorldMapActionButton.lua"/>
	<Script file="WorldMapPOIQuantizer.lua"/>
	<Script file="WorldMapFrame.lua"/>
	<Include file="WorldMapFrameTemplates.xml"/>

	<Frame name="WorldMapPingTemplate" mixin="WorldMapPingMixin" virtual="true">
		<Size x="70" y="70"/>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture parentKey="Expand" file="Interface/minimap/UI-Minimap-Ping-Expand" alpha="0">
					<Size x="32" y="32"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="DriverAnimation" mixin="WorldMapPingAnimationMixin">
				<Alpha childKey="Expand" fromAlpha="0" toAlpha="1"                      duration="0.2" order="1"/>
				<Alpha childKey="Expand" fromAlpha="1" toAlpha="0" startDelay=".45"     duration="0.2" order="1"/>
				<Alpha childKey="Expand" fromAlpha="0" toAlpha="1" startDelay=".575"    duration="0.2" order="1"/>
				<Alpha childKey="Expand" fromAlpha="1" toAlpha="0" startDelay=".95"     duration="0.2" order="1"/>

				<Scripts>
					<OnPlay method="OnPlay"/>
					<OnFinished method="OnStop"/>
					<OnStop method="OnStop"/>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="ScaleAnimation" looping="REPEAT">
				<Scale childKey="Expand" fromScaleX=".5" fromScaleY=".5" toScaleX="1.2" toScaleY="1.2" duration=".575" order="1"/>
			</AnimationGroup>
		</Animations>
	</Frame>

	<Frame name="WorldMapFrame" frameStrata="FULLSCREEN" setAllPoints="true" hidden="true" enableKeyboard="true" enableMouse="true" clampedToScreen="true" toplevel="true" >
		<Layers>
			<Layer level="BACKGROUND" textureSubLevel="-8">
				<Texture name="BlackoutWorld">
					<Anchors>
						<Anchor point="BOTTOMLEFT"/>
						<Anchor point="TOPRIGHT"/>
					</Anchors>
					<Color r="0.0" g="0.0" b="0.0"/>
				</Texture>
			</Layer>
		</Layers>
		<Animations>
			<AnimationGroup parentKey="AnimAlphaIn">
				<Alpha parentKey="Alpha" fromAlpha="0" toAlpha="0.65" startDelay="0.25" duration="0.55" order="1"/>
				<Scripts>
					<OnFinished>
						WorldMapFrame:SetAlpha(WORLD_MAP_MAX_ALPHA);
					</OnFinished>
				</Scripts>
			</AnimationGroup>
			<AnimationGroup parentKey="AnimAlphaOut">
				<Alpha parentKey="Alpha" fromAlpha="0.65" toAlpha="0" startDelay="0.25" duration="0.55" order="1"/>
				<Scripts>
					<OnFinished>
						WorldMapFrame:SetAlpha(WORLD_MAP_MIN_ALPHA);
					</OnFinished>
				</Scripts>
			</AnimationGroup>
		</Animations>
		<Frames>
			<Frame parentKey="BorderFrame" inherits="PortraitFrameTemplate">
				<Size x="1022" y="766" />
				<Anchors>
					<Anchor point="TOP" x="1" y="-1"/>
				</Anchors>
				<Layers>
					<Layer level="OVERLAY" textureSubLevel="1">
						<Texture parentKey="ButtonFrameEdge" atlas="UI-OuterBorderButtonPatch" useAtlasSize="true">
							<Anchors>
								<Anchor point="TOPRIGHT" x="-36" y="0"/>
							</Anchors>
						</Texture>
					</Layer>
					<Layer level="BACKGROUND" textureSubLevel="-5">
						<Texture name="WW" inherits="_UI-Frame-InnerTopTile" parentKey="InsetBorderTop">
							<Size x="698" y="3"/>
							<Anchors>
								<Anchor point="TOPLEFT" x="2" y="-63"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Frames>
					<Frame parentKey="Inset" useParentLevel="true" inherits="InsetFrameTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" x="5" y="-63" />
							<Anchor point="BOTTOMRIGHT" x="-7" y="28" />
						</Anchors>
					</Frame>
					<Frame parentKey="MaximizeMinimizeFrame" inherits="MaximizeMinimizeButtonFrameTemplate">
						<Anchors>
							<Anchor point="RIGHT" relativeKey="$parent.CloseButton" relativePoint="LEFT" x="10" y="0"/>
						</Anchors>
						<Scripts>
							<OnLoad function="WorldMapFrame_MaximizeMinimizeFrame_OnLoad"/>
						</Scripts>
					</Frame>
				</Frames>
			</Frame>
			<Frame name="WorldMapTitleDropDown" inherits="UIDropDownMenuTemplate" hidden="true">
				<Anchors>
					<Anchor point="TOP" x="-80" y="-35"/>
				</Anchors>
			</Frame>
			<Button name="$parentTutorialButton" parentKey="MainHelpButton" inherits="MainHelpPlateButton">
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent" x="44" y="18" />
				</Anchors>
				<Scripts>
					<OnLoad>
						self:SetFrameLevel(70); -- NavBar sets itself to framelevel 50...
					</OnLoad>
					<OnClick function="WorldMapFrame_ToggleTutorial"/>
				</Scripts>
			</Button>
			<ScrollFrame name="WorldMapScrollFrame">
                <Size x="1002" y="668"/>
                <Anchors>
                    <Anchor point="TOP" x="0" y="-68"/>
                </Anchors>
				<Scripts>
					<OnMouseWheel function="WorldMapScrollFrame_OnMouseWheel"/>
				</Scripts>
				<ScrollChild>
					<Frame>
						<Size x="10" y="10"/>
						<Anchors>
							<Anchor point="TOPLEFT" x="0" y="0"/>
						</Anchors>
						<Frames>
							<Frame name="WorldMapDetailFrame" hidden="false" frameLevel="100">
								<Size x="1002" y="668"/>
								<Anchors>
									<Anchor point="TOPLEFT" x="0" y="0"/>
								</Anchors>
								<Layers>
									<Layer level="BACKGROUND">
										<Texture name="WorldMapDetailTile1">
											<Size x="256" y="256"/>
											<Anchors>
												<Anchor point="TOPLEFT"/>
											</Anchors>
										</Texture>
										<Texture name="WorldMapDetailTile2">
											<Size x="256" y="256"/>
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="WorldMapDetailTile1" relativePoint="TOPRIGHT"/>
											</Anchors>
										</Texture>
										<Texture name="WorldMapDetailTile3">
											<Size x="256" y="256"/>
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="WorldMapDetailTile2" relativePoint="TOPRIGHT"/>
											</Anchors>
										</Texture>
										<Texture name="WorldMapDetailTile4">
											<Size x="256" y="256"/>
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="WorldMapDetailTile3" relativePoint="TOPRIGHT"/>
											</Anchors>
										</Texture>
										<Texture name="WorldMapDetailTile5">
											<Size x="256" y="256"/>
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="WorldMapDetailTile1" relativePoint="BOTTOMLEFT"/>
											</Anchors>
										</Texture>
										<Texture name="WorldMapDetailTile6">
											<Size x="256" y="256"/>
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="WorldMapDetailTile5" relativePoint="TOPRIGHT"/>
											</Anchors>
										</Texture>
										<Texture name="WorldMapDetailTile7">
											<Size x="256" y="256"/>
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="WorldMapDetailTile6" relativePoint="TOPRIGHT"/>
											</Anchors>
										</Texture>
										<Texture name="WorldMapDetailTile8">
											<Size x="256" y="256"/>
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="WorldMapDetailTile7" relativePoint="TOPRIGHT"/>
											</Anchors>
										</Texture>
										<Texture name="WorldMapDetailTile9">
											<Size x="256" y="256"/>
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="WorldMapDetailTile5" relativePoint="BOTTOMLEFT"/>
											</Anchors>
										</Texture>
										<Texture name="WorldMapDetailTile10">
											<Size x="256" y="256"/>
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="WorldMapDetailTile9" relativePoint="TOPRIGHT"/>
											</Anchors>
										</Texture>
										<Texture name="WorldMapDetailTile11">
											<Size x="256" y="256"/>
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="WorldMapDetailTile10" relativePoint="TOPRIGHT"/>
											</Anchors>
										</Texture>
										<Texture name="WorldMapDetailTile12">
											<Size x="256" y="256"/>
											<Anchors>
												<Anchor point="TOPLEFT" relativeTo="WorldMapDetailTile11" relativePoint="TOPRIGHT"/>
											</Anchors>
										</Texture>
									</Layer>
									<Layer level="BACKGROUND" textureSubLevel="1">
										<Texture name="WorldMapInvasionOverlay" atlas="legioninvasion-map-cover" />
										<Texture name="WorldMapAreaPOIBannerOverlay" atlas="legioninvasion-map-cover" hidden="true"/>
									</Layer>
									<Layer level="OVERLAY">
										<Texture name="WorldMapHighlight" alphaMode="ADD" hidden="true">
											<Size x="128" y="128"/>
											<Anchors>
												<Anchor point="TOPLEFT"/>
											</Anchors>
										</Texture>
									</Layer>
									<Layer level="ARTWORK">
										<FontString name="WorldMapZoneInfo" inherits="SubZoneTextFont" justifyH="CENTER" hidden="true">
											<Anchors>
												<Anchor point="BOTTOM" relativePoint="BOTTOM" x="0" y="20"/>
											</Anchors>
										</FontString>
										<FontString name="MapFramerateLabel" hidden="true" inherits="SystemFont_Shadow_Med1" text="FRAMERATE_LABEL">
											<Anchors>
												<Anchor point="BOTTOM" relativePoint="BOTTOM" x="0" y="64"/>
											</Anchors>
										</FontString>
										<FontString name="MapFramerateText" hidden="true" inherits="SystemFont_Shadow_Med1">
											<Anchors>
												<Anchor point="LEFT" relativeTo="MapFramerateLabel" relativePoint="RIGHT"/>
											</Anchors>
										</FontString>
									</Layer>
								</Layers>
								<Frames>
									<QuestPOIFrame name="WorldMapBlobFrame" setAllPoints="true">
										<Layers>
											<Layer level="ARTWORK">
												<Texture name="WorldMapBlobFrameTexture" setAllPoints="true" />
											</Layer>
										</Layers>
										<Scripts>
											<OnLoad function="WorldMapBlobFrame_OnLoad" />
											<OnUpdate function="WorldMapBlobFrame_OnUpdate"/>
										</Scripts>
									</QuestPOIFrame>
									<ScenarioPOIFrame name="ScenarioPOIFrame" setAllPoints="true">
										<Layers>
											<Layer level="ARTWORK">
												<Texture name="WorldMapBlobFrameTexture" setAllPoints="true" />
											</Layer>
										</Layers>
										<Scripts>
											<OnLoad>
												self:SetFillTexture("Interface\\WorldMap\\UI-QuestBlob-Inside");
												self:SetBorderTexture("Interface\\WorldMap\\UI-QuestBlob-Outside");
												self:SetFillAlpha(128);
												self:SetBorderAlpha(192);
												self:SetBorderScalar(1.0);
											</OnLoad>
											<OnUpdate function="ScenarioPOIFrame_OnUpdate"/>
										</Scripts>
									</ScenarioPOIFrame>
									<Button name="WorldMapButton" setAllPoints="true">
										<Layers>
											<Layer level="ARTWORK">
												<Texture parentKey="AzerothHighlight" file="Interface\WorldMap\Cosmic\Cosmic-Azeroth-Highlight" hidden="true">
													<Size x="1024" y="512"/>
													<Anchors>
														<Anchor point="BOTTOMLEFT" x="0" y="0"/>
													</Anchors>
												</Texture>
												<Texture parentKey="OutlandHighlight" file="Interface\WorldMap\Cosmic\Cosmic-Outland-Highlight" hidden="true">
													<Size x="1024" y="512"/>
													<Anchors>
														<Anchor point="BOTTOMLEFT" x="0" y="0"/>
													</Anchors>
												</Texture>
												<Texture parentKey="DraenorHighlight" file="Interface\WorldMap\Cosmic\Cosmic-Draenor-Highlight" hidden="true">
													<Size x="1024" y="512"/>
													<Anchors>
														<Anchor point="TOPLEFT" x="0" y="0"/>
													</Anchors>
												</Texture>
											</Layer>
										</Layers>
										<Frames>
											<Frame name="WorldMapUnitDropDown" inherits="UIDropDownMenuTemplate" clampedToScreen="true" id="1" hidden="true"/>
											<Frame name="WorldMapCorpse" inherits="WorldMapCorpseTemplate">
												<Size x="24" y="24"/>
											</Frame>
											<Frame name="WorldMapDeathRelease" inherits="WorldMapCorpseTemplate">
												<Size x="24" y="24"/>
												<Scripts>
													<OnEnter>
														local x, y = self:GetCenter();
														local parentX, parentY = self:GetParent():GetCenter();
														if ( x > parentX ) then
															WorldMapTooltip:SetOwner(self, "ANCHOR_LEFT");
														else
															WorldMapTooltip:SetOwner(self, "ANCHOR_RIGHT");
														end
														WorldMapTooltip:SetText(SPIRIT_HEALER_RELEASE_RED);
														WorldMapTooltip:Show();
													</OnEnter>
												</Scripts>
											</Frame>
											<Button name="OutlandButton" inherits="WorldMapFakeButtonTemplate"  hidden="true">
												<Size x="241" y="253"/>
												<Anchors>
													<Anchor point="TOPLEFT" x="126" y="-337"/>
												</Anchors>
												<Scripts>
													<OnLoad>
														self.continent = 3;
													</OnLoad>
													<OnEnter>
														self:GetParent().OutlandHighlight:Show();
													</OnEnter>
													<OnLeave>
														self:GetParent().OutlandHighlight:Hide();
													</OnLeave>
												</Scripts>
											</Button>
											<Button name="AzerothButton" inherits="WorldMapFakeButtonTemplate" hidden="true">
												<Size x="330" y="342"/>
												<Anchors>
													<Anchor point="TOPLEFT" x="611" y="-288"/>
												</Anchors>
												<Scripts>
													<OnLoad>
														self.continent = 0;
													</OnLoad>
													<OnEnter>
														self:GetParent().AzerothHighlight:Show();
													</OnEnter>
													<OnLeave>
														self:GetParent().AzerothHighlight:Hide();
													</OnLeave>
												</Scripts>
											</Button>
											<Button name="DraenorButton" inherits="WorldMapFakeButtonTemplate"  hidden="true">
												<Size x="273" y="261"/>
												<Anchors>
													<Anchor point="TOPLEFT" x="329" y="-142"/>
												</Anchors>
												<Scripts>
													<OnLoad>
														self.continent = 7;
														self:SetFrameLevel(OutlandButton:GetFrameLevel()+1);
													</OnLoad>
													<OnEnter>
														self:GetParent().DraenorHighlight:Show();
													</OnEnter>
													<OnLeave>
														self:GetParent().DraenorHighlight:Hide();
													</OnLeave>
												</Scripts>
											</Button>
											<Button name="TheMaelstromButton" inherits="WorldMapMaelstromButtonTemplate" text="TheMaelstrom">
												<Size x="120" y="120"/>
												<Anchors>
													<Anchor point="TOPLEFT" x="305" y="-140"/>
												</Anchors>
												<HighlightTexture name="$parentHighlight" file="Interface\WorldMap\TheMaelstrom\TheMaelstromHighlight" alphaMode="ADD">
													<Size x="128" y="128"/>
													<Anchors>
														<Anchor point="CENTER" x="0" y="-2"/>
													</Anchors>
												</HighlightTexture>
											</Button>
											<Button name="DeepholmButton" inherits="WorldMapMaelstromButtonTemplate" text="Deepholm">
												<Size x="120" y="120"/>
												<Anchors>
													<Anchor point="TOPLEFT" x="450" y="-130"/>
												</Anchors>
												<HighlightTexture name="$parentHighlight" file="Interface\WorldMap\Deepholm\DeepholmHighlight" alphaMode="ADD">
													<Size x="160" y="160"/>
													<Anchors>
														<Anchor point="CENTER" x="5" y="-20"/>
													</Anchors>
												</HighlightTexture>
											</Button>
											<Button name="KezanButton" inherits="WorldMapMaelstromButtonTemplate" text="Kezan">
												<Size x="254" y="240"/>
												<Anchors>
													<Anchor point="TOPLEFT" x="236" y="-380"/>
												</Anchors>
												<HighlightTexture name="$parentHighlight" file="Interface\WorldMap\Kezan\KezanHighlight" alphaMode="ADD">
													<Size x="360" y="360"/>
													<Anchors>
														<Anchor point="CENTER" x="-5" y="-65"/>
													</Anchors>
												</HighlightTexture>
											</Button>
											<Button name="LostIslesButton" inherits="WorldMapMaelstromButtonTemplate" text="TheLostIsles">
												<Size x="152" y="180"/>
												<Anchors>
													<Anchor point="TOPLEFT" x="75" y="-266"/>
												</Anchors>
												<HighlightTexture name="$parentHighlight" file="Interface\WorldMap\TheLostIsles\TheLostIslesHighlight" alphaMode="ADD">
													<Size x="250" y="250"/>
													<Anchors>
														<Anchor point="CENTER" x="5" y="-40"/>
													</Anchors>
												</HighlightTexture>
											</Button>
											
											<Button name="KrokuunButton" inherits="WorldMapFakeButtonTemplate">
												<Size x="270" y="160"/>
												<Anchors>
													<Anchor point="BOTTOMRIGHT" x="-270" y="140"/>
												</Anchors>
												<KeyValues>
													<KeyValue key="zoneID" value="1135" type="number"/>
												</KeyValues>
												<Scripts>
													<OnLoad>
														self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
													</OnLoad>
												</Scripts>
												<HighlightTexture atlas="Krokuun_Highlight" useAtlasSize="true" alphaMode="ADD">
													<Anchors>
														<Anchor point="BOTTOMRIGHT" relativeKey="$parent.$parent" relativePoint="BOTTOMRIGHT"/>
													</Anchors>
												</HighlightTexture>
											</Button>
											<Button name="MacAreeButton" inherits="WorldMapFakeButtonTemplate">
												<Size x="200" y="175"/>
												<Anchors>
													<Anchor point="TOPRIGHT" x="-280" y="-120"/>
												</Anchors>
												<KeyValues>
													<KeyValue key="zoneID" value="1170" type="number"/>
												</KeyValues>
												<Scripts>
													<OnLoad>
														self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
													</OnLoad>
												</Scripts>
												<HighlightTexture atlas="MacAree_Highlight" useAtlasSize="true" alphaMode="ADD">
													<Anchors>
														<Anchor point="TOPRIGHT" relativeKey="$parent.$parent" relativePoint="TOPRIGHT"/>
													</Anchors>
												</HighlightTexture>
											</Button>
											<Button name="AntoranWastesButton" inherits="WorldMapFakeButtonTemplate">
												<Size x="210" y="220"/>
												<Anchors>
													<Anchor point="BOTTOMLEFT" x="220" y="190"/>
												</Anchors>
												<KeyValues>
													<KeyValue key="zoneID" value="1171" type="number"/>
												</KeyValues>
												<Scripts>
													<OnLoad>
														self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
													</OnLoad>
												</Scripts>
												<HighlightTexture atlas="AntoranWastes_Highlight" useAtlasSize="true" alphaMode="ADD">
													<Anchors>
														<Anchor point="BOTTOMLEFT" relativeKey="$parent.$parent" relativePoint="BOTTOMLEFT"/>
													</Anchors>
												</HighlightTexture>
											</Button>
											
											<Button name="BrokenIslesArgusButton" inherits="WorldMapFakeButtonTemplate">
												<Size x="270" y="250"/>
												<Anchors>
													<Anchor point="TOPRIGHT" x="0" y="0"/>
												</Anchors>
												<KeyValues>
													<KeyValue key="continent" value="9" type="number"/>
												</KeyValues>
												<Scripts>
													<OnLoad>
														self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
													</OnLoad>
												</Scripts>
												<HighlightTexture atlas="BrokenIslesHightlight" useAtlasSize="true" alphaMode="ADD">
													<Anchors>
														<Anchor point="TOPRIGHT" relativeKey="$parent.$parent" relativePoint="TOPRIGHT"/>
													</Anchors>
												</HighlightTexture>
											</Button>
										</Frames>
										<Scripts>
											<OnLoad>
												self:RegisterForClicks("LeftButtonUp", "RightButtonUp");
											</OnLoad>
											<OnUpdate function="WorldMapButton_OnUpdate"/>
											<OnMouseDown function="WorldMapButton_OnMouseDown"/>
											<OnMouseUp function="WorldMapButton_OnMouseUp"/>
											<OnClick function="WorldMapButton_OnClick"/>
										</Scripts>
									</Button>
									<Frame name="WorldMapBossButtonFrame" setAllPoints="true"/>
								</Frames>
							</Frame>
							<Frame name="WorldMapPOIFrame" frameLevel="300">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="WorldMapDetailFrame" x="0" y="0"/>
									<Anchor point="BOTTOMRIGHT" relativeTo="WorldMapDetailFrame" x="0" y="0"/>
								</Anchors>
								<Frames>
									<CinematicModel parentKey="WorldQuestCompletedBySpellModel" facingLeft="true">
										<Size x="400" y="400"/>
										<Scripts>
											<OnLoad>
												self:RegisterEvent("UI_SCALE_CHANGED");
												self:RegisterEvent("DISPLAY_SIZE_CHANGED");
												self:SetDisplayInfo(11686); <!-- 11686 is invisible stalker -->
											</OnLoad>
											<OnEvent>
												self:RefreshCamera();
											</OnEvent>
										</Scripts>
									</CinematicModel>
									<Frame parentKey="POIPing" inherits="WorldMapPingTemplate"/>
								</Frames>
							</Frame>
							<UnitPositionFrame name="WorldMapUnitPositionFrame" frameLevel="1500" inherits="UnitPositionFrameTemplate">
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="WorldMapDetailFrame" x="0" y="0"/>
									<Anchor point="BOTTOMRIGHT" relativeTo="WorldMapDetailFrame" x="0" y="0"/>
								</Anchors>
							</UnitPositionFrame>
						</Frames>
					</Frame>
				</ScrollChild>
			</ScrollFrame>
			<Frame parentKey="UIElementsFrame" frameLevel="10">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="WorldMapScrollFrame" x="0" y="0"/>
					<Anchor point="BOTTOMRIGHT" relativeTo="WorldMapScrollFrame" x="0" y="0"/>
				</Anchors>
				<Frames>
					<Frame name="WorldMapFrameAreaFrame">
						<Size x="400" y="128"/>
						<Anchors>
							<Anchor point="TOP" x="0" y="-10"/>
						</Anchors>
						<Layers>
							<Layer level="OVERLAY">
								<FontString name="WorldMapFrameAreaLabel" inherits="WorldMapTextFont">
									<Anchors>
										<Anchor point="TOP" x="0" y="-20"/>
									</Anchors>
								</FontString>
								<Texture name="WorldMapFrameAreaLabelTexture" hidden="true">
									<Anchors>
										<Anchor point="RIGHT" relativeTo="WorldMapFrameAreaLabel" relativePoint="LEFT" x="0" y="0"/>
									</Anchors>
								</Texture>
								<FontString name="WorldMapFrameAreaDescription" inherits="SubZoneTextFont">
									<Anchors>
										<Anchor point="TOP" relativeTo="WorldMapFrameAreaLabel" relativePoint="BOTTOM" x="0" y="-10"/>
									</Anchors>
								</FontString>
								<FontString name="WorldMapFrameAreaPetLevels" inherits="SubZoneTextFont">
									<Anchors>
										<Anchor point="TOP" relativeTo="WorldMapFrameAreaLabel" relativePoint="BOTTOM" x="0" y="-10"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
					</Frame>
					<Frame name="WorldMapLevelDropDown" inherits="UIDropDownMenuTemplate" id="2">
						<Anchors>
							<Anchor point="TOPLEFT" x="-17" y="2"/>
						</Anchors>
					</Frame>
					<Frame parentKey="TrackingOptionsButton">
						<Size x="32" y="32"/>
						<Anchors>
							<Anchor point="TOPRIGHT" relativeTo="WorldMapScrollFrame" x="-4" y="-4"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND" textureSubLevel="-1">
								<Texture atlas="MapCornerShadow-Right" useAtlasSize="true">
									<Anchors>
										<Anchor point="TOPRIGHT" x="4" y="4"/>
									</Anchors>
									<TexCoords left="0" right="1" top="1" bottom="0"/>
								</Texture>
							</Layer>
							<Layer level="BACKGROUND">
								<Texture parentKey="Background" file="Interface\Minimap\UI-Minimap-Background">
									<Size x="25" y="25"/>
									<Anchors>
										<Anchor point="TOPLEFT" x="2" y="-4"/>
									</Anchors>
									<Color r="1" g="1" b="1" a="1"/>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<Texture parentKey="Icon" file="Interface\Minimap\Tracking\None">
									<Size x="20" y="20"/>
									<Anchors>
										<Anchor point="TOPLEFT" x="6" y="-6"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="OVERLAY">
								<Texture parentKey="IconOverlay" hidden="true">
									<Anchors>
										<Anchor point="TOPLEFT" relativeKey="$parent.Icon"/>
										<Anchor point="BOTTOMRIGHT" relativeKey="$parent.Icon"/>
									</Anchors>
									<Color r="0.0" g="0.0" b="0.0" a="0.5"/>
								</Texture>
							</Layer>
						</Layers>
						<Frames>
							<Frame name="$parentDropDown" parentKey="DropDown" inherits="UIDropDownMenuTemplate" clampedToScreen="true" hidden="true">
								<Scripts>
									<OnLoad>
										UIDropDownMenu_Initialize(self, WorldMapTrackingOptionsDropDown_Initialize, "MENU");
									</OnLoad>
								</Scripts>
							</Frame>
							<Button parentKey="Button">
								<Size x="32" y="32"/>
								<Anchors>
									<Anchor point="TOPLEFT"/>
								</Anchors>
								<Layers>
									<Layer level="BORDER">
										<Texture parentKey="Border" file="Interface\Minimap\MiniMap-TrackingBorder">
											<Size x="54" y="54"/>
											<Anchors>
												<Anchor point="TOPLEFT"/>
											</Anchors>
										</Texture>
									</Layer>
									<Layer level="OVERLAY">
										<Texture parentKey="Shine" file="Interface\ComboFrame\ComboPoint" alphaMode="ADD" hidden="true">
											<Size x="27" y="27"/>
											<Anchors>
												<Anchor point="TOPLEFT">
													<Offset x="2" y="-2"/>
												</Anchor>
											</Anchors>
											<TexCoords left="0.5625" right="1" top="0" bottom="1"/>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnClick>
										local parent = self:GetParent();
										ToggleDropDownMenu(1, nil, parent.DropDown, parent, 0, -5);
										PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
									</OnClick>
									<OnMouseDown>
										local parent = self:GetParent();
										parent.Icon:SetPoint("TOPLEFT", parent, "TOPLEFT", 8, -8);
										parent.IconOverlay:Show();
									</OnMouseDown>
									<OnMouseUp>
										local parent = self:GetParent();
										parent.Icon:SetPoint("TOPLEFT", parent, "TOPLEFT", 6, -6);
										parent.IconOverlay:Hide();
									</OnMouseUp>
									<OnEnter>
										--GameTooltip:SetOwner(self, "ANCHOR_LEFT");
										--GameTooltip:SetText(TRACKING, 1, 1, 1);
										--GameTooltip:AddLine(MINIMAP_TRACKING_TOOLTIP_NONE, nil, nil, nil, true);
										--GameTooltip:Show();
									</OnEnter>
									<OnLeave>
										GameTooltip:Hide();
									</OnLeave>
								</Scripts>
								<HighlightTexture alphaMode="ADD" file="Interface\Minimap\UI-Minimap-ZoomButton-Highlight"/>
							</Button>
						</Frames>
					</Frame>
					<Button parentKey="OpenQuestPanelButton" hidden="true">
						<Size x="32" y="32"/>
						<Anchors>
							<Anchor point="BOTTOMRIGHT" x="-2" y="1"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture atlas="MapCornerShadow-Right" useAtlasSize="true">
									<Anchors>
										<Anchor point="BOTTOMRIGHT" x="2" y="-1"/>
									</Anchors>
								</Texture>
							</Layer>
						</Layers>
						<NormalTexture atlas="QuestCollapse-Show-Up"/>
						<PushedTexture atlas="QuestCollapse-Show-Down"/>
						<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD" setAllPoints="true">
							<Size x="48" y="48"/>
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
						</HighlightTexture>
						<Scripts>
							<OnClick>
								PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
								QuestMapFrame_Open(true);
							</OnClick>
						</Scripts>
					</Button>
					<Button parentKey="CloseQuestPanelButton" hidden="true">
						<Size x="32" y="32"/>
						<Anchors>
							<Anchor point="BOTTOMRIGHT" x="-2" y="1"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture atlas="MapCornerShadow-Right" useAtlasSize="true">
									<Anchors>
										<Anchor point="BOTTOMRIGHT" x="2" y="-1"/>
									</Anchors>
								</Texture>
							</Layer>
						</Layers>
						<NormalTexture atlas="QuestCollapse-Hide-Up"/>
						<PushedTexture atlas="QuestCollapse-Hide-Down"/>
						<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD" setAllPoints="true">
							<Size x="48" y="48"/>
							<Anchors>
								<Anchor point="CENTER"/>
							</Anchors>
						</HighlightTexture>
						<Scripts>
							<OnClick>
								PlaySound(SOUNDKIT.IG_MAINMENU_OPTION_CHECKBOX_ON);
								QuestMapFrame_Close(true);
							</OnClick>
						</Scripts>
					</Button>
					<Frame parentKey="BountyBoard" inherits="WorldMapBountyBoardTemplate" />
					<Frame parentKey="ActionButton" inherits="WorldMapActionButtonTemplate" />
				</Frames>
			</Frame>

			<Frame name="WorldMapFrameNavBar" inherits="NavBarTemplate" parentKey="NavBar">
				<Size x="1000" y="34"/>
				<Anchors>
					<Anchor point="TOPLEFT" relativeKey="$parent.BorderFrame" x="10" y="-23"/>
				</Anchors>
				<Layers>
					<Layer level="BORDER" textureSubLevel="-5">
						<Texture inherits="UI-Frame-InnerBotLeftCorner" parentKey="InsetBorderBottomLeft">
							<Anchors>
								<Anchor point="BOTTOMLEFT"  x="-3" y="-3"/>
							</Anchors>
						</Texture>
						<Texture inherits="UI-Frame-InnerBotRight" parentKey="InsetBorderBottomRight">
							<Anchors>
								<Anchor point="BOTTOMRIGHT"  x="3" y="-3"/>
							</Anchors>
						</Texture>
						<Texture inherits="_UI-Frame-InnerBotTile" parentKey="InsetBorderBottom">
							<Anchors>
								<Anchor point="BOTTOMLEFT" relativeKey="$parent.InsetBorderBottomLeft" relativePoint="BOTTOMRIGHT"/>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.InsetBorderBottomRight" relativePoint="BOTTOMLEFT"/>
							</Anchors>
						</Texture>
						<Texture inherits="!UI-Frame-InnerLeftTile" parentKey="InsetBorderLeft">
							<Anchors>
								<Anchor point="TOPLEFT" x="-3" y="0"/>
								<Anchor point="BOTTOMLEFT" relativeKey="$parent.InsetBorderBottomRight" relativePoint="TOPLEFT"/>
							</Anchors>
						</Texture>
						<Texture inherits="!UI-Frame-InnerRightTile" parentKey="InsetBorderRight">
							<Anchors>
								<Anchor point="TOPRIGHT"  x="3" y="0"/>
								<Anchor point="BOTTOMRIGHT" relativeKey="$parent.InsetBorderBottomRight" relativePoint="TOPRIGHT"/>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad function="WorldMapFrame_OnLoad"/>
			<OnShow function="WorldMapFrame_OnShow"/>
			<OnHide function="WorldMapFrame_OnHide"/>
			<OnEvent function="WorldMapFrame_OnEvent"/>
			<OnUpdate function="WorldMapFrame_OnUpdate"/>
			<OnKeyDown function="WorldMapFrame_OnKeyDown"/>
		</Scripts>
	</Frame>
	<GameTooltip name="WorldMapCompareTooltip1" clampedToScreen="true" frameStrata="TOOLTIP" hidden="true" parent="WorldMapFrame" inherits="ShoppingTooltipTemplate"/>
	<GameTooltip name="WorldMapCompareTooltip2" clampedToScreen="true" frameStrata="TOOLTIP" hidden="true" parent="WorldMapFrame" inherits="ShoppingTooltipTemplate"/>
	<GameTooltip name="WorldMapTooltip" frameStrata="TOOLTIP" hidden="true" parent="WorldMapFrame" inherits="GameTooltipTemplate">
		<Frames>
			<Frame parentKey="ItemTooltip" inherits="EmbeddedItemTooltip" hidden="true">
				<Size x="100" y="100"/>
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativePoint="BOTTOMLEFT" x="10" y="13"/>
				</Anchors>
				<KeyValues>
					<KeyValue key="yspacing" value="13" type="number"/>
				</KeyValues>
				<Scripts>
					<OnLoad inherit="prepend">
						self.Tooltip.shoppingTooltips = { WorldMapCompareTooltip1, WorldMapCompareTooltip2 };
					</OnLoad>
				</Scripts>
			</Frame>
		</Frames>
		<Scripts>
			<OnLoad>
				self:SetBackdropBorderColor(TOOLTIP_DEFAULT_COLOR.r, TOOLTIP_DEFAULT_COLOR.g, TOOLTIP_DEFAULT_COLOR.b);
				self:SetBackdropColor(TOOLTIP_DEFAULT_BACKGROUND_COLOR.r, TOOLTIP_DEFAULT_BACKGROUND_COLOR.g, TOOLTIP_DEFAULT_BACKGROUND_COLOR.b);
				self.shoppingTooltips = { WorldMapCompareTooltip1, WorldMapCompareTooltip2 };
			</OnLoad>
			<OnUpdate>
				if ( self.recalculatePadding ) then
					self.recalculatePadding = nil;
					GameTooltip_CalculatePadding(self);
				end
				self.updateTooltip = (self.updateTooltip or TOOLTIP_UPDATE_TIME) - elapsed;
				if ( self.updateTooltip > 0 ) then
					return;
				end
				self.updateTooltip = TOOLTIP_UPDATE_TIME;

				local owner = self:GetOwner();
				if ( owner and owner.UpdateTooltip ) then
					owner:UpdateTooltip();
				end
			</OnUpdate>
			<OnHide inherit="prepend">
				WorldMapTooltip.ItemTooltip:Hide();
				self:SetPadding(0, 0);
			</OnHide>
			<OnSizeChanged inherit="prepend">
				self.recalculatePadding = true;
			</OnSizeChanged>
		</Scripts>
	</GameTooltip>
	<Frame name="WorldMapScreenAnchor" hidden="true" movable="true">
		<Size x="1" y="1"/>
		<Anchors>
			<Anchor point="TOPLEFT" x="10" y="-118"/>
		</Anchors>
	</Frame>
	<Button name="WorldMapTitleButton" parent="WorldMapFrame">
		<Size x="0" y="22" />
		<Anchors>
			<Anchor point="TOPLEFT" x="0" y="0" />
			<Anchor point="TOPRIGHT" x="-46" y="0" />
		</Anchors>
		<Scripts>
			<OnLoad function="WorldMapTitleButton_OnLoad" />
			<OnClick function="WorldMapTitleButton_OnClick" />
			<OnDragStart function="WorldMapTitleButton_OnDragStart" />
			<OnDragStop function="WorldMapTitleButton_OnDragStop" />
		</Scripts>
	</Button>
	<Frame name="WorldMapTaskTooltipStatusBar" inherits="TooltipProgressBarTemplate"/>
	
	<!-- This refers to the cosmic-style buttons statically so it needs to be loaded after the xml. -->
	<Script file="WorldMapFrameCosmicButtons.lua"/>
</Ui>
